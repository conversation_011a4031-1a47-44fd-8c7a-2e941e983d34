﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Reflection;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.ViewInfo;
using eBay.Service.Core.Sdk;
using Fonlow.Net.Http;
using TaxonomyAPI;
using uBuyFirst.GUI;
using uBuyFirst.Intl;
using uBuyFirst.Item;
using uBuyFirst.Prefs;
using uBuyFirst.Properties;
using uBuyFirst.Tools;
using Category = uBuyFirst.GUI.Category;

namespace uBuyFirst
{
    public partial class FormSpecificsColumns : RibbonForm
    {
        public FormSpecificsColumns()
        {
            InitializeComponent();
            boxAllCatSpecifics.DataSource = ItemSpecifics.CategorySpecificsList;
            boxCustomColumnSite.Properties.Items.AddRange(Intl.CountryProvider.GetEbaySiteNameList());
            CategoryLookup.InitTreelistLookup(treeListLookUpEdit1TreeList, boxCustomColumnSite.Text).ConfigureAwait(false);
        }

        private void boxCurrentCatSpecifics_DrawItem(object sender, ListBoxDrawItemEventArgs e)
        {
            FormatCustomColumnListbox(e);
        }

        private void boxAllCatSpecifics_DrawItem(object sender, ListBoxDrawItemEventArgs e)
        {
            FormatCustomColumnListbox(e);
        }

        private static void FormatCustomColumnListbox(ListBoxDrawItemEventArgs e)
        {
            try
            {
                string categoryName = ((CategorySpecific)e.Item).CategoryName;
                if (categoryName != null)
                {
                    string examples = ((CategorySpecific)e.Item).Examples;
                    if (examples != null)
                    {
                        var s = examples.Any() ? $"{categoryName}  ({examples})" : categoryName;
                        PropertyInfo pi = e.GetType().GetProperty("ViewInfo", BindingFlags.Instance | BindingFlags.NonPublic);
                        if (pi != null)
                        {
                            var vi = (CheckedListBoxViewInfo)pi.GetValue(e, null);
                            vi.GetItemInfoByPoint(e.Bounds.Location).Text = s;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("FormatCustomColumnListbox: ", ex);
            }
        }

        private void btnCreateCustomColumns_Click(object sender, EventArgs e)
        {
            try
            {
                foreach (var item in boxUserSelectedSpecifics.CheckedItems)
                {
                    var specific = (CategorySpecific)((ListBoxItem)item).Value;
                    if (ItemSpecifics.CategorySpecificsList.All(a => a.CategoryName != specific.CategoryName))
                    {
                        ItemSpecifics.CategorySpecificsList.Add(specific);
                    }
                }

                boxUserSelectedSpecifics.Refresh();
                boxAllCatSpecifics.Refresh();
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("CreateCustomColumns: ", ex);
            }
        }

        private void btnRemoveCustomColumns_Click(object sender, EventArgs e)
        {
            try
            {
                List<CategorySpecific> list = boxAllCatSpecifics.CheckedItems.Cast<CategorySpecific>().ToList();
                for (int i = list.Count - 1; i >= 0; i--)
                {
                    if (ItemSpecifics.CategorySpecificsList.Contains(list[i]))
                        ItemSpecifics.CategorySpecificsList.Remove(list[i]);
                }

                boxUserSelectedSpecifics.Refresh();
                boxAllCatSpecifics.Refresh();
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("RemoveCustomColumns: ", ex);
            }
        }

        private async void btnFindCustomColumns_Click(object sender, EventArgs e)
        {
            btnFindCustomColumns.Enabled = false;
            if (treeListLookUpEdit1.EditValue is not Category cat)
            {
                XtraMessageBox.Show("Please enter a valid Category ID.");
                btnFindCustomColumns.Enabled = true;
                return;
            }

            if (cat.LeafCategoryTreeNode != true)
            {
                XtraMessageBox.Show("Top level categories not supported. Please select a sub category.");
                btnFindCustomColumns.Enabled = true;
                return;
            }

            if (treeListLookUpEdit1.EditValue.ToString().Trim().Length == 0)
            {
                XtraMessageBox.Show(En_US.FormCustomColumns_btnFindCustomColumns_Click_Please__provide_a_numeric_Category_ID);
            }
            else
            {
                btnFindCustomColumns.Enabled = false;
                boxUserSelectedSpecifics.Items.Clear();

                var categoryID = treeListLookUpEdit1.EditValue.ToString().Trim();
                var siteName = boxCustomColumnSite.EditValue.ToString();
                var marketplaceID = CountryProvider.GetEbaySite(siteName).BrowseAPIID;
                var aspectList = await GetCategorySpecificsTaxonomyAPI(categoryID, marketplaceID);
                foreach (var aspect in aspectList)
                {
                    boxUserSelectedSpecifics.Items.Add(aspect);
                }
            }

            btnFindCustomColumns.Enabled = true;
        }

        private async Task<List<CategorySpecific>> GetCategorySpecificsTaxonomyAPI(string categoryID, string marketplaceID)
        {
            var client = new HttpClient
            {
                BaseAddress = new Uri(ConnectionConfig.TaxonomyAPIURL)
            };
            var aspectList = new List<CategorySpecific>();
            var taxonomyAPIClass = new TaxonomyAPIClass(client);

            try
            {
                var marketplaceTreeID = await taxonomyAPIClass.GetDefaultCategoryTreeIdAsync(marketplaceID, HandleHeaders);
                var categoryAspectsResponse = await taxonomyAPIClass.GetItemAspectsForCategoryAsync(categoryID, marketplaceTreeID.CategoryTreeId, HandleHeaders);

                var aspects = categoryAspectsResponse.Aspects;
                if (aspects?.Length > 0)
                {
                    var recommendations = aspects[0];
                    string examples = "";
                    foreach (var aspect in aspects)
                    {
                        if (aspect.AspectValues != null)
                        {
                            var asp = aspect.AspectValues.Select(a => a.LocalizedValue).ToList();

                            examples = string.Join(", ", asp);
                        }

                        var cs = new CategorySpecific
                        {
                            CategoryName = aspect.LocalizedAspectName,
                            Examples = examples
                        };
                        aspectList.Add(cs);
                    }

                    if (categoryID == "9355")
                    {
                        if (aspectList.All(a => a.CategoryName != "Carrier"))
                        {
                            var cs = new CategorySpecific
                            {
                                CategoryName = "Carrier",
                                Examples = ""
                            };
                            aspectList.Add(cs);
                        }
                    }
                }
            }
            catch (ApiException ex)
            {
                Form1.Log.Error("{0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, ex);
                XtraMessageBox.Show(ex.Message);
            }
            catch (WebApiRequestException ex)
            {
                if (!string.IsNullOrWhiteSpace(ex.Response))
                {
                    XtraMessageBox.Show(ex.Response);
                    return aspectList;
                }
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("GetCategorySpecificsTaxonomyAPI", ex);
            }

            return aspectList;
        }

        private void HandleHeaders(HttpRequestHeaders headers)
        {
            string token;
            if (!string.IsNullOrEmpty(ConnectionConfig.Token))
                token = ConnectionConfig.Token;
            else
                token = ProgramState.HWID;

            headers.Add("Authorization", "Bearer " + token);
        }

        private void btnClose_Click(object sender, EventArgs e)
        {
            Close();
        }

        private void FormCustomColumns_Load(object sender, EventArgs e)
        {
            //AutoMeasurement.Client.TrackScreenView("Screen - " + Text);
        }

        private void FormCustomColumns_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == 27)
                Close();
        }

        private void hyperlink_Click(object sender, EventArgs e)
        {
            var hyperLink = (HyperlinkLabelControl)sender;
            if (hyperLink.Tag != null)
                Process.Start(hyperLink.Tag.ToString());
        }

        private async void treeListLookUpEdit1_BeforePopup(object sender, EventArgs e)
        {
            await CategoryLookup.InitTreelistLookup(treeListLookUpEdit1TreeList, boxCustomColumnSite.Text);
        }

        private void boxCustomColumnSite_SelectedIndexChanged(object sender, EventArgs e)
        {
            CategoryLookup.DataSourceToNull(treeListLookUpEdit1TreeList);
        }

        private void btnAddSpecificManually_Click(object sender, EventArgs e)
        {
            try
            {
                var specificName = txtManualSpecific.Text;
                var specific = new CategorySpecific()
                {
                    CategoryName = specificName
                };
                if (ItemSpecifics.CategorySpecificsList.All(a => a.CategoryName != specific.CategoryName))
                {
                    ItemSpecifics.CategorySpecificsList.Add(specific);
                }

                txtManualSpecific.Clear();
                boxUserSelectedSpecifics.Refresh();
                boxAllCatSpecifics.Refresh();
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("CreateCustomColumns: ", ex);
            }
        }
    }
}
