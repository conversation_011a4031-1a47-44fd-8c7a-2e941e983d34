using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.RestockReporting.Models;
using uBuyFirst.RestockReporting.Services;
using uBuyFirst.Restocker.Models;

namespace uBuyFirst.Tests.RestockReporting.Services
{
    [TestClass]
    public class ItemHistoryLoggerTests
    {
        private FileItemHistoryLogger _logger;
        private string _testBasePath;
        private string _testErrorPath;

        [TestInitialize]
        public void Setup()
        {
            // Create temporary test directories
            _testBasePath = Path.Combine(Path.GetTempPath(), "ItemHistoryTests", Guid.NewGuid().ToString());
            _testErrorPath = Path.Combine(_testBasePath, "errors");

            Directory.CreateDirectory(_testBasePath);
            Directory.CreateDirectory(_testErrorPath);

            var options = new ItemHistoryOptions
            {
                BasePath = _testBasePath,
                ErrorLogPath = _testErrorPath,
                EnableLogging = true,
                CreateDailyFolders = true
            };

            _logger = new FileItemHistoryLogger(options);
        }

        [TestCleanup]
        public void Cleanup()
        {
            _logger?.Dispose();

            // Clean up test directories
            if (Directory.Exists(_testBasePath))
            {
                Directory.Delete(_testBasePath, true);
            }
        }

        [TestMethod]
        public async Task LogItemProcessingAsync_WithValidContext_CreatesJsonFile()
        {
            // Arrange
            var context = CreateTestItemProcessingContext();

            // Act
            await _logger.LogItemProcessingAsync(context);

            // Assert
            var expectedFolder = Path.Combine(_testBasePath, context.Timestamp.ToString("yyyy-MM-dd"));
            Assert.IsTrue(Directory.Exists(expectedFolder), "Daily folder should be created");

            var jsonFiles = Directory.GetFiles(expectedFolder, "*.json");
            Assert.AreEqual(1, jsonFiles.Length, "Should create exactly one JSON file");

            var jsonContent = File.ReadAllText(jsonFiles[0]);
            Assert.IsTrue(jsonContent.Contains("\"itemId\": \"123456789\""), "JSON should contain item ID");
            Assert.IsTrue(jsonContent.Contains("\"outcome\": \"purchased\""), "JSON should contain outcome");
        }

        [TestMethod]
        public async Task LogItemProcessingAsync_WithSuccessfulPurchase_IgnoresSecondWrite()
        {
            // Arrange
            var context1 = CreateTestItemProcessingContext(); // This creates a successful purchase context
            var context2 = CreateTestItemProcessingContext();
            context2.Reason = "Updated reason - should be ignored"; // Change content to verify ignore behavior

            // Act
            await _logger.LogItemProcessingAsync(context1);
            await _logger.LogItemProcessingAsync(context2); // This should be ignored

            // Assert
            var expectedFolder = Path.Combine(_testBasePath, context1.Timestamp.ToString("yyyy-MM-dd"));
            var jsonFiles = Directory.GetFiles(expectedFolder, "*.json");
            Assert.AreEqual(1, jsonFiles.Length, "Should create only one JSON file");

            // Verify the file contains the original content (not updated)
            var jsonContent = File.ReadAllText(jsonFiles[0]);
            Assert.IsTrue(jsonContent.Contains("\"reason\": \"Successfully purchased 2 items\""), "File should contain original content");
            Assert.IsFalse(jsonContent.Contains("should be ignored"), "File should not contain updated content");
        }

        [TestMethod]
        public async Task LogItemProcessingAsync_WithFailedPurchase_OverwritesFile()
        {
            // Arrange
            var context1 = CreateFailedPurchaseContext();
            var context2 = CreateFailedPurchaseContext();
            context2.Reason = "Updated failed purchase reason"; // Change content to verify overwrite

            // Act
            await _logger.LogItemProcessingAsync(context1);
            await _logger.LogItemProcessingAsync(context2);

            // Assert
            var expectedFolder = Path.Combine(_testBasePath, context1.Timestamp.ToString("yyyy-MM-dd"));
            var jsonFiles = Directory.GetFiles(expectedFolder, "*.json");
            Assert.AreEqual(1, jsonFiles.Length, "Should create only one JSON file (overwritten)");

            // Verify the file contains the updated content
            var jsonContent = File.ReadAllText(jsonFiles[0]);
            Assert.IsTrue(jsonContent.Contains("\"reason\": \"Updated failed purchase reason\""), "File should contain updated content");
            Assert.IsFalse(jsonContent.Contains("Failed to purchase"), "File should not contain original content");
        }

        [TestMethod]
        public async Task LogItemProcessingAsync_WithFilteredOutItem_OverwritesFile()
        {
            // Arrange
            var context1 = CreateFilteredOutContext();
            var context2 = CreateFilteredOutContext();
            context2.Reason = "Updated filtered reason"; // Change content to verify overwrite

            // Act
            await _logger.LogItemProcessingAsync(context1);
            await _logger.LogItemProcessingAsync(context2);

            // Assert
            var expectedFolder = Path.Combine(_testBasePath, context1.Timestamp.ToString("yyyy-MM-dd"));
            var jsonFiles = Directory.GetFiles(expectedFolder, "*.json");
            Assert.AreEqual(1, jsonFiles.Length, "Should create only one JSON file (overwritten)");

            // Verify the file contains the updated content
            var jsonContent = File.ReadAllText(jsonFiles[0]);
            Assert.IsTrue(jsonContent.Contains("\"reason\": \"Updated filtered reason\""), "File should contain updated content");
        }



        [TestMethod]
        public async Task LogItemProcessingAsync_WithNullContext_DoesNotThrow()
        {
            // Act & Assert - Should not throw
            await _logger.LogItemProcessingAsync(null);
        }

        [TestMethod]
        public async Task LogItemProcessingAsync_WithInvalidPath_LogsErrorButContinues()
        {
            // Arrange
            var invalidOptions = new ItemHistoryOptions
            {
                BasePath = "Z:\\InvalidPath\\That\\Does\\Not\\Exist",
                ErrorLogPath = _testErrorPath,
                EnableLogging = true
            };
            var invalidLogger = new FileItemHistoryLogger(invalidOptions);
            var context = CreateTestItemProcessingContext();

            // Act - Should not throw
            await invalidLogger.LogItemProcessingAsync(context);

            // Assert - Error should be logged
            var errorFiles = Directory.GetFiles(_testErrorPath, "*_errors.log");
            Assert.IsTrue(errorFiles.Length > 0, "Should create error log file");

            var errorContent = File.ReadAllText(errorFiles[0]);
            Assert.IsTrue(errorContent.Contains("Failed to write item history"), "Should log the error");
        }

        [TestMethod]
        public void FileItemHistoryLogger_WithDisabledLogging_DoesNotLog()
        {
            // Arrange
            var disabledOptions = new ItemHistoryOptions
            {
                BasePath = _testBasePath,
                ErrorLogPath = _testErrorPath,
                EnableLogging = false
            };
            var disabledLogger = new FileItemHistoryLogger(disabledOptions);
            var context = CreateTestItemProcessingContext();

            // Act
            var task = disabledLogger.LogItemProcessingAsync(context);

            // Assert
            Assert.IsTrue(task.IsCompleted, "Should complete immediately when disabled");

            var expectedFolder = Path.Combine(_testBasePath, context.Timestamp.ToString("yyyy-MM-dd"));
            Assert.IsFalse(Directory.Exists(expectedFolder), "Should not create folder when disabled");
        }

        [TestMethod]
        public void GenerateFileName_WithValidContext_CreatesCorrectFormat()
        {
            // Arrange
            var context = CreateTestItemProcessingContext();

            // Act
            var fileName = _logger.GenerateFileName(context);

            // Assert
            // Check that filename contains all expected components (timestamp will vary)
            Assert.IsTrue(fileName.StartsWith("123456789_JOB-001_kw-test-123_Test_Filter_"), "Should start with ItemId_JobId_KeywordId_FilterAlias_");
            Assert.IsTrue(fileName.EndsWith(".json"), "Should end with .json");
            Assert.IsTrue(fileName.Contains("143022_"), "Should contain timestamp component");
        }

        [TestMethod]
        public void GenerateFileName_WithSpecialCharactersInFilterAlias_SanitizesFilename()
        {
            // Arrange
            var context = CreateTestItemProcessingContext();
            context.FilterRule.FilterAlias = "Test/Filter: With<Special>Characters|And\"Quotes*?";

            // Act
            var fileName = _logger.GenerateFileName(context);

            // Assert
            Assert.IsTrue(fileName.StartsWith("123456789_JOB-001_kw-test-123_Test_Filter__With_Special_Characters_And_Quotes___"), "Should sanitize special characters in filter alias");
            Assert.IsTrue(fileName.EndsWith(".json"), "Should end with .json");
        }

        [TestMethod]
        public void GenerateFileName_WithLongFilterAlias_TruncatesAlias()
        {
            // Arrange
            var context = CreateTestItemProcessingContext();
            context.FilterRule.FilterAlias = "This is a very long filter alias that exceeds the maximum allowed length for filenames and should be truncated";

            // Act
            var fileName = _logger.GenerateFileName(context);

            // Assert
            var expectedAlias = "This_is_a_very_long_filter_alias_that_exceeds_the_"; // 50 chars max
            Assert.IsTrue(fileName.StartsWith($"123456789_JOB-001_kw-test-123_{expectedAlias}_"), "Should truncate long filter alias to 50 characters");
            Assert.IsTrue(fileName.EndsWith(".json"), "Should end with .json");
        }

        [TestMethod]
        public void GenerateFileName_WithNullFilterAlias_UsesUnknown()
        {
            // Arrange
            var context = CreateTestItemProcessingContext();
            context.FilterRule.FilterAlias = null;

            // Act
            var fileName = _logger.GenerateFileName(context);

            // Assert
            Assert.IsTrue(fileName.StartsWith("123456789_JOB-001_kw-test-123_unknown_"), "Should use 'unknown' when filter alias is null");
            Assert.IsTrue(fileName.EndsWith(".json"), "Should end with .json");
        }

        [TestMethod]
        public void GenerateFileName_WithDifferentFilterAliases_CreatesDifferentFilenames()
        {
            // Arrange
            var context1 = CreateTestItemProcessingContext();
            context1.FilterRule.FilterAlias = "Filter A";

            var context2 = CreateTestItemProcessingContext();
            context2.FilterRule.FilterAlias = "Filter B";

            // Act
            var fileName1 = _logger.GenerateFileName(context1);
            var fileName2 = _logger.GenerateFileName(context2);

            // Assert
            Assert.IsTrue(fileName1.Contains("Filter_A_"), "Should include Filter A in filename");
            Assert.IsTrue(fileName2.Contains("Filter_B_"), "Should include Filter B in filename");
            Assert.AreNotEqual(fileName1, fileName2, "Different filter aliases should create different filenames");
        }

        [TestMethod]
        public void GenerateFileName_WithSameContextCalledTwice_CreatesDifferentFilenames()
        {
            // Arrange
            var context1 = CreateTestItemProcessingContext();
            var context2 = CreateTestItemProcessingContext();
            // Simulate slight time difference
            context2.Timestamp = context1.Timestamp.AddMilliseconds(1);

            // Act
            var fileName1 = _logger.GenerateFileName(context1);
            var fileName2 = _logger.GenerateFileName(context2);

            // Assert
            Assert.AreNotEqual(fileName1, fileName2, "Same context with different timestamps should create different filenames");
        }

        private ItemProcessingContext CreateTestItemProcessingContext()
        {
            return new ItemProcessingContext
            {
                Timestamp = new DateTime(2024, 12, 19, 14, 30, 22, DateTimeKind.Utc),
                Outcome = "purchased",
                Reason = "Successfully purchased 2 items",
                ItemData = new ItemHistoryData
                {
                    ItemId = "123456789",
                    Title = "Test Item",
                    CurrentPrice = 25.99m,
                    Condition = "New",
                    Seller = "test-seller"
                },
                KeywordState = new KeywordSnapshot
                {
                    KeywordId = "kw-test-123",
                    Alias = "Test Keyword",
                    JobId = "JOB-001",
                    RequiredQuantity = 5,
                    PurchasedQuantity = 2
                },
                FilterRule = new FilterRuleContext
                {
                    FilterAlias = "Test Filter",
                    Expression = "Price <= 40",
                    Matched = true,
                    EvaluationResult = "Filter matched"
                },
                TransactionResult = new TransactionResult
                {
                    Attempted = true,
                    Success = true,
                    PurchasePrice = 25.99m,
                    Quantity = 2
                }
            };
        }

        private ItemProcessingContext CreateFailedPurchaseContext()
        {
            return new ItemProcessingContext
            {
                Timestamp = new DateTime(2024, 12, 19, 14, 30, 22, DateTimeKind.Utc),
                Outcome = "purchase_failed",
                Reason = "Failed to purchase - insufficient funds",
                ItemData = new ItemHistoryData
                {
                    ItemId = "987654321",
                    Title = "Failed Test Item",
                    CurrentPrice = 15.99m,
                    Condition = "Used",
                    Seller = "failed-seller"
                },
                KeywordState = new KeywordSnapshot
                {
                    KeywordId = "kw-failed-123",
                    Alias = "Failed Test Keyword",
                    JobId = "JOB-002",
                    RequiredQuantity = 3,
                    PurchasedQuantity = 0
                },
                FilterRule = new FilterRuleContext
                {
                    FilterAlias = "Test Filter",
                    Expression = "Price <= 40",
                    Matched = true,
                    EvaluationResult = "Filter matched"
                },
                TransactionResult = new TransactionResult
                {
                    Attempted = true,
                    Success = false,
                    ErrorMessage = "Insufficient funds"
                }
            };
        }

        private ItemProcessingContext CreateFilteredOutContext()
        {
            return new ItemProcessingContext
            {
                Timestamp = new DateTime(2024, 12, 19, 14, 30, 22, DateTimeKind.Utc),
                Outcome = "filtered_out",
                Reason = "Item filtered out - price too high",
                ItemData = new ItemHistoryData
                {
                    ItemId = "555666777",
                    Title = "Filtered Test Item",
                    CurrentPrice = 99.99m,
                    Condition = "New",
                    Seller = "filtered-seller"
                },
                KeywordState = new KeywordSnapshot
                {
                    KeywordId = "kw-filtered-123",
                    Alias = "Filtered Test Keyword",
                    JobId = "JOB-003",
                    RequiredQuantity = 2,
                    PurchasedQuantity = 0
                },
                FilterRule = new FilterRuleContext
                {
                    FilterAlias = "Price Filter",
                    Expression = "Price <= 50",
                    Matched = false,
                    EvaluationResult = "Filter did not match - price too high"
                },
                TransactionResult = new TransactionResult
                {
                    Attempted = false,
                    Success = false
                }
            };
        }
    }
}
