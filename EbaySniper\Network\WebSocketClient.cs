﻿using System;
using System.Net.WebSockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace uBuyFirst.Network
{
    internal sealed class WebSocketClient
    {
        private ClientWebSocket _client = new();
        private readonly Uri _serverUri;
        private readonly CancellationTokenSource _cts = new();
        private bool _intentionalDisconnect;

        public event EventHandler<string>? MessageReceived;

        public WebSocketClient(string serverUrl)
        {
            _serverUri = new Uri(serverUrl);
        }

        public async Task Connect()
        {
            var isConnected = await TryConnect();
            if (isConnected)
            {
                await Listen();
            }
            else
            {
                Console.WriteLine(@"Failed to establish connection.");
            }
        }

        private async Task<bool> TryConnect(int maxAttempts = int.MaxValue)
        {
            var attempts = 0;
            while (attempts < maxAttempts)
            {
                try
                {
                    Console.WriteLine(@"Attempting to connect...");
                    await _client.ConnectAsync(_serverUri, _cts.Token);
                    if (_client.State == WebSocketState.Open)
                    {
                        Console.WriteLine(@"Connected to server");
                        return true; // Successfully connected
                    }
                }
                catch
                {
                    attempts++;
                    Console.WriteLine(@"Connection failed. Retrying...");
                    await Task.Delay(5000); // Wait for 5 seconds before retrying
                }
            }

            return false; // Failed to connect after maxAttempts
        }

        private async Task Listen()
        {
            var responseBuffer = new byte[1024];
            while (true) // Infinite loop to keep listening and reconnecting if necessary
            {
                if (_intentionalDisconnect)
                {
                    break; // Exit the loop if disconnected intentionally
                }

                if (_client.State == WebSocketState.Open)
                {
                    try
                    {
                        var response = await _client.ReceiveAsync(new ArraySegment<byte>(responseBuffer), _cts.Token);
                        var message = Encoding.UTF8.GetString(responseBuffer, 0, response.Count);
                        OnMessageReceived(message);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($@"Error receiving data: {ex.Message}. Attempting to reconnect...");
                        await Reconnect();
                    }
                }
                else
                {
                    Console.WriteLine(@"Connection is not open. Attempting to reconnect...");
                    await Reconnect();
                }
            }
        }

        private async Task Reconnect()
        {
            if (_intentionalDisconnect)
            {
                return; // Do not reconnect if disconnected intentionally
            }

            _client.Dispose();
            _client = new ClientWebSocket();
            await TryConnect();
        }

        public async Task SendMessage(string message)
        {
            if (_client.State == WebSocketState.Open)
            {
                var buffer = Encoding.UTF8.GetBytes(message);
                await _client.SendAsync(new ArraySegment<byte>(buffer), WebSocketMessageType.Text, endOfMessage: true, _cts.Token);
            }
        }

        public async Task Disconnect()
        {
            _intentionalDisconnect = true; // Mark that this is an intentional disconnect
            if (_client.State != WebSocketState.Closed)
            {
                await _client.CloseAsync(WebSocketCloseStatus.NormalClosure, string.Empty, CancellationToken.None);
                Console.WriteLine(@"Disconnected from server");
            }
        }

        private void OnMessageReceived(string message)
        {
            MessageReceived?.Invoke(this, message);
        }
    }
}
