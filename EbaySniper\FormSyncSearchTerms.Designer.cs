﻿// Licensed to the .NET Foundation under one or more agreements.
// The .NET Foundation licenses this file to you under the MIT license.
// See the LICENSE file in the project root for more information.

namespace uBuyFirst
{
    partial class FormSyncSearchTerms
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.ribbon = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.ribbonPageGroup1 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPage1 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.txtFileUrl = new DevExpress.XtraEditors.TextEdit();
            this.btnFetchFile = new DevExpress.XtraEditors.SimpleButton();
            this.memoPreviewFile = new DevExpress.XtraEditors.MemoEdit();
            this.lblGoogleSpreadsheetUrl = new DevExpress.XtraEditors.LabelControl();
            this.lblPreviewFile = new DevExpress.XtraEditors.LabelControl();
            this.timeSpanEditFetchInterval = new DevExpress.XtraEditors.TimeSpanEdit();
            this.lblFileFetchInterval = new DevExpress.XtraEditors.LabelControl();
            this.checkEditEnableAutoFetch = new DevExpress.XtraEditors.CheckEdit();
            this.lblDescription = new DevExpress.XtraEditors.LabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.ribbon)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFileUrl.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.memoPreviewFile.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeSpanEditFetchInterval.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditEnableAutoFetch.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // ribbon
            // 
            this.ribbon.ExpandCollapseItem.Id = 0;
            this.ribbon.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.ribbon.ExpandCollapseItem});
            this.ribbon.Location = new System.Drawing.Point(0, 0);
            this.ribbon.MaxItemId = 1;
            this.ribbon.Name = "ribbon";
            this.ribbon.ShowApplicationButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbon.ShowPageHeadersMode = DevExpress.XtraBars.Ribbon.ShowPageHeadersMode.Hide;
            this.ribbon.Size = new System.Drawing.Size(540, 27);
            // 
            // ribbonPageGroup1
            // 
            this.ribbonPageGroup1.Name = "ribbonPageGroup1";
            this.ribbonPageGroup1.Text = "ribbonPageGroup1";
            // 
            // ribbonPage1
            // 
            this.ribbonPage1.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup1});
            this.ribbonPage1.Name = "ribbonPage1";
            this.ribbonPage1.Text = "ribbonPage1";
            // 
            // txtFileUrl
            // 
            this.txtFileUrl.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtFileUrl.Location = new System.Drawing.Point(12, 64);
            this.txtFileUrl.MenuManager = this.ribbon;
            this.txtFileUrl.Name = "txtFileUrl";
            this.txtFileUrl.Properties.NullValuePrompt = "https://docs.google.com/spreadsheets/d/135xaduCNEGPX3tY43E3b7BMg2t3yWuUY1gFMYuUht" +
    "h8/edit?gid=1281444697#gid=1281444697";
            this.txtFileUrl.Size = new System.Drawing.Size(389, 20);
            this.txtFileUrl.TabIndex = 2;
            this.txtFileUrl.EditValueChanged += new System.EventHandler(this.txtFileUrl_EditValueChanged);
            // 
            // btnFetchFile
            // 
            this.btnFetchFile.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnFetchFile.Location = new System.Drawing.Point(407, 62);
            this.btnFetchFile.Name = "btnFetchFile";
            this.btnFetchFile.Size = new System.Drawing.Size(112, 23);
            this.btnFetchFile.TabIndex = 3;
            this.btnFetchFile.Text = "Fetch and Apply";
            this.btnFetchFile.Click += new System.EventHandler(this.btnFetchFile_Click);
            // 
            // memoPreviewFile
            // 
            this.memoPreviewFile.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.memoPreviewFile.Enabled = false;
            this.memoPreviewFile.Location = new System.Drawing.Point(12, 110);
            this.memoPreviewFile.MenuManager = this.ribbon;
            this.memoPreviewFile.Name = "memoPreviewFile";
            this.memoPreviewFile.Size = new System.Drawing.Size(389, 102);
            this.memoPreviewFile.TabIndex = 4;
            // 
            // lblGoogleSpreadsheetUrl
            // 
            this.lblGoogleSpreadsheetUrl.Location = new System.Drawing.Point(12, 44);
            this.lblGoogleSpreadsheetUrl.Name = "lblGoogleSpreadsheetUrl";
            this.lblGoogleSpreadsheetUrl.Size = new System.Drawing.Size(70, 13);
            this.lblGoogleSpreadsheetUrl.TabIndex = 5;
            this.lblGoogleSpreadsheetUrl.Text = "Spreasheet url";
            // 
            // lblPreviewFile
            // 
            this.lblPreviewFile.Location = new System.Drawing.Point(12, 91);
            this.lblPreviewFile.Name = "lblPreviewFile";
            this.lblPreviewFile.Size = new System.Drawing.Size(57, 13);
            this.lblPreviewFile.TabIndex = 6;
            this.lblPreviewFile.Text = "File Preview";
            // 
            // timeSpanEditFetchInterval
            // 
            this.timeSpanEditFetchInterval.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.timeSpanEditFetchInterval.EditValue = System.TimeSpan.Parse("30.00:00:00");
            this.timeSpanEditFetchInterval.Location = new System.Drawing.Point(120, 232);
            this.timeSpanEditFetchInterval.MenuManager = this.ribbon;
            this.timeSpanEditFetchInterval.Name = "timeSpanEditFetchInterval";
            this.timeSpanEditFetchInterval.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.timeSpanEditFetchInterval.Properties.MaskSettings.Set("mask", "[d\'d\'.]hh\'h\':mm\'m\'");
            this.timeSpanEditFetchInterval.Properties.MaskSettings.Set("defaultPart", DevExpress.Data.Mask.TimeSpanMaskPart.Minutes);
            this.timeSpanEditFetchInterval.Properties.MaskSettings.Set("allowNegativeValues", false);
            this.timeSpanEditFetchInterval.Properties.MaskSettings.Set("inputMode", DevExpress.Data.Mask.TimeSpanMaskInputMode.Restricted);
            this.timeSpanEditFetchInterval.Properties.MaxValue = System.TimeSpan.Parse("30.00:00:00");
            this.timeSpanEditFetchInterval.Properties.MinValue = System.TimeSpan.Parse("00:01:00");
            this.timeSpanEditFetchInterval.Properties.UseMaskAsDisplayFormat = true;
            this.timeSpanEditFetchInterval.Properties.EditValueChanged += new System.EventHandler(this.timeSpanEditFetchInterval_Properties_EditValueChanged);
            this.timeSpanEditFetchInterval.Size = new System.Drawing.Size(103, 20);
            this.timeSpanEditFetchInterval.TabIndex = 7;
            // 
            // lblFileFetchInterval
            // 
            this.lblFileFetchInterval.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.lblFileFetchInterval.Location = new System.Drawing.Point(12, 235);
            this.lblFileFetchInterval.Name = "lblFileFetchInterval";
            this.lblFileFetchInterval.Size = new System.Drawing.Size(90, 13);
            this.lblFileFetchInterval.TabIndex = 9;
            this.lblFileFetchInterval.Text = "Auto fetch interval";
            // 
            // checkEditEnableAutoFetch
            // 
            this.checkEditEnableAutoFetch.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.checkEditEnableAutoFetch.Location = new System.Drawing.Point(12, 273);
            this.checkEditEnableAutoFetch.MenuManager = this.ribbon;
            this.checkEditEnableAutoFetch.Name = "checkEditEnableAutoFetch";
            this.checkEditEnableAutoFetch.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.checkEditEnableAutoFetch.Properties.Appearance.Options.UseFont = true;
            this.checkEditEnableAutoFetch.Properties.Caption = "Enable auto fetch";
            this.checkEditEnableAutoFetch.Properties.GlyphAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.checkEditEnableAutoFetch.Size = new System.Drawing.Size(126, 19);
            this.checkEditEnableAutoFetch.TabIndex = 10;
            this.checkEditEnableAutoFetch.CheckedChanged += new System.EventHandler(this.checkEditEnableAutoFetch_CheckedChanged);
            // 
            // lblDescription
            // 
            this.lblDescription.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.lblDescription.Location = new System.Drawing.Point(12, 300);
            this.lblDescription.Name = "lblDescription";
            this.lblDescription.Size = new System.Drawing.Size(514, 13);
            this.lblDescription.TabIndex = 12;
            this.lblDescription.Text = "* All your existing search terms will be replaced by search terms from spreadshee" +
    "t. Please, create a backup.";
            // 
            // FormSyncSearchTerms
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.AutoValidate = System.Windows.Forms.AutoValidate.EnableAllowFocusChange;
            this.ClientSize = new System.Drawing.Size(540, 325);
            this.Controls.Add(this.lblDescription);
            this.Controls.Add(this.checkEditEnableAutoFetch);
            this.Controls.Add(this.lblFileFetchInterval);
            this.Controls.Add(this.timeSpanEditFetchInterval);
            this.Controls.Add(this.lblPreviewFile);
            this.Controls.Add(this.lblGoogleSpreadsheetUrl);
            this.Controls.Add(this.memoPreviewFile);
            this.Controls.Add(this.btnFetchFile);
            this.Controls.Add(this.txtFileUrl);
            this.Controls.Add(this.ribbon);
            this.IconOptions.Image = global::uBuyFirst.Properties.Resources.uGrad;
            this.Name = "FormSyncSearchTerms";
            this.Ribbon = this.ribbon;
            this.Text = "Sync";
            ((System.ComponentModel.ISupportInitialize)(this.ribbon)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtFileUrl.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.memoPreviewFile.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.timeSpanEditFetchInterval.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditEnableAutoFetch.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private DevExpress.XtraBars.Ribbon.RibbonControl ribbon;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup1;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPage1;
        private DevExpress.XtraEditors.TextEdit txtFileUrl;
        private DevExpress.XtraEditors.SimpleButton btnFetchFile;
        private DevExpress.XtraEditors.MemoEdit memoPreviewFile;
        private DevExpress.XtraEditors.LabelControl lblGoogleSpreadsheetUrl;
        private DevExpress.XtraEditors.LabelControl lblPreviewFile;
        private DevExpress.XtraEditors.TimeSpanEdit timeSpanEditFetchInterval;
        private DevExpress.XtraEditors.LabelControl lblFileFetchInterval;
        private DevExpress.XtraEditors.CheckEdit checkEditEnableAutoFetch;
        private DevExpress.XtraEditors.LabelControl lblDescription;
    }
}
