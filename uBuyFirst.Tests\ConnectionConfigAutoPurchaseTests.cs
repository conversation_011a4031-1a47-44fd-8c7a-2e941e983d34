﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Prefs;
using System.Reflection;

namespace uBuyFirst.Tests
{
    [TestClass]
    public class ConnectionConfigAutoPurchaseTests
    {
        [TestInitialize]
        public void TestInitialize()
        {
            // Reset ConnectionConfig to default state before each test
            ConnectionConfig.RestockerEnabled = false;
        }

        [TestMethod]
        public void AutoPurchaseSystemEnabled_DefaultValue_ShouldBeFalse()
        {
            // Arrange & Act
            var defaultValue = ConnectionConfig.RestockerEnabled;

            // Assert
            Assert.IsFalse(defaultValue, "RestockerEnabled should default to false");
        }

        [TestMethod]
        public void AutoPurchaseSystemEnabled_SetToTrue_ShouldReturnTrue()
        {
            // Arrange & Act
            ConnectionConfig.RestockerEnabled = true;

            // Assert
            Assert.IsTrue(ConnectionConfig.RestockerEnabled, "RestockerEnabled should be true when set to true");
        }

        [TestMethod]
        public void AutoPurchaseSystemEnabled_SetToFalse_ShouldReturnFalse()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = true; // Set to true first

            // Act
            ConnectionConfig.RestockerEnabled = false;

            // Assert
            Assert.IsFalse(ConnectionConfig.RestockerEnabled, "RestockerEnabled should be false when set to false");
        }

        [TestMethod]
        public void ParseServerJson_WithAutoPurchaseSystemEnabledTrue_ShouldSetPropertyToTrue()
        {
            // Arrange
            var jsonString = @"{
                ""RestockerEnabled"": true,
                ""TradingAPIEnabled"": true,
                ""FindingAPIEnabled"": true
            }";

            // Act
            var parseMethod = typeof(ConnectionConfig).GetMethod("ParseServerJson", BindingFlags.NonPublic | BindingFlags.Static);
            parseMethod?.Invoke(null, new object[] { jsonString });

            // Assert
            Assert.IsTrue(ConnectionConfig.RestockerEnabled, "RestockerEnabled should be true when parsed from JSON");
        }

        [TestMethod]
        public void ParseServerJson_WithAutoPurchaseSystemEnabledFalse_ShouldSetPropertyToFalse()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = true; // Set to true first
            var jsonString = @"{
                ""RestockerEnabled"": false,
                ""TradingAPIEnabled"": true,
                ""FindingAPIEnabled"": true
            }";

            // Act
            var parseMethod = typeof(ConnectionConfig).GetMethod("ParseServerJson", BindingFlags.NonPublic | BindingFlags.Static);
            parseMethod?.Invoke(null, new object[] { jsonString });

            // Assert
            Assert.IsFalse(ConnectionConfig.RestockerEnabled, "RestockerEnabled should be false when parsed from JSON");
        }

        [TestMethod]
        public void ParseServerJson_WithoutAutoPurchaseSystemEnabled_ShouldDefaultToFalse()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = true; // Set to true first
            var jsonString = @"{
                ""TradingAPIEnabled"": true,
                ""FindingAPIEnabled"": true
            }";

            // Act
            var parseMethod = typeof(ConnectionConfig).GetMethod("ParseServerJson", BindingFlags.NonPublic | BindingFlags.Static);
            parseMethod?.Invoke(null, new object[] { jsonString });

            // Assert
            Assert.IsFalse(ConnectionConfig.RestockerEnabled, "RestockerEnabled should default to false when not present in JSON");
        }

        [TestMethod]
        public void ParseServerJson_WithNullAutoPurchaseSystemEnabled_ShouldDefaultToFalse()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = true; // Set to true first
            var jsonString = @"{
                ""RestockerEnabled"": null,
                ""TradingAPIEnabled"": true,
                ""FindingAPIEnabled"": true
            }";

            // Act
            var parseMethod = typeof(ConnectionConfig).GetMethod("ParseServerJson", BindingFlags.NonPublic | BindingFlags.Static);
            parseMethod?.Invoke(null, new object[] { jsonString });

            // Assert
            Assert.IsFalse(ConnectionConfig.RestockerEnabled, "RestockerEnabled should default to false when null in JSON");
        }

        [TestCleanup]
        public void TestCleanup()
        {
            // Reset ConnectionConfig to default state after each test
            ConnectionConfig.RestockerEnabled = false;
        }
    }
}
