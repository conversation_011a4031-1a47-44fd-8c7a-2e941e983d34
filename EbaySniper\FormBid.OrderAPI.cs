﻿using System;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using uBuyFirst.Purchasing.BuyAPI;
using uBuyFirst.Purchasing.OrderApi;

namespace uBuyFirst
{
    public partial class FormBid
    {
        private async Task<bool> PurchaseOrderAPI(XtraMessageBoxForm confirmationMessageBox)
        {
            const string confirmationMessage = "<br>   By clicking Purchase, you agree that you've read and <br>   accepted "
                                               + "<href=http://pages.ebay.com/help/policies/user-agreement.html>eBay\'s User Agreement</href> "
                                               + "and you consent to <br>"
                                               + "   <href=http://pages.ebay.com/help/policies/privacy-policy.html>eBay\'s Privacy Notice</href> "
                                               + "and receiving marketing<br>"
                                               + "   communications from eBay.";

            var labelControl = new LabelControl();
            labelControl.Text = confirmationMessage;
            labelControl.Name = "label1";
            labelControl.Dock = DockStyle.Fill;
            labelControl.Width = 400;
            labelControl.Height = 200;
            labelControl.AllowHtmlString = true;
            labelControl.Appearance.TextOptions.WordWrap = WordWrap.Wrap;
            labelControl.HyperlinkClick += LabelControl_HyperlinkClick;
            confirmationMessageBox.Controls.Add(labelControl);

            confirmationMessageBox.ShowMessageBoxDialog(new XtraMessageBoxArgs(LookAndFeel, Owner,
                "<br>                                                  "
                + "<br>                                                  "
                + "<br>                                                  "
                + "<br>                                                  ", "Are you sure you want to purchase?", new[] { DialogResult.Yes, DialogResult.No }, null, 1, DefaultBoolean.True));

            if (confirmationMessageBox.DialogResult != DialogResult.Yes)
                return true;

            await MakeOauthPurchase();

            return false;
        }

        private async Task MakeOauthPurchase()
        {
            if (string.IsNullOrEmpty(_orderService.CheckoutResponse?.checkoutSessionId))
            {
                XtraMessageBox.Show("There was an error while adding item to cart");
            }

            lblPurchaseResult.Text = "Placing order";
            var placeOrderResult = await _orderService.PlaceOrder();

            lblPurchaseResult.Text = placeOrderResult?.purchaseOrderPaymentStatus;
            if (placeOrderResult?.purchaseOrderPaymentStatus == "PAID")
            {
                lblPurchaseResult.ForeColor = Color.MediumSeaGreen;
            }
            else if (placeOrderResult?.purchaseOrderPaymentStatus == "PENDING")
            {
                lblPurchaseResult.Text = "Your payment is being processed through PayPal";
                lblPurchaseResult.ForeColor = Color.MediumSeaGreen;
            }
            else
            {
                lblPurchaseResult.ForeColor = Color.Red;
            }
        }

        private async Task CheckoutOrderAPI()
        {
            if (Program.Sandbox)
            {
                btnBuyItnow.Enabled = false;
                var eBayUser = Form1.EBayAccountsList.FirstOrDefault(a => a.UserName == lcEbayAccount.SelectedItem.ToString());
                if (eBayUser == null)
                {
                    XtraMessageBox.Show($"User '{lcEbayAccount.SelectedItem}' not found");

                    return;
                }

                _orderService = new OrderService(eBayUser.OAuthUserToken);
                lblPurchaseResult.ForeColor = Color.DarkGoldenrod;
                lblPurchaseResult.Text = "Adding to cart";
                await _orderService.RunCheckoutRequest(D.ItemID, (int)spinEditBuyNowQuantity.Value, D.EbayAccount?.ShippingAddress);

                if (_orderService.CheckoutResponse.errors?.Count > 0)
                {
                    XtraMessageBox.Show(_orderService.CheckoutResponse.errors.First().message + "\n" + _orderService.CheckoutResponse.errors.First().longMessage);

                    return;
                }

                lblPurchaseResult.Text = "Added to cart";

                UpdateOrderApiPricing();
                for (var i = 0; i < _orderService.CheckoutResponse.lineItems.First().shippingOptions.Length; i++)
                {
                    ShippingOption option = _orderService.CheckoutResponse.lineItems.First().shippingOptions[i];
                    if (option.baseDeliveryCost.currency == "USD")
                        option.baseDeliveryCost.currency = "$";

                    var radioItem = new RadioGroupItem(option.shippingOptionId, $"{option.shippingServiceCode} - {option.baseDeliveryCost.currency} {option.baseDeliveryCost.value}");
                    radioItem.Tag = option.shippingOptionId;
                    rdioShippingOptions.Properties.Items.Add(radioItem);
                    if (option.selected)
                    {
                        rdioShippingOptions.SelectedIndex = i;
                    }
                }

                btnBuyItnow.Enabled = true;
            }
        }

        private async void rdioShippingOptions_Properties_SelectedIndexChanged(object sender, EventArgs e)
        {
            var shippingOption = rdioShippingOptions.Properties.Items[rdioShippingOptions.SelectedIndex];
            var lineItemId = _orderService.CheckoutResponse.lineItems.First().lineItemId;
            var optionId = (string)shippingOption.Tag;
            rdioShippingOptions.Enabled = false;
            await _orderService.UpdateShippingOption(lineItemId, optionId);
            rdioShippingOptions.Enabled = true;
            if (_orderService.CheckoutResponse.errors?.Count > 0)
            {
                XtraMessageBox.Show(_orderService.CheckoutResponse.errors.First().message + "\n" + _orderService.CheckoutResponse.errors.First().longMessage);

                return;
            }

            UpdateOrderApiPricing();
        }

        private void UpdateOrderApiPricing()
        {
            if (string.IsNullOrEmpty(_orderService.CheckoutResponse?.checkoutSessionId))
                return;

            if (_orderService.CheckoutResponse.pricingSummary.tax.currency == "USD")
                _orderService.CheckoutResponse.pricingSummary.tax.currency = "$";

            if (_orderService.CheckoutResponse.pricingSummary.total.currency == "USD")
                _orderService.CheckoutResponse.pricingSummary.total.currency = "$";

            if (_orderService.CheckoutResponse.pricingSummary.deliveryCost.currency == "USD")
                _orderService.CheckoutResponse.pricingSummary.deliveryCost.currency = "$";

            lciTax.Text = _orderService.CheckoutResponse.pricingSummary.tax.currency + "" + _orderService.CheckoutResponse.pricingSummary.tax.value;
            lcTotalPrice.Text = _orderService.CheckoutResponse.pricingSummary.total.currency + "" + _orderService.CheckoutResponse.pricingSummary.total.value;
            lcShipping.Text = _orderService.CheckoutResponse.pricingSummary.deliveryCost.currency + "" + _orderService.CheckoutResponse.pricingSummary.deliveryCost.value;
        }
    }
}
