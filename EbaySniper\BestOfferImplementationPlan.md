# Best Offer Submission Implementation Plan (via HTTP Emulation)

## Goal

Implement a new method for submitting Best Offers in the EbaySniper application, specifically for users with credit card payments enabled (`CreditCardService.CreditCardPaymentEnabled` - treated as a global setting). This method will emulate the multi-step HTTP request sequence observed in browser traffic logs, using Firefox cookies for session management, replacing the current `PlaceOffer` API call for these scenarios.

## Analysis Summary

Based on traffic logs in the `offer/` directory, the Best Offer submission via the web interface involves the following 5 steps:

1.  **Load Offer Page:** `GET www.ebay.com/bo/makeOffer/{itemId}` (Returns HTML form).
2.  **Submit for Review:** `POST www.ebay.com/bo/reviewOffer/{itemId}` (JSON payload: offer details; Returns JSON with checkout URL/payload).
3.  **Final Submission (Checkout):** `POST checkout.ebay.com/int/m/bo` (Form-urlencoded payload: final offer details).
4.  **Backend Processing/Auth:** `POST checkout.ebay.com/ajax/m/bo?action=initAuth` (JSON payload: context/auth; Returns JSON with `pseudoOrderId`).
5.  **Confirmation/Notification:** `POST www.ebay.com/bo/sendOffer/{itemId}` (JSON payload: offer details + `pseudoOrderId`; Returns JSON for confirmation page).

Analysis of `Purchasing/CreditCardCheckout.cs` shows existing mechanisms for:
*   Reading Firefox cookies via `CookieManager.ReadCookiesFirefox`.
*   Using a `CookieContainer` within the `Order` object.
*   Delegating HTTP operations to service classes (like `CreditCardService`).
*   Performing asynchronous network calls.

## Implementation Strategy

Create new, dedicated classes for the Best Offer flow, but reuse existing core components where applicable.

## Proposed C# Implementation

1.  **Modify `BuyingService.Order` Class:**
    *   Add `OfferPrice` property (e.g., `decimal OfferPrice`).
    *   Add `OfferMessage` property (e.g., `string OfferMessage`).
    *   Add properties for intermediate data storage (e.g., `List<string> SrtTokens`, `string CheckoutUrl`, `string FinalOfferPayload`, `string PseudoOrderId`). Consider if a dedicated sub-object within `Order` is cleaner.
    *   Review `CheckoutState` enum or add a new `OfferStatus` enum/property for tracking the specific steps of this flow (e.g., `OfferNotStarted`, `OfferReviewNeeded`, `OfferSubmitting`, `OfferSent`, `OfferFailed`).

2.  **Create `Purchasing/BestOfferService.cs`:**
    *   Static class containing asynchronous methods (`async Task<...>`) for each HTTP step:
        *   `LoadMakeOfferPageAsync(BuyingService.Order order)`:
            *   Performs GET request (Log 1).
            *   Uses `order.CookieContainer`.
            *   Sets appropriate headers (User-Agent, Referer, etc.).
            *   Parses response HTML to extract necessary data (e.g., `srt` tokens, form action URLs, hidden input values) and stores them in the `order` object.
            *   Returns success/failure status or relevant data.
        *   `SubmitOfferForReviewAsync(BuyingService.Order order)`:
            *   Performs POST request (Log 2).
            *   Uses `order.CookieContainer`.
            *   Builds JSON payload using `order.OfferPrice`, `order.OfferMessage`, `order.ItemID`, `order.Quantity`, extracted tokens.
            *   Sets `Content-Type: application/json`.
            *   Parses JSON response to extract `checkoutUrl` and `finalPayload` data, storing them in the `order` object.
            *   Returns success/failure status or relevant data.
        *   `SubmitFinalOfferAsync(BuyingService.Order order)`:
            *   Performs POST request (Log 3) to `order.CheckoutUrl`.
            *   Uses `order.CookieContainer`.
            *   Builds form-urlencoded payload from `order.FinalOfferPayload` data.
            *   Sets `Content-Type: application/x-www-form-urlencoded`.
            *   Handles response (likely HTML confirmation/redirect).
            *   Returns success/failure status.
        *   `InitializeAuthAsync(BuyingService.Order order)`:
            *   Performs POST request (Log 4).
            *   Uses `order.CookieContainer`.
            *   Builds JSON payload with context, auth details, tokens.
            *   Sets `Content-Type: application/json`.
            *   Parses JSON response to extract `pseudoOrderId` and stores it in the `order` object.
            *   Returns success/failure status.
        *   `SendOfferConfirmationAsync(BuyingService.Order order)`:
            *   Performs POST request (Log 5).
            *   Uses `order.CookieContainer`.
            *   Builds JSON payload including `order.pseudoOrderId` and offer details.
            *   Sets `Content-Type: application/json`.
            *   Parses JSON response (confirmation data).
            *   Returns final success/failure status.
    *   Implement robust error handling for network issues, non-200 status codes, and parsing errors in each method.
    *   Reuse existing `HttpClient` patterns (handler configuration, default headers) if available, or establish a standard way within this service.

3.  **Create `Purchasing/BestOfferCheckout.cs`:**
    *   Static class to orchestrate the process.
    *   `ExecuteBestOfferSubmissionAsync(DataList d, decimal offerPrice, int quantity, string message)` method:
        *   Check the global `CreditCardService.CreditCardPaymentEnabled` setting.
        *   If `false`, potentially log a message and return/throw, or fall back to `PlaceOffer`.
        *   If `true`:
            *   Initialize `d.Order` if null, setting action type appropriately.
            *   Set `d.Order.OfferPrice`, `d.Order.Quantity`, `d.Order.OfferMessage`.
            *   Set initial `OfferStatus` (e.g., `OfferNotStarted`).
            *   Call `CookieManager.ReadCookiesFirefox(".ebay.com")` and assign to `d.Order.CookieContainer`. Log start/stop.
            *   `await BestOfferService.LoadMakeOfferPageAsync(d.Order)`. Check result, update status, handle errors.
            *   `await BestOfferService.SubmitOfferForReviewAsync(d.Order)`. Check result, update status, handle errors.
            *   `await BestOfferService.SubmitFinalOfferAsync(d.Order)`. Check result, update status, handle errors.
            *   `await BestOfferService.InitializeAuthAsync(d.Order)`. Check result, update status, handle errors.
            *   `await BestOfferService.SendOfferConfirmationAsync(d.Order)`. Check result, set final status (`OfferSent` or `OfferFailed`).
            *   Update `d.SetStatus()` based on the final outcome.
            *   Use `ContinueWith(..., TaskScheduler.FromCurrentSynchronizationContext())` for UI updates via `d.SetStatus` if necessary, similar to `CreditCardCheckout.cs`.

4.  **Integration:**
    *   Modify the calling code (likely in `Placeoffer` or similar) to check `CreditCardPaymentEnabled` and call `BestOfferCheckout.ExecuteBestOfferSubmissionAsync` instead of the `PlaceOffer` API when appropriate.

5.  **Utilities:**
    *   Ensure necessary helper functions for HTML parsing (e.g., using HtmlAgilityPack) and JSON parsing (e.g., using Newtonsoft.Json or System.Text.Json) are available and utilized.

## Sequence Diagram

```mermaid
sequenceDiagram
    participant Caller (e.g., Placeoffer logic)
    participant BestOfferCheckout
    participant CookieManager
    participant BestOfferService
    participant HttpClient
    participant EbayServers (www/checkout)

    Caller->>BestOfferCheckout: ExecuteBestOfferSubmissionAsync(d, price, qty, msg)
    activate BestOfferCheckout
    BestOfferCheckout->>BestOfferCheckout: Check CreditCardPaymentEnabled (Global)
    alt Credit Card Enabled
        BestOfferCheckout->>CookieManager: ReadCookiesFirefox(".ebay.com")
        activate CookieManager
        CookieManager-->>BestOfferCheckout: cookieContainer
        deactivate CookieManager
        BestOfferCheckout->>BestOfferService: LoadMakeOfferPageAsync(order)
        activate BestOfferService
        BestOfferService->>HttpClient: GET /bo/makeOffer (with cookies)
        activate HttpClient
        HttpClient->>EbayServers: Request 1 (Log 1)
        EbayServers-->>HttpClient: HTML Response
        HttpClient-->>BestOfferService: HTML Response
        deactivate HttpClient
        BestOfferService->>BestOfferService: Parse HTML, extract data (store in order)
        BestOfferService-->>BestOfferCheckout: Success/Data for next step
        deactivate BestOfferService

        BestOfferCheckout->>BestOfferService: SubmitOfferForReviewAsync(order)
        activate BestOfferService
        BestOfferService->>HttpClient: POST /bo/reviewOffer (JSON, with cookies)
        activate HttpClient
        HttpClient->>EbayServers: Request 2 (Log 2)
        EbayServers-->>HttpClient: JSON Response
        HttpClient-->>BestOfferService: JSON Response
        deactivate HttpClient
        BestOfferService->>BestOfferService: Parse JSON, extract checkoutUrl, finalPayload (store in order)
        BestOfferService-->>BestOfferCheckout: Success/Data for next step
        deactivate BestOfferService

        BestOfferCheckout->>BestOfferService: SubmitFinalOfferAsync(order)
        activate BestOfferService
        BestOfferService->>HttpClient: POST checkout.ebay.com/int/m/bo (Form, with cookies)
        activate HttpClient
        HttpClient->>EbayServers: Request 3 (Log 3)
        EbayServers-->>HttpClient: HTML Response
        HttpClient-->>BestOfferService: HTML Response
        deactivate HttpClient
        BestOfferService-->>BestOfferCheckout: Success
        deactivate BestOfferService

        BestOfferCheckout->>BestOfferService: InitializeAuthAsync(order)
        activate BestOfferService
        BestOfferService->>HttpClient: POST checkout.ebay.com/ajax/m/bo?action=initAuth (JSON, with cookies)
        activate HttpClient
        HttpClient->>EbayServers: Request 4 (Log 4)
        EbayServers-->>HttpClient: JSON Response
        HttpClient-->>BestOfferService: JSON Response
        deactivate HttpClient
        BestOfferService->>BestOfferService: Parse JSON, extract pseudoOrderId (store in order)
        BestOfferService-->>BestOfferCheckout: Success/pseudoOrderId
        deactivate BestOfferService

        BestOfferCheckout->>BestOfferService: SendOfferConfirmationAsync(order)
        activate BestOfferService
        BestOfferService->>HttpClient: POST /bo/sendOffer (JSON, with cookies)
        activate HttpClient
        HttpClient->>EbayServers: Request 5 (Log 5)
        EbayServers-->>HttpClient: JSON Response
        HttpClient-->>BestOfferService: JSON Response
        deactivate HttpClient
        BestOfferService-->>BestOfferCheckout: Final Success/Failure
        deactivate BestOfferService

        BestOfferCheckout->>BestOfferCheckout: Update d.SetStatus() (UI Thread)
    else Fallback
        BestOfferCheckout->>Caller: Use PlaceOffer API (or return error)
    end
    BestOfferCheckout-->>Caller: Result
    deactivate BestOfferCheckout