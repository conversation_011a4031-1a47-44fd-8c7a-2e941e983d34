# Restocker Reporting System

## Overview

The Restocker Reporting System provides comprehensive reporting capabilities for tracking purchase activities, transaction details, and generating user-friendly reports. The system supports two main types of reports:

1. **Detailed Transaction Reports** - Complete audit trail for specific keyword/job combinations
2. **User Reports** - User-friendly reports with all spreadsheet data and purchase information

## Features

- **Comprehensive Data**: Includes all spreadsheet columns, purchase quantities, and transaction details
- **HTML Step Tracking**: Captures and stores HTML content from the last purchase step (success/error pages)
- **CSV Export**: Export reports to CSV format for analysis
- **Flexible Filtering**: Filter by date ranges, job IDs, keyword IDs, and transaction status
- **Automatic HTML Storage**: Saves HTML content with organized file naming

## Implementation Status

✅ **COMPLETED**:
- Report models (TransactionDetailReport, UserReport, ReportFilter)
- Service interfaces (IReportService, IReportExportService)
- Service implementations (ReportService, ReportExportService)
- Database schema updates (LastStepHtml column added)
- Comprehensive unit tests (71 tests covering all functionality)

## Usage Examples

### 1. Generate Detailed Transaction Report

```csharp
// Inject the service
var reportService = new ReportService(purchaseTrackerRepository);

// Generate report for specific keyword and job
var report = await reportService.GenerateTransactionDetailReportAsync("keyword-123", "job-456");

// Export to CSV
var exportService = new ReportExportService();
await exportService.ExportTransactionDetailReportToCsvAsync(report, @"C:\Reports\transaction_detail.csv");
```

### 2. Generate User Report with Filtering

```csharp
// Create filter for last 30 days
var filter = new ReportFilter
{
    StartDate = DateTime.Now.AddDays(-30),
    EndDate = DateTime.Now,
    IncludeCompleted = true,
    IncludeFailed = true,
    IncludePending = false
};

// Generate user reports
var userReports = await reportService.GenerateUserReportAsync(filter);

// Export to CSV
await exportService.ExportUserReportToCsvAsync(userReports, @"C:\Reports\user_report.csv");
```

### 3. Save HTML Content from Purchase Steps

```csharp
var exportService = new ReportExportService();

// Save HTML content from purchase step
var htmlContent = "<html><body><h1>Purchase Successful</h1></body></html>";
var filePath = await exportService.SaveHtmlContentAsync(
    htmlContent, 
    "keyword-123", 
    "job-456", 
    DateTime.Now);

Console.WriteLine($"HTML saved to: {filePath}");
```

## Report Data Structure

### User Report Fields

The user report includes all spreadsheet columns plus purchase information:

**Core Data:**
- KeywordId, JobId, KeywordAlias
- Keywords, SearchInDescription
- PriceMin, PriceMax
- Categories, Condition, EbaySite
- LocatedIn, ShipsTo, ShipZipcode
- Sellers, SellerType
- Interval, Threads, ViewName, ListingType

**Purchase Data:**
- RequiredQuantity, PurchasedQuantity
- LastOrderStatus, LastTransactionTime
- LastStepHtml (reference to saved HTML file)
- RemainingQuantity, CompletionPercentage, IsCompleted

### Transaction Detail Report Fields

**Summary Information:**
- KeywordId, JobId, KeywordAlias
- TotalQuantityPurchased, TotalAmountSpent
- SuccessfulAttempts, FailedAttempts
- LastTransactionStatus, LastAttemptResult

**Detailed Data:**
- Complete list of PurchaseTransactions
- Complete list of PurchaseAttempts
- Chronological ordering by date

## File Organization

### HTML Content Storage

HTML files are automatically saved with the following naming convention:
```
{timestamp}_{keywordId}_{jobId}.html
```

Example: `20241211_143022_keyword123_job456.html`

**Default Storage Location:**
```
%APPDATA%\Restocker\HtmlSteps\
```

### CSV Export Format

**User Report CSV Headers:**
```
Time,KeywordId,JobId,KeywordAlias,Keywords,RequiredQuantity,PurchasedQuantity,
LastOrderStatus,PriceMin,PriceMax,Categories,Condition,EbaySite,LocatedIn,
ShipsTo,ShipZipcode,Sellers,SellerType,Interval,Threads,ViewName,ListingType,
SearchInDescription,RemainingQuantity,CompletionPercentage,IsCompleted,
LastTransactionTime,ReportGeneratedAt
```

## Database Schema Updates

The following column was added to the PurchaseTransactions table:

```sql
ALTER TABLE PurchaseTransactions ADD COLUMN LastStepHtml TEXT;
```

This column stores the HTML content from the last purchase step (success or error page from eBay).

## Service Dependencies

### IReportService Dependencies
- `IPurchaseTrackerRepository` - For data access

### IReportExportService Dependencies
- File system access for HTML storage
- CSV generation capabilities

## Configuration

### HTML Storage Configuration

```csharp
// Custom HTML storage directory
var exportService = new ReportExportService(@"C:\CustomPath\HtmlSteps");

// Default directory (recommended)
var exportService = new ReportExportService();
```

### Cleanup Configuration

```csharp
// Clean up HTML files older than 30 days (default)
var deletedCount = await exportService.CleanupOldHtmlFilesAsync();

// Custom retention period (60 days)
var deletedCount = await exportService.CleanupOldHtmlFilesAsync(60);
```

## Integration Points

### With Existing Restocker System

The reporting system integrates seamlessly with the existing Restocker infrastructure:

1. **Database Integration**: Uses the same `IPurchaseTrackerRepository`
2. **Model Compatibility**: Works with existing `PurchaseTransaction` and `PurchaseAttempt` models
3. **Keyword Data**: Integrates with `Keyword2Find` objects for complete spreadsheet data

### With Purchase Execution

To capture HTML content during purchase execution, update the `PurchaseExecutionService`:

```csharp
// In PurchaseExecutionService
var transaction = new PurchaseTransaction
{
    // ... other properties
    LastStepHtml = capturedHtmlContent
};

await _repository.AddTransactionAsync(transaction);
```

## Testing

The reporting system includes comprehensive unit tests:

- **ReportModelTests**: 12 tests covering model functionality
- **ReportServiceTests**: 8 tests covering report generation
- **ReportExportServiceTests**: 10 tests covering export functionality

Run tests with:
```bash
dotnet test --filter "TestCategory=Reporting"
```

## Future Enhancements

Potential future improvements:

1. **Scheduled Reports**: Automatic report generation on schedules
2. **Email Integration**: Email reports to users
3. **Dashboard Integration**: Real-time reporting widgets
4. **Advanced Filtering**: More sophisticated filter options
5. **Report Templates**: Customizable report formats
6. **Data Visualization**: Charts and graphs for purchase trends

## Troubleshooting

### Common Issues

1. **HTML Storage Permission Errors**
   - Ensure write permissions to the HTML storage directory
   - Check disk space availability

2. **CSV Export Encoding Issues**
   - Files are saved with UTF-8 encoding by default
   - Use appropriate CSV readers that support UTF-8

3. **Large Report Performance**
   - Use date range filters to limit data size
   - Consider pagination for very large datasets

### Error Handling

The reporting system includes comprehensive error handling:
- Null parameter validation
- Invalid filter validation
- File system error handling
- Database connection error handling

All methods throw appropriate exceptions with descriptive messages for debugging.
