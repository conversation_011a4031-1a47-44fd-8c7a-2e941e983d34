﻿using System;
using System.Threading.Tasks;
using DevExpress.XtraEditors;
using Telegram.Bot;
using Telegram.Bot.Types;
using Telegram.Bot.Types.Enums;
using Telegram.Bot.Types.ReplyMarkups;
using uBuyFirst.Tools;

namespace uBuyFirst.Telegram
{
    public class BotCommandHandler
    {
        private readonly TelegramSender _telegramSender;

        public BotCommandHandler(TelegramSender telegramSender)
        {
            _telegramSender = telegramSender;
        }

        public async Task HandleMessageReceivedAsync(ITelegramBotClient botClient, Message message)
        {
            if (message?.Type != MessageType.Text)
                return;

            if (message.Chat.Username != _telegramSender.TelegramAccount)
            {
                try
                {
                    await botClient.SendTextMessageAsync(message.Chat.Id, "Access denied. Set your telegram username in program settings.");
                    return;
                }
                catch (Exception)
                {
                    return;
                }
            }

            _telegramSender.TelegramChatID = message.Chat.Id.ToString();
            await SendControlButtons(botClient);
        }

        public async Task HandleCallbackQueryReceivedAsync(ITelegramBotClient botClient, CallbackQuery callbackQuery)
        {
            if (callbackQuery.From.Username != _telegramSender.TelegramAccount)
            {
                await botClient.SendTextMessageAsync(callbackQuery.Message.Chat.Id, "Access denied. Set your telegram username in program settings.");
                return;
            }

            await botClient.AnswerCallbackQueryAsync(callbackQueryId: callbackQuery.Id, text: $"Received {callbackQuery.Data}");

            switch (callbackQuery.Data)
            {
                case "Start Search":
                    if (Form1.Instance._searchService?.Running != true)
                    {
                        Form1.Instance.InvokeIfRequired(() => Form1.Instance.StartWorking());
                        await SendControlButtons(botClient);
                    }
                    break;
                case "Stop Search":
                    if (Form1.Instance._searchService?.Running == true)
                    {
                        Form1.Instance.InvokeIfRequired(() => Form1.Instance.StopWorking().ContinueWith((a) => SendControlButtons(botClient).ConfigureAwait(false)).ConfigureAwait(true));
                    }
                    break;
                case "Enable Notifications":
                    _telegramSender.Enabled = true;
                    await SendControlButtons(botClient);
                    break;
                case "Disable Notifications":
                    _telegramSender.Enabled = false;
                    await SendControlButtons(botClient);
                    break;
            }
        }

        public async Task<Message> SendControlButtons(ITelegramBotClient botClient)
        {
            var startSearch = InlineKeyboardButton.WithCallbackData("Start Search", "Start Search");
            var stopSearch = InlineKeyboardButton.WithCallbackData("Stop Search", "Stop Search");
            var enableNotifications = InlineKeyboardButton.WithCallbackData("Enable Notifications", "Enable Notifications");
            var disableNotifications = InlineKeyboardButton.WithCallbackData("Disable Notifications", "Disable Notifications");

            var inlineKeyboard = new InlineKeyboardMarkup(new[]
            {
                Form1.Instance._searchService?.Running == true ? stopSearch : startSearch,
                _telegramSender.Enabled ? disableNotifications : enableNotifications
            });

            var searchStatus = Form1.Instance._searchService?.Running == true ? "Started" : "Stopped";
            var telegramNotifications = _telegramSender.Enabled ? "Enabled" : "Disabled";

            if (!string.IsNullOrEmpty(_telegramSender.TelegramChatID))
            {
                try
                {
                    return await botClient.SendTextMessageAsync(_telegramSender.TelegramChatID, "Search: " + searchStatus + "\r\n" + "Notifications: " + telegramNotifications, replyMarkup: inlineKeyboard);
                }
                catch (Exception ex)
                {
                    if (TelegramSender.HandleTelegramException(ex, _telegramSender.GetBotName()))
                        return new Message();

                    if (ex.Message.Contains("Too Many Requests"))
                    {
                        XtraMessageBox.Show("Telegram message limit reached. Please, try again later.");
                        return new Message();
                    }

                    throw;
                }
            }
            else
            {
                return new Message();
            }
        }
    }
}
