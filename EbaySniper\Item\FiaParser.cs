﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using eBay.Service.Core.Soap;
using eBay.Services.Finding;
using uBuyFirst.Data;

namespace uBuyFirst.Item
{
    class FiaParser
    {
        public static void SearchItem2Row(DataRow row, SearchItem searchItem, ref DataList dataList)
        {
            if (searchItem.unitPrice != null)
            {
                row["Quantity"] = searchItem.unitPrice?.quantity;
            }

            row["Category Name"] = searchItem.primaryCategory.categoryName;
            var secCategoryID = "";
            if (searchItem.secondaryCategory?.categoryId != null)
            {
                secCategoryID = ", " + searchItem.secondaryCategory.categoryId;
            }

            row["Category ID"] = searchItem.primaryCategory.categoryId + secCategoryID;
            row["From Country"] = searchItem.country;
            row["To Country"] = searchItem.shippingInfo.shipToLocations.ToArray().Aggregate((current, next) => current + "," + next);
            if (searchItem.listingInfo.bestOfferEnabled)
            {
                row["Best Offer"] = "True";
            }
            else
            {
                row["Best Offer"] = "False";
            }

            if (searchItem.isMultiVariationListingSpecified && searchItem.isMultiVariationListing)
            {
                row["Variation"] = "True";
            }
            else
            {
                row["Variation"] = "False";
            }

            row["Location"] = searchItem.location;
            row["AutoPay"] = searchItem.autoPay;
            row["Commit To Buy"] = !searchItem.autoPay;
            row["Title"] = searchItem.title;
            row["Shipping Type"] = searchItem.shippingInfo.shippingType;

            row["Returns"] = searchItem.returnsAccepted ? "Returns Accepted" : "No returns accepted";
            if (searchItem.condition?.conditionId != null)
            {
                row["Condition"] = searchItem.condition?.conditionDisplayName;
                dataList.ConditionID = searchItem.condition?.conditionId.ToString();
            }

            dataList.SetStatus(ItemStatus.Active); // searchItem.sellingStatus?.sellingState;
            //if (searchItem.productId?.Value != null)
            //				row["UPC"] = searchItem.productId.Value;
            row["Seller Name"] = searchItem.sellerInfo.sellerUserName;
            row["Feedback Rating"] = searchItem.sellerInfo.positiveFeedbackPercent;
            row["Feedback Score"] = searchItem.sellerInfo.feedbackScore;
            /*
            dataList.FoundTime = new DateTimeWithDiff { Utc = nowEST, TimeDiff = Math.Round((nowEST - TimeZoneInfo.ConvertTimeFromUtc(searchItem.listingInfo.startTime.ToUniversalTime(), zoneEST)).TotalSeconds, 0) };
            row["Posted Time"] = (TimeZoneInfo.ConvertTimeFromUtc(searchItem.listingInfo.startTime.ToUniversalTime(), zoneEST));
            postedTimeDict.TryAdd(searchItem.itemId, (TimeZoneInfo.ConvertTimeFromUtc(searchItem.listingInfo.startTime.ToUniversalTime(), zoneEST)));
            */
            if (searchItem.galleryURL != null)
            {
                dataList.GalleryUrl = searchItem.pictureURLLarge;
            }

            dataList.Pictures = new List<string>();
            //if (searchItem.galleryInfoContainer!=null)
            //	dataList.Pictures = searchItem.galleryInfoContainer.Select(a => a.Value).ToList();
            //if (string.IsNullOrEmpty(dataList.GalleryUrl) && dataList.Pictures.Count > 0)
            {
                //dataList.GalleryUrl = dataList.Pictures[0];
            }
            //if (dataList.Pictures.Count > 0) dataList.Pictures.RemoveAt(0);

            double itemPriceValue = 0;
            var currencyStr = "USD";
            if (searchItem.listingInfo.listingType == "FixedPrice" || searchItem.listingInfo.listingType == "StoreInventory")
            {
                if (searchItem.sellingStatus != null)
                {
                    itemPriceValue = searchItem.sellingStatus.currentPrice.Value;
                    currencyStr = searchItem.sellingStatus.currentPrice.currencyId;
                }
            }

            if (searchItem.listingInfo.listingType == "AuctionWithBIN" && searchItem.listingInfo.buyItNowAvailable)
            {
                itemPriceValue = searchItem.listingInfo.buyItNowPrice.Value;
                currencyStr = searchItem.listingInfo.buyItNowPrice.currencyId;
            }

            Enum.TryParse(currencyStr, true, out CurrencyCodeType _);
        }
    }
}
