# Keyword-Level Tracking Architecture

## Overview

The Restocker module uses a **keyword-level tracking architecture** that stores purchase requirements directly on `Keyword2Find` objects rather than in a separate database table. This approach simplifies the system by eliminating the need for complex synchronization between keywords and database requirements.

## Architecture Benefits

### Simplified Data Flow
- **Single Source of Truth**: Purchase requirements live on keywords
- **Direct Access**: No database lookup needed during purchase execution
- **Better Performance**: Fewer database queries during item processing
- **Easier CSV Import/Export**: No separate sync required

### Reduced Complexity
- **No Sync Issues**: Eliminates synchronization problems between keywords and database
- **Clearer Logic**: Purchase decisions use keyword properties directly
- **Simpler Testing**: Easier to test with in-memory keyword objects

## Data Storage Strategy

### Keyword Level (Primary Storage)
```csharp
public class Keyword2Find
{
    // Restocker properties
    public string JobId { get; set; } = string.Empty;
    public int RequiredQuantity { get; set; } = 0;
    public int PurchasedQuantity { get; set; } = 0;
}
```

**Purpose**: Store current purchase requirements and progress
**Updated**: 
- During CSV import/export
- Immediately after successful purchases
- During sync operations from transaction logs

### Database Level (Transaction Logging)
```sql
-- Simplified schema - only transaction logging
CREATE TABLE PurchaseTransactions (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    KeywordId TEXT NOT NULL,        -- Links to Keyword2Find.Id
    JobId TEXT NOT NULL,            -- For reporting grouping
    ItemId TEXT NOT NULL,
    ItemTitle TEXT NOT NULL,
    PurchasePrice DECIMAL NOT NULL,
    Quantity INTEGER NOT NULL,
    PurchaseDate DATETIME NOT NULL,
    Status TEXT NOT NULL,
    TransactionId TEXT,
    PaymentMethod TEXT,
    ShippingAddress TEXT,
    Notes TEXT
);

CREATE TABLE PurchaseAttempts (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    KeywordId TEXT NOT NULL,        -- Links to Keyword2Find.Id
    JobId TEXT NOT NULL,
    ItemId TEXT NOT NULL,
    AttemptDate DATETIME NOT NULL,
    Result TEXT NOT NULL,
    ErrorMessage TEXT
);
```

**Purpose**: Log transactions for reporting and audit trail
**NOT Used For**: Purchase decision logic (keywords are the source of truth)

## Purchase Execution Flow

### 1. Item Processing
```csharp
// During item processing, keyword is accessible
var keyword = dataList.OriginatingKeyword ?? FindKeywordById(dataList.KeywordId);
```

### 2. Purchase Decision
```csharp
// Direct keyword access - no database lookup
if (keyword.PurchasedQuantity >= keyword.RequiredQuantity)
    return PurchaseExecutionResult.CreateSkipped("Required quantity already purchased");

var remainingQuantity = keyword.RequiredQuantity - keyword.PurchasedQuantity;
var quantityToPurchase = Math.Min(remainingQuantity, dataList.QuantityAvailable);
```

### 3. Purchase Execution
```csharp
// Execute purchase using existing CreditCardCheckout
var result = await ExecutePurchaseAsync(dataList, quantityToPurchase, keyword, filterAlias);

if (result.Success)
{
    // Update keyword immediately
    keyword.PurchasedQuantity += quantityToPurchase;
    
    // Log transaction for reporting
    await LogTransactionAsync(keyword, dataList, quantityToPurchase, result);
}
```

## Synchronization Strategy

### When Sync is Needed
1. **Application Startup**: Ensure keyword quantities match transaction logs
2. **After CSV Import**: Sync imported keywords with existing transaction history
3. **Data Recovery**: After database recovery or migration
4. **Periodic Verification**: Optional integrity checks

### Sync Implementation
```csharp
public async Task SyncKeywordQuantityAsync(Keyword2Find keyword)
{
    if (string.IsNullOrEmpty(keyword.JobId))
        return; // No Restocker data
    
    // Calculate from transaction logs
    var actualPurchased = await GetPurchasedQuantityFromTransactionsAsync(
        keyword.Id, keyword.JobId);
    
    // Update keyword if different
    if (keyword.PurchasedQuantity != actualPurchased)
    {
        keyword.PurchasedQuantity = actualPurchased;
        // Optionally log the correction
    }
}
```

## Integration Points

### DataList Enhancement
```csharp
public class DataList
{
    // Add reference to originating keyword
    public Keyword2Find OriginatingKeyword { get; set; }
    
    // Alternative: use keyword ID for lookup
    public string KeywordId { get; set; }
}
```

### NewItem.cs Integration
```csharp
// Set keyword reference during item processing
datalist.OriginatingKeyword = foundItem.Keyword2Find;
datalist.KeywordId = foundItem.Keyword2Find?.Id;
```

### RestockFilterAction Integration
```csharp
public FilterExecutionResult Execute(FilterExecutionContext context)
{
    var dataList = GetDataListFromContext(context);
    var keyword = dataList.OriginatingKeyword;
    
    if (keyword == null || string.IsNullOrEmpty(keyword.JobId))
        return FilterExecutionResult.CreateSkipped("No Restocker configuration");
    
    // Execute purchase using keyword data directly
    var result = await _purchaseService.TryPurchaseItemAsync(dataList, context.FilterRule.Alias);
    return ConvertToFilterResult(result);
}
```

## Migration from Database-Based Approach

### Steps to Migrate
1. **Export existing requirements** from PurchaseRequirements table
2. **Update keywords** with JobId, RequiredQuantity from exported data
3. **Sync PurchasedQuantity** from transaction logs
4. **Remove PurchaseRequirements table** and related code
5. **Update all services** to use keyword-based approach

### Data Preservation
- All transaction history is preserved
- Purchase requirements are moved to keywords
- No data loss during migration

## Testing Strategy

### Unit Testing Benefits
```csharp
[TestMethod]
public async Task TryPurchaseItemAsync_WithKeywordData_ExecutesPurchase()
{
    // Arrange - create keyword with test data
    var keyword = new Keyword2Find
    {
        Id = "test-keyword",
        JobId = "JOB-001",
        RequiredQuantity = 5,
        PurchasedQuantity = 2
    };
    
    var dataList = new DataList { OriginatingKeyword = keyword };
    
    // Act - test purchase logic directly
    var result = await _service.TryPurchaseItemAsync(dataList, "TestFilter");
    
    // Assert - verify keyword was updated
    Assert.AreEqual(3, keyword.PurchasedQuantity); // 2 + 1 purchased
}
```

### Integration Testing
- Test CSV import/export with keyword data
- Test sync operations between keywords and transaction logs
- Test purchase execution with real keyword objects

## Performance Considerations

### Advantages
- **Faster Purchase Decisions**: No database lookup during item processing
- **Reduced Database Load**: Fewer queries during high-volume processing
- **Better Scalability**: In-memory keyword access scales better

### Memory Usage
- **Minimal Impact**: Keyword objects already in memory
- **Three Additional Properties**: JobId (string), RequiredQuantity (int), PurchasedQuantity (int)
- **Negligible Overhead**: Compared to existing keyword data

## Error Handling

### Keyword Not Found
```csharp
if (keyword == null)
    return PurchaseExecutionResult.CreateSkipped("Keyword not found for item");

if (string.IsNullOrEmpty(keyword.JobId))
    return PurchaseExecutionResult.CreateSkipped("No Restocker configuration for keyword");
```

### Sync Failures
```csharp
try
{
    await SyncKeywordQuantityAsync(keyword);
}
catch (Exception ex)
{
    // Log error but don't fail purchase execution
    _logger.LogWarning($"Failed to sync keyword quantity: {ex.Message}");
    // Continue with current keyword values
}
```

## Future Enhancements

### Potential Improvements
1. **Automatic Sync Scheduling**: Periodic background sync of all keywords
2. **Conflict Resolution**: Handle cases where keyword and database values differ
3. **Batch Operations**: Optimize sync operations for large keyword sets
4. **Audit Trail**: Track changes to keyword purchase quantities

### Extensibility
- Framework supports additional keyword-level properties
- Easy to add new purchase-related fields
- Simple to extend reporting with keyword-based data
