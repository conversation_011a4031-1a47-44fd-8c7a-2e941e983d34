﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraBars.Alerter;
using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using uBuyFirst.Data;
using uBuyFirst.Images;
using uBuyFirst.Other;
using uBuyFirst.Tools;
using uBuyFirst.Watchlist;

namespace uBuyFirst
{
    public partial class FormAlert : RibbonForm
    {
        public AlertControl AControl;
        public List<Keyword2Find> EbaySearches;

        public FormAlert()
        {
            InitializeComponent();
            icbFormDisplaySpeed.Properties.Items.AddEnum(typeof(AlertFormDisplaySpeed));
            icbFormLocation.Properties.Items.AddEnum(typeof(AlertFormLocation));
            icbShowingEffect.Properties.Items.AddEnum(typeof(AlertFormShowingEffect));
            seAutoFormDelay.EditValue = AlertOptionsClass.AlertOptions.AutoFormDelay / 1000;
            icbFormDisplaySpeed.EditValue = AlertOptionsClass.AlertOptions.FormDisplaySpeed;
            icbFormLocation.EditValue = AlertOptionsClass.AlertOptions.FormLocation;
            icbShowingEffect.EditValue = AlertOptionsClass.AlertOptions.FormShowingEffect;
            seFormMaxCount.Value = AlertOptionsClass.AlertOptions.FormMaxCount;
            ceAutoHeight.Checked = AlertOptionsClass.AlertOptions.AutoHeight;
            ceWidth.EditValue = AlertOptionsClass.AlertOptions.Width;
            ceHeight.EditValue = AlertOptionsClass.AlertOptions.Height;
            txtCaption.Text = AlertOptionsClass.AlertOptions.TitleTemplate;
            txtText.Text = AlertOptionsClass.AlertOptions.BodyTemplate;
            spinEditFontSize.EditValue = AlertOptionsClass.AlertOptions.FontDelta;
            checkEditShowImage.Checked = AlertOptionsClass.AlertOptions.ShowImage;
            checkEditEnable.Checked = AlertOptionsClass.AlertOptions.Enabled;
        }

        private async void btnTestAlert_Click(object sender, EventArgs e)
        {
            AControl.AppearanceCaption.FontSizeDelta = (int)spinEditFontSize.Value - 4;
            AControl.AppearanceText.FontSizeDelta = (int)spinEditFontSize.Value - 5;
            var oldHeight = AlertOptionsClass.AlertOptions.Height;
            var oldWidth = AlertOptionsClass.AlertOptions.Width;

            AControl.AutoFormDelay = Convert.ToInt32(seAutoFormDelay.Value) * 1000;
            AControl.AutoHeight = ceAutoHeight.Checked;

            AControl.FormDisplaySpeed = (AlertFormDisplaySpeed)icbFormDisplaySpeed.EditValue;
            AControl.FormLocation = (AlertFormLocation)icbFormLocation.EditValue;
            AControl.FormMaxCount = (int)seFormMaxCount.Value;
            AControl.FormShowingEffect = (AlertFormShowingEffect)icbShowingEffect.EditValue;
            AlertOptionsClass.AlertOptions.Height = Convert.ToInt32(ceHeight.EditValue);
            AlertOptionsClass.AlertOptions.Width = Convert.ToInt32(ceWidth.EditValue);

            var oldTitle = AlertOptionsClass.AlertOptions.TitleTemplate;
            var oldBody = AlertOptionsClass.AlertOptions.BodyTemplate;
            var columnsOk = AlertOptionsClass.AlertOptions.SetTitleBody(txtCaption.Text, txtText.Text);
            (var row, var gridControl) = Helpers.GetRandomRow(EbaySearches);
            
            if (columnsOk)
            {
                if (row == null)
                {
                    XtraMessageBox.Show("To test alert, please, get some results first. Click \"Start\" button");
                }
                else
                {
                    var title = AlertOptionsClass.AlertOptions.TitleTemplate;
                    var body = AlertOptionsClass.AlertOptions.BodyTemplate;
                    var datalist = (DataList)row["Blob"];
                    
                    title = AlertOptionsClass.AlertOptions.TitleColumns.Aggregate(title,
                        (current, columnName) => current.Replace("{" + columnName + "}", AlertOptionsClass.GetRowValue(row, columnName, datalist)));
                    body = AlertOptionsClass.AlertOptions.BodyColumns.Aggregate(body,
                        (current, columnName) => current.Replace("{" + columnName + "}", AlertOptionsClass.GetRowValue(row, columnName, datalist)));

                    var alertInfo = new AlertInfo(title, body)
                    {
                        Tag = Tuple.Create(row, gridControl)
                    };
                    if (checkEditShowImage.Checked)
                    {
                        var avatarImagePath = await ImageTools.GetImageFromDiskOrInternet(datalist.GalleryUrl);
                        if (File.Exists(avatarImagePath))
                        {
                            var imageBitmap = await ImageTools.ReadFileToBitmap(avatarImagePath);
                            var maxWidth = AlertOptionsClass.AlertOptions.Width / 3;
                            if (imageBitmap.Size.Width > maxWidth)
                            {
                                imageBitmap = ImageProcessor.ResizeImage(imageBitmap, (int)maxWidth, imageBitmap.Size.Height, true, false);
                            }

                            alertInfo.Image = imageBitmap; //image access exceptions
                        }
                    }

                    AControl.Show(this, alertInfo);
                }
            }

            AlertOptionsClass.AlertOptions.TitleTemplate = oldTitle;
            AlertOptionsClass.AlertOptions.BodyTemplate = oldBody;
            AlertOptionsClass.AlertOptions.SetTitleBody(oldTitle, oldBody);
            AControl.AutoFormDelay = AlertOptionsClass.AlertOptions.AutoFormDelay;
            AControl.FormDisplaySpeed = AlertOptionsClass.AlertOptions.FormDisplaySpeed;
            AControl.FormLocation = AlertOptionsClass.AlertOptions.FormLocation;
            AControl.FormShowingEffect = AlertOptionsClass.AlertOptions.FormShowingEffect;
            AControl.FormMaxCount = AlertOptionsClass.AlertOptions.FormMaxCount;
            AControl.AutoHeight = AlertOptionsClass.AlertOptions.AutoHeight;
            AlertOptionsClass.AlertOptions.Height = oldHeight;
            AlertOptionsClass.AlertOptions.Width = oldWidth;
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            var columnsOk = AlertOptionsClass.AlertOptions.SetTitleBody(txtCaption.Text, txtText.Text);
            if (columnsOk)
            {
                AlertOptionsClass.AlertOptions.TitleTemplate = txtCaption.Text;
                AlertOptionsClass.AlertOptions.BodyTemplate = txtText.Text;
                AlertOptionsClass.AlertOptions.AutoFormDelay = Convert.ToInt32(seAutoFormDelay.Value) * 1000;
                AlertOptionsClass.AlertOptions.FormDisplaySpeed = (AlertFormDisplaySpeed)icbFormDisplaySpeed.EditValue;
                AlertOptionsClass.AlertOptions.FormLocation = (AlertFormLocation)icbFormLocation.EditValue;
                AlertOptionsClass.AlertOptions.FormShowingEffect = (AlertFormShowingEffect)icbShowingEffect.EditValue;
                AlertOptionsClass.AlertOptions.FormMaxCount = Convert.ToInt32(seFormMaxCount.Value);
                AlertOptionsClass.AlertOptions.AutoHeight = ceAutoHeight.Checked;
                AlertOptionsClass.AlertOptions.Width = Convert.ToInt32(ceWidth.EditValue);
                AlertOptionsClass.AlertOptions.Height = Convert.ToInt32(ceHeight.EditValue);
                AlertOptionsClass.AlertOptions.FontDelta = Convert.ToInt32(spinEditFontSize.EditValue);
                AlertOptionsClass.AlertOptions.ShowImage = checkEditShowImage.Checked;
                AlertOptionsClass.AlertOptions.Enabled = checkEditEnable.Checked;
                AlertOptionsClass.AlertOptions.SetAlertControlSettings(AControl);
                DialogResult = DialogResult.OK;
                Close();
            }
        }

        private void hyperlinkLabelControlHelp_Click(object sender, EventArgs e)
        {
            var hyperLink = (HyperlinkLabelControl)sender;
            if (hyperLink.Tag != null)
                Process.Start(hyperLink.Tag.ToString());
        }

        private void FormAlert_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == 27)
                Close();
        }

        private void labelControl1_Click(object sender, EventArgs e)
        {
            Loggers.EnableDebugLogger();
        }
    }
}
