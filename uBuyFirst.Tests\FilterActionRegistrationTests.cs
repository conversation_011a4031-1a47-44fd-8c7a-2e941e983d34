﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Filters;
using uBuyFirst.Prefs;
using System.Reflection;
using System.Collections.Generic;
using System;

namespace uBuyFirst.Tests
{
    [TestClass]
    public class FilterActionRegistrationTests
    {
        [TestInitialize]
        public void TestInitialize()
        {
            // Reset ConnectionConfig to default state before each test
            ConnectionConfig.RestockerEnabled = false;

            // Clear the registries to ensure clean state
            ClearFilterActionFactory();
            ClearFilterActionUIRegistry();
        }

        [TestMethod]
        public void FilterActionFactory_Initialize_WithAutoPurchaseDisabled_ShouldNotRegisterRestockAction()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = false;

            // Act
            FilterActionFactory.Initialize(null);

            // Assert
            var isRegistered = IsRestockActionRegistered();
            Assert.IsFalse(isRegistered, "RestockFilterAction should not be registered when RestockerEnabled is false");
        }

        [TestMethod]
        public void FilterActionFactory_Initialize_WithAutoPurchaseEnabled_ShouldRegisterRestockAction()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = true;

            // Act
            FilterActionFactory.Initialize(null);

            // Assert
            var isRegistered = IsRestockActionRegistered();
            Assert.IsTrue(isRegistered, "RestockFilterAction should be registered when RestockerEnabled is true");
        }

        [TestMethod]
        public void FilterActionFactory_Reinitialize_FromDisabledToEnabled_ShouldRegisterRestockAction()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = false;
            FilterActionFactory.Initialize(null);
            var initiallyRegistered = IsRestockActionRegistered();
            Assert.IsFalse(initiallyRegistered, "RestockFilterAction should not be initially registered");

            // Act
            ConnectionConfig.RestockerEnabled = true;
            FilterActionFactory.Reinitialize(null);

            // Assert
            var finallyRegistered = IsRestockActionRegistered();
            Assert.IsTrue(finallyRegistered, "RestockFilterAction should be registered after reinitializing with RestockerEnabled = true");
        }

        [TestMethod]
        public void FilterActionFactory_Reinitialize_FromEnabledToDisabled_ShouldUnregisterRestockAction()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = true;
            FilterActionFactory.Initialize(null);
            var initiallyRegistered = IsRestockActionRegistered();
            Assert.IsTrue(initiallyRegistered, "RestockFilterAction should be initially registered");

            // Act
            ConnectionConfig.RestockerEnabled = false;
            FilterActionFactory.Reinitialize(null);

            // Assert
            var finallyRegistered = IsRestockActionRegistered();
            Assert.IsFalse(finallyRegistered, "RestockFilterAction should not be registered after reinitializing with RestockerEnabled = false");
        }

        [TestMethod]
        public void FilterActionUIRegistry_Initialize_WithAutoPurchaseDisabled_ShouldNotRegisterRestockUIConfigurator()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = false;

            // Act
            FilterActionUIRegistry.Reinitialize(); // Use Reinitialize to trigger fresh initialization

            // Assert
            var isRegistered = IsRestockUIConfiguratorRegistered();
            Assert.IsFalse(isRegistered, "RestockFilterUIConfigurator should not be registered when RestockerEnabled is false");
        }

        [TestMethod]
        public void FilterActionUIRegistry_Initialize_WithAutoPurchaseEnabled_ShouldRegisterRestockUIConfigurator()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = true;

            // Act
            FilterActionUIRegistry.Reinitialize(); // Use Reinitialize to trigger fresh initialization

            // Assert
            var isRegistered = IsRestockUIConfiguratorRegistered();
            Assert.IsTrue(isRegistered, "RestockFilterUIConfigurator should be registered when RestockerEnabled is true");
        }

        [TestMethod]
        public void FilterActionUIRegistry_Reinitialize_FromDisabledToEnabled_ShouldRegisterRestockUIConfigurator()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = false;
            FilterActionUIRegistry.Reinitialize();
            var initiallyRegistered = IsRestockUIConfiguratorRegistered();
            Assert.IsFalse(initiallyRegistered, "RestockFilterUIConfigurator should not be initially registered");

            // Act
            ConnectionConfig.RestockerEnabled = true;
            FilterActionUIRegistry.Reinitialize();

            // Assert
            var finallyRegistered = IsRestockUIConfiguratorRegistered();
            Assert.IsTrue(finallyRegistered, "RestockFilterUIConfigurator should be registered after reinitializing with RestockerEnabled = true");
        }

        [TestMethod]
        public void FilterActionUIRegistry_Reinitialize_FromEnabledToDisabled_ShouldUnregisterRestockUIConfigurator()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = true;
            FilterActionUIRegistry.Reinitialize();
            var initiallyRegistered = IsRestockUIConfiguratorRegistered();
            Assert.IsTrue(initiallyRegistered, "RestockFilterUIConfigurator should be initially registered");

            // Act
            ConnectionConfig.RestockerEnabled = false;
            FilterActionUIRegistry.Reinitialize();

            // Assert
            var finallyRegistered = IsRestockUIConfiguratorRegistered();
            Assert.IsFalse(finallyRegistered, "RestockFilterUIConfigurator should not be registered after reinitializing with RestockerEnabled = false");
        }

        [TestMethod]
        public void FilterActionFactory_LegacyMigration_WithAutoPurchaseEnabled_ShouldIncludeRestockMapping()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = true;

            // Act
            FilterActionFactory.Initialize(null);

            // Assert
            var hasLegacyMapping = HasRestockLegacyMapping();
            Assert.IsTrue(hasLegacyMapping, "Legacy migration mapping for 'Restock' should exist when RestockerEnabled is true");
        }

        [TestMethod]
        public void FilterActionFactory_LegacyMigration_WithAutoPurchaseDisabled_ShouldNotIncludeRestockMapping()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = false;

            // Act
            FilterActionFactory.Initialize(null);

            // Assert
            var hasLegacyMapping = HasRestockLegacyMapping();
            Assert.IsFalse(hasLegacyMapping, "Legacy migration mapping for 'Restock' should not exist when RestockerEnabled is false");
        }

        #region Helper Methods

        private bool IsRestockActionRegistered()
        {
            try
            {
                var registryField = typeof(FilterActionFactory).GetField("_actionRegistry", BindingFlags.NonPublic | BindingFlags.Static);
                var registry = registryField?.GetValue(null) as Dictionary<string, Func<IFilterAction>>;
                return registry?.ContainsKey(RestockFilterAction.IDENTIFIER) ?? false;
            }
            catch
            {
                return false;
            }
        }

        private bool IsRestockUIConfiguratorRegistered()
        {
            try
            {
                var configuratorsField = typeof(FilterActionUIRegistry).GetField("_uiConfigurators", BindingFlags.NonPublic | BindingFlags.Static);
                var configurators = configuratorsField?.GetValue(null) as Dictionary<string, IFilterActionUIConfigurator>;
                return configurators?.ContainsKey(RestockFilterAction.IDENTIFIER) ?? false;
            }
            catch
            {
                return false;
            }
        }

        private bool HasRestockLegacyMapping()
        {
            try
            {
                var migrationField = typeof(FilterActionFactory).GetField("_legacyActionMigration", BindingFlags.NonPublic | BindingFlags.Static);
                var migration = migrationField?.GetValue(null) as Dictionary<string, string>;
                return migration?.ContainsKey("Restock") ?? false;
            }
            catch
            {
                return false;
            }
        }

        private void ClearFilterActionFactory()
        {
            try
            {
                var registryField = typeof(FilterActionFactory).GetField("_actionRegistry", BindingFlags.NonPublic | BindingFlags.Static);
                var registry = registryField?.GetValue(null) as Dictionary<string, Func<IFilterAction>>;
                registry?.Clear();

                var migrationField = typeof(FilterActionFactory).GetField("_legacyActionMigration", BindingFlags.NonPublic | BindingFlags.Static);
                var migration = migrationField?.GetValue(null) as Dictionary<string, string>;
                migration?.Clear();

                var initializedField = typeof(FilterActionFactory).GetField("_initialized", BindingFlags.NonPublic | BindingFlags.Static);
                initializedField?.SetValue(null, false);

                var lastRestockerStateField = typeof(FilterActionFactory).GetField("_lastRestockerEnabledState", BindingFlags.NonPublic | BindingFlags.Static);
                lastRestockerStateField?.SetValue(null, false);
            }
            catch
            {
                // Ignore errors in cleanup
            }
        }

        private void ClearFilterActionUIRegistry()
        {
            try
            {
                var configuratorsField = typeof(FilterActionUIRegistry).GetField("_uiConfigurators", BindingFlags.NonPublic | BindingFlags.Static);
                var configurators = configuratorsField?.GetValue(null) as Dictionary<string, IFilterActionUIConfigurator>;
                configurators?.Clear();

                var initializedField = typeof(FilterActionUIRegistry).GetField("_initialized", BindingFlags.NonPublic | BindingFlags.Static);
                initializedField?.SetValue(null, false);
            }
            catch
            {
                // Ignore errors in cleanup
            }
        }

        #endregion

        [TestCleanup]
        public void TestCleanup()
        {
            // Reset ConnectionConfig to default state after each test
            ConnectionConfig.RestockerEnabled = false;

            // Clear the registries to ensure clean state for next test
            ClearFilterActionFactory();
            ClearFilterActionUIRegistry();
        }
    }
}
