﻿using System;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Threading;
using System.Windows.Forms;
using DevExpress.Utils;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using uBuyFirst.Other;
using uBuyFirst.Purchasing;
using uBuyFirst.Tools;

namespace uBuyFirst
{
    public partial class FormBid
    {
        private void FormBid_FormClosing(object sender, FormClosingEventArgs e)
        {
            SaveLayout();
            foreach (var label in layoutControl1.Controls.OfType<LabelControl>())
            {
                label.Text = "";
            }
        }

        private void SaveLayout()
        {
            try
            {
                layoutControl1.SaveLayoutToXml(Path.Combine(Folders.Layout, "Layout Bid Window.xml"));
            }
            catch (Exception)
            {
                //TODO: handle this
            }
        }

        private void LoadLayout()
        {
            if (_isLayoutLoaded)
                return;

            _isLayoutLoaded = true;
            var file = Path.Combine(Folders.Layout, "Layout Bid Window.xml");
            layoutControl1.BeginUpdate();

            var stream = new MemoryStream();
            layoutControl1.SaveLayoutToStream(stream);
            stream.Seek(0, SeekOrigin.Begin);

            if (!Program.Sandbox && File.Exists(file))
                try
                {
                    layoutControl1.RestoreLayoutFromXml(file);
                }
                catch (Exception e)
                {
                    layoutControl1.RestoreLayoutFromStream(stream);
                    stream.Seek(0, SeekOrigin.Begin);
                    ExM.ubuyExceptionHandler("LoadLayout BidForm: ", e);
                }

            SetLayoutControlFont(layoutControl1.Root.AppearanceItemCaption.Font, layoutControl1.Root.AppearanceItemCaption.FontSizeDelta);
            layoutControl1.EndUpdate();
        }

        private void barButtonItemChangeAppearance_ItemClick(object sender, ItemClickEventArgs e)
        {
            popupMenuItemDetails.ShowPopup(MousePosition);
        }

        private void barButtonItemCustomizeLayout_ItemClick(object sender, ItemClickEventArgs e)
        {
            layoutControl1.ShowCustomizationForm();
        }

        private void barButtonItemResetLayout_ItemClick(object sender, ItemClickEventArgs e)
        {
            layoutControl1.BeginUpdate();
            layoutControl1.RestoreDefaultLayout();
            SetLayoutControlFont(layoutControl1.Root.AppearanceItemCaption.Font, layoutControl1.Root.AppearanceItemCaption.FontSizeDelta);
            layoutControl1.EndUpdate();
        }

        private void SetLayoutControlFont(Font f, int delta)
        {
            if (layoutControl1.Root.AppearanceItemCaption.FontSizeDelta < 0)
                layoutControl1.Root.AppearanceItemCaption.FontSizeDelta = 0;

            layoutControl1.Root.AppearanceItemCaption.Font = new Font(f.FontFamily, (float)8.25, f.Style);
            layoutControl1.Appearance.Control.FontSizeDelta = delta;
            lcTitle.Font = new Font(lcTitle.Font.FontFamily, layoutControl1.Appearance.Control.Font.Size + 3, FontStyle.Bold);
        }

        private void barButtonItemIncreaseFont_ItemClick(object sender, ItemClickEventArgs e)
        {
            layoutControl1.Root.AppearanceItemCaption.FontSizeDelta = layoutControl1.Root.AppearanceItemCaption.FontSizeDelta + 1;
            SetLayoutControlFont(layoutControl1.Root.AppearanceItemCaption.Font, layoutControl1.Root.AppearanceItemCaption.FontSizeDelta);
        }

        private void barButtonItemDecreaseFont_ItemClick(object sender, ItemClickEventArgs e)
        {
            layoutControl1.Root.AppearanceItemCaption.FontSizeDelta = layoutControl1.Root.AppearanceItemCaption.FontSizeDelta - 1;
            SetLayoutControlFont(layoutControl1.Root.AppearanceItemCaption.Font, layoutControl1.Root.AppearanceItemCaption.FontSizeDelta);
        }

        private void layoutControl1_LayoutUpgrade(object sender, LayoutUpgradeEventArgs e)
        {
            layoutControl1.BeginUpdate();
            layoutControl1.RestoreDefaultLayout();
            layoutControl1.EndUpdate();
        }

        private void FormBid_LocationChanged(object sender, EventArgs e)
        {
            PlaceOfferWindowLocation = Location;
            PlaceOfferWindowSize = Size;
        }

        private void SetFormSizeAndLocation()
        {
            if (PlaceOfferWindowSize is { Height: > 100, Width: > 100 })
            {
                Size = PlaceOfferWindowSize;
            }

            if (PlaceOfferWindowLocation is { X: > 0, Y: > 0 })
            {
                StartPosition = FormStartPosition.Manual;
                Location = PlaceOfferWindowLocation;
            }
        }

        private void FormBid_SizeChanged(object sender, EventArgs e)
        {
            PlaceOfferWindowSize = Size;
        }

        private void FormClose_OnESC(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == 27)
            {
                Close();
                _getItemCancelTokenSourceTask.Cancel();
                _getItemCancelTokenSourceTask = new CancellationTokenSource();
                _getItemCancelToken = _getItemCancelTokenSourceTask.Token;
            }
        }

        private void FormBid_Load(object sender, EventArgs e)
        {
            LoadLayout();
            spinEditBestOfferPricePerItem.Value = Math.Round((decimal)D.ItemPricing.ItemPrice.Value * spinEditOfferPercentage.Value / 100, 2);
            //AutoMeasurement.Client.TrackScreenView("Screen - " + Text);
            if (Program.Sandbox)
            {
                linkeBayUserAgreement.Visible = true;
                linkeBayPrivacyPolicy.Visible = true;
                pictureEditLogo.Visible = true;
            }
        }

        #region Init

        private void InitializeLayout()
        {
            layoutControl1.LayoutVersion = "4";
            layoutControl1.SetDefaultLayout();
        }

        #endregion

        #region Init

        private void SetButtonFocus(Placeoffer.OrderAction orderAction)
        {
            if (orderAction == Placeoffer.OrderAction.MakeOffer)
            {
                lciMakeOffertab.ParentTabbedGroup.SelectedTabPage = lciMakeOffertab;
                spinEditBestOfferPricePerItem.Properties.AllowFocused = true;
                spinEditBestOfferPricePerItem.Select();
                spinEditBestOfferPricePerItem.Focus();
                AcceptButton = btnBestOffer;
            }
            else
            {
                layoutControlGroup2.ParentTabbedGroup.SelectedTabPage = layoutControlGroup2;
                AcceptButton = btnBuyItnow;
                btnBuyItnow.Select();
                btnBuyItnow.Focus();
            }
        }

        #endregion
    }
}
