﻿namespace uBuyFirst
{
    partial class FormPushbullet
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.Utils.SuperToolTip superToolTip1 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem1 = new DevExpress.Utils.ToolTipItem();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormPushbullet));
            DevExpress.Utils.SuperToolTip superToolTip2 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem2 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip3 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem3 = new DevExpress.Utils.ToolTipItem();
            this.formAssistant1 = new DevExpress.XtraBars.FormAssistant();
            this.ribbonControl1 = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.barStaticItem1 = new DevExpress.XtraBars.BarStaticItem();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.repositoryItemTextEdit2 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.txtTitleTemplate = new DevExpress.XtraEditors.TextEdit();
            this.toolTipController1 = new DevExpress.Utils.ToolTipController(this.components);
            this.popupMenuColumnNames = new DevExpress.XtraBars.PopupMenu(this.components);
            this.memoBodyTemplate = new DevExpress.XtraEditors.MemoEdit();
            this.lnkPushbulletAuth = new DevExpress.XtraEditors.HyperlinkLabelControl();
            this.lblAuthExplanation = new DevExpress.XtraEditors.LabelControl();
            this.lblPushbulletToken = new DevExpress.XtraEditors.LabelControl();
            this.txtToken = new DevExpress.XtraEditors.TextEdit();
            this.lblTitleTemplate = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.btnSave = new DevExpress.XtraEditors.SimpleButton();
            this.btnTestMessage = new DevExpress.XtraEditors.SimpleButton();
            this.btnVerify = new DevExpress.XtraEditors.SimpleButton();
            this.chkSendNotifications = new DevExpress.XtraEditors.CheckEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.radioGroup1 = new DevExpress.XtraEditors.RadioGroup();
            this.lblCombinePushes = new DevExpress.XtraEditors.LabelControl();
            this.boxDeviceList = new DevExpress.XtraEditors.ComboBoxEdit();
            this.lnkWhyDoINeedThis = new DevExpress.XtraEditors.HyperlinkLabelControl();
            this.chkCheckoutUrl = new DevExpress.XtraEditors.CheckEdit();
            this.lblStatusValue = new DevExpress.XtraEditors.LabelControl();
            this.lblStatus = new DevExpress.XtraEditors.LabelControl();
            this.dropDownButton1 = new DevExpress.XtraEditors.DropDownButton();
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTitleTemplate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenuColumnNames)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.memoBodyTemplate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtToken.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSendNotifications.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroup1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.boxDeviceList.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCheckoutUrl.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // ribbonControl1
            // 
            this.ribbonControl1.ExpandCollapseItem.Id = 0;
            this.ribbonControl1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.ribbonControl1.ExpandCollapseItem,
            this.ribbonControl1.SearchEditItem,
            this.barStaticItem1});
            this.ribbonControl1.Location = new System.Drawing.Point(0, 0);
            this.ribbonControl1.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.ribbonControl1.MaxItemId = 6;
            this.ribbonControl1.Name = "ribbonControl1";
            this.ribbonControl1.OptionsMenuMinWidth = 385;
            this.ribbonControl1.OptionsPageCategories.ShowCaptions = false;
            this.ribbonControl1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1,
            this.repositoryItemTextEdit2});
            this.ribbonControl1.ShowApplicationButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowDisplayOptionsMenuButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowExpandCollapseButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowPageHeadersMode = DevExpress.XtraBars.Ribbon.ShowPageHeadersMode.Hide;
            this.ribbonControl1.Size = new System.Drawing.Size(581, 32);
            // 
            // barStaticItem1
            // 
            this.barStaticItem1.Id = 5;
            this.barStaticItem1.Name = "barStaticItem1";
            // 
            // repositoryItemTextEdit1
            // 
            this.repositoryItemTextEdit1.AutoHeight = false;
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // repositoryItemTextEdit2
            // 
            this.repositoryItemTextEdit2.AutoHeight = false;
            this.repositoryItemTextEdit2.Name = "repositoryItemTextEdit2";
            // 
            // txtTitleTemplate
            // 
            this.txtTitleTemplate.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtTitleTemplate.EditValue = "{Item Price} - {Title}";
            this.txtTitleTemplate.Location = new System.Drawing.Point(119, 247);
            this.txtTitleTemplate.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.txtTitleTemplate.Name = "txtTitleTemplate";
            this.ribbonControl1.SetPopupContextMenu(this.txtTitleTemplate, this.popupMenuColumnNames);
            this.txtTitleTemplate.Properties.NullValuePrompt = "ex. {Item Price} - {Title}";
            this.txtTitleTemplate.Size = new System.Drawing.Size(317, 20);
            this.txtTitleTemplate.TabIndex = 2;
            this.txtTitleTemplate.ToolTip = "Use right click menu to choose available fields.";
            this.txtTitleTemplate.ToolTipController = this.toolTipController1;
            // 
            // popupMenuColumnNames
            // 
            this.popupMenuColumnNames.ItemLinks.Add(this.barStaticItem1);
            this.popupMenuColumnNames.Name = "popupMenuColumnNames";
            this.popupMenuColumnNames.OptionsMultiColumn.ColumnCount = 2;
            this.popupMenuColumnNames.Ribbon = this.ribbonControl1;
            this.popupMenuColumnNames.BeforePopup += new System.ComponentModel.CancelEventHandler(this.popupMenuColumnNames_BeforePopup);
            // 
            // memoBodyTemplate
            // 
            this.memoBodyTemplate.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.memoBodyTemplate.EditValue = "{Condition Description}\r\nSeller: {Feedback Rating}% - {Feedback Score} ";
            this.memoBodyTemplate.Location = new System.Drawing.Point(119, 290);
            this.memoBodyTemplate.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.memoBodyTemplate.Name = "memoBodyTemplate";
            this.ribbonControl1.SetPopupContextMenu(this.memoBodyTemplate, this.popupMenuColumnNames);
            this.memoBodyTemplate.Properties.NullValuePrompt = "ex. Condition: {Condition}, {Condition Description}";
            this.memoBodyTemplate.Size = new System.Drawing.Size(317, 103);
            this.memoBodyTemplate.TabIndex = 3;
            this.memoBodyTemplate.ToolTip = "Use right click menu to choose available fields.";
            // 
            // lnkPushbulletAuth
            // 
            this.lnkPushbulletAuth.Cursor = System.Windows.Forms.Cursors.Hand;
            this.lnkPushbulletAuth.Location = new System.Drawing.Point(15, 48);
            this.lnkPushbulletAuth.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.lnkPushbulletAuth.Name = "lnkPushbulletAuth";
            this.lnkPushbulletAuth.Size = new System.Drawing.Size(72, 13);
            this.lnkPushbulletAuth.TabIndex = 3;
            this.lnkPushbulletAuth.Text = "Pushbullet.com";
            this.lnkPushbulletAuth.ToolTip = "Register/Login at PushBullet website";
            this.lnkPushbulletAuth.Click += new System.EventHandler(this.lnkPushbulletAuth_Click);
            // 
            // lblAuthExplanation
            // 
            this.lblAuthExplanation.AllowHtmlString = true;
            this.lblAuthExplanation.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.lblAuthExplanation.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical;
            this.lblAuthExplanation.Cursor = System.Windows.Forms.Cursors.Default;
            this.lblAuthExplanation.LineVisible = true;
            this.lblAuthExplanation.Location = new System.Drawing.Point(15, 73);
            this.lblAuthExplanation.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.lblAuthExplanation.Name = "lblAuthExplanation";
            this.lblAuthExplanation.Size = new System.Drawing.Size(506, 26);
            toolTipItem1.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image")));
            toolTipItem1.Appearance.Options.UseImage = true;
            toolTipItem1.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image1")));
            superToolTip1.Items.Add(toolTipItem1);
            this.lblAuthExplanation.SuperTip = superToolTip1;
            this.lblAuthExplanation.TabIndex = 4;
            this.lblAuthExplanation.Text = "uBuyFirst needs your PushBullet access token to send push notifications.\r\nYou can" +
    " find the token in your Pushbullet Account -> Settings -> Account -> Access Toke" +
    "n";
            this.lblAuthExplanation.ToolTipController = this.toolTipController1;
            // 
            // lblPushbulletToken
            // 
            this.lblPushbulletToken.Location = new System.Drawing.Point(15, 127);
            this.lblPushbulletToken.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.lblPushbulletToken.Name = "lblPushbulletToken";
            this.lblPushbulletToken.Size = new System.Drawing.Size(67, 13);
            toolTipItem2.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image2")));
            toolTipItem2.Appearance.Options.UseImage = true;
            toolTipItem2.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image3")));
            superToolTip2.Items.Add(toolTipItem2);
            this.lblPushbulletToken.SuperTip = superToolTip2;
            this.lblPushbulletToken.TabIndex = 5;
            this.lblPushbulletToken.Text = "Access token:";
            // 
            // txtToken
            // 
            this.txtToken.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtToken.Location = new System.Drawing.Point(119, 123);
            this.txtToken.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.txtToken.MenuManager = this.ribbonControl1;
            this.txtToken.Name = "txtToken";
            this.txtToken.Properties.NullValuePrompt = "ex. v1JGVgrKUgj7vpHmldmI08fXQTIVKUgj7vpHmldmI";
            this.txtToken.Size = new System.Drawing.Size(317, 20);
            toolTipItem3.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image4")));
            toolTipItem3.Appearance.Options.UseImage = true;
            toolTipItem3.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image5")));
            superToolTip3.Items.Add(toolTipItem3);
            this.txtToken.SuperTip = superToolTip3;
            this.txtToken.TabIndex = 0;
            // 
            // lblTitleTemplate
            // 
            this.lblTitleTemplate.Location = new System.Drawing.Point(15, 251);
            this.lblTitleTemplate.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.lblTitleTemplate.Name = "lblTitleTemplate";
            this.lblTitleTemplate.Size = new System.Drawing.Size(71, 13);
            this.lblTitleTemplate.TabIndex = 5;
            this.lblTitleTemplate.Text = "Title Template:";
            this.lblTitleTemplate.ToolTip = "Use right click menu to choose available fields.";
            this.lblTitleTemplate.ToolTipController = this.toolTipController1;
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(15, 290);
            this.labelControl2.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(73, 13);
            this.labelControl2.TabIndex = 9;
            this.labelControl2.Text = "Body template:";
            this.labelControl2.ToolTip = "Use right click menu to choose available fields.";
            // 
            // btnSave
            // 
            this.btnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSave.Enabled = false;
            this.btnSave.Location = new System.Drawing.Point(458, 452);
            this.btnSave.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(106, 22);
            this.btnSave.TabIndex = 6;
            this.btnSave.Text = "Save";
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // btnTestMessage
            // 
            this.btnTestMessage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnTestMessage.Enabled = false;
            this.btnTestMessage.Location = new System.Drawing.Point(458, 341);
            this.btnTestMessage.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btnTestMessage.Name = "btnTestMessage";
            this.btnTestMessage.Size = new System.Drawing.Size(106, 22);
            this.btnTestMessage.TabIndex = 4;
            this.btnTestMessage.Text = "Send test message";
            this.btnTestMessage.Click += new System.EventHandler(this.simpleTestMessage_Click);
            // 
            // btnVerify
            // 
            this.btnVerify.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnVerify.Location = new System.Drawing.Point(458, 121);
            this.btnVerify.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.btnVerify.Name = "btnVerify";
            this.btnVerify.Size = new System.Drawing.Size(106, 22);
            this.btnVerify.TabIndex = 1;
            this.btnVerify.Text = "Verify";
            this.btnVerify.Click += new System.EventHandler(this.btnVerify_Click);
            // 
            // chkSendNotifications
            // 
            this.chkSendNotifications.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.chkSendNotifications.Location = new System.Drawing.Point(230, 454);
            this.chkSendNotifications.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.chkSendNotifications.MenuManager = this.ribbonControl1;
            this.chkSendNotifications.Name = "chkSendNotifications";
            this.chkSendNotifications.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold);
            this.chkSendNotifications.Properties.Appearance.Options.UseFont = true;
            this.chkSendNotifications.Properties.Caption = "Push notifications enabled";
            this.chkSendNotifications.Size = new System.Drawing.Size(205, 20);
            this.chkSendNotifications.TabIndex = 5;
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(15, 158);
            this.labelControl1.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(75, 13);
            this.labelControl1.TabIndex = 9;
            this.labelControl1.Text = "Send to device:";
            this.labelControl1.ToolTip = "After you\'ve provided your token, select your remote device that will recieve pus" +
    "h notifications from uBuyFirst.";
            // 
            // radioGroup1
            // 
            this.radioGroup1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.radioGroup1.Location = new System.Drawing.Point(119, 201);
            this.radioGroup1.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.radioGroup1.MenuManager = this.ribbonControl1;
            this.radioGroup1.Name = "radioGroup1";
            this.radioGroup1.Properties.Columns = 2;
            this.radioGroup1.Properties.Items.AddRange(new DevExpress.XtraEditors.Controls.RadioGroupItem[] {
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "Multiple items - one push"),
            new DevExpress.XtraEditors.Controls.RadioGroupItem(null, "One item - one push")});
            this.radioGroup1.Size = new System.Drawing.Size(317, 39);
            this.radioGroup1.TabIndex = 13;
            this.radioGroup1.ToolTip = "If you receive just a few items per hour choose \"One item - one push\"\r\n\"Multiple " +
    "items - one push\" - items will be sent in batches of 10 or every 10 seconds.";
            this.radioGroup1.SelectedIndexChanged += new System.EventHandler(this.radioGroup1_SelectedIndexChanged);
            // 
            // lblCombinePushes
            // 
            this.lblCombinePushes.Location = new System.Drawing.Point(14, 212);
            this.lblCombinePushes.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.lblCombinePushes.Name = "lblCombinePushes";
            this.lblCombinePushes.Size = new System.Drawing.Size(63, 13);
            this.lblCombinePushes.TabIndex = 5;
            this.lblCombinePushes.Text = "Aggregation:";
            this.lblCombinePushes.ToolTip = "If you receive just a few items per hour choose \"One item - one push\"\r\n\"Multiple " +
    "items - one push\" - items will be sent in batches of 10 or every 10 seconds.";
            // 
            // boxDeviceList
            // 
            this.boxDeviceList.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.boxDeviceList.Location = new System.Drawing.Point(119, 154);
            this.boxDeviceList.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.boxDeviceList.MenuManager = this.ribbonControl1;
            this.boxDeviceList.Name = "boxDeviceList";
            this.boxDeviceList.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.boxDeviceList.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.boxDeviceList.Size = new System.Drawing.Size(108, 20);
            this.boxDeviceList.TabIndex = 17;
            this.boxDeviceList.ToolTip = "After you\'ve provided your token, select your remote device that will recieve pus" +
    "h notifications from uBuyFirst.";
            this.boxDeviceList.MouseDown += new System.Windows.Forms.MouseEventHandler(this.boxDeviceList_MouseDown);
            // 
            // lnkWhyDoINeedThis
            // 
            this.lnkWhyDoINeedThis.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.lnkWhyDoINeedThis.Cursor = System.Windows.Forms.Cursors.Hand;
            this.lnkWhyDoINeedThis.Location = new System.Drawing.Point(15, 464);
            this.lnkWhyDoINeedThis.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.lnkWhyDoINeedThis.Name = "lnkWhyDoINeedThis";
            this.lnkWhyDoINeedThis.Size = new System.Drawing.Size(21, 13);
            this.lnkWhyDoINeedThis.TabIndex = 3;
            this.lnkWhyDoINeedThis.Text = "Help";
            this.lnkWhyDoINeedThis.ToolTip = "https://ubuyfirst.com/receive-new-ebay-listings-on-mobile-with-pushbullet/";
            this.lnkWhyDoINeedThis.Click += new System.EventHandler(this.lnkWhyDoINeedThis_Click);
            // 
            // chkCheckoutUrl
            // 
            this.chkCheckoutUrl.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.chkCheckoutUrl.Location = new System.Drawing.Point(230, 425);
            this.chkCheckoutUrl.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.chkCheckoutUrl.MenuManager = this.ribbonControl1;
            this.chkCheckoutUrl.Name = "chkCheckoutUrl";
            this.chkCheckoutUrl.Properties.Caption = "Item url points to checkout page  ";
            this.chkCheckoutUrl.Size = new System.Drawing.Size(205, 20);
            this.chkCheckoutUrl.TabIndex = 19;
            this.chkCheckoutUrl.ToolTip = "Unchecked - Clicking an item will lead to eBay item view.\r\nChecked - Clicking an " +
    "item will skip item page and lead directly to checkout page.";
            this.chkCheckoutUrl.CheckedChanged += new System.EventHandler(this.chkCheckoutUrl_CheckedChanged);
            // 
            // lblStatusValue
            // 
            this.lblStatusValue.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.lblStatusValue.Location = new System.Drawing.Point(119, 406);
            this.lblStatusValue.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.lblStatusValue.Name = "lblStatusValue";
            this.lblStatusValue.Size = new System.Drawing.Size(0, 13);
            this.lblStatusValue.TabIndex = 21;
            this.lblStatusValue.ToolTip = "Displays Pushbullet limits state";
            // 
            // lblStatus
            // 
            this.lblStatus.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.lblStatus.Location = new System.Drawing.Point(15, 406);
            this.lblStatus.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new System.Drawing.Size(35, 13);
            this.lblStatus.TabIndex = 21;
            this.lblStatus.Text = "Status:";
            this.lblStatus.ToolTip = "Displays Pushbullet limits state";
            // 
            // dropDownButton1
            // 
            this.dropDownButton1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.dropDownButton1.DropDownArrowStyle = DevExpress.XtraEditors.DropDownArrowStyle.Show;
            this.dropDownButton1.DropDownControl = this.popupMenuColumnNames;
            this.dropDownButton1.Location = new System.Drawing.Point(458, 292);
            this.dropDownButton1.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.dropDownButton1.MenuManager = this.ribbonControl1;
            this.dropDownButton1.Name = "dropDownButton1";
            this.dropDownButton1.Size = new System.Drawing.Size(106, 22);
            this.dropDownButton1.TabIndex = 22;
            this.dropDownButton1.Text = "Fields";
            // 
            // FormPushbullet
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(581, 487);
            this.Controls.Add(this.dropDownButton1);
            this.Controls.Add(this.lblStatus);
            this.Controls.Add(this.lblStatusValue);
            this.Controls.Add(this.chkCheckoutUrl);
            this.Controls.Add(this.boxDeviceList);
            this.Controls.Add(this.chkSendNotifications);
            this.Controls.Add(this.btnVerify);
            this.Controls.Add(this.btnTestMessage);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.memoBodyTemplate);
            this.Controls.Add(this.txtTitleTemplate);
            this.Controls.Add(this.lblCombinePushes);
            this.Controls.Add(this.lblTitleTemplate);
            this.Controls.Add(this.txtToken);
            this.Controls.Add(this.lblPushbulletToken);
            this.Controls.Add(this.lblAuthExplanation);
            this.Controls.Add(this.lnkWhyDoINeedThis);
            this.Controls.Add(this.lnkPushbulletAuth);
            this.Controls.Add(this.radioGroup1);
            this.Controls.Add(this.ribbonControl1);
            this.IconOptions.Icon = ((System.Drawing.Icon)(resources.GetObject("FormPushbullet.IconOptions.Icon")));
            this.Margin = new System.Windows.Forms.Padding(3, 4, 3, 4);
            this.Name = "FormPushbullet";
            this.Ribbon = this.ribbonControl1;
            this.Text = "Pushbullet";
            this.Shown += new System.EventHandler(this.FormPushbullet_Shown);
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTitleTemplate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenuColumnNames)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.memoBodyTemplate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtToken.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSendNotifications.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.radioGroup1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.boxDeviceList.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkCheckoutUrl.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.FormAssistant formAssistant1;
        private DevExpress.XtraBars.Ribbon.RibbonControl ribbonControl1;
        private DevExpress.XtraEditors.HyperlinkLabelControl lnkPushbulletAuth;
        private DevExpress.XtraEditors.LabelControl lblAuthExplanation;
        private DevExpress.XtraEditors.LabelControl lblPushbulletToken;
        private DevExpress.XtraEditors.TextEdit txtToken;
        private DevExpress.XtraEditors.LabelControl lblTitleTemplate;
        private DevExpress.XtraEditors.TextEdit txtTitleTemplate;
        private DevExpress.XtraEditors.MemoEdit memoBodyTemplate;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SimpleButton btnSave;
        private DevExpress.XtraEditors.SimpleButton btnTestMessage;
        private DevExpress.XtraEditors.SimpleButton btnVerify;
        private DevExpress.XtraEditors.CheckEdit chkSendNotifications;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.RadioGroup radioGroup1;
        private DevExpress.XtraEditors.LabelControl lblCombinePushes;
        private DevExpress.Utils.ToolTipController toolTipController1;
        private DevExpress.XtraEditors.ComboBoxEdit boxDeviceList;
        private DevExpress.XtraBars.PopupMenu popupMenuColumnNames;
        private DevExpress.XtraBars.BarStaticItem barStaticItem1;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit2;
        private DevExpress.XtraEditors.HyperlinkLabelControl lnkWhyDoINeedThis;
        private DevExpress.XtraEditors.CheckEdit chkCheckoutUrl;
        private DevExpress.XtraEditors.LabelControl lblStatusValue;
        private DevExpress.XtraEditors.LabelControl lblStatus;
        private DevExpress.XtraEditors.DropDownButton dropDownButton1;
    }
}