﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using System.Xml.Serialization;
using DevExpress.XtraEditors;
using Telegram.Bot;
using Telegram.Bot.Exceptions;
using Telegram.Bot.Extensions.Polling;
using Telegram.Bot.Types;
using Telegram.Bot.Types.Enums;
using Telegram.Bot.Types.ReplyMarkups;
using uBuyFirst.Data;
using uBuyFirst.Prefs;
using uBuyFirst.Tools;
using Message = Telegram.Bot.Types.Message;

namespace uBuyFirst.Telegram
{
    [Obfuscation(Exclude = true)]
    public class TelegramSender : ITelegramSender
    {
        [XmlIgnore]
        private TelegramBotClient? TGBot;

        [XmlIgnore]
        public string AccountConnectionState { get; set; } = "Disconnected.";

        [XmlIgnore]
        private DateTimeOffset _limitTimestamp;

        private string _botUsername = "";

        public string TelegramBotID { get; set; } = "";
        public string TelegramAccount { get; set; } = "@";
        public string TelegramChatID { get; set; } = "";
        public string BodyTemplate { get; set; } = "";
        public int MaxMessagesPerMinute { get; set; } = 1200;
        public int PicturesCountToAttach { get; set; } = 1;
        public bool Enabled { get; set; }
        public List<string> BodyColumns { get; set; } = new();

        public int CombinePushes { get; set; }
        private CancellationTokenSource? _cts;

        private readonly ConcurrentQueue<Tuple<DataRow, DateTime>> _messageQueue = new();
        private readonly SemaphoreSlim _semaphore = new(1, 1);
        private readonly Timer _queueProcessingTimer;
        private const int QueueProcessingIntervalSeconds = 10;
        private readonly TimeSpan _messageLifetime = TimeSpan.FromMinutes(1);
        private TokenBucket _tokenBucket1Sec;
        private TokenBucket _tokenBucket1Min;
        private MessageTemplateManager _messageTemplateManager;
        private readonly BotCommandHandler _commandHandler;
        public TelegramSender()
        {
            _queueProcessingTimer = new Timer(o => _ = ProcessQueue(), null, TimeSpan.Zero, TimeSpan.FromSeconds(QueueProcessingIntervalSeconds));
            _commandHandler = new BotCommandHandler(this);
            SetMaxMessagesPerMinute(MaxMessagesPerMinute);
            _messageTemplateManager = new MessageTemplateManager(this);
        }

        public void CreateTelegramBotClient(string botID)
        {
            TGBot = new TelegramBotClient(botID);
        }
        public bool SetBody(string bodyTemplateStr)
        {
            return _messageTemplateManager.SetBody(bodyTemplateStr);
        }
        public string GetBotState()
        {
            if (TGBot == null)
            {
                return "Disconnected.";
            }

            if (IsListening())
                return "Bot " + GetBotName() + " is listening.";
            else
                return "Disconnected.";
        }

        public void PushItem(DataRow row)
        {
            try
            {
                if ((DateTimeOffset.UtcNow - _limitTimestamp).TotalSeconds < 120)
                {
                    return;
                }

                if (TGBot == null)
                {
                    return;
                }
                _messageQueue.Enqueue(new Tuple<DataRow, DateTime>(row, DateTime.UtcNow));

                Task.Run(ProcessQueue);
            }
            catch (Exception ex)
            {
                if (HandleTelegramException(ex, GetBotName()))
                {
                    if (!ex.Message.Contains("Bad Request:"))
                        Enabled = false;

                    return;
                }

                if (ex.Message.Contains("Too Many Requests"))
                {
                    _limitTimestamp = DateTimeOffset.UtcNow;
                }

                ExM.ubuyExceptionHandler("_(info)_Telegram.", ex);
            }
        }


        private async Task ProcessQueue()
        {
            // Prevent multiple threads from processing the queue simultaneously
            if (!_semaphore.Wait(0)) return;
            try
            {                
                var queueProcessed = false;
                while (_messageQueue.TryDequeue(out var tupleRowDate))
                {
                    queueProcessed = true;
                    // Check message lifetime
                    if (DateTime.UtcNow - tupleRowDate.Item2 > _messageLifetime)
                    {
                        continue; // Discard if older than 1 minute
                    }
                    // Check rate limits


                    if (tupleRowDate.Item1 != null && tupleRowDate.Item1.RowState != DataRowState.Deleted && tupleRowDate.Item1.RowState != DataRowState.Detached)
                    {
                        if(tupleRowDate.Item1["Blob"] == DBNull.Value)
                            continue;
                        var d = (DataList)tupleRowDate.Item1["Blob"];
                        var pictureUrls = GetPictureUrls(d);
                        var actualPictureCount = 1 + Math.Min(pictureUrls.Count, PicturesCountToAttach);
                        if (CanSendMessage(actualPictureCount))
                        {
                            try
                            {
                                await SendMessageAsync(tupleRowDate.Item1);
                            }
                            catch (Exception ex)
                            {
                                HandleTelegramException(ex, "");
                            }
                        }
                        else
                        {
                            Debug.WriteLine("Rate limit hit. Queueing message.");
                            // If rate limit hit, re-enqueue the message for later processing
                            _messageQueue.Enqueue(tupleRowDate);
                            break; // Stop processing to avoid exceeding rate limits
                        }
                    }
                }

                if (!queueProcessed)
                {
                    // If the queue was empty, re-enqueue the task to be processed later
                    _queueProcessingTimer.Change(TimeSpan.FromSeconds(QueueProcessingIntervalSeconds), TimeSpan.FromSeconds(QueueProcessingIntervalSeconds));
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }

        private static List<string> GetPictureUrls(DataList d)
        {
            var pictureUrls = new List<string>();
            if (!string.IsNullOrWhiteSpace(d.GalleryUrl))
                pictureUrls.Add(d.GalleryUrl);
            pictureUrls.AddRange(d.Pictures);
            pictureUrls = pictureUrls.Distinct().ToList();
            return pictureUrls;
        }

        private async Task SendMessageAsync(DataRow row)
        {
            try
            {
                var d = (DataList)row["Blob"];
                var body = _messageTemplateManager.GetFormattedMessage(row, d);

                if (string.IsNullOrWhiteSpace(body))
                {
                    return;
                }


                var siteID = "";
                if (d.EBaySite.SiteID != "0")
                    siteID = $"/{d.EBaySite.SiteID}";

                var itemIDLicenseKey = d.ItemID + "" + ProgramState.SerialNumberShort.Replace("-", "");
                var shortItemID = $"/{ItemIdShortener.ShortenItemID(itemIDLicenseKey)}";

                var viewUrl = $"https://ubuyfirst.com/l{siteID}{shortItemID}";
                var checkoutUrl = $"https://ubuyfirst.com/c{siteID}{shortItemID}";
                var contactUrl = $"https://ubuyfirst.com/s{siteID}{shortItemID}";
                body = body.Replace("{ViewUrl}", viewUrl);
                body = body.Replace("{CheckoutUrl}", checkoutUrl);
                body = body.Replace("{ContactUrl}", contactUrl);

                var pushMessage = $"{body}";
                try
                {
                    var viewButton = InlineKeyboardButton.WithUrl(char.ConvertFromUtf32(0x1F310) + " View", viewUrl);
                    var contactButton = InlineKeyboardButton.WithUrl("Contact", contactUrl);
                    var checkoutButton = InlineKeyboardButton.WithUrl(char.ConvertFromUtf32(0x1F6D2) + " Checkout", checkoutUrl);

                    var buttonList = new InlineKeyboardMarkup(new[] { viewButton, contactButton, checkoutButton });
                    var mediaList = new List<InputMediaPhoto>();
                    var pictureUrls = GetPictureUrls(d);
                    var actualPictureCount = Math.Min(pictureUrls.Count, PicturesCountToAttach);
                    if (actualPictureCount > 0)
                        mediaList.AddRange(pictureUrls.Take(actualPictureCount).Select(p => new InputMediaPhoto(new InputMedia(p))).ToList());

                    var disableWebPagePreview = true;
                    if (mediaList.Count == 1)
                    {
                        var mediaUrl = mediaList.FirstOrDefault()?.Media.Url;
                        if (!string.IsNullOrWhiteSpace(mediaUrl))
                        {
                            disableWebPagePreview = false;
                            pushMessage = $"<a href=\"{mediaUrl}\">&#8205;</a> {pushMessage}";
                        }
                    }

                   

                    if (mediaList.Count > 1)
                    {
                        try
                        {
                            Stat.TelegramCounter += mediaList.Count;

                            await TGBot.SendMediaGroupAsync(TelegramChatID, mediaList, true);
                        }
                        catch (Exception ex)
                        {

                        }
                    }

                    await TGBot.SendTextMessageAsync(chatId: TelegramChatID, text: pushMessage,
                        parseMode: ParseMode.Html, entities: null, disableWebPagePreview: disableWebPagePreview,
                        disableNotification: false, replyToMessageId: 0, allowSendingWithoutReply: false, replyMarkup: buttonList);

                    Stat.TelegramCounter++;
                }
                catch (Exception ex)
                {
                    HandleTelegramException(ex, ""); // Handle the exception
                }
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("Telegram: ", ex);
            }
        }
        private bool CanSendMessage(int tokens)
        {
            var allowedPer1Sec = _tokenBucket1Sec.TryConsumeTokens(tokens);
            var allowedPer1Min = _tokenBucket1Min.TryConsumeTokens(tokens);

            return allowedPer1Sec && allowedPer1Min;
        }

        public static bool HandleTelegramException(Exception ex, string botName)
        {
            if (ex.Message.Contains("Forbidden: bot was blocked by the user"))
            {
                var msgText = "Telegram bot " + botName + " is blocked. To unblock - send any message to bot " + botName + " from your telegram account.";

                XtraMessageBox.Show(new XtraMessageBoxArgs(Form1.Instance.LookAndFeel, Form1.Instance, msgText));

                return true;
            }

            if (ex.Message.Contains("chat not found"))
            {
                var msgText = "To activate your bot: Send any message to bot " + botName + " from your telegram account.";
                XtraMessageBox.Show(new XtraMessageBoxArgs(Form1.Instance.LookAndFeel, Form1.Instance, msgText));

                return true;
            }

            if (ex.Message.Contains("Unauthorized"))
            {
                var msgText = "Telegram HTTP API token is invalid. Provide a valid token.";
                XtraMessageBox.Show(new XtraMessageBoxArgs(Form1.Instance.LookAndFeel, Form1.Instance, msgText));

                return true;
            }

            if (ex.Message.Contains("Bad Request: group send failed"))
            {
                return true;
            }

            if (ex.Message.Contains("Not a valid InputFile"))
            {
                return true;
            }

            if (ex.Message.Contains("Bad Request: failed to get HTTP URL content"))
            {
                return true;
            }

            return false;
        }

        public void StartListening()
        {
            if (TGBot == null)
                return;
            _cts = new CancellationTokenSource();
            try
            {
                TGBot.StartReceiving(HandleUpdateAsync, HandleErrorAsync, new ReceiverOptions(), _cts.Token);
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
            }
        }

        public void StopListening()
        {
            //await TGBot.CloseAsync();
            if (_cts?.IsCancellationRequested == true)
                return;
            try
            {
                _cts?.Cancel();
            }
            catch (WebException)
            {
            }
            catch (TaskCanceledException)
            {
            }
            finally
            {
                _cts?.Dispose();
            }
        }

        public bool IsListening()
        {
            return _cts?.IsCancellationRequested == false;
        }

        public async Task HandleUpdateAsync(ITelegramBotClient botClient, global::Telegram.Bot.Types.Update update, CancellationToken cancellationToken)
        {
            switch (update.Type)
            {
                case UpdateType.Message:
                    await _commandHandler.HandleMessageReceivedAsync(botClient, update.Message!);
                    break;
                case UpdateType.EditedMessage:
                    await _commandHandler.HandleMessageReceivedAsync(botClient, update.EditedMessage!);
                    break;
                case UpdateType.CallbackQuery:
                    await _commandHandler.HandleCallbackQueryReceivedAsync(botClient, update.CallbackQuery!);
                    break;
                case UpdateType.InlineQuery:
                    //handler = BotOnInlineQueryReceived(botClient, update.InlineQuery!);
                    break;
                case UpdateType.ChosenInlineResult:
                    //handler = BotOnChosenInlineResultReceived(botClient, update.ChosenInlineResult!);
                    break;
                default:
                    await UnknownUpdateHandlerAsync(botClient, update);
                    break;
            }

            try
            {
                //await handler;
            }
            catch (Exception exception)
            {
                await HandleErrorAsync(botClient, exception, cancellationToken);
            }
        }

        private static Task UnknownUpdateHandlerAsync(ITelegramBotClient botClient, global::Telegram.Bot.Types.Update update)
        {
            Debug.WriteLine($"Unknown update type: {update.Type}");
            return Task.CompletedTask;
        }

        public static Task HandleErrorAsync(ITelegramBotClient botClient, Exception exception, CancellationToken cancellationToken)
        {
            var ErrorMessage = exception switch
            {
                ApiRequestException apiRequestException => $"Telegram API Error:\n[{apiRequestException.ErrorCode}]\n{apiRequestException.Message}",
                _ => exception.ToString()
            };

            return Task.CompletedTask;
        }

        public async Task<Message> SendControlButtons()
        {
            var startSearch = InlineKeyboardButton.WithCallbackData("Start Search", "Start Search");
            var stopSearch = InlineKeyboardButton.WithCallbackData("Stop Search", "Stop Search");
            var enableNotifications = InlineKeyboardButton.WithCallbackData("Enable Notifications", "Enable Notifications");
            var disableNotifications = InlineKeyboardButton.WithCallbackData("Disable Notifications", "Disable Notifications");

            var inlineKeyboard = new InlineKeyboardMarkup(new[] { Form1.Instance._searchService?.Running == true ? stopSearch : startSearch, Enabled ? disableNotifications : enableNotifications });
            var searchStatus = Form1.Instance._searchService?.Running == true ? "Started" : "Stopped";
            var telegramNotifications = Enabled ? "Enabled" : "Disabled";

            if (!string.IsNullOrEmpty(TelegramChatID))
            {
                try
                {
                    var result = await TGBot.SendTextMessageAsync(TelegramChatID, "Search: " + searchStatus + "\r\n" + "Notifications: " + telegramNotifications, replyMarkup: inlineKeyboard);

                    return result;
                }
                catch (Exception ex)
                {
                    if (HandleTelegramException(ex, GetBotName()))
                        return new Message();

                    if (ex.Message.Contains("Too Many Requests"))
                    {
                        XtraMessageBox.Show("Telegram message limit reached. Please, try again later.");

                        return new Message();
                    }

                    throw;
                }
            }
            else
            {
                return new Message();
            }
        }

        public void SetBotName(string botUsername)
        {
            _botUsername = botUsername;
        }

        public string GetBotName()
        {
            return _botUsername;
        }

        public async Task<bool> BotAndAccountCheckAsync()
        {
            var result = await SendControlButtons();

            if (result.Chat == null)
                return false;

            TelegramChatID = result.Chat.Id.ToString();
            AccountConnectionState = "Connected.";

            return true;
        }

        public async Task<bool> ApplyBotToken(string token)
        {
            if (IsListening())
                StopListening();

            TGBot = new TelegramBotClient(token);

            TelegramBotID = token;
            var bot = await TGBot.GetMeAsync();

            if (string.IsNullOrEmpty(bot.Username))
                return false;

            SetBotName(bot.Username);
            StartListening();

            return true;
        }

        public async Task ConnectTelegramAccount(string userName)
        {
            if (userName.Length < 6)
                return;

            TelegramAccount = userName;
            if (string.IsNullOrEmpty(TelegramChatID))
            {
                AccountConnectionState = "Disconnected. To connect your account: Send any message to bot " + GetBotName() + " from " + TelegramAccount;
            }
            else
            {
                var result = await BotAndAccountCheckAsync();
            }
        }

        public void SetMaxMessagesPerMinute(int telegramSenderMaxMessagesPerMinute)
        {
            MaxMessagesPerMinute = telegramSenderMaxMessagesPerMinute;
            _tokenBucket1Sec = new TokenBucket(25, 25, TimeSpan.FromSeconds(1));
            _tokenBucket1Min = new TokenBucket(telegramSenderMaxMessagesPerMinute, telegramSenderMaxMessagesPerMinute / 60, TimeSpan.FromSeconds(1));
        }

        public void ClearQueue()
        {
            while (_messageQueue.Count>0)
            {
                _messageQueue.TryDequeue(out _);
            }
        }
    }
}
