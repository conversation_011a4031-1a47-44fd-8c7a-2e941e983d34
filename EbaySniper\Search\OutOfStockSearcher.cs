﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using uBuyFirst.CustomClasses;
using uBuyFirst.Search.Status;
using uBuyFirst.Tools;

namespace uBuyFirst.Search
{
    internal static class OutOfStockSearcher
    {
        public static async Task SearchWithShoppingAPI(IEnumerable<string> batch, Keyword2Find kw, ViewReporter viewReporter, CancellationToken ct)
        {
            //List<KeyValuePair<FoundItem, int>> items = FetchItems(batch);
            //await ProcessItems(kw, viewReporter, ct, items);
        }

        public static async Task SearchWithBrowseAPI(IEnumerable<string> batch, Keyword2Find kw, ViewReporter viewReporter, CancellationToken ct)
        {
            var items = await GetItemsStatus.FetchBatchStatusBrowseAPI(batch.ToList());
            if (items == null)
            {
                return;
            }

            var foundItems = new List<KeyValuePair<FoundItem, int>>();

            foreach (var coreItem in items)
            {
                DateTime startTime = DateTime.SpecifyKind(DateTime.MinValue, DateTimeKind.Utc);
                if (DateTime.TryParse(coreItem.ItemCreationDate, CultureInfo.InvariantCulture, DateTimeStyles.AdjustToUniversal, out var itemCreationDate))
                {
                    startTime = itemCreationDate;
                }

                var isMultiVariation = false;
                var isInitialSearch = false;
                var sellerUsername = coreItem.Seller?.Username ?? null;
                var itemID = coreItem.ItemId.Replace("v1|", "").Replace("|0", "");
                var categoryPath = coreItem.CategoryPath?.Split(':').ToArray();
                var foundItem = new FoundItem(null, kw, itemID, startTime, SearchSource.API2, isMultiVariation, isInitialSearch, sellerUsername, null, null, categoryPath);
                var estimatedAvailableQuantity = 0;
                if (coreItem.EstimatedAvailabilities?.FirstOrDefault() is { } firstOrDefault)
                    estimatedAvailableQuantity = firstOrDefault.EstimatedAvailableQuantity ?? 0;
                foundItems.Add(new KeyValuePair<FoundItem, int>(foundItem, estimatedAvailableQuantity));

            }

            await ProcessItems(kw, viewReporter, ct, foundItems);
        }

        private static async Task ProcessItems(Keyword2Find kw2Find, ViewReporter viewReporter, CancellationToken ct, List<KeyValuePair<FoundItem, int>> itemIDQuantity)
        {
            var itemsInStock = FilterInStockItems(itemIDQuantity);
            var itemIDs = itemsInStock.Select(item => item.Key.ItemID.Replace("v1|", "").Replace("|0", "")).ToList();

            List<string> addedItems;
            if (kw2Find.ChildrenCore.Count == 0)
            {
                addedItems = SearchService.AddItemsToStorage(itemIDs);
            }
            else
            {
                addedItems = SearchService.AddItemsToStorage(kw2Find, itemIDs);
            }

            var newItems = itemsInStock.Where(item => addedItems.Contains(item.Key.ItemID.Replace("v1|", "").Replace("|0", "")));
            var foundItems = new List<FoundItem>();
            foreach (var newItem in newItems)
            {
                foundItems.Add(newItem.Key);
            }

            if (ct.IsCancellationRequested)
                return;

            SearchService.SendItemsForProcessing(foundItems, viewReporter);
            await Task.Delay(1000);
        }

        private static List<KeyValuePair<FoundItem, int>> FilterInStockItems(List<KeyValuePair<FoundItem, int>> items)
        {
            var itemsInStock = items.Where(item => item.Value > 0).ToList();

            return itemsInStock;
        }

        private static GetMultipleItemsResponseItem[] FetchItems(IEnumerable<string> itemIDs)
        {
            Stat.OutOfStockCounter++;
            var ids = string.Join(",", itemIDs);
            var result = Network.NetTools.ShoppingApiCall(ids, "GetMultipleItems", "IncludeSelector=Details,Variations&");

            if (string.IsNullOrEmpty(result))
                return new GetMultipleItemsResponseItem[] { };

            try
            {
                var getItemStatusResponse = result.ParseXml<GetMultipleItemsResponse>();
                var items = getItemStatusResponse.MultipleItem;

                return items;
            }
            catch (Exception e)
            {
            }

            return new GetMultipleItemsResponseItem[] { };
        }

        private static GetMultipleItemsResponseItem[] FetchItemsWithBrowseAPI(IEnumerable<string> itemIDs)
        {
            var ids = string.Join(",", itemIDs);
            var result = Network.NetTools.ShoppingApiCall(ids, "GetMultipleItems", "IncludeSelector=Details&");

            if (string.IsNullOrEmpty(result))
                return new GetMultipleItemsResponseItem[] { };

            try
            {
                var getItemStatusResponse = result.ParseXml<GetMultipleItemsResponse>();
                var items = getItemStatusResponse.MultipleItem;

                return items;
            }
            catch (Exception e)
            {
            }

            return new GetMultipleItemsResponseItem[] { };
        }
    }
}
