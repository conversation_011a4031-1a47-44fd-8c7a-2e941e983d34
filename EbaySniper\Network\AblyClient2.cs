﻿using System;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Text.RegularExpressions;
using IO.Ably;
using IO.Ably.Encryption;
using IO.Ably.Realtime;
using uBuyFirst.Other;
using uBuyFirst.Prefs;
using LogLevel = IO.Ably.LogLevel;
using Message = IO.Ably.Message;

namespace uBuyFirst.Network
{
    public class AblyClient
    {
        private readonly Action<Message> _postAblyItemToResultsGrid;
        private SeerUser _seerUser;
        private AblyRealtime _realtime;
        private readonly string _channelPresenceName = "ubfp:presence3";
        private IRealtimeChannel _channelPresence;
        private IRealtimeChannel _channelItem;
        private bool _mustBeConnected;
        private static int _tokenLifetimeMinutes = 180 * 1;
        public int RetryCount;
        private int _serverTimeoutMin = 2;

        public AblyClient(Action<Message> postAblyItemToResultsGrid)
        {
            _postAblyItemToResultsGrid = postAblyItemToResultsGrid;
            //var channels = new[] { "8789ABC", "DEFHIJ", "KLMNOP", "QRSTUV", "WXYZab", "cdefgh", "ijklmn", "opqrst", "Guvwxyz", };
            var channels = new[] { "DEFHIJ", "KLMNOP", "QRSTUV", "WXYZab", "cdefgh", "ijklm78n", "op89ABqrst", "GuvwxyzC", };
            for (int i = 0; i < channels.Length; i++)
            {
                if (Regex.IsMatch(ProgramState.HWID, @"^[" + channels[i] + "]"))
                {
                    _channelPresenceName = "ubfp:presence" + (3 + i);
                    break;
                }
            }

            DefaultLogger.LoggerSink = new AblyLogger();
        }

        public void BeConnected(SeerUser user)
        {
            try
            {
                SetLoggerDebugLevel();
                _seerUser = user;
                _mustBeConnected = true;

                if (_realtime == null)
                {
                    _realtime = new AblyRealtime(GetClientOptions());
                    _realtime.Connection.ConnectionStateChanged += Realtime_ConnectionStateChanged;
                }

                _realtime.Connect();
            }
            catch (Exception ex)
            {
                Loggers.LogError(ex);
            }
        }

        public void BeDisconnected()
        {
            _mustBeConnected = false;
            _realtime?.Close();
        }

        private void Realtime_ConnectionStateChanged(object sender, ConnectionStateChange e)
        {
            Debug.WriteLine("Conn: " + e.Current);
            switch (e.Current)
            {
                case ConnectionState.Failed:
                    Loggers.LogError($"Keywords:[{_seerUser?.EbaySearches.Count}] {e.Reason}");

                    break;
                case ConnectionState.Connected:
                    if (_channelPresence == null)
                    {
                        _channelPresence = _realtime.Channels.Get(_channelPresenceName, new ChannelOptions(true, Crypto.GetDefaultParams("bXlrZXlkc2RkZGRkZGRkZA==")));
                        _channelPresence.StateChanged += ChannelPresence_StateChanged;
                        if (Debugger.IsAttached)
                        {
                            //_channelPresence.Presence.Subscribe(message => File.AppendAllText("_Presence.log", $"{DateTime.UtcNow.ToString("s")} {message.Action} {message.Timestamp.ToString()}\r\n"));
                        }
                    }

                    if (_channelItem == null)
                    {
                        var channelItemName = "ubfi:" + _seerUser.HWID;
                        _channelItem = _realtime.Channels.Get($"{channelItemName}", new ChannelOptions(false, Crypto.GetDefaultParams("bXlrZXlkc2RkZGRkZGRkZA==")));
                        _channelItem.Subscribe(_postAblyItemToResultsGrid);
                        //_channelItem.StateChanged += ChannelStateChanged;
                    }

                    Debug.WriteLine("Ably: State changed Attach Presence, Channel");
                    if (_realtime?.Connection.State == ConnectionState.Connected)
                    {
                        try
                        {
                            _channelPresence.Attach();
                            _channelItem.Attach();
                        }
                        catch (Exception exception)
                        {
                            Debug.WriteLine("Ably ex: " + exception);
                        }
                    }
                    else
                    {
                        Debug.WriteLine("Ably: Error attaching State: " + _realtime?.Connection.State);
                    }

                    break;
            }
        }

        private void ChannelPresence_StateChanged(object sender, ChannelStateChange args)
        {
            Debug.WriteLine($"Ably: Presence Channel state -- {args.Current}");
            switch (args.Current)
            {
                case ChannelState.Attached:
                    try
                    {
                        EnterToPresenceChannel();
                    }
                    catch (Exception ex)
                    {
                        var state = _channelPresence?.State.ToString();
                        if (state != null)
                            ex.Source = ex.Source.Insert(0, $"-------------- Channel State: {state} --------------");

                        Loggers.LogError(ex);
                        _realtime.Close();
                    }

                    break;
                case ChannelState.Failed:
                    Loggers.LogError($"Ably Presence channel failed: {args.Error}");

                    break;
            }
        }

        private async void EnterToPresenceChannel()
        {
            Debug.WriteLine("Ably: Entering presence channel");
            try
            {
                _seerUser.StartTime = DateTimeOffset.UtcNow.DateTime.AddHours(-7).ToString("s");
                var uncompressedString = Newtonsoft.Json.JsonConvert.SerializeObject(_seerUser);
                var compressedString = "packed:" + Serializator.CompressString(uncompressedString);

                if (_realtime?.Connection.State != ConnectionState.Connected)
                {
                    _realtime?.Close();

                    return;
                }

                if (compressedString.Length < 40000)
                {
                    await _channelPresence.Presence.EnterAsync(compressedString);
                }
                else
                {
                    _seerUser.HWID = $"[{_seerUser.EbaySearches.Count}]" + _seerUser.HWID;
                    _seerUser.EbaySearches = _seerUser.EbaySearches.GetRange(0, 20);
                    await _channelPresence.Presence.EnterAsync(_seerUser);
                }
            }
            catch (Exception ex)
            {
                var state = _channelPresence?.State.ToString();
                if (state != null)
                    ex.Source = ex.Source.Insert(0, $"-------------- Channel State: {state} --------------");

                Loggers.LogError(ex);
                _realtime.Close();
            }
        }

        private ClientOptions GetClientOptions()
        {
            if (ProgramState.SerialNumber.StartsWith("ROMA"))
            {
                _tokenLifetimeMinutes = 3;
            }

            var clientID = ProgramState.HWID;
            if (string.IsNullOrEmpty(clientID))
            {
                clientID = "NOHWID" + new Random().Next(100, 1000);
            }

            var authUrl = new Uri($"https://ubuyfirst.com/uBuyFirst/try/tokens/ubft.php?hwid={WebUtility.UrlEncode(clientID)}&ttl={_tokenLifetimeMinutes * 60}&cb={new Random().Next(100000)}");
            var options = new ClientOptions();
            options.UseBinaryProtocol = true;
            options.UseTokenAuth = true;
            options.Tls = true;
            options.AuthUrl = authUrl;
            options.AuthMethod = HttpMethod.Post;
            options.AutoConnect = false;
            options.HttpMaxRetryCount = 10;
            options.HttpMaxRetryDuration = TimeSpan.FromSeconds(60);
            options.FallbackHosts = null;
            var isWindows7 = Environment.OSVersion.Version.Major == 6 && Environment.OSVersion.Version.Minor < 2 || Environment.OSVersion.Version.Major < 6;
            if (isWindows7)
                options.TransportFactory = new WebSocket4NetTransport.WebSocketTransportFactory();

            return options;
        }

        public string GetAblyClientState()
        {
            if (Program.Sandbox)
                return "";

            var channelStates = _realtime?.Channels?.Select(c => c.State);
            string states = "Null";
            if (channelStates != null)
                states = string.Join(",", channelStates);

            return $"{_realtime?.Connection?.State},{states};";
        }

        public ConnectionState GetConnectionState()
        {
            if (_realtime?.Connection == null)
                return ConnectionState.Disconnected;

            return _realtime.Connection.State;
        }

        public bool NeedRestart()
        {
            if (!_mustBeConnected)
                return false;

            switch (_realtime?.Connection?.State)
            {
                case ConnectionState.Closed:
                case ConnectionState.Disconnected:
                case ConnectionState.Suspended:
                    return true;

                case ConnectionState.Failed:
                    IncreaseErrorCount();

                    return true;

                case ConnectionState.Connected:
                    var ch1Attached = _channelItem?.State == ChannelState.Attaching || _channelItem?.State == ChannelState.Attached;
                    var ch2Attached = _channelPresence?.State == ChannelState.Attaching || _channelPresence?.State == ChannelState.Attached;
                    var channelsAttached = ch1Attached && ch2Attached;

                    var realtimeOk = _realtime != null;
                    var connOk = _realtime.Connection != null;
                    var errorNone = _realtime.Connection.ErrorReason == null;
                    var connectionOk = realtimeOk && connOk && errorNone;
                    if (channelsAttached && connectionOk)
                    {
                        if (IsServerFail())
                        {
                            return true;
                        }

                        RetryCount = 0;

                        return false;
                    }
                    else
                    {
                        Debug.WriteLine("Ably: Conn&Ch Fail");

                        IncreaseErrorCount();

                        return true;
                    }
            }

            return false;
        }

        private bool IsServerFail()
        {
            if (_realtime.Connection.ConfirmedAliveAt.HasValue)
                if ((DateTimeOffset.UtcNow - _realtime.Connection.ConfirmedAliveAt.Value).TotalMinutes > _serverTimeoutMin)
                {
                    Debug.WriteLine("Ably: Server fail");

                    return true;
                }

            return false;
        }

        private void IncreaseErrorCount()
        {
            RetryCount++;
            SetLoggerDebugLevel();
        }

        public void SetLoggerDebugLevel()
        {
            if (Debugger.IsAttached)
            {
                DefaultLogger.LogLevel = LogLevel.Warning;
                return;
            }

            if (RetryCount == 10)
            {
                DefaultLogger.LogLevel = LogLevel.Debug;
            }
            else
            {
                DefaultLogger.LogLevel = LogLevel.Error;
            }
        }

        public void Disconnect()
        {
            _realtime?.Close();
        }
    }
}
