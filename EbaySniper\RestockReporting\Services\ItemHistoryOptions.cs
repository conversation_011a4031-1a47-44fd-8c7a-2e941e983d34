using System;
using System.IO;
using System.Windows.Forms;
using uBuyFirst.Other;

namespace uBuyFirst.RestockReporting.Services
{
    /// <summary>
    /// Configuration options for item history logging
    /// </summary>
    public class ItemHistoryOptions
    {
        /// <summary>
        /// Base path for storing item history files
        /// Default: [SettingsFolder]/Reports/ItemHistory
        /// </summary>
        public string BasePath { get; set; }

        /// <summary>
        /// Path for storing error log files
        /// Default: [SettingsFolder]/Reports/ItemHistory/errors
        /// </summary>
        public string ErrorLogPath { get; set; }

        /// <summary>
        /// Whether item history logging is enabled
        /// Default: true
        /// </summary>
        public bool EnableLogging { get; set; } = true;

        /// <summary>
        /// Whether to create daily folders for organizing files
        /// Default: true
        /// </summary>
        public bool CreateDailyFolders { get; set; } = true;

        /// <summary>
        /// Maximum number of retry attempts for file operations
        /// Default: 0 (no retries)
        /// </summary>
        public int MaxRetries { get; set; } = 0;

        /// <summary>
        /// Creates default options with standard paths
        /// </summary>
        public static ItemHistoryOptions CreateDefault()
        {
            var settingsPath = Folders.Settings;
            var reportsPath = Path.Combine(settingsPath, "Reports");
            var itemHistoryPath = Path.Combine(reportsPath, "ItemHistory");
            var errorsPath = Path.Combine(itemHistoryPath, "errors");

            return new ItemHistoryOptions
            {
                BasePath = itemHistoryPath,
                ErrorLogPath = errorsPath,
                EnableLogging = true,
                CreateDailyFolders = true,
                MaxRetries = 0
            };
        }

        /// <summary>
        /// Validates the configuration and ensures directories exist
        /// </summary>
        /// <returns>True if configuration is valid and directories are accessible</returns>
        public bool ValidateAndEnsureDirectories()
        {
            try
            {
                if (string.IsNullOrEmpty(BasePath))
                    return false;

                if (string.IsNullOrEmpty(ErrorLogPath))
                    return false;

                // Ensure base directory exists
                if (!Directory.Exists(BasePath))
                {
                    Directory.CreateDirectory(BasePath);
                }

                // Ensure error directory exists
                if (!Directory.Exists(ErrorLogPath))
                {
                    Directory.CreateDirectory(ErrorLogPath);
                }

                // Test write access
                var testFile = Path.Combine(BasePath, $"test_{Guid.NewGuid()}.tmp");
                File.WriteAllText(testFile, "test");
                File.Delete(testFile);

                return true;
            }
            catch
            {
                return false;
            }
        }
    }
}
