﻿// ReSharper disable UnusedMember.Global

namespace uBuyFirst.Data
{
    public enum EmptyLineBehavior
    {
        /// <summary>
        ///     Empty lines are interpreted as a line with zero columns.
        /// </summary>
        NoColumns,

        /// <summary>
        ///     Empty lines are interpreted as a line with a single empty column.
        /// </summary>
        EmptyColumn,

        /// <summary>
        ///     Empty lines are skipped over as though they did not exist.
        /// </summary>
        Ignore,

        /// <summary>
        ///     An empty line is interpreted as the end of the input file.
        /// </summary>
        EndOfFile
    }
}