﻿using System.Numerics;

namespace uBuyFirst.Telegram
{
    public class ItemIdShortener
    {
        public class BaseConversion
        {
            private const string BaseChars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_~";

            public static string DecodeShortenedItemID(string shortenedID)
            {
                // Convert from custom base to BigInteger
                var number = ConvertCustomBaseToBigInteger(shortenedID);

                // Convert BigInteger back to base-36
                return ConvertBigIntegerToBase36(number);
            }

            private static BigInteger ConvertCustomBaseToBigInteger(string input)
            {
                BigInteger result = 0;
                foreach (var c in input)
                {
                    result = result * BaseChars.Length + BaseChars.IndexOf(c);
                }

                return result;
            }

            private static string ConvertBigIntegerToBase36(BigInteger number)
            {
                const string digits = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
                if (number == 0)
                    return "0";

                var result = string.Empty;
                while (number > 0)
                {
                    var index = (int)(number % 36);
                    result = digits[index] + result;
                    number /= 36;
                }

                return result;
            }
        }

        public static string ShortenItemID(string itemID)
        {
            // Assuming input is in base-36 (0-9, A-Z)
            var input = ConvertBase36ToBigInteger(itemID);

            const string baseChars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_~";
            var result = string.Empty;
            var targetBase = baseChars.Length;
            do
            {
                var index = (int)(input % targetBase);
                result = baseChars[index] + result;
                input /= targetBase;
            } while (input > 0);

            return result;
        }

        private static BigInteger ConvertBase36ToBigInteger(string input)
        {
            const string digits = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
            BigInteger result = 0;
            foreach (var c in input.ToUpper())
            {
                result = result * 36 + digits.IndexOf(c);
            }

            return result;
        }
    }
}
