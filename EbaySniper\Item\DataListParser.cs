﻿using System;
using System.Data;
using System.Diagnostics;
using System.Linq;
using DevExpress.XtraGrid;
using uBuyFirst.Data;
using uBuyFirst.Intl;
using uBuyFirst.Parsing;
using uBuyFirst.Pricing;
using uBuyFirst.Search;
using uBuyFirst.Time;

namespace uBuyFirst.Item
{
    internal static class DataListParser
    {
        public static DataList InitDataList(FoundItem foundItem, string termAlias, EBaySite eBaySite, string availableTo, string zip)
        {
            var datalist = new DataList();
            datalist.SourceB = foundItem.BrowseItem == null ? DataList.Source2.SS : DataList.Source2.SB;
            datalist.Zip = zip;
            datalist.AvailableTo = availableTo;
            datalist.EBaySite = eBaySite;
            datalist.Term = termAlias;
            var ebayAccount = Form1.EBayAccountsList.FirstOrDefault();
            if (ebayAccount != null)
                datalist.EbayAccount = ebayAccount;
            datalist.ConditionID = "Unspecified";
            datalist.Source = foundItem.ItemSource;
            datalist.SellerName = "";
            datalist.FeedbackRating = 0;
            datalist.FeedbackScore = 0;
            datalist.LastStatusCheck = new DateTimeWithDiff(DateTime.UtcNow, null);
            datalist.ItemPricing = new ItemPricing();
            datalist.ItemShipping = new ItemShipping();
            DateParser.SetPostedTime(datalist, foundItem.StartTime);

            if (foundItem.ItemID.Contains("|"))
            {
                datalist.ItemID = foundItem.ItemID.Split('|')[0];
                datalist.GroupItemID = foundItem.ItemID;
            }
            else if (foundItem.IsMultivariation)
            {
                datalist.ItemID = foundItem.ItemID.Split('|')[0];
                datalist.GroupItemID = foundItem.ItemID;
            }
            else
                datalist.ItemID = foundItem.ItemID;

            return datalist;
        }

        public static void CopyDataListToRow(DataRow row, DataList dataList,GridControl gridControl)
        {
            dataList.Row = row;
            dataList.GridControl = gridControl;
            row["Auction Price"] = dataList.AuctionPrice;
            row["AutoPay"] = dataList.AutoPay;
            row["Best Offer Count"] = dataList.BestOfferCount;
            row["Best Offer"] = dataList.BestOffer;
            row["Bids"] = dataList.Bids;
            row["Category ID"] = dataList.CategoryID;
            row["Category Name"] = dataList.CategoryName;
            row["Commit To Buy"] = dataList.CommitToBuy;
            row["Condition Description"] = dataList.ConditionDescription;
            row["Condition"] = dataList.Condition;
            row["Description"] = dataList.Description;
            row["Ebay Website"] = dataList.Site;
            row["Feedback Rating"] = dataList.FeedbackRating;
            row["Feedback Score"] = dataList.FeedbackScore;
            row["Found Time"] = dataList.FoundTime.LocalTime;
            row["From Country"] = dataList.FromCountry;
            row["Item Price"] = dataList.ItemPrice;
            row["ItemID"] = dataList.ItemID;
            row["Listing Type"] = dataList.ListingType;
            row["Location"] = dataList.Location;
            row["Page Views"] = dataList.PageViews;
            row["Payment"] = dataList.Payment;
            row["Quantity"] = dataList.QuantityAvailable;
            row["Relist Parent ID"] = dataList.RelistParentID ?? "";
            row["Returns"] = dataList.Returns;
            row["Seller Name"] = dataList.SellerName;
            row["Ship Additional Item"] = dataList.ShipAdditionalItem;
            row["Shipping Days"] = dataList.ShippingDays;
            row["Dispatch Days"] = dataList.DispatchDays;
            row["Authenticity"] = dataList.Authenticity;
            row["Seller Registration"] = dataList.SellerRegistration;
            row["Seller Country"] = dataList.SellerCountry;
            row["Seller Store"] = dataList.SellerStore;
            row["Seller Business"] = dataList.SellerIsBusiness;
            row["Shipping Type"] = dataList.ShippingType;
            row["Shipping Delivery"] = dataList.ShippingDelivery;
            row["Shipping"] = dataList.Shipping;
            row["Source"] = dataList.Source;

            row["Status"] = dataList.ItemStatus;
            row["Store Name"] = dataList.StoreName;
            row["Sub Search"] = dataList.SubSearch;
            row["Term"] = dataList.Term;
            row["Time Left"] = dataList.TimeLeft;
            row["Title"] = dataList.Title;
            row["To Country"] = dataList.ToCountry;
            row["Total Price"] = dataList.TotalPrice;

            row["Brand"] = dataList.Brand;
            row["Model"] = dataList.Model;
            row["MPN"] = dataList.MPN;
            row["Product Reference ID"] = dataList.ProductReferenceID;
            row["UPC"] = dataList.UPC;
            row["Variation"] = dataList.Variation;
            row["VAT Number"] = dataList.VATNumber;

            dataList.SetStatus(dataList.ItemStatus);
            if (Debugger.IsAttached)
                row["Status Time"] = new DateTimeWithDiff(DateTime.UtcNow, null).LocalTime;
            if (dataList.SoldTime != null)
                row["Sold Time"] = dataList.SoldTime.LocalTime;

            if (dataList.StartTimeLocal != null)
                row["Posted Time"] = dataList.StartTimeLocal.Value.LocalDateTime;

            ItemSpecifics.AssignItemSpecificsToRow(row, dataList.ItemSpecifics);
        }
    }
}
