﻿using System;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using eBay.Service.Call;
using eBay.Service.Core.Sdk;
using eBay.Service.Core.Soap;
using uBuyFirst.API.TradingAPI;
using uBuyFirst.Data;
using uBuyFirst.Prefs;
using uBuyFirst.Pricing;
using uBuyFirst.Stats;
using uBuyFirst.Tools;

namespace uBuyFirst.Purchasing
{
    public static class QuickPurchaseManager
    {
        internal static bool QuickPurchase(GridView grView, DataList d)
        {
            if (string.IsNullOrEmpty(d.ItemID))
                return false;

            if (!ConnectionConfig.TradingAPIEnabled)
                return false;

            if (!d.CommitToBuy)
                return false;
            if (d.Variation)
                return false;
            if (!Form1.LicenseUtility.CurrentLimits.PlaceOfferEnabled)
                return false;

            if (!UserSettings.SkipBuyConfirmation)
            {
                var messageBoxFormConfirmation = BuildXtraMessageBoxForm(grView, d);

                if (messageBoxFormConfirmation.DialogResult != DialogResult.Yes)
                    return true;
            }

            PlaceOfferCall placeOfferCall = null;
            try
            {
                placeOfferCall = SetUpPlaceOfferQuick(d, Placeoffer.PurchaseAllQuantity);

                if (placeOfferCall == null)
                    return true;
                if (!System.Diagnostics.Debugger.IsAttached)
                    placeOfferCall.Execute();
                //autopay
                var response = placeOfferCall.ApiResponse;
                //string message;
                var messageBoxForm = new XtraMessageBoxForm();
                if (!string.IsNullOrWhiteSpace(response?.TransactionID))
                {
                    var purchasedMessage = DateTime.Now.ToString("HH:mm:ss") + @"<b> Purchased</b>";
                    var xtraMessageBoxArgs = new XtraMessageBoxArgs(grView.GridControl.LookAndFeel, grView.GridControl, purchasedMessage, "Success", new[] { DialogResult.OK }, SystemIcons.Information,
                        0, DefaultBoolean.True);
                    messageBoxForm.ShowMessageBoxDialog(xtraMessageBoxArgs);
                    //AutoMeasurement.Client.TrackEvent("Bought_OK", "Purchasing", Analytics.GAid, 1);
                    Stat.PlaceOfferWonCounter++;
                    var placeOfferWonAmountUSD = CurrencyConverter.ConvertToUSD(placeOfferCall.Offer.MaxBid.Value * placeOfferCall.Offer.Quantity, placeOfferCall.Offer.MaxBid.currencyID.ToString());
                    Stat.PlaceOfferWonAmount += (int)placeOfferWonAmountUSD;
                    Pixel.Track(Pixel.EventType.PlaceOfferWin, placeOfferCall.ItemID, placeOfferWonAmountUSD);

                    return true;
                }

                var emptyResponseMessage = DateTime.Now.ToString("HH:mm:ss") + @" Error in PlaceOffer. Empty response";
                messageBoxForm.ShowMessageBoxDialog(new XtraMessageBoxArgs(grView.GridControl.LookAndFeel, grView.GridControl, emptyResponseMessage, "Purchase failed.", new[] { DialogResult.OK },
                    SystemIcons.Error));
                //AutoMeasurement.Client.TrackEvent("Purchase_Empty", "Purchasing", Analytics.GAid, 1);
                Stat.PlaceOfferEmptyCounter++;

                if (response != null)
                {
                    var errors = response.Errors;
                    if (placeOfferCall.HasError)
                    {
                        messageBoxForm = new XtraMessageBoxForm();
                        var apiErrorMessage = TradingAPIService.ShowApiErrors(errors);
                        var xtraMessageBoxArgs = new XtraMessageBoxArgs(grView.GridControl.LookAndFeel, grView.GridControl, "Error: " + apiErrorMessage, "Purchase failed.", new[] { DialogResult.OK },
                            SystemIcons.Error, 0, DefaultBoolean.True);
                        messageBoxForm.ShowMessageBoxDialog(xtraMessageBoxArgs);
                    }
                }
            }
            catch (ApiException ex)
            {
                var errors = ex.Errors;
                if (placeOfferCall != null && placeOfferCall.HasError)
                {
                    var messageBoxFormResult = new XtraMessageBoxForm();
                    var apiErrorMessage = TradingAPIService.ShowApiErrors(errors);
                    messageBoxFormResult.ShowMessageBoxDialog(new XtraMessageBoxArgs(grView.GridControl.LookAndFeel, grView.GridControl, apiErrorMessage, "Purchase failed.", new[] { DialogResult.OK },
                        SystemIcons.Error, 0, DefaultBoolean.True));
                }
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("MakePlaceOfferCall: ", ex);
            }

            Pixel.Track(Pixel.EventType.PlaceOfferLost, d.ItemID);
            //   MakePlaceOfferCall();
            return true;
        }

        private static XtraMessageBoxForm BuildXtraMessageBoxForm(GridView grView, DataList d)
        {

            var messageBoxForm = new XtraMessageBoxForm();
            var message = $"<b>{d.ItemPricing.ItemPrice.FormatPrice()} - {d.Title}\n\nBuy it now?</b><br><br> ";

            var qtyCheckBox = new CheckBox { Name = "c1", Text = d.QuantityAvailable + " items", Location = new Point(85, 62), Checked = Placeoffer.PurchaseAllQuantity };
            if (d.QuantityAvailable > 1)
                messageBoxForm.Controls.Add(qtyCheckBox);

            var messageBoxArgs = new XtraMessageBoxArgs(grView.GridControl.LookAndFeel, grView.GridControl, message, "Purchase. Final confirmation.", new[] { DialogResult.Yes, DialogResult.No },
                null, 0, DefaultBoolean.True);
            messageBoxForm.ShowMessageBoxDialog(messageBoxArgs);

            Placeoffer.PurchaseAllQuantity = qtyCheckBox.Checked;
            return messageBoxForm;
        }

        private static PlaceOfferCall SetUpPlaceOfferQuick(DataList d, bool purchaseAllQuantity = false)
        {
            try
            {
                var placeOfferCall = new PlaceOfferCall();
                var eBayUser = Form1.EBayAccountsList.FirstOrDefault(a => a.UserName == d.EbayAccount?.UserName);
                if (eBayUser == null)
                {
                    XtraMessageBox.Show($"User '{d.EbayAccount?.UserName}' not found");

                    return null;
                }

                placeOfferCall.ApiContext = ConnectionConfig.GetApiContextPlaceOffer(d.EBaySite.SiteCode, d.EbayAccount?.TokenPo);
                placeOfferCall.ApiRequest.InvocationID = Guid.NewGuid().ToString().Replace("-", "");
                placeOfferCall.AbstractRequest.EndUserIP = ProgramState.PublicIp;
                placeOfferCall.ItemID = d.ItemID;
                placeOfferCall.Offer = new OfferType();
                placeOfferCall.Offer.Action = BidActionCodeType.Purchase;

                var effectivePurchasePrice = d.ItemPricing.GetEffectivePurchasePrice();
                placeOfferCall.Offer.MaxBid = new AmountType()
                {
                    Value = effectivePurchasePrice.Value, currencyID = (CurrencyCodeType)Enum.Parse(typeof(CurrencyCodeType), effectivePurchasePrice.Currency)
                };
                var affiliateAction = d.Term == "Watchlist" ? "Watch" : "QuickPlaceOffer";
                if (!Program.AffiliateOff && !Program.Sandbox)
                    placeOfferCall.AffiliateTrackingDetails = new AffiliateTrackingDetailsType
                    {
                        AffiliateUserID = AffiliateTool.GetCustomIDLicenseLong(d.ItemID, affiliateAction),
                        ApplicationDeviceType = ApplicationDeviceTypeCodeType.Desktop,
                        TrackingID = Config.CampaignID,
                        TrackingPartnerCode = "9"
                    };

                if (purchaseAllQuantity)
                    placeOfferCall.Offer.Quantity = d.QuantityAvailable;
                else
                    placeOfferCall.Offer.Quantity = 1;

                return placeOfferCall;
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("SetUpPlaceOffer: ", ex);
            }

            return null;
        }
    }
}
