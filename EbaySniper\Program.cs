﻿using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Runtime;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;
using DevExpress.LookAndFeel;
using DevExpress.Skins;
using DevExpress.UserSkins;
using DevExpress.XtraEditors;
using Microsoft.Extensions.DependencyInjection;
using uBuyFirst.Other;
using uBuyFirst.Services;
using uBuyFirst.Services.Caching;
using uBuyFirst.Time;
using uBuyFirst.Update;

namespace uBuyFirst
{
    internal static class Program
    {
        public static bool AffiliateOff;
        public static bool Sandbox = false; //Debugger.IsAttached;
        public static bool LogFilters;
        public static bool FirstVisit { get; set; }

        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        ///
        [DllImport("User32")]
        public static extern IntPtr FindWindow(string p0, string p1);

        [DllImport("User32")]
        public static extern bool IsIconic(IntPtr p0);

        [DllImport("User32")]
        public static extern bool SetForegroundWindow(IntPtr p0);

        [DllImport("User32")]
        public static extern bool ShowWindow(IntPtr p0, int p1);

        [STAThread]
        private static void Main(string[] args)
        {
            Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
            Application.ThreadException += new ThreadExceptionEventHandler(Application_ThreadException);
            AppDomain.CurrentDomain.UnhandledException += new UnhandledExceptionEventHandler(CurrentDomain_UnhandledException);

            ProfileOptimization.SetProfileRoot(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location));
            ProfileOptimization.StartProfile("Optimization.dll");
            Debug.WriteLine(DateTime.Now.ToString("T") + "**Main**");
            foreach (string arg in args)
            {
                switch (arg.ToLower())
                {
                    case "timesync":
                        TimeSync.SetEbayTime();

                        return;
                    case "affiliateoff":
                        AffiliateOff = true;

                        break;
                    case "sandbox":
                        AffiliateOff = true;
                        Sandbox = true;

                        break;
                    case "portable":
                        Folders.PortableMode = true;
                        break;
                    case "logfilters":
                        LogFilters = true;
                        break;

                    case "debug":
                        Loggers.EnableDebugLogger();

                        break;
                    case "optimizehidden":
                        Debug.WriteLine("Optimization started");
                        var proc = new ProcessStartInfo();

                        proc.UseShellExecute = true;
                        proc.WorkingDirectory = Environment.CurrentDirectory;
                        proc.FileName = @"C:\Windows\Microsoft.NET\Framework64\v4.0.30319\ngen.exe";
                        proc.Arguments = $"install {Assembly.GetExecutingAssembly().Location}";
                        proc.Verb = "runas";
                        var p = new Process { StartInfo = proc };
                        try
                        {
                            p.StartInfo.WindowStyle = ProcessWindowStyle.Hidden;
                            p.Start();
                            p.WaitForExit();
                        }
                        catch
                        {
                            // The user refused to allow privileges elevation.
                            // Do nothing and return directly ...
                        }

                        Environment.Exit(0);

                        break;
                    case "optimize":
                        var proc2 = new ProcessStartInfo();
                        proc2.UseShellExecute = true;
                        proc2.WorkingDirectory = Environment.CurrentDirectory;
                        proc2.FileName = $"{Assembly.GetExecutingAssembly().Location}";
                        proc2.Arguments = "optimizehidden";
                        proc2.Verb = "runas";
                        var p2 = new Process { StartInfo = proc2 };
                        try
                        {
                            p2.Start();
                            p2.WaitForExit();
                        }
                        catch
                        {
                            // The user refused to allow privileges elevation.
                            // Do nothing and return directly ...
                        }

                        Debug.WriteLine("Optimization completed");
                        Environment.Exit(0);

                        break;

                    case "formtrialsub":
                        // NOTE: These styles need to be here for this to come out right.
                        Application.EnableVisualStyles();
                        Application.SetCompatibleTextRenderingDefault(false);
                        WindowsFormsSettings.ForceDirectXPaint();
                        BonusSkins.Register();
                        SkinManager.EnableFormSkins();
                        UserLookAndFeel.Default.SetSkinStyle("WXI");
                        var form1 = new Form1(true);
                        var formTrialSub = new FormTrialSubscriptionPrompt(0, null, true);
                        Application.Run(formTrialSub);
                        return;
                }
            }

            var upgradeStarted = Upgrader.TryUpgrade();
            if (upgradeStarted)
                Environment.Exit(0);

            using (Mutex mutex = new Mutex(false, "Global\\65cb7721-daed-4576-8601-49ef1f20b4d0"))
            {
                if (!mutex.WaitOne(0, false))
                {
                    IntPtr ptr = FindWindow(null, "uBuyFirst");
                    if (ptr != IntPtr.Zero)
                    {
                        try
                        {
                            if (IsIconic(ptr))
                            {
                                ShowWindow(ptr, 9);
                            }

                            //else
                            {
                                SetForegroundWindow(ptr);
                            }
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show(ex.Message);
                        }
                    }
                }
                else
                {
                    Application.EnableVisualStyles();
                    Application.SetCompatibleTextRenderingDefault(false);
                    WindowsFormsSettings.ForceDirectXPaint();
                    BonusSkins.Register();
                    SkinManager.EnableFormSkins();

                    // Configure Dependency Injection
                    var services = new ServiceCollection();
                    services.AddSingleton<ICountryService>(_ => new CountryService());
                    services.AddTransient<ISellerService, SellerService>();
                    services.AddTransient<Form1>(); // Register Form1
                    services.AddSingleton(typeof(ICacheService<,>), typeof(InMemoryCacheService<,>));

                    // Build the service provider
                    var serviceProvider = services.BuildServiceProvider();

                    // Resolve Form1 from the container and run the application
                    Application.Run(serviceProvider.GetRequiredService<Form1>());
                }
            }

            /*
            Application.EnableVisualStyles();
            Application.SetCompatibleTextRenderingDefault(false);
            Application.Run(new Form1());
            */
        }

        private static void Application_ThreadException(object sender, ThreadExceptionEventArgs e)
        {
            ShowExceptionDetails(e.Exception);
        }

        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            ShowExceptionDetails(e.ExceptionObject as Exception);
        }

        private static void ShowExceptionDetails(Exception ex)
        {
            if (ex != null)
            {
                string exceptionMessage = GetFullExceptionMessage(ex);
                MessageBox.Show(exceptionMessage, "Unhandled Exception");
            }
        }

        private static string GetFullExceptionMessage(Exception ex)
        {
            string message = $"Exception: {ex.Message}\nSource: {ex.Source}\nStack Trace:\n{ex.StackTrace}";

            if (ex.InnerException != null)
            {
                message += "\n\nInner Exception:\n" + GetFullExceptionMessage(ex.InnerException);
            }

            return message;
        }

        public static void DeregisterExceptionHandlers()
        {
            Application.ThreadException -= new ThreadExceptionEventHandler(Application_ThreadException);
            AppDomain.CurrentDomain.UnhandledException -= new UnhandledExceptionEventHandler(CurrentDomain_UnhandledException);
        }
    }
}
