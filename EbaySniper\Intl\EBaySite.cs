﻿using System;
using System.Diagnostics.CodeAnalysis;
using eBay.Service.Core.Soap;

namespace uBuyFirst.Intl
{
    [Serializable]
    [SuppressMessage("ReSharper", "NotAccessedField.Global")]
    public class EBaySite
    {
        private readonly string _domain;
        public readonly string GlobalID;
        public readonly string BrowseAPIID;
        public readonly string Language;
        public readonly string PicSuffix;
        public readonly string Programid;
        public readonly string Roverlink;

        public readonly SiteCodeType SiteCode;
        public readonly string SiteID;
        public readonly string SiteName;
        public readonly string Territory;

        private EBaySite()
        {
        }

        public EBaySite(string inputstr)
        {
            var values = inputstr.Trim().Split('\t');
            GlobalID = values[0].Trim();
            BrowseAPIID = values[1].Trim();
            Language = values[2].Trim();
            Territory = values[3].Trim();
            SiteName = values[4].Trim();
            SiteID = values[5].Trim();
            SiteCode = (SiteCodeType)Enum.Parse(typeof(SiteCodeType), values[6].Trim());
            _domain = values[7].Trim();
            PicSuffix = values[8].Trim();
            Programid = values[9].Trim();
            Roverlink = values[10].Trim();
        }

        public string Domain
        {
            get
            {
                if (Program.Sandbox && !_domain.Contains("sandbox"))
                    return _domain.Replace("ebay.", "sandbox.ebay.");
                return _domain;
            }
        }

        public override string ToString()
        {
            return SiteName;
        }
    }
}