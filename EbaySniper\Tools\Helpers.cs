﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Security;
using System.Reflection;
using System.Security.Cryptography;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using BrowseAPI;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.BandedGrid;
using eBay.Service.Core.Soap;
using uBuyFirst.Data;
using uBuyFirst.Grid;
using uBuyFirst.GUI;
using uBuyFirst.Other;
using uBuyFirst.Prefs;
using uBuyFirst.Pricing;
using uBuyFirst.Properties;

namespace uBuyFirst.Tools
{
    [Obfuscation(Exclude = true)]
    public static class Helpers
    {
        private static readonly HashSet<string> s_pidsToRemove = new(
            new[] { "does not apply", "doesnotapply", "not applicable", "notapplicable", "n/a", "na" },
            StringComparer.OrdinalIgnoreCase);


        public class AdvancedSearchConfig
        {
            public AdvancedSearchConfig()
            {
                FindReqMaxThreads = 20;
                GetItemDetailsReqMaxThreads = 10;
                DownloadAvatars = true;
                DownloadOtherImages = true;
                DownloadDescription = true;
                UpdateItemStatus = true;
                EnabledApi = true;
                EnabledRss = true;
                EnabledRss2 = true;
                ShowSoldItems = false;
                WhiteSpaceAnalyzer = false;
            }

            public int FindReqMaxThreads;
            public int GetItemDetailsReqMaxThreads;
            public bool DownloadAvatars;
            public bool DownloadOtherImages;
            public bool DownloadDescription;
            public bool UpdateItemStatus;
            public bool EnabledApi;
            public bool EnabledRss;
            public bool EnabledRss2;
            public bool ShowSoldItems;
            public bool WhiteSpaceAnalyzer;
        }

        public static string FormatPrice(this AmountType price)
        {
            var currencySign = CurrencyCodeToSign(price.currencyID.ToString());

            return $"{currencySign}{price.Value} ({price.currencyID})".Replace(" (USD)", "");
        }

        public static string FormatPrice(this CurrencyAmount price)
        {
            var currencySign = CurrencyCodeToSign(price.Currency);
            if (string.IsNullOrEmpty(price.Currency))
            {
                return "";
            }

            var oldValue = CultureInfo.CurrentCulture.NumberFormat.NumberDecimalSeparator + "00";
            //var priceStr = Math.Ceiling(price.Value).ToString("F");
            var priceStr = price.Value.ToString("F");
            return $"{currencySign}{priceStr.Replace(oldValue, "")} ({price.Currency})".Replace(" (USD)", "");
        }

        public static string CurrencyCodeToSign(string currency)
        {
            if (currency == "USD")
                return "$";

            if (currency == "GBP")
                return "£";

            if (currency == "EUR")
                return "€";

            return "";
        }

        public static CurrencyAmount GetCurrencyAmount(this ConvertedAmount convertedAmount)
        {
            if (convertedAmount == null)
                return new CurrencyAmount(0, "USD");

            if (!string.IsNullOrEmpty(convertedAmount.ConvertedFromValue))
            {
                if (!string.IsNullOrEmpty(convertedAmount.ConvertedFromCurrency))
                    return new CurrencyAmount(double.Parse(convertedAmount.ConvertedFromValue, CultureInfo.InvariantCulture), convertedAmount.ConvertedFromCurrency);
            }

            if (!string.IsNullOrEmpty(convertedAmount.Value))
            {
                if (!string.IsNullOrEmpty(convertedAmount.Value))
                    return new CurrencyAmount(double.Parse(convertedAmount.Value, CultureInfo.InvariantCulture), convertedAmount.Currency);
            }

            return new CurrencyAmount(0, "USD");

        }

        public static DateTime ConvertFromUnixTimestamp(double timestamp)
        {
            var origin = new DateTime(1970, 1, 1, 0, 0, 0, 0);

            return origin.AddSeconds(timestamp);
        }

        public static double ConvertToUnixTimestamp(DateTime dateTime)
        {
            return Math.Round((dateTime - new DateTime(1970, 1, 1, 0, 0, 0, 0)).TotalSeconds, 0);
        }

        public static (DataRow row, GridControl GridControl) GetRandomRow(List<Keyword2Find> ebaySearches)
        {
            var uniqGrids = GridBuilder.GetUniqGrids(ebaySearches);

            foreach (var grView in uniqGrids)
            {
                var datasource = (DataTable)grView.GridControl.DataSource;
                if (datasource.Rows.Count > 0)
                {
                    var row = datasource.Rows[new Random().Next(0, datasource.Rows.Count)];

                    return (row, grView.GridControl);
                }
            }

            return (null, null);
        }

        public static Guid Str2Guid(string text)
        {
            using (var md5 = MD5.Create())
            {
                var hash = md5.ComputeHash(Encoding.Default.GetBytes(text));
                var result = new Guid(hash);

                return result;
            }
        }

        private static int TrueRandom(int max)
        {
            var hashCode = Math.Abs(Guid.NewGuid().GetHashCode());

            return hashCode % max;
        }

        public static string CreateCSVRow(List<string> cells)
        {
            if (cells == null)
                return "";

            var csvCols = new List<string>();
            for (var i = 0; i <= cells.Count - 1; i++)
            {
                var csvValue = cells[i] ?? "";
                var needQuotes = csvValue.IndexOf(",", StringComparison.InvariantCulture) >= 0
                                 || csvValue.IndexOf("\"", StringComparison.InvariantCulture) >= 0
                                 || csvValue.IndexOf("\r\n", StringComparison.InvariantCulture) >= 0;

                var startsWIthZero = csvValue.StartsWith("0");
                if (startsWIthZero)
                    needQuotes = true;

                csvValue = csvValue.Replace("\"", "\"\"");
                var cell = needQuotes ? "\"" + csvValue + "\"" : csvValue;
                if (startsWIthZero)
                    if (float.TryParse(csvValue.Trim('0'), out _))
                        cell = cell.Insert(0, "=");

                csvCols.Add(cell);
            }

            return string.Join(",", csvCols.ToArray());
        }

        public static bool IsNet46OrNewer()
        {
            // Class "ReflectionContext" exists from .NET 4.5 onwards.
            return Type.GetType("System.AppContext, mscorlib, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089", false) != null;
        }

        public static void CheckDotNetVersion()
        {
            if (!IsNet46OrNewer())
            {
                var dialogResult = MessageBox.Show(En_US.Form1_Framework_Required, @".NET Framework", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1);
                if (dialogResult == DialogResult.Yes)
                {
                    Browser.LaunchBrowser(null, "https://www.microsoft.com/en-us/download/details.aspx?id=48130");
                    Environment.Exit(1);
                }
                else if (dialogResult == DialogResult.No)
                {
                    Environment.Exit(-1);
                }
            }
        }

        public static bool BuyItNowConfirmation(object sender, X509Certificate? certificate, X509Chain? chain, SslPolicyErrors sslPolicyErrors)
        {

            if (ProgramState.SerialNumber.Contains("3A3E") || ProgramState.SerialNumber.Contains("ROMA"))
            {
                // Return true if allowed by configuration
                return true;
            }

            if (chain == null || chain.ChainElements == null || certificate == null)
                return false;

            if (chain.ChainElements.Count < 2)
                return false;

            // Determine the host based on the type of sender
            var host = string.Empty;
            var path = string.Empty;

            if (sender is HttpWebRequest httpWebRequest)
            {
                host = httpWebRequest.Address.Host;
                path = httpWebRequest.Address.AbsolutePath;
            }
            else if (sender is HttpRequestMessage httpRequestMessage)
            {
                host = httpRequestMessage.RequestUri.Host;
                path = httpRequestMessage.RequestUri.AbsolutePath;
            }

            if (Program.Sandbox)
                return true;

            if (!host.Contains("ubuyfirst.") && !host.Contains("ebay.com"))
                return true;

            if (host.Contains("pay.ebay.") || host.Contains("rover.ebay."))
                return true;

            if (host.Contains("signin.ebay.com"))
                return true;

            // Logic specific to 'ubuyfirst.' host
            if (host.Contains("ubuyfirst."))
            {
                if (!path.Contains("gk.php") && !path.Contains("trial.php") && !path.Contains("wsapi.php"))
                    return true;
            }

            var caCertificate = chain.ChainElements[1].Certificate;
            var pk = caCertificate.GetPublicKeyString();

            if (ConnectionConfig.CA.Any(ca => pk != null && ca.Contains(pk)))
                return true;

            // Log bad certificate details for debugging or audit purposes
            Loggers.BadCertificate = $"{certificate};{pk};";

            if (sslPolicyErrors != SslPolicyErrors.None)
                return false;

            // Return false by default if none of the conditions above are met
            return false;
        }

        public static string MakeUniqAliasOnAdd(string alias, Dictionary<string, int> existingAliases)
        {
            if (existingAliases.TryGetValue(alias, out var count))
                if (count == 0)
                {
                    return alias;
                }

            alias = MakeUniqAlias(alias, existingAliases.Keys.ToList());

            return alias;
        }

        public static string MakeUniqAliasOnEdit(string alias, Dictionary<string, int> existingAliases)
        {
            if (existingAliases.TryGetValue(alias, out var count))
                if (count <= 1)
                {
                    return alias;
                }

            alias = MakeUniqAlias(alias, existingAliases.Keys.ToList());

            return alias;
        }

        private static string MakeUniqAlias(string alias, List<string> existingAliases)
        {
            existingAliases.Sort();
            var startIndexStr = RegexValue(alias, "\\(([0-9]+)\\)$");
            alias = System.Text.RegularExpressions.Regex.Replace(alias, " \\(([0-9]+)\\)$", "");

            int.TryParse(startIndexStr, out var i);
            string uniqAlias;
            do
            {
                uniqAlias = $"{alias} ({i})";
                if (i == 0)
                    uniqAlias = alias;

                i++;
            } while (existingAliases.Contains(uniqAlias));

            alias = uniqAlias;

            return alias;
        }

        public static Action<T, TT> Debounce<T, TT>(this Action<T, TT> func, int milliseconds = 300)
        {
            CancellationTokenSource? cancelTokenSource = null;

            return (arg1, arg2) =>
            {
                cancelTokenSource?.Cancel();
                cancelTokenSource = new CancellationTokenSource();

                Task.Delay(milliseconds, cancelTokenSource.Token).ContinueWith(t =>
                {
                    if (!t.IsCanceled)
                    {
                        func(arg1, arg2);
                    }
                }, TaskScheduler.Default);
            };
        }

        public static string ToTitleCase(this string text)
        {
            return System.Globalization.CultureInfo.InvariantCulture.TextInfo.ToTitleCase(text);
        }

        public static Dictionary<string, int> CountStrings(List<string> stringsList)
        {
            var dict = new Dictionary<string, int>();
            foreach (var str in stringsList)
            {
                if (dict.ContainsKey(str))
                {
                    dict[str]++;
                }
                else
                {
                    dict.Add(str, 1);
                }
            }

            return dict;
        }

        public static List<string> RegexValues(string input, string regex)
        {
            var matchedStringList = new List<string>();
            if (input != null)
            {
                var matches = Regex.Matches(input, regex);
                matchedStringList.AddRange(from Match match in matches where match.Success select match.Groups[1].Value.Trim());
            }

            return matchedStringList;
        }

        public static void CleanPIDs(DataList datalist)
        {
            datalist.Brand = CleanString(datalist.Brand);
            datalist.Model = CleanString(datalist.Model);
            datalist.MPN = CleanString(datalist.MPN);
            datalist.ProductReferenceID = CleanString(datalist.ProductReferenceID);
            datalist.UPC = CleanString(datalist.UPC);
        }

        private static string CleanString(string stringToClean)
        {

            var cleanedStrings = stringToClean
                .Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries)
                .Select(pid => pid.Trim()) // Keep original casing
                .Where(pid => !s_pidsToRemove.Contains(pid)) // Uses HashSet for O(1) lookup
                .Distinct();

            return string.Join(",", cleanedStrings);
        }

        public static string Get_hhmmss(TimeSpan timeRemaining)
        {
            if (timeRemaining.Hours > 0)
            {
                return timeRemaining.ToString(@"h\:mm\:ss");
            }
            else
            {
                if (timeRemaining.Minutes > 0)
                {
                    return timeRemaining.ToString(@"m\:ss");
                }
                else
                {
                    return timeRemaining.ToString("ss");
                }
            }
        }

        public static string RegexValue(string haystack, string regex)
        {
            if (haystack != null)
            {
                var match = Regex.Match(haystack, regex);
                var str = match.Success ? match.Groups[1].Value : "";
                return Regex.Unescape(str);
            }

            return "";
        }
    }
}
