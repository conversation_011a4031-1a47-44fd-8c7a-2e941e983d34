﻿using System.Collections.Generic;
using System.Data;
using System.Text.RegularExpressions;
using uBuyFirst.Data;
using uBuyFirst.Prefs;
using uBuyFirst.Tools;
using uBuyFirst.Views;


namespace uBuyFirst.Telegram
{
    public class MessageTemplateManager
    {
        TelegramSender _telegramSender; 
        public MessageTemplateManager(TelegramSender telegramSender)
        {
            _telegramSender = telegramSender;
        }

        public bool SetBody(string bodyTemplateStr)
        {
            var bodyColumns = Helpers.RegexValues(bodyTemplateStr, "{(.*?)}");

            if (!ColumnsManager.CheckExistingColumns(bodyColumns))
                return false;

            _telegramSender.BodyColumns = bodyColumns;
            _telegramSender.BodyTemplate = bodyTemplateStr;

            return true;
        }

        public string GetFormattedMessage(DataRow row, DataList d)
        {
            var body = _telegramSender.BodyTemplate;
            if (string.IsNullOrWhiteSpace(body))
            {
                return "";
            }

            foreach (var columnName in _telegramSender.BodyColumns)
            {
                if (columnName == "ViewUrl" || columnName == "CheckoutUrl" || columnName == "ContactUrl")
                    continue;

                body = body.Replace("{" + columnName + "}", GetRowValue(row, columnName, d));
            }

            return body;
        }

        private static string GetRowValue(DataRow? row, string columnName, DataList d)
        {
            switch (columnName)
            {
                case "Total Price":
                    return d.ItemPricing.GetTotalPrice(d.ItemShipping.FullSingleShippingPrice).FormatPrice();

                case "Item Price":
                    return d.ItemPricing.ItemPrice.FormatPrice();

                case "Auction Price":
                    if (d.ItemPricing.AuctionPrice != null)
                    {
                        return d.ItemPricing.AuctionPrice.FormatPrice();
                    }
                    return "0";

                case "Shipping":
                    return d.ItemShipping.FullSingleShippingPrice.FormatPrice();

                case "Ship Additional Item":
                    return d.ItemShipping.ShipAdditionalItem.FormatPrice();

                case "Found Time":
                    return d.FoundTime.ToString();

                case "Description":
                    var description = row["Description"].ToString();

                    if (description.Length > 3000)
                        return "";

                    description = System.Net.WebUtility.HtmlDecode(description);
                    description = Regex.Replace(description, "(<style.+?</style>)|(<script.+?</script>)", "", RegexOptions.IgnoreCase | RegexOptions.Singleline);
                    var plainDescription = Regex.Replace(description, @"<[^>]*>", string.Empty, RegexOptions.Compiled);

                    return plainDescription;

                case "ItemID":
                    if (ProgramState.SerialNumber.StartsWith("ROMA") || ProgramState.SerialNumber.StartsWith("1BE9-4A48"))
                    {
                        return row["ItemID"]?.ToString();
                    }

                    return "";

                default:
                    if (row?.Table?.Columns != null &&
                        row.Table.Columns.Contains(columnName) &&
                        !row.IsNull(columnName) &&
                        row[columnName] != null)
                    {
                        return row[columnName].ToString();
                    }
                    
                    return "";
            }
        }
    }
}


