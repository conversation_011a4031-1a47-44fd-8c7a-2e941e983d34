using System;
using System.IO;
using System.Threading.Tasks;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.RestockReporting.Models;
using uBuyFirst.RestockReporting.Services;
using uBuyFirst.Restocker.Models;
using uBuyFirst.Other;

namespace uBuyFirst.Tests.Forms
{
    [TestClass]
    public class Form1RestockReportTests
    {
        private string _testReportsPath;
        private string _testItemHistoryPath;

        [TestInitialize]
        public void Setup()
        {
            // Create unique temporary test directories for each test
            var uniqueId = Guid.NewGuid().ToString();
            _testReportsPath = Path.Combine(Path.GetTempPath(), "Form1RestockReportTests", uniqueId);
            _testItemHistoryPath = Path.Combine(_testReportsPath, "Reports", "ItemHistory");
            Directory.CreateDirectory(_testItemHistoryPath);
        }

        [TestCleanup]
        public void Cleanup()
        {
            // Clean up test directories with retry logic
            if (Directory.Exists(_testReportsPath))
            {
                try
                {
                    Directory.Delete(_testReportsPath, true);
                }
                catch (IOException)
                {
                    // Ignore cleanup errors - temp files will be cleaned up by OS
                }
            }
        }

        [TestMethod]
        public async Task CreateRestockReportCsv_WithValidData_CreatesTimestampedCsvFile()
        {
            // Arrange
            await CreateTestItemHistoryData();
            var reportsFolder = Path.Combine(_testReportsPath, "Reports");

            // Act
            var csvFilePath = await CreateRestockReportCsv();

            // Assert
            Assert.IsTrue(File.Exists(csvFilePath), "CSV file should be created");

            // Verify filename format: RestockReport_{datetime}.csv
            var fileName = Path.GetFileName(csvFilePath);
            Assert.IsTrue(fileName.StartsWith("RestockReport_"), "Filename should start with RestockReport_");
            Assert.IsTrue(fileName.EndsWith(".csv"), "Filename should end with .csv");

            // Verify timestamp format in filename (YYYYMMDD_HHMMSS_fff)
            var timestampPart = fileName.Substring("RestockReport_".Length, fileName.Length - "RestockReport_".Length - ".csv".Length);
            Assert.IsTrue(DateTime.TryParseExact(timestampPart, "yyyyMMdd_HHmmss_fff", null, System.Globalization.DateTimeStyles.None, out _),
                "Timestamp in filename should be in format YYYYMMDD_HHMMSS_fff");
        }

        [TestMethod]
        public async Task CreateRestockReportCsv_WithValidData_ContainsAllExpectedColumns()
        {
            // Arrange
            await CreateTestItemHistoryData();

            // Act
            var csvFilePath = await CreateRestockReportCsv();

            // Assert
            var csvContent = File.ReadAllText(csvFilePath);
            var lines = csvContent.Split('\n');
            var headerLine = lines[0];

            // Verify key columns are present (based on ItemHistoryExporter)
            Assert.IsTrue(headerLine.Contains("Timestamp"), "Should contain Timestamp column");
            Assert.IsTrue(headerLine.Contains("Outcome"), "Should contain Outcome column");
            Assert.IsTrue(headerLine.Contains("Reason"), "Should contain Reason column");
            Assert.IsTrue(headerLine.Contains("ItemId"), "Should contain ItemId column");
            Assert.IsTrue(headerLine.Contains("Title"), "Should contain Title column");
            Assert.IsTrue(headerLine.Contains("CurrentPrice"), "Should contain CurrentPrice column");
            Assert.IsTrue(headerLine.Contains("Condition"), "Should contain Condition column");
            Assert.IsTrue(headerLine.Contains("Seller"), "Should contain Seller column");
            Assert.IsTrue(headerLine.Contains("KeywordId"), "Should contain KeywordId column");
            Assert.IsTrue(headerLine.Contains("Alias"), "Should contain Alias column");
            Assert.IsTrue(headerLine.Contains("RequiredQuantity"), "Should contain RequiredQuantity column");
            Assert.IsTrue(headerLine.Contains("PurchasedQuantity"), "Should contain PurchasedQuantity column");
        }

        [TestMethod]
        public async Task CreateRestockReportCsv_WithValidData_ContainsTestData()
        {
            // Arrange
            await CreateTestItemHistoryData();

            // Act
            var csvFilePath = await CreateRestockReportCsv();

            // Assert
            var csvContent = File.ReadAllText(csvFilePath);

            // Verify test data is present
            Assert.IsTrue(csvContent.Contains("123456789"), "Should contain test item ID");
            Assert.IsTrue(csvContent.Contains("Test Item for RestockReport"), "Should contain test item title");
            Assert.IsTrue(csvContent.Contains("purchased"), "Should contain purchase outcome");
            Assert.IsTrue(csvContent.Contains("Test Keyword Alias"), "Should contain keyword alias");
        }

        [TestMethod]
        public async Task CreateRestockReportCsv_CreatesReportsFolder_IfNotExists()
        {
            // Arrange
            var reportsFolder = Path.Combine(_testReportsPath, "Reports");
            // Delete the Reports folder to test creation
            if (Directory.Exists(reportsFolder))
            {
                Directory.Delete(reportsFolder, true);
            }
            Assert.IsFalse(Directory.Exists(reportsFolder), "Reports folder should not exist initially");

            await CreateTestItemHistoryData();

            // Act
            var csvFilePath = await CreateRestockReportCsv();

            // Assert
            Assert.IsTrue(Directory.Exists(reportsFolder), "Reports folder should be created");
            Assert.IsTrue(File.Exists(csvFilePath), "CSV file should be created in Reports folder");
        }

        /// <summary>
        /// Simulates the functionality that should be implemented in barButtonItemRestockReport_ItemClick
        /// </summary>
        private async Task<string> CreateRestockReportCsv()
        {
            // This simulates what the actual implementation should do
            var options = new ItemHistoryOptions
            {
                BasePath = _testItemHistoryPath,
                CreateDailyFolders = true,
                EnableLogging = true
            };
            var exporter = new ItemHistoryExporter(options);

            // Create Reports folder if it doesn't exist
            var reportsFolder = Path.Combine(_testReportsPath, "Reports");
            if (!Directory.Exists(reportsFolder))
            {
                Directory.CreateDirectory(reportsFolder);
            }

            // Generate timestamped filename with milliseconds to avoid conflicts
            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss_fff");
            var fileName = $"RestockReport_{timestamp}.csv";
            var csvFilePath = Path.Combine(reportsFolder, fileName);

            // Export data from last 30 days (or all available data)
            var endDate = DateTime.Now;
            var startDate = endDate.AddDays(-30);

            await exporter.ExportToCsvAsync(startDate, endDate, csvFilePath);

            return csvFilePath;
        }

        /// <summary>
        /// Creates test item history data for testing
        /// </summary>
        private async Task CreateTestItemHistoryData()
        {
            var options = new ItemHistoryOptions
            {
                BasePath = _testItemHistoryPath,
                CreateDailyFolders = true,
                EnableLogging = true
            };
            var logger = new FileItemHistoryLogger(options);

            var testContext = new ItemProcessingContext
            {
                Timestamp = DateTime.UtcNow,
                Outcome = "purchased",
                Reason = "Test purchase for RestockReport",
                ItemData = new ItemHistoryData
                {
                    ItemId = "123456789",
                    Title = "Test Item for RestockReport",
                    CurrentPrice = 29.99m,
                    Condition = "New",
                    Seller = "test-seller-restock"
                },
                KeywordState = new KeywordSnapshot
                {
                    KeywordId = "kw-restock-test",
                    Alias = "Test Keyword Alias",
                    JobId = "JOB-RESTOCK-001",
                    RequiredQuantity = 10,
                    PurchasedQuantity = 3
                },
                FilterRule = new FilterRuleContext
                {
                    FilterAlias = "Restock Test Filter",
                    Expression = "Price <= 50",
                    Matched = true,
                    EvaluationResult = "Filter matched for test"
                },
                TransactionResult = new TransactionResult
                {
                    Attempted = true,
                    Success = true,
                    PurchasePrice = 29.99m,
                    Quantity = 3
                }
            };

            await logger.LogItemProcessingAsync(testContext);
        }
    }
}
