using System;
using System.Drawing;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace uBuyFirst.GUI
{
    internal static class WebView
    {
        private const int FeatureDisableNavigationSounds = 21;
        private const int SetFeatureOnProcess = 0x00000002;

        public static string HexConverter(Color c)
        {
            return "#" + c.R.ToString("X2") + c.G.ToString("X2") + c.B.ToString("X2");
        }

        [DllImport("urlmon.dll")]
        [PreserveSig]
        [return: MarshalAs(UnmanagedType.Error)]
        private static extern int CoInternetSetFeatureEnabled(int featureEntry, [MarshalAs(UnmanagedType.U4)] int dwFlags, bool fEnable);

        public static void DisableClickSounds()
        {
            CoInternetSetFeatureEnabled(FeatureDisableNavigationSounds, SetFeatureOnProcess, true);
        }

        public static void SetZoom(WebBrowser webBrowser, int zoom)
        {
            if (webBrowser.DocumentText.Length == 0)
                return;

            const int olecmdidZoom = 63;
            const int olecmdexecoptDontpromptuser = 2;
            dynamic obj = webBrowser.ActiveXInstance;
            obj.ExecWB(olecmdidZoom, olecmdexecoptDontpromptuser, zoom, IntPtr.Zero);
        }
    }
}