using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using DevExpress.XtraTreeList;
using uBuyFirst.API.TradingAPI;
using uBuyFirst.Intl;
using uBuyFirst.Other;

namespace uBuyFirst.GUI
{
    internal static class CategoryLookup
    {
        public static async Task InitTreelistLookup(TreeList treeList, string siteName)
        {
            try
            {
                if (treeList.DataSource != null && treeList.Tag != null && treeList.Tag.ToString() == siteName)
                    return;
                treeList.Tag = siteName;
                AddColumns(treeList);
                var marketplaceID = CountryProvider.GetEbaySite(siteName).BrowseAPIID;
                ShowDownloadingTip(treeList, marketplaceID);

                var categoryService = new CategoryServiceBrowseAPI();
                await categoryService.GetSiteCategories(marketplaceID).ContinueWith(t =>
                {
                    var dataSource = t.Result;
                    SetTreelistDataSource(dataSource, treeList);
                }, CancellationToken.None, TaskContinuationOptions.OnlyOnRanToCompletion, TaskScheduler.FromCurrentSynchronizationContext());
            }
            catch (Exception exception)
            {
                Loggers.LogError(exception);
            }
        }

        public static void AddColumns(TreeList treeList)
        {
            if (treeList.Columns.Count == 0)
            {
                treeList.Columns.AddVisible("Name");
                treeList.Columns.AddVisible("CategoryID");
            }
        }

        private static void ShowDownloadingTip(TreeList treeList, string siteCodeType)
        {
            treeList.Nodes.Clear();
            if (treeList.Nodes.Count == 0)
            {
                treeList.DataSource = null;
                var categoryFilePath = Path.Combine(Folders.Settings, "Categories." + siteCodeType + ".json");
                if (File.Exists(categoryFilePath))
                    treeList.Nodes.Add("Please, wait...", "");
                else
                    treeList.Nodes.Add("Downloading category tree. Please, wait a minute.", "");
            }

            treeList.BestFitColumns();
        }

        private static void SetTreelistDataSource(Categories dataSource, TreeList treeList)
        {
            if (dataSource == null || dataSource.Count == 0)
                return;
            treeList.BeginUpdate();
            treeList.DataSource = dataSource;
            treeList.ExpandAll();
            treeList.CollapseAll();
            treeList.EndUpdate();
        }

        public static void DataSourceToNull(TreeList treeList)
        {
            treeList.DataSource = null;
        }
    }
}