﻿using System.ComponentModel;
using DevExpress.Utils;
using DevExpress.XtraBars;
using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;

namespace uBuyFirst
{
    partial class FormSpecificsColumns
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormSpecificsColumns));
            this.formAssistant1 = new DevExpress.XtraBars.FormAssistant();
            this.toolTipController1 = new DevExpress.Utils.ToolTipController(this.components);
            this.ribbonControl1 = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.btnRemoveCustomColumns = new DevExpress.XtraEditors.SimpleButton();
            this.btnCreateCustomColumns = new DevExpress.XtraEditors.SimpleButton();
            this.boxAllCatSpecifics = new DevExpress.XtraEditors.CheckedListBoxControl();
            this.boxUserSelectedSpecifics = new DevExpress.XtraEditors.CheckedListBoxControl();
            this.btnFindCustomColumns = new DevExpress.XtraEditors.SimpleButton();
            this.boxCustomColumnSite = new DevExpress.XtraEditors.ComboBoxEdit();
            this.lblCategoryID = new DevExpress.XtraEditors.HyperlinkLabelControl();
            this.btnClose = new DevExpress.XtraEditors.SimpleButton();
            this.hyperlinkLabelControl1 = new DevExpress.XtraEditors.HyperlinkLabelControl();
            this.treeListLookUpEdit1 = new DevExpress.XtraEditors.TreeListLookUpEdit();
            this.treeListLookUpEdit1TreeList = new DevExpress.XtraTreeList.TreeList();
            this.groupControlFindSpecifics = new DevExpress.XtraEditors.GroupControl();
            this.groupControlCreatedSpecifics = new DevExpress.XtraEditors.GroupControl();
            this.groupControlAddSpecificManually = new DevExpress.XtraEditors.GroupControl();
            this.btnAddSpecificManually = new DevExpress.XtraEditors.SimpleButton();
            this.txtManualSpecific = new DevExpress.XtraEditors.TextEdit();
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.boxAllCatSpecifics)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.boxUserSelectedSpecifics)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.boxCustomColumnSite.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeListLookUpEdit1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeListLookUpEdit1TreeList)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControlFindSpecifics)).BeginInit();
            this.groupControlFindSpecifics.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControlCreatedSpecifics)).BeginInit();
            this.groupControlCreatedSpecifics.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControlAddSpecificManually)).BeginInit();
            this.groupControlAddSpecificManually.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtManualSpecific.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // ribbonControl1
            // 
            this.ribbonControl1.ExpandCollapseItem.Id = 0;
            this.ribbonControl1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.ribbonControl1.ExpandCollapseItem,
            this.ribbonControl1.SearchEditItem});
            this.ribbonControl1.Location = new System.Drawing.Point(0, 0);
            this.ribbonControl1.MaxItemId = 1;
            this.ribbonControl1.Name = "ribbonControl1";
            this.ribbonControl1.OptionsPageCategories.ShowCaptions = false;
            this.ribbonControl1.ShowApplicationButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowDisplayOptionsMenuButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowExpandCollapseButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowPageHeadersMode = DevExpress.XtraBars.Ribbon.ShowPageHeadersMode.Hide;
            this.ribbonControl1.ShowQatLocationSelector = false;
            this.ribbonControl1.ShowToolbarCustomizeItem = false;
            this.ribbonControl1.Size = new System.Drawing.Size(765, 49);
            this.ribbonControl1.Toolbar.ShowCustomizeItem = false;
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(9, 59);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(90, 13);
            this.labelControl2.TabIndex = 14;
            this.labelControl2.Text = "Available specifics:";
            // 
            // btnRemoveCustomColumns
            // 
            this.btnRemoveCustomColumns.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.btnRemoveCustomColumns.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnRemoveCustomColumns.Location = new System.Drawing.Point(12, 513);
            this.btnRemoveCustomColumns.Name = "btnRemoveCustomColumns";
            this.btnRemoveCustomColumns.Size = new System.Drawing.Size(88, 23);
            this.btnRemoveCustomColumns.TabIndex = 5;
            this.btnRemoveCustomColumns.Text = "Remove";
            this.btnRemoveCustomColumns.Click += new System.EventHandler(this.btnRemoveCustomColumns_Click);
            // 
            // btnCreateCustomColumns
            // 
            this.btnCreateCustomColumns.Location = new System.Drawing.Point(5, 187);
            this.btnCreateCustomColumns.Name = "btnCreateCustomColumns";
            this.btnCreateCustomColumns.Size = new System.Drawing.Size(90, 23);
            this.btnCreateCustomColumns.TabIndex = 3;
            this.btnCreateCustomColumns.Text = "Create columns";
            this.btnCreateCustomColumns.ToolTip = resources.GetString("btnCreateCustomColumns.ToolTip");
            this.btnCreateCustomColumns.Click += new System.EventHandler(this.btnCreateCustomColumns_Click);
            // 
            // boxAllCatSpecifics
            // 
            this.boxAllCatSpecifics.CheckOnClick = true;
            this.boxAllCatSpecifics.Cursor = System.Windows.Forms.Cursors.Default;
            this.boxAllCatSpecifics.Dock = System.Windows.Forms.DockStyle.Fill;
            this.boxAllCatSpecifics.HorizontalScrollbar = true;
            this.boxAllCatSpecifics.Location = new System.Drawing.Point(2, 20);
            this.boxAllCatSpecifics.Name = "boxAllCatSpecifics";
            this.boxAllCatSpecifics.Size = new System.Drawing.Size(761, 164);
            this.boxAllCatSpecifics.TabIndex = 4;
            this.boxAllCatSpecifics.DrawItem += new DevExpress.XtraEditors.ListBoxDrawItemEventHandler(this.boxAllCatSpecifics_DrawItem);
            // 
            // boxUserSelectedSpecifics
            // 
            this.boxUserSelectedSpecifics.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.boxUserSelectedSpecifics.CheckOnClick = true;
            this.boxUserSelectedSpecifics.Cursor = System.Windows.Forms.Cursors.Default;
            this.boxUserSelectedSpecifics.HorizontalScrollbar = true;
            this.boxUserSelectedSpecifics.Location = new System.Drawing.Point(5, 78);
            this.boxUserSelectedSpecifics.Name = "boxUserSelectedSpecifics";
            this.boxUserSelectedSpecifics.Size = new System.Drawing.Size(755, 103);
            this.boxUserSelectedSpecifics.TabIndex = 2;
            this.boxUserSelectedSpecifics.DrawItem += new DevExpress.XtraEditors.ListBoxDrawItemEventHandler(this.boxCurrentCatSpecifics_DrawItem);
            // 
            // btnFindCustomColumns
            // 
            this.btnFindCustomColumns.Location = new System.Drawing.Point(301, 22);
            this.btnFindCustomColumns.Name = "btnFindCustomColumns";
            this.btnFindCustomColumns.Size = new System.Drawing.Size(104, 23);
            this.btnFindCustomColumns.TabIndex = 1;
            this.btnFindCustomColumns.Text = "Find items specifics";
            this.btnFindCustomColumns.ToolTip = resources.GetString("btnFindCustomColumns.ToolTip");
            this.btnFindCustomColumns.Click += new System.EventHandler(this.btnFindCustomColumns_Click);
            // 
            // boxCustomColumnSite
            // 
            this.boxCustomColumnSite.EditValue = "eBay US";
            this.boxCustomColumnSite.Location = new System.Drawing.Point(195, 24);
            this.boxCustomColumnSite.MenuManager = this.ribbonControl1;
            this.boxCustomColumnSite.Name = "boxCustomColumnSite";
            this.boxCustomColumnSite.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.boxCustomColumnSite.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.boxCustomColumnSite.Size = new System.Drawing.Size(100, 20);
            this.boxCustomColumnSite.TabIndex = 0;
            this.boxCustomColumnSite.ToolTip = resources.GetString("boxCustomColumnSite.ToolTip");
            this.boxCustomColumnSite.SelectedIndexChanged += new System.EventHandler(this.boxCustomColumnSite_SelectedIndexChanged);
            // 
            // lblCategoryID
            // 
            this.lblCategoryID.Cursor = System.Windows.Forms.Cursors.Hand;
            this.lblCategoryID.Location = new System.Drawing.Point(9, 27);
            this.lblCategoryID.Name = "lblCategoryID";
            this.lblCategoryID.Size = new System.Drawing.Size(59, 13);
            this.lblCategoryID.TabIndex = 8;
            this.lblCategoryID.Tag = "http://faq.frooition.com/ebay_categories.php";
            this.lblCategoryID.Text = "Category ID";
            this.lblCategoryID.ToolTip = resources.GetString("lblCategoryID.ToolTip");
            this.lblCategoryID.Click += new System.EventHandler(this.hyperlink_Click);
            // 
            // btnClose
            // 
            this.btnClose.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnClose.Location = new System.Drawing.Point(670, 513);
            this.btnClose.Name = "btnClose";
            this.btnClose.Size = new System.Drawing.Size(75, 23);
            this.btnClose.TabIndex = 6;
            this.btnClose.Text = "Close";
            this.btnClose.Click += new System.EventHandler(this.btnClose_Click);
            // 
            // hyperlinkLabelControl1
            // 
            this.hyperlinkLabelControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.hyperlinkLabelControl1.Cursor = System.Windows.Forms.Cursors.Hand;
            this.hyperlinkLabelControl1.Location = new System.Drawing.Point(359, 523);
            this.hyperlinkLabelControl1.Name = "hyperlinkLabelControl1";
            this.hyperlinkLabelControl1.Size = new System.Drawing.Size(21, 13);
            this.hyperlinkLabelControl1.TabIndex = 17;
            this.hyperlinkLabelControl1.Tag = "https://ubuyfirst.com/04-search-results-configuration/";
            this.hyperlinkLabelControl1.Text = "Help";
            this.hyperlinkLabelControl1.ToolTip = resources.GetString("hyperlinkLabelControl1.ToolTip");
            this.hyperlinkLabelControl1.Click += new System.EventHandler(this.hyperlink_Click);
            // 
            // treeListLookUpEdit1
            // 
            this.treeListLookUpEdit1.EditValue = "";
            this.treeListLookUpEdit1.Location = new System.Drawing.Point(80, 24);
            this.treeListLookUpEdit1.MenuManager = this.ribbonControl1;
            this.treeListLookUpEdit1.Name = "treeListLookUpEdit1";
            this.treeListLookUpEdit1.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.treeListLookUpEdit1.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.treeListLookUpEdit1.Properties.NullText = "";
            this.treeListLookUpEdit1.Properties.NullValuePrompt = "Ex. 9355";
            this.treeListLookUpEdit1.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;
            this.treeListLookUpEdit1.Properties.TreeList = this.treeListLookUpEdit1TreeList;
            this.treeListLookUpEdit1.Size = new System.Drawing.Size(100, 20);
            this.treeListLookUpEdit1.TabIndex = 19;
            this.treeListLookUpEdit1.ToolTip = resources.GetString("treeListLookUpEdit1.ToolTip");
            this.treeListLookUpEdit1.BeforePopup += new System.EventHandler(this.treeListLookUpEdit1_BeforePopup);
            // 
            // treeListLookUpEdit1TreeList
            // 
            this.treeListLookUpEdit1TreeList.Location = new System.Drawing.Point(0, 0);
            this.treeListLookUpEdit1TreeList.Name = "treeListLookUpEdit1TreeList";
            this.treeListLookUpEdit1TreeList.OptionsCustomization.CustomizationFormSearchBoxVisible = true;
            this.treeListLookUpEdit1TreeList.OptionsFilter.ExpandNodesOnFiltering = true;
            this.treeListLookUpEdit1TreeList.OptionsFilter.FilterMode = DevExpress.XtraTreeList.FilterMode.ParentBranch;
            this.treeListLookUpEdit1TreeList.OptionsFind.AllowFindPanel = true;
            this.treeListLookUpEdit1TreeList.OptionsFind.AlwaysVisible = true;
            this.treeListLookUpEdit1TreeList.OptionsFind.ClearFindOnClose = false;
            this.treeListLookUpEdit1TreeList.OptionsFind.FindDelay = 500;
            this.treeListLookUpEdit1TreeList.OptionsView.FocusRectStyle = DevExpress.XtraTreeList.DrawFocusRectStyle.RowFocus;
            this.treeListLookUpEdit1TreeList.OptionsView.ShowIndentAsRowStyle = true;
            this.treeListLookUpEdit1TreeList.Size = new System.Drawing.Size(400, 200);
            this.treeListLookUpEdit1TreeList.TabIndex = 0;
            // 
            // groupControlFindSpecifics
            // 
            this.groupControlFindSpecifics.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupControlFindSpecifics.Controls.Add(this.btnCreateCustomColumns);
            this.groupControlFindSpecifics.Controls.Add(this.treeListLookUpEdit1);
            this.groupControlFindSpecifics.Controls.Add(this.lblCategoryID);
            this.groupControlFindSpecifics.Controls.Add(this.boxCustomColumnSite);
            this.groupControlFindSpecifics.Controls.Add(this.btnFindCustomColumns);
            this.groupControlFindSpecifics.Controls.Add(this.labelControl2);
            this.groupControlFindSpecifics.Controls.Add(this.boxUserSelectedSpecifics);
            this.groupControlFindSpecifics.Location = new System.Drawing.Point(0, 30);
            this.groupControlFindSpecifics.Name = "groupControlFindSpecifics";
            this.groupControlFindSpecifics.Size = new System.Drawing.Size(765, 219);
            this.groupControlFindSpecifics.TabIndex = 21;
            this.groupControlFindSpecifics.Text = "Item specifics search";
            // 
            // groupControlCreatedSpecifics
            // 
            this.groupControlCreatedSpecifics.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupControlCreatedSpecifics.Controls.Add(this.boxAllCatSpecifics);
            this.groupControlCreatedSpecifics.Location = new System.Drawing.Point(0, 321);
            this.groupControlCreatedSpecifics.Name = "groupControlCreatedSpecifics";
            this.groupControlCreatedSpecifics.Size = new System.Drawing.Size(765, 186);
            this.groupControlCreatedSpecifics.TabIndex = 22;
            this.groupControlCreatedSpecifics.Text = "Items specifics columns:";
            // 
            // groupControlAddSpecificManually
            // 
            this.groupControlAddSpecificManually.Anchor = System.Windows.Forms.AnchorStyles.Left;
            this.groupControlAddSpecificManually.Controls.Add(this.txtManualSpecific);
            this.groupControlAddSpecificManually.Controls.Add(this.btnAddSpecificManually);
            this.groupControlAddSpecificManually.Location = new System.Drawing.Point(0, 255);
            this.groupControlAddSpecificManually.Name = "groupControlAddSpecificManually";
            this.groupControlAddSpecificManually.Size = new System.Drawing.Size(765, 60);
            this.groupControlAddSpecificManually.TabIndex = 23;
            this.groupControlAddSpecificManually.Text = "Can\'t find your item specific? Add it manually.";
            // 
            // btnAddSpecificManually
            // 
            this.btnAddSpecificManually.Location = new System.Drawing.Point(130, 27);
            this.btnAddSpecificManually.Name = "btnAddSpecificManually";
            this.btnAddSpecificManually.Size = new System.Drawing.Size(90, 23);
            this.btnAddSpecificManually.TabIndex = 0;
            this.btnAddSpecificManually.Text = "Create Column";
            this.btnAddSpecificManually.Click += new System.EventHandler(this.btnAddSpecificManually_Click);
            // 
            // txtManualSpecific
            // 
            this.txtManualSpecific.Location = new System.Drawing.Point(9, 28);
            this.txtManualSpecific.MenuManager = this.ribbonControl1;
            this.txtManualSpecific.Name = "txtManualSpecific";
            this.txtManualSpecific.Properties.NullValuePrompt = "Ex. Item Weight";
            this.txtManualSpecific.Size = new System.Drawing.Size(115, 20);
            this.txtManualSpecific.TabIndex = 1;
            // 
            // FormSpecificsColumns
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(765, 552);
            this.Controls.Add(this.groupControlAddSpecificManually);
            this.Controls.Add(this.groupControlCreatedSpecifics);
            this.Controls.Add(this.groupControlFindSpecifics);
            this.Controls.Add(this.hyperlinkLabelControl1);
            this.Controls.Add(this.btnClose);
            this.Controls.Add(this.btnRemoveCustomColumns);
            this.Controls.Add(this.ribbonControl1);
            this.IconOptions.Icon = ((System.Drawing.Icon)(resources.GetObject("FormSpecificsColumns.IconOptions.Icon")));
            this.Name = "FormSpecificsColumns";
            this.Ribbon = this.ribbonControl1;
            this.Text = "Items Specifics";
            this.Load += new System.EventHandler(this.FormCustomColumns_Load);
            this.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.FormCustomColumns_KeyPress);
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.boxAllCatSpecifics)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.boxUserSelectedSpecifics)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.boxCustomColumnSite.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeListLookUpEdit1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.treeListLookUpEdit1TreeList)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControlFindSpecifics)).EndInit();
            this.groupControlFindSpecifics.ResumeLayout(false);
            this.groupControlFindSpecifics.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControlCreatedSpecifics)).EndInit();
            this.groupControlCreatedSpecifics.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControlAddSpecificManually)).EndInit();
            this.groupControlAddSpecificManually.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtManualSpecific.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private FormAssistant formAssistant1;
        private ToolTipController toolTipController1;
        private RibbonControl ribbonControl1;
        private LabelControl labelControl2;
        private SimpleButton btnRemoveCustomColumns;
        private SimpleButton btnCreateCustomColumns;
        private CheckedListBoxControl boxAllCatSpecifics;
        private CheckedListBoxControl boxUserSelectedSpecifics;
        private SimpleButton btnFindCustomColumns;
        private ComboBoxEdit boxCustomColumnSite;
        private HyperlinkLabelControl lblCategoryID;
        private SimpleButton btnClose;
        private HyperlinkLabelControl hyperlinkLabelControl1;
        private TreeListLookUpEdit treeListLookUpEdit1;
        private DevExpress.XtraTreeList.TreeList treeListLookUpEdit1TreeList;
        private GroupControl groupControlFindSpecifics;
        private GroupControl groupControlCreatedSpecifics;
        private GroupControl groupControlAddSpecificManually;
        private SimpleButton btnAddSpecificManually;
        private TextEdit txtManualSpecific;
    }
}