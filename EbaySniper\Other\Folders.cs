﻿using System;
using System.IO;
using System.Reflection;

namespace uBuyFirst.Other
{
    public static class Folders
    {
        public static bool PortableMode;
        public static string Layout;
        public static string Settings;
        public static string Workspace;
        public static string Logs;
        public static string SkuManagerScripts;
        public static string Tmp;
        public static string Backup;
        public static string? CefSharpBinaries;
        public static string CefSharpRootCache;

        public static void SetupFolders()
        {
            if (PortableMode)
                Settings = Directory.GetParent(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location)).FullName;
            else
                Settings = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "uBuyFirst");

            Workspace = Path.Combine(Settings, "Workspaces");
            Layout = Settings;
            Logs = Path.Combine(Settings, "Logs");
            Backup = Path.Combine(Settings, "Backup");
            SkuManagerScripts = Path.Combine(Settings, "SkuManagerScripts");
            CefSharpBinaries = Path.Combine(Settings, "Bin", "x64");
            CefSharpRootCache = Path.Combine(Settings, "Chrome");
            Tmp = Path.Combine(Path.GetTempPath(), "uBuyFirst");
            if (!Directory.Exists(Settings))
                Directory.CreateDirectory(Settings);

            if (!Directory.Exists(Logs))
                Directory.CreateDirectory(Logs);

            if (!Directory.Exists(SkuManagerScripts))
                Directory.CreateDirectory(SkuManagerScripts);

            if (!Directory.Exists(Workspace))
                Directory.CreateDirectory(Workspace);

            if (!Directory.Exists(Layout))
                Directory.CreateDirectory(Layout);

            if (!Directory.Exists(Tmp))
                Directory.CreateDirectory(Tmp);
            
            if (!Directory.Exists(Backup))
                Directory.CreateDirectory(Backup);
            
            if (!Directory.Exists(CefSharpBinaries))
                Directory.CreateDirectory(CefSharpBinaries);

            if (!Directory.Exists(CefSharpRootCache))
                Directory.CreateDirectory(CefSharpRootCache);

            if (File.Exists(Path.Combine(Logs, "FoundItems.csv")))
            {
                if (new FileInfo(Path.Combine(Logs, "FoundItems.csv")).Length > 10 * 1000 * 1000)
                    try
                    {
                        File.Delete(Path.Combine(Logs, "FoundItems.csv"));
                    }
                    catch (Exception)
                    {
                        // ignored
                    }
            }

            if (File.Exists(Path.Combine(Logs, "SubSearches.csv")))
            {
                var size = new FileInfo(Path.Combine(Logs, "SubSearches.csv")).Length;
                if (size > 50000000)
                {
                    try
                    {
                        File.Delete(Path.Combine(Logs, "SubSearches.csv"));
                    }
                    catch (Exception)
                    {
                        // ignored
                    }
                }
            }
        }
    }
}
