﻿using System;
using System.IO;
using uBuyFirst.Other;
using uBuyFirst.Prefs;

namespace uBuyFirst.Purchasing
{
    public static class PaymentLogger
    {
        public static void LogPayment(string itemID, string stage, string html)
        {
            if (!ProgramState.SerialNumber.StartsWith("3A3E") && !ProgramState.SerialNumber.StartsWith("ROMA"))
            {
                return;
            }

            var patchedHtml = html.Replace("historyPushURL", "");
            var paymentLogFolder = Path.Combine(Folders.Logs, "Payments");
            if (!Directory.Exists(paymentLogFolder))
            {
                Directory.CreateDirectory(paymentLogFolder);
            }

            var fileName = $"{DateTime.Now.ToString("s").Replace("T", " ").Replace(":", ".")}_{itemID}_{stage}.html";
            var filePath = Path.Combine(paymentLogFolder, fileName);
            File.WriteAllText(filePath, patchedHtml);
        }

        public static void LogPaymentToFile(string message)
        {
            if (!ProgramState.SerialNumber.StartsWith("3A3E") && !ProgramState.SerialNumber.StartsWith("ROMA"))
            {
                return;
            }

            var paymentLogFolder = Path.Combine(Folders.Logs, "Payments");
            if (!Directory.Exists(paymentLogFolder))
            {
                Directory.CreateDirectory(paymentLogFolder);
            }

            var row = $"{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff").Replace("T", " ").Replace(":", ".")} {message}\r\n";
            var filePath = Path.Combine(paymentLogFolder, "_log.txt");
            File.AppendAllText(filePath, row);
        }
    }
}
