﻿using System;
using System.IO;
using System.Security.Cryptography;
using uBuyFirst.Prefs;

namespace uBuyFirst.Security
{
    public static class Utility
    {
        public static string ToUrlSafeBase64(byte[] input)
        {
            return Convert.ToBase64String(input).Replace("+", "-").Replace("/", "_");
        }

        public static byte[] FromUrlSafeBase64(string input)
        {
            var replace = input.Replace("-", "+").Replace("_", "/");
            return Convert.FromBase64String(replace);
        }
    }

    public class AeStoPhpCryptography
    {
        private readonly byte[] _key;
        private readonly byte[] _iv;

        /// <summary>
        /// Gets the encryption key as a base64 encoded string.
        /// </summary>
        public string EncryptionKeyString => Convert.ToBase64String(_key);

        /// <summary>
        /// Gets the initialization key as a base64 encoded string.
        /// </summary>
        public string EncryptionIvString => Convert.ToBase64String(_iv);

        /// <summary>
        /// Gets the encryption key.
        /// </summary>
        public byte[] EncryptionKey => _key;

        /// <summary>
        /// Gets the initialization key.
        /// </summary>
        public byte[] EncryptionIv => _iv;

        public AeStoPhpCryptography()
        {
            _key = new byte[256 / 8];
            _iv = new byte[128 / 8];

            GenerateRandomKeys();
        }

        /*
                public AeStoPhpCryptography(string key, string iv)
                {
                    _key = Convert.FromBase64String(key);
                    _iv = Convert.FromBase64String(iv);

                    if (_key.Length * 8 != 256)
                        throw new Exception("The Key must be exactally 256 bits long!");
                    if (_iv.Length * 8 != 128)
                        throw new Exception("The IV must be exactally 128 bits long!");
                }
        */

        /// <summary>
        /// Generate the cryptographically secure random 256 bit Key and 128 bit IV for the AES algorithm.
        /// </summary>
        public void GenerateRandomKeys()
        {
            var random = new RNGCryptoServiceProvider();
            random.GetBytes(_key);
            random.GetBytes(_iv);
        }

        /// <summary>
        /// Encrypt a message and get the encrypted message in a URL safe form of base64.
        /// </summary>
        /// <param name="plainText">The message to encrypt.</param>
        public string Encrypt(string plainText)
        {
            return Utility.ToUrlSafeBase64(Encrypt2(plainText));
        }

        /// <summary>
        /// Encrypt a message using AES.
        /// </summary>
        /// <param name="plainText">The message to encrypt.</param>
        private byte[] Encrypt2(string plainText)
        {
            try
            {
                var aes = new RijndaelManaged
                {
                    Padding = PaddingMode.PKCS7,
                    Mode = CipherMode.CBC,
                    KeySize = 256,
                    Key = _key,
                    IV = _iv
                };

                var encryptor = aes.CreateEncryptor(aes.Key, aes.IV);

                var msEncrypt = new MemoryStream();
                var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write);
                var swEncrypt = new StreamWriter(csEncrypt);

                swEncrypt.Write(plainText);

                swEncrypt.Close();
                csEncrypt.Close();
                aes.Clear();

                return msEncrypt.ToArray();
            }
            catch (Exception ex)
            {
                throw new CryptographicException("Problem trying to encrypt.", ex);
            }
        }

        /// <summary>
        /// Decrypt a message that is in a url safe base64 encoded string.
        /// </summary>
        /// <param name="cipherText">The string to decrypt.</param>
        public string Decrypt(string cipherText)
        {
            return Decrypt2(Utility.FromUrlSafeBase64(cipherText), cipherText);
        }

        /// <summary>
        /// Decrypt a message that was AES encrypted.
        /// </summary>
        /// <param name="cipherText">The string to decrypt.</param>
        /// <param name="text"></param>
        private string Decrypt2(byte[] cipherText, string text)
        {
            try
            {
                var aes = new RijndaelManaged
                {
                    Padding = PaddingMode.PKCS7,
                    Mode = CipherMode.CBC,
                    KeySize = 256,
                    Key = _key,
                    IV = _iv
                };

                var decryptor = aes.CreateDecryptor(aes.Key, aes.IV);

                var msDecrypt = new MemoryStream(cipherText);
                var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
                var srDecrypt = new StreamReader(csDecrypt);

                var plaintext = srDecrypt.ReadToEnd();

                srDecrypt.Close();
                csDecrypt.Close();
                msDecrypt.Close();
                aes.Clear();

                return plaintext;
            }
            catch (Exception ex)
            {
                Form1.Log.Error("{0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber + ";" + _key + ";" + _iv + ":" + text, ex);
                throw new CryptographicException("There seems to be a problem while communicating with ubuyfirst.com. Please, try again.", ex);
            }
        }
    }
}
