﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using uBuyFirst.Prefs;

namespace uBuyFirst
{
    public partial class FormIgnoreSellers : DevExpress.XtraBars.Ribbon.RibbonForm
    {
        private bool _sortAscending = true; // This field tracks the sorting order

        public FormIgnoreSellers()
        {
            InitializeComponent();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            UserSettings.BlockedSellers = new HashSet<string>(txtBlockedSellers.Lines.Select(word => word.Trim()).Where(word => !string.IsNullOrWhiteSpace(word)).Distinct());
            DialogResult = DialogResult.OK;
            Close();
        }

        private void FormIgnoreSellers_Load(object sender, EventArgs e)
        {
            txtBlockedSellers.Lines = UserSettings.BlockedSellers.ToArray();
        }

        private void btnCancel_Click(object sender, EventArgs e)
        {
            Close();
        }

        private void btnSort_Click(object sender, EventArgs e)
        {
            var filteredLines = txtBlockedSellers.Lines.Select(word => word.Trim()).Where(word => !string.IsNullOrWhiteSpace(word)).Distinct();

            if (_sortAscending)
            {
                txtBlockedSellers.Lines = filteredLines.OrderBy(word => word).ToArray();
                btnSort.Text = "Sort ▼";
            }
            else
            {
                txtBlockedSellers.Lines = filteredLines.OrderByDescending(word => word).ToArray();
                btnSort.Text = "Sort ▲";
            }

            _sortAscending = !_sortAscending;
        }
    }
}

