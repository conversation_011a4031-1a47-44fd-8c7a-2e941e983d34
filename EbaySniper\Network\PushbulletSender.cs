﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using System.Text;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Timers;
using System.Xml.Serialization;
using PushbulletSharp;
using PushbulletSharp.Models.Requests;
using PushbulletSharp.Models.Responses;
using uBuyFirst.Data;
using uBuyFirst.Prefs;
using uBuyFirst.Telegram;
using uBuyFirst.Tools;
using uBuyFirst.Views;
using Timer = System.Timers.Timer;

namespace uBuyFirst.Network
{
    [Obfuscation(Exclude = true)]
    public class PushbulletSender : IDisposable
    {
        [XmlIgnore]
        public PushbulletClient PbClient;

        [XmlIgnore]
        private Timer _noteTimer;

        [XmlIgnore]
        private readonly List<string> _bodyRows;

        public string uBuyFirstIden;

        public PushbulletSender()
        {
            _bodyRows = new List<string>();
        }

        public List<string> SeenPushes { get; set; }
        public string Token { get; set; }
        public string BodyTemplate { get; set; }
        public string TitleTemplate { get; set; }
        public bool SendCheckoutUrl { get; set; }
        public bool Enabled { get; set; }
        public List<string> TitleColumns { get; set; }
        public List<string> BodyColumns { get; set; }
        public string ActiveIden { get; set; }
        public string ActiveNickname { get; set; }
        public int CombinePushes { get; set; }

        public void PushLink(DataRow row)
        {
            try
            {
                var push = new PushLinkRequest();
                push.Title = TitleTemplate;
                push.Body = BodyTemplate;
                var d = (DataList)row["Blob"];
                foreach (var columnName in TitleColumns)
                {
                    push.Title = push.Title.Replace("{" + columnName + "}", GetRowValue(row, columnName, d));
                }

                if (string.IsNullOrWhiteSpace(push.Title))
                {
                    push.Title = "Title is empty";
                }

                foreach (var columnName in BodyColumns)
                {
                    push.Body = push.Body.Replace("{" + columnName + "}", GetRowValue(row, columnName, d));
                }

                if (string.IsNullOrWhiteSpace(push.Body))
                {
                    push.Title = "Body is empty.";
                }

                var urlType = "l";
                if (SendCheckoutUrl)
                {
                    urlType = "c";
                }

                push.Url = $"https://ubuyfirst.com/{urlType}";
                if (d.EBaySite.SiteID != "0")
                    push.Url += $"/{d.EBaySite.SiteID}";
                var itemIDLicenseKey = d.ItemID + "" + ProgramState.SerialNumberShort.Replace("-", "");
                push.Url += $"/{ItemIdShortener.ShortenItemID(itemIDLicenseKey)}";
                push.DeviceIden = ActiveIden;
                push.SourceDeviceIden = uBuyFirstIden;
                Task.Run(() =>
                {
                    try
                    {
                        var response = PbClient?.PushLink(push);
                        var pushReceivedEventHandlerArgs = new PushReceivedEventHandlerArgs(response);
                        PushReceivedEvent?.Invoke(PbClient, pushReceivedEventHandlerArgs);
                        Stat.PushBulletCounter++;
                    }
                    catch (Exception ex)
                    {
                        if (ex.Message.Contains("No valid access token provided"))
                        {
                            if (PbClient != null)
                                PbClient.AccessToken = "";
                            Enabled = false;
                            Token = "";
                        }
                    }
                }).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("PushLink: ", ex);
            }
        }

        private void NoteTimer_Elapsed(object sender, ElapsedEventArgs e)
        {
            SendNote();
        }

        public delegate void PushReceivedEventHandler(object sender, PushReceivedEventHandlerArgs args);

        public delegate void PushExceptionEventHandler(object sender, PushExceptionEventHandlerArgs args);

        public class PushReceivedEventHandlerArgs
        {
            public PushReceivedEventHandlerArgs(PushResponse pushResponse)
            {
                XRatelimitRemaining = pushResponse.XRatelimitRemaining;
                XRatelimitLimit = pushResponse.XRatelimitLimit;
                XRatelimitReset = pushResponse.XRatelimitReset;
            }

            public long XRatelimitLimit { get; set; }
            public long XRatelimitRemaining { get; set; }
            public long XRatelimitReset { get; set; }
        }

        public event PushReceivedEventHandler PushReceivedEvent;
        public event PushExceptionEventHandler PushExceptionEvent;

        private void SendNote()
        {
            var push = new PushNoteRequest();
            push.Title = _bodyRows.Count.ToString();
            push.Body = string.Join("\r\n\r\n", _bodyRows);
            _bodyRows.Clear();
            push.DeviceIden = ActiveIden;
            push.SourceDeviceIden = uBuyFirstIden;
            if (!string.IsNullOrEmpty(push.Body))
            {
                Task.Run(() =>
                {
                    try
                    {
                        var response = PbClient.PushNote(push);
                        var pushReceivedEventHandlerArgs = new PushReceivedEventHandlerArgs(response);
                        PushReceivedEvent?.Invoke(PbClient, pushReceivedEventHandlerArgs);
                        Stat.PushBulletCounter++;
                    }
                    catch (Exception ex)
                    {
                        if (ex.Message.Contains("No valid access token provided"))
                        {
                            PbClient.AccessToken = "";
                            Enabled = false;
                            Token = "";
                        }

                        if (ex.Message.Contains("Too Many Requests"))
                        {
                            PushExceptionEvent?.Invoke(PbClient, new PushExceptionEventHandlerArgs() { Message = "You have been ratelimited. Try again in an hour." });
                        }
                    }
                }).ConfigureAwait(false);
            }
        }

        public void PushLinksNote(DataRow row)
        {
            try
            {
                if (_noteTimer == null)
                {
                    _noteTimer = new Timer(10 * 1000);
                    _noteTimer.Start();
                    _noteTimer.Elapsed += NoteTimer_Elapsed;
                    _noteTimer.AutoReset = false;
                }

                if (_bodyRows.Count == 0)
                {
                    _noteTimer.Stop();
                    _noteTimer.Start();
                }

                var d = (DataList)row["Blob"];

                var itemlist = BodyTemplate + "\r\n";
                string url;
                var itemIDLicenseKey = d.ItemID + "" + ProgramState.SerialNumberShort.Replace("-", "");

                if (d.EBaySite.SiteID == "0")
                {
                    url = $"https://ubuyfirst.com/l/{ItemIdShortener.ShortenItemID(itemIDLicenseKey)}";
                }
                else
                {
                    url = $"https://ubuyfirst.com/l/{d.EBaySite.SiteID}/{ItemIdShortener.ShortenItemID(itemIDLicenseKey)}";
                }

                itemlist += url;
                foreach (var columnName in BodyColumns)
                {
                    itemlist = itemlist.Replace("{" + columnName + "}", GetRowValue(row, columnName, d));
                }

                _bodyRows.Add(itemlist);
                if (_bodyRows.Count >= 10)
                {
                    SendNote();
                }
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("PushLinksNote: ", ex);
            }
        }

        private static string GetRowValue(DataRow row, string columnName, DataList d)
        {
            switch (columnName)
            {
                case "Total Price":
                    return d.ItemPricing.GetTotalPrice(d.ItemShipping.FullSingleShippingPrice).FormatPrice();

                case "Item Price":
                    return d.ItemPricing.ItemPrice.FormatPrice();

                case "Auction Price":
                    return d.ItemPricing.AuctionPrice?.FormatPrice();

                case "Shipping":
                    return d.ItemShipping.FullSingleShippingPrice.FormatPrice();

                case "Ship Additional Item":
                    return d.ItemShipping.ShipAdditionalItem.FormatPrice();

                case "Found Time":
                    return d.FoundTime.ToString();

                case "Description":
                case "ItemID":
                    return "";
                default:
                    return row[columnName].ToString();
            }
        }

        public bool SetTitleBody(string titleTemplateStr, string bodyTemplateStr)
        {
            var titleColumns = RegexValues(titleTemplateStr, "{(.*?)}");
            var bodyColumns = RegexValues(bodyTemplateStr, "{(.*?)}");

            if (!ColumnsManager.CheckExistingColumns(titleColumns))
                return false;

            if (!ColumnsManager.CheckExistingColumns(bodyColumns))
                return false;

            TitleColumns = titleColumns;
            BodyColumns = bodyColumns;
            TitleTemplate = titleTemplateStr;
            BodyTemplate = bodyTemplateStr;
            return true;
        }

        private static List<string> RegexValues(string input, string regex)
        {
            var matchedStringList = new List<string>();
            if (input != null)
            {
                var matches = Regex.Matches(input, regex);
                matchedStringList.AddRange(from Match match in matches where match.Success select match.Groups[1].Value.Trim());
            }

            return matchedStringList;
        }

        public static void CreateDevice(string token)
        {
            try
            {
                byte[] buffer = Encoding.ASCII.GetBytes("nickname=uBuyFirst&type=windows&icon=desktop&manufacturer=uBuyFirst");
                HttpWebRequest request = (HttpWebRequest)WebRequest.Create("https://api.pushbullet.com/v2/devices");
                request.Headers.Add("Authorization", "Bearer " + token);
                request.Method = "POST";
                request.ContentType = "application/x-www-form-urlencoded";
                request.ContentLength = buffer.Length;
                Stream postData = request.GetRequestStream();
                postData.Write(buffer, 0, buffer.Length);
                postData.Close();

                // Get and return response
                HttpWebResponse response = (HttpWebResponse)request.GetResponse();
                var responseStream = response.GetResponseStream();
                if (responseStream != null)
                {
                    var answer = new StreamReader(responseStream);
                    answer.ReadToEnd();
                }
            }
            catch (Exception)
            {
            }
        }

        public void Dispose()
        {
            _noteTimer?.Dispose();
        }

        public class PushExceptionEventHandlerArgs
        {
            public string Message;
        }
    }
}
