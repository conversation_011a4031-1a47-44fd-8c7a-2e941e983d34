﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text;
using IO.Ably;
using uBuyFirst.Prefs;

namespace uBuyFirst.Network
{
    public class AblyLogger : ILoggerSink
    {
        public StringBuilder LogCache;

        static readonly Dictionary<LogLevel, ConsoleColor> s_colors = new Dictionary<LogLevel, ConsoleColor>()
        {
            { LogLevel.Error, ConsoleColor.Red },
            { LogLevel.Warning, ConsoleColor.Yellow },
            { LogLevel.Debug, ConsoleColor.Cyan },
        };

        private TextWriterTraceListener _textWriterTraceListener;

        public AblyLogger()
        {
            var logFilePath = Path.Combine("logs", "ably.log");
            if (File.Exists(logFilePath))
                File.Delete(logFilePath);
            LogCache = new StringBuilder();
            _textWriterTraceListener = new TextWriterTraceListener(logFilePath);
            //Debug.Listeners.Add(_textWriterTraceListener);

            //Debug.AutoFlush = true;

        }

        void ILoggerSink.LogEvent(LogLevel level, string message)
        {
            if (Debugger.IsAttached)
            {
                _textWriterTraceListener.WriteLine(message);
                _textWriterTraceListener.Flush();
                ConsoleEx.WriteLine(s_colors[level], "    " + message);
                LogCache.Append(message);
                LogCache.Append("\r\n");
                if (LogCache.Length > 10)
                {
                    var contents = LogCache.ToString();
                    LogCache.Clear();
                    //File.AppendAllText(Path.Combine("logs", "ably1.log"), contents);
                }
            }
            else
            {
                Form1.Log.Trace($"{ProgramState.UBFVersion},{ProgramState.HWID + ProgramState.SerialNumber} {message}");
            }
        }
    }
}