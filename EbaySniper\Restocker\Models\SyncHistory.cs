using System;

namespace uBuyFirst.Restocker.Models
{
    /// <summary>
    /// Represents a record of spreadsheet synchronization events
    /// </summary>
    public class SyncHistory
    {
        public int Id { get; set; }
        
        /// <summary>
        /// When the synchronization occurred
        /// </summary>
        public DateTime SyncDate { get; set; }
        
        /// <summary>
        /// Name of the file that was synchronized
        /// </summary>
        public string FileName { get; set; } = string.Empty;
        
        /// <summary>
        /// Number of new requirements added during sync
        /// </summary>
        public int RequirementsAdded { get; set; }
        
        /// <summary>
        /// Number of existing requirements updated during sync
        /// </summary>
        public int RequirementsUpdated { get; set; }
        
        /// <summary>
        /// Number of requirements deactivated during sync
        /// </summary>
        public int RequirementsDeactivated { get; set; }

        public SyncHistory()
        {
            SyncDate = DateTime.UtcNow;
        }
    }
}
