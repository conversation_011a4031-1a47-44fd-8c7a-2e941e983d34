﻿using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Text;
using System.Windows.Forms;
using uBuyFirst.Prefs;
using uBuyFirst.Tools;

namespace uBuyFirst
{
    public partial class FormReportDialog : Form
    {
        public FormReportDialog()
        {
            InitializeComponent();
        }

        [DllImport("wininet.dll", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern bool InternetGetCookieEx(string pchUrl, string pchCookieName, StringBuilder pchCookieData, ref uint pcchCookieData, int dwFlags, IntPtr lpReserved);

        const int InternetCookieHttponly = 0x00002000;

        public static string GetGlobalCookies(string uri)
        {
            uint datasize = 1024;
            StringBuilder cookieData = new StringBuilder((int) datasize);
            if (InternetGetCookieEx(uri, null, cookieData, ref datasize, InternetCookieHttponly, IntPtr.Zero) && cookieData.Length > 0)
            {
                return cookieData.ToString().Replace(';', ',');
            }
            else
            {
                return null;
            }
        }

        private void FormReportDialog_Load(object sender, EventArgs e)
        {
        }

        private void linkLabelWebsite_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            Process.Start("https://ubuyfirst.com");
        }

        private void linkLabelEmail_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            Process.Start("mailto:<EMAIL>");
        }

        private void button1_Click(object sender, EventArgs e)
        {
            ExM.CreateReport();
            Close();
        }

        private void FormReportDialog_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == 27)
            {
                Close();
            }
        }

        private void lblVisitWebsite_Click(object sender, EventArgs e)
        {

        }

        private void linkLabelLiveSupport_LinkClicked(object sender, LinkLabelLinkClickedEventArgs e)
        {
            Process.Start("https://ubuyfirst.com/uBuyFirst/livesupport.html");
        }
    }
}
