﻿using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Reflection;

namespace uBuyFirst.Prefs
{
    [Obfuscation(Exclude = true)]
    public static class ProgramState
    {
        public static bool InitialSearchCompleted;
        public static bool Isdebug;
        public static Stopwatch TotalRunningStopwatch;
        public static Stopwatch SearchingStopwatchGA4;
        public static string PublicIp="************";
        public static string HWID = "";
        public static string SerialNumber = "";
        public static string UBFVersion;
        public static Stopwatch Idlesw;
        public static string LastSelectedItemID;
        public static string ChromeUA = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36";
        public static string SerialNumberShort { get; set; } = "";
        public static List<string> ColumnsUsedInFilters { get; set; } = new();

        public static long UBuyFirstRedirectTimestamp = 0;
    }
}
