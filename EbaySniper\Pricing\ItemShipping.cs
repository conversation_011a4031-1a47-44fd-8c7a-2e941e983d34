﻿namespace uBuyFirst.Pricing
{
    public class ItemShipping
    {
        public ItemShipping()
        {
            BareSingleShippingPrice = new CurrencyAmount(0, "USD");
            FullSingleShippingPrice = new CurrencyAmount(0, "USD");
            ShipAdditionalItem = new CurrencyAmount(0, "USD");
        }

        public ParsingStatus ShippingStatusBrowseAPI;
        public ParsingStatus ShippingStatusTradingAPI;
        public CurrencyAmount FullSingleShippingPrice;
        public CurrencyAmount ShipAdditionalItem;
        public CurrencyAmount BareSingleShippingPrice;
        public double ShippingInsuranceCost;
        public double PackageHandlingCost;
        public double ImportCost;

        public CurrencyAmount GetQuantityShipPrice(int quantity, string ShippingType)
        {
            if (ShippingType == "CALCULATED" || ShippingType == "Calculated" || ShippingType == "CalculatedDomesticFlatInternational")
            {
                var calculatedShipping = (BareSingleShippingPrice.Value + ShippingInsuranceCost) * quantity + PackageHandlingCost;
                return new CurrencyAmount(calculatedShipping, BareSingleShippingPrice.Currency);
            }

            var flatRate = FullSingleShippingPrice;
            if (quantity > 0)
                flatRate.Value += (quantity - 1) * ShipAdditionalItem.Value;

            return flatRate;
        }

        public enum ParsingStatus
        {
            NotFetched,
            Fetching,
            Success,
            Fail
        }
    }
}