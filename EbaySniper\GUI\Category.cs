﻿using System.ComponentModel;

namespace uBuyFirst.GUI
{
    public class Category
    {
        public bool? LeafCategoryTreeNode { get; set; }
        public string CategoryID { get; set; }
        public string Name { get; set; }

        [Browsable(false)]
        public Categories Categories { get; set; }

        public Category(string name, string categoryID, bool? leafCategoryTreeNode)
        {
            LeafCategoryTreeNode = leafCategoryTreeNode;
            Name = name;
            CategoryID = categoryID;

            //Categories = new Categories();
        }

        public override string ToString()
        {
            return CategoryID;
        }
    }
}