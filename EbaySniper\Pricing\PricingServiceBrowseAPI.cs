﻿using System;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using BrowseAPI;
using uBuyFirst.Data;
using uBuyFirst.Tools;

namespace uBuyFirst.Pricing
{
    public class PricingServiceBrowseAPI
    {
        public void ParseItemShipping(ShippingOption[]? itemShippingOptions, DataList dataList)
        {
            try
            {
                var shippingOption = itemShippingOptions?.FirstOrDefault();
                if (shippingOption == null)
                {
                    dataList.ItemShipping.ShippingStatusBrowseAPI = ItemShipping.ParsingStatus.Fail;
                    dataList.SetShippingType("");
                }
                else
                {
                    ParseShippingPrice(shippingOption, dataList.ItemShipping);

                    var s = shippingOption.ShippingCostType;
                    if (s == null)
                    {
                        if (shippingOption.Type == "Standard Shipping")
                        {
                            s = "Flat";
                        }
                        else
                        {
                            s = "";
                        }
                    }

                    dataList.SetShippingType(s);
                    if (shippingOption?.MaxEstimatedDeliveryDate != null)
                    {
                        var maxDeliveryDate = DateTime.Parse(shippingOption.MaxEstimatedDeliveryDate, CultureInfo.InvariantCulture).ToUniversalTime();
                        var maxShippingDays = (int)Math.Floor((maxDeliveryDate - DateTime.UtcNow).TotalDays);
                        dataList.ShippingDays = maxShippingDays;
                    }
                }
            }
            catch (Exception e)
            {
                Debug.WriteLine("GetItemPricing:" + e.Message);
            }
        }

        public static void ParseItemPricing(global::BrowseAPI.Item item, ItemPricing itemPricing)
        {

            itemPricing.ItemPrice = GetBinPrice(item.BuyingOptions, item.Price);
            itemPricing.AuctionPrice = GetAuctionPrice(item.BuyingOptions, item.CurrentBidPrice);
        }

        private static void ParseShippingPrice(ShippingOption shippingOption, ItemShipping itemShipping)
        {
            try
            {
                itemShipping.PackageHandlingCost = 0;
                itemShipping.ShippingInsuranceCost = 0;
                itemShipping.ImportCost = shippingOption.ImportCharges.GetCurrencyAmount().Value;
                itemShipping.BareSingleShippingPrice = shippingOption.ShippingCost.GetCurrencyAmount();
                itemShipping.FullSingleShippingPrice = shippingOption.ShippingCost.GetCurrencyAmount();
                itemShipping.FullSingleShippingPrice.Value += itemShipping.ImportCost;
                itemShipping.ShipAdditionalItem = shippingOption.AdditionalShippingCostPerUnit.GetCurrencyAmount();
                itemShipping.ShippingStatusBrowseAPI = ItemShipping.ParsingStatus.Success;
            }
            catch (Exception)
            {
                itemShipping.ShippingStatusBrowseAPI = ItemShipping.ParsingStatus.Fail;
                if (Debugger.IsAttached)
                    throw;
            }
        }

        public static CurrencyAmount GetBinPrice(string[] buyingOptions, ConvertedAmount price)
        {
            var itemPrice = new CurrencyAmount(0, "USD");

            if (buyingOptions.Contains("Buy it Now") || buyingOptions.Contains("CLASSIFIED_AD"))
            {
                itemPrice = price.GetCurrencyAmount();
            }

            return itemPrice;
        }

        public static CurrencyAmount GetAuctionPrice(string[] buyingOptions, ConvertedAmount price)
        {
            var itemPrice = new CurrencyAmount(0, "USD");

            if (buyingOptions.Contains("Auction") && price != null)
            {
                itemPrice = price.GetCurrencyAmount();
            }

            return itemPrice;
        }
    }
}
