﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using MQTTnet;
using MQTTnet.Client;
using MQTTnet.Extensions.ManagedClient;
using MQTTnet.Formatter;
using MQTTnet.Packets;
using uBuyFirst.Network;
using uBuyFirst.Other;
using uBuyFirst.Prefs;
using MQTTnet.Extensions.WebSocket4Net;
using MQTTnet.Protocol;
using MQTTnet.Server;
using uBuyFirst.License;

namespace uBuyFirst.MQTT
{
    internal class MQTTManager
    {
        private IManagedMqttClient? _mqttClient1;
        private IManagedMqttClient? _mqttClient23;
        private SeerUser _seerUser;
        private string _searchesTopic = "";
        private string _resultsTopic;
        private readonly Action<MqttApplicationMessage> _postMqqtItemToResultsGrid;
        private ArraySegment<byte> _serializedSeerUser;

        public MQTTManager(Action<MqttApplicationMessage> postMqqtItemToResultsGrid)
        {
            _postMqqtItemToResultsGrid = postMqqtItemToResultsGrid;
        }

        public async Task Connect(SeerUser user)
        {
            _seerUser = user;
            _seerUser.StartTime = DateTimeOffset.UtcNow.DateTime.ToString("s");
            var suffix = DateTimeOffset.UtcNow.ToUnixTimeSeconds();
            var mqttClientID = $"{user.HWID}|{suffix}";
            var encodedMqttClientID = EncodeMQTTTopic(mqttClientID);
            _searchesTopic = $"s/{encodedMqttClientID}";
            _resultsTopic = $"r/{encodedMqttClientID}";

            var uncompressedString = Newtonsoft.Json.JsonConvert.SerializeObject(_seerUser);
            var compressedString = "packed:" + Serializator.CompressString(uncompressedString);
            var payloadBytes = Encoding.UTF8.GetBytes(compressedString);
            _serializedSeerUser = new ArraySegment<byte>(payloadBytes);

            if (_mqttClient1 == null)
            {
                _mqttClient1 = CreateClient();
                _mqttClient1.ConnectedAsync += MqttClient_ConnectedAsync1;
                _mqttClient1.ConnectingFailedAsync += _mqttClient1_ConnectingFailedAsync;
            }

            //if (_mqttClient2 == null)
            //{
            //    _mqttClient2 = CreateClient();
            //    _mqttClient2.ConnectedAsync += MqttClient_ConnectedAsync2;
            //}

            await StartClient(ConnectionConfig.MQTTEndPoint1, encodedMqttClientID, _mqttClient1);
            //await StartClient(ConnectionConfig.MQTTEndPoint2, encodedHWID, _mqttClient2);
        }

        private Task _mqttClient1_ConnectingFailedAsync(ConnectingFailedEventArgs arg)
        {
            return Task.CompletedTask;
        }

        private static string EncodeMQTTTopic(string userHWID)
        {
            var encodedHWID = userHWID.Replace("%", "_percent_");
            encodedHWID = encodedHWID.Replace("/", "_slash_");
            encodedHWID = encodedHWID.Replace("+", "_plus_");
            return encodedHWID;
        }

        private static string DecodeMQTTTopic(string encodedHWID)
        {
            var decodedHWID = encodedHWID.Replace("_percent_", "%");
            decodedHWID = decodedHWID.Replace("_slash_", "/");
            decodedHWID = decodedHWID.Replace("_plus_", "+");
            return decodedHWID;
        }

        private IManagedMqttClient CreateClient()
        {
            var factory = GetMqttFactory();
            var mqttClient = factory.CreateManagedMqttClient();
            mqttClient.ApplicationMessageReceivedAsync += e =>
            {
                _postMqqtItemToResultsGrid(e.ApplicationMessage);
                return Task.CompletedTask;
            };
            return mqttClient;
        }

        private static MqttFactory GetMqttFactory()
        {
            MqttFactory factory;
            var isWindows7 = Environment.OSVersion.Version.Major == 6 && Environment.OSVersion.Version.Minor < 2 || Environment.OSVersion.Version.Major < 6;
            if (isWindows7)
            {
                factory = new MqttFactory().UseWebSocket4Net();
            }
            else
            {
                factory = new MqttFactory();
            }

            return factory;
        }

        private async Task StartClient(string mqttServer, string userHWID, IManagedMqttClient mqttClient)
        {
            var mqttClientTlsOptions = new MqttClientTlsOptions
            {
                UseTls = true,
                CertificateValidationHandler = args =>
                {
                    if (args.Sender is not HttpWebRequest request)
                    {
                        return true;
                    }

                    var host = request.Host;
                    if (host.EndsWith("ubuyfirst.net"))
                    {
                        if (ProgramState.SerialNumber.StartsWith("ROMA"))
                            return true;
                        
                        if (args.Chain.ChainElements.Count > 0)
                        {
                            var publicKeyString = args.Chain.ChainElements[1].Certificate.GetPublicKeyString();
                            var isValid = ConnectionConfig.CA.Any(ca => publicKeyString != null && ca.Contains(publicKeyString));
                            return isValid;
                        }
                    }

                    return true;
                }
            };
            var options = new MqttClientOptionsBuilder()
                .WithTlsOptions(mqttClientTlsOptions)
                .WithProtocolVersion(MqttProtocolVersion.V500)
                .WithClientId(userHWID)
                .WithCleanSession()
                .WithWebSocketServer(builder => builder.WithUri(mqttServer))
                .WithWillTopic(_searchesTopic)
                .WithWillPayload("")
                .WithWillRetain()
                .WithWillUserProperty("T", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString())
                .WithWillQualityOfServiceLevel(MqttQualityOfServiceLevel.AtLeastOnce)
                .Build();

            var managedMqttClientOptions = new ManagedMqttClientOptionsBuilder()
                .WithMaxPendingMessages(1)
                .WithAutoReconnectDelay(TimeSpan.FromSeconds(new Random().Next(30, 60)))
                .WithPendingMessagesOverflowStrategy(MqttPendingMessagesOverflowStrategy.DropOldestQueuedMessage)
                .WithClientOptions(options)
                .Build();
            try
            {
                if (!mqttClient.IsConnected)
                {
                    await mqttClient.StartAsync(managedMqttClientOptions);
                }
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
            }
        }

        private Task MqttClient_ConnectedAsync1(MqttClientConnectedEventArgs arg)
        {
            var message = new MqttApplicationMessageBuilder()
                .WithTopic(_searchesTopic)
                .WithRetainFlag()
                .WithPayloadSegment(_serializedSeerUser)
                .WithUserProperty("T", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString())
                .WithUserProperty("F", LicenseUtility.GetHardwareFingerprint())
                .WithUserProperty("HWID", ProgramState.HWID)
                .Build();
            _mqttClient1?.SubscribeAsync(new List<MqttTopicFilter> { new() { Topic = _resultsTopic } });
            _mqttClient1?.EnqueueAsync(message);
            return Task.CompletedTask;
        }

        //private Task MqttClient_ConnectedAsync2(MqttClientConnectedEventArgs arg)
        //{
        //    var uncompressedString = Newtonsoft.Json.JsonConvert.SerializeObject(_seerUser);
        //    var compressedString = "packed:" + Serializator.CompressString(uncompressedString);
        //    var payloadBytes = Encoding.UTF8.GetBytes(compressedString);
        //    var payloadSegment = new ArraySegment<byte>(payloadBytes);
        //    var message = new MqttApplicationMessageBuilder()
        //        .WithTopic(_searchesTopic)
        //        .WithRetainFlag()
        //        .WithUserProperty("T", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString())
        //        .WithPayloadSegment(payloadSegment)
        //        .Build();
        //    _mqttClient2?.SubscribeAsync(new List<MqttTopicFilter> { new() { Topic = _resultsTopic } });
        //    _mqttClient2?.EnqueueAsync(message);
        //    return Task.CompletedTask;
        //}

        public async Task Disconnect()
        {
            if (string.IsNullOrWhiteSpace(_searchesTopic))
            {
                return;
            }

            var payloadBytes = Encoding.UTF8.GetBytes("");
            var payloadSegment = new ArraySegment<byte>(payloadBytes);
            var message = new MqttApplicationMessageBuilder()
                .WithTopic(_searchesTopic)
                .WithRetainFlag()
                .WithUserProperty("T", DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString())
                .WithPayloadSegment(payloadSegment)
                .Build();
            if (_mqttClient1 is { IsStarted: true })
            {
                await _mqttClient1.EnqueueAsync(message).ConfigureAwait(true);
                await Task.Delay(1500);
                await _mqttClient1.StopAsync();
                //await _mqttClient1.EnqueueAsync(message).ContinueWith(async (a) => { await _mqttClient1.StopAsync(); });
            }

            //if (_mqttClient2 is { IsStarted: true })
            //{
            //    await _mqttClient2.EnqueueAsync(message).ContinueWith(async (a) => { await _mqttClient2.StopAsync(); });
            //    //await _mqttClient2.EnqueueAsync(message);
            //    //await _mqttClient2.StopAsync();
            //}
        }

        public string GetStatus()
        {
            var isStarted1 = _mqttClient1 is { IsStarted: true } ? "Started" : "Not started";
            var isConnected1 = _mqttClient1 is { IsConnected: true } ? "Connected" : "Disconnected";
            //var isStarted2 = _mqttClient2 is { IsStarted: true } ? "Started" : "Not started";
            //var isConnected2 = _mqttClient2 is { IsConnected: true } ? "Connected" : "Disconnected";
            return $"{isStarted1}, {isConnected1} "
                //+ $"| {isStarted2}, {isConnected2}"
                ;

        }
    }
}
