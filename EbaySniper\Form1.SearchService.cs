﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Windows.Forms;
using uBuyFirst.BrowseAPI;
using uBuyFirst.Filters;
using uBuyFirst.Prefs;
using uBuyFirst.Purchasing;
using uBuyFirst.Purchasing.Cookies;
using uBuyFirst.Search;
using uBuyFirst.Search.FIA;
using uBuyFirst.Stats;
using uBuyFirst.Tools;

namespace uBuyFirst
{
    public partial class Form1
    {
        private async void MainStart(IEnumerable<Keyword2Find> kw2FindList)
        {
            try
            {
                if (ConnectionConfig.CheckoutEnabled)
                {
                    if (ProgramState.SerialNumber.StartsWith("3A3E") || Debugger.IsAttached)
                    {
                        CookieManager.ReadCookiesFirefox(new[] { ".ebay.com" });
                    }
                }

                ProgramState.ColumnsUsedInFilters = XFilterManager.ExtractColumns();
                var enabledKeywords = kw2FindList.Where(kw => kw.KeywordEnabled == CheckState.Checked).ToList();
                enabledKeywords = KeywordTools.ApplyKeywordLimit(enabledKeywords);
                treeList1.RefreshDataSource();
                for (var i = 0; i < treeList1.Nodes.Count; i++)
                {
                    SetNodeChecked(treeList1.Nodes[i]);
                    for (var j = 0; j < treeList1.Nodes[i].Nodes.Count; j++)
                    {
                        SetNodeChecked(treeList1.Nodes[i].Nodes[j]);
                    }
                }

                ProgramState.InitialSearchCompleted = false;
                ProgramState.TotalRunningStopwatch.Restart();
                ProgramState.SearchingStopwatchGA4.Restart();
                ProgramState.Idlesw.Restart();
                CreditCardService.ActiveSessionCount = 0;

                Stat._errorsCount = 0;
                Stat.TotalItemsProcessed = 0;
                Stat.ItemsFoundCounter = 0;
                RequestQueueManager.Instance.ResetGeneralItemQueueCount();
                Stat.GetItemReqCount = 0;
                Stat.OtherImagesReqCount = 0;
                Stat.StatusUpdateCounter = 0;
                Stat.OutOfStockCounter = 0;
                Stat.AvatarReqCount = 0;
                Stat.FindReqCount = 0;
                Stat.GetItemErrors = 0;
                FiaService.FindItemsAdvancedFails = 0;
                Analytics.AddEvent("", "SearchStarted", 1);

                FiaBuilder.CreateSearchRequests(enabledKeywords);

                _searchService = new SearchService(SearchConfigManager.Instance.GetConfigForSerialization(), LicenseUtility, _viewReporter, _synchronizationContext, this.HandleNewItem);

                _searchService.Running = true;

                StartPushClients();

                await _searchService.PerformInitialSearch(enabledKeywords);
                await _searchService.WaitForInitialSearch2();

                if (!_searchService.Running)
                    return;

                ProgramState.InitialSearchCompleted = true;
                ProgramState.TotalRunningStopwatch.Restart();
                ProgramState.Idlesw.Start();

                Stat.ResetStatsAfterInitialSearch();
                if (GoogleAnalytics != null)
                    await GoogleAnalytics?.ReportProgramConfig(enabledKeywords);

                Analytics.SendPanelList(dockManager1.Panels);

                _searchService.SearchLoopV2(enabledKeywords);
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("Main start3: ", ex);
            }
        }
    }
}
