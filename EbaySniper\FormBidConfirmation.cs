﻿using System;
using System.Drawing;
using System.Windows.Forms;

namespace uBuyFirst
{
    public partial class FormBidConfirmation : DevExpress.XtraEditors.XtraForm
    {
        private static FormStartPosition _formStartPosition = FormStartPosition.CenterParent;
        private static Point _location;
        private static Size _size = new Size();

        public FormBidConfirmation()
        {
            InitializeComponent();
            StartPosition = _formStartPosition;
            if (StartPosition == FormStartPosition.Manual)
                Location = _location;

            if (_size.Height != 0 & _size.Width != 0)
                Size = _size;
        }

        private void buttonYes_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.Yes;
        }

        private void buttonNo_Click(object sender, EventArgs e)
        {
            DialogResult = DialogResult.No;
        }

        private void FormBidConfirmation_ResizeEnd(object sender, EventArgs e)
        {
            _formStartPosition = FormStartPosition.Manual;
            _location = Location;
            _size = Size;
        }
    }
}