﻿using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Text.RegularExpressions;
using DevExpress.XtraBars.Alerter;
using uBuyFirst.Data;
using uBuyFirst.Tools;
using uBuyFirst.Views;

[assembly:
    Obfuscation(Exclude = false,
        Feature = "preset(minimum);" +
                  "+anti debug;" +
                  "+anti dump;" +
                  "+anti ildasm;" +
                  "+anti tamper(key=dynamic);" +
                  "+constants;" +
                  "+ctrl flow;" +
                  "+invalid metadata;" +
                  "+ref proxy;" +
                  "-rename;")]
[assembly: Obfuscation(Exclude = false, Feature = "generate debug symbol: true")]

namespace uBuyFirst
{
    public class AlertOptionsClass
    {
        public static AlertOptionsClass AlertOptions;
        public int AutoFormDelay;
        public bool AutoHeight;
        public List<string> BodyColumns;
        public string BodyTemplate;
        public bool Enabled;
        public int FontDelta;
        public AlertFormDisplaySpeed FormDisplaySpeed;
        public AlertFormLocation FormLocation;
        public int FormMaxCount;
        public AlertFormShowingEffect FormShowingEffect;
        public int Height;
        public bool ShowImage;
        public List<string> TitleColumns;
        public string TitleTemplate;
        public int Width;
        public static AlertControl AlertControl1;

        public void SetAlertControlSettings(AlertControl alertControl)
        {
            if (TitleTemplate == null)
            {
                TitleTemplate = "<b>{Item Price} - {Title}</b>";
                BodyTemplate = "<i>Condition:</i> {Condition}\n {Condition Description}\n<i>Seller:</i> {Feedback Rating}% - {Feedback Score}";
                AutoFormDelay = alertControl.AutoFormDelay;
                FormDisplaySpeed = alertControl.FormDisplaySpeed;
                FormLocation = alertControl.FormLocation;
                FormShowingEffect = alertControl.FormShowingEffect;
                FormMaxCount = alertControl.FormMaxCount;
                AutoHeight = alertControl.AutoHeight;
                AlertOptions.SetTitleBody(TitleTemplate, BodyTemplate);
                Width = 400;
                Height = 150;
                FormMaxCount = 10;
                FontDelta = 5;
            }

            alertControl.AutoFormDelay = AutoFormDelay;
            alertControl.FormDisplaySpeed = FormDisplaySpeed;
            alertControl.FormLocation = FormLocation;
            alertControl.FormShowingEffect = FormShowingEffect;
            alertControl.FormMaxCount = FormMaxCount;
            alertControl.AutoHeight = AutoHeight;
            alertControl.AppearanceCaption.FontSizeDelta = FontDelta - 4;
            alertControl.AppearanceText.FontSizeDelta = FontDelta - 5;
        }

        public bool SetTitleBody(string titleTemplateStr, string bodyTemplateStr)
        {
            var titleColumns = RegexValues(titleTemplateStr, "{(.*?)}");
            var bodyColumns = RegexValues(bodyTemplateStr, "{(.*?)}");

            if (!ColumnsManager.CheckExistingColumns(titleColumns))
                return false;

            if (!ColumnsManager.CheckExistingColumns(bodyColumns))
                return false;

            TitleColumns = titleColumns;
            BodyColumns = bodyColumns;
            TitleTemplate = titleTemplateStr;
            BodyTemplate = bodyTemplateStr;
            return true;
        }

        private static List<string> RegexValues(string input, string regex)
        {
            var matchedStringList = new List<string>();
            if (input != null)
            {
                var matches = Regex.Matches(input, regex);
                matchedStringList.AddRange(from Match match in matches where match.Success select match.Groups[1].Value.Trim());
            }

            return matchedStringList;
        }

        public static string GetRowValue(DataRow row, string columnName, DataList d)
        {
            switch (columnName)
            {
                case "Total Price":
                    return d.ItemPricing.GetTotalPrice(d.ItemShipping.FullSingleShippingPrice).FormatPrice();

                case "Item Price":
                    return d.ItemPricing.ItemPrice.FormatPrice();

                case "Auction Price":
                    return d.ItemPricing.AuctionPrice?.FormatPrice();

                case "Shipping":
                    return d.ItemShipping.FullSingleShippingPrice.FormatPrice();

                case "Ship Additional Item":
                    return d.ItemShipping.ShipAdditionalItem.FormatPrice();

                case "Found Time":
                    return d.FoundTime.ToString();

                case "Description":
                case "ItemID":
                    return "";

                default:
                    return row[columnName].ToString();
            }
        }
    }
}