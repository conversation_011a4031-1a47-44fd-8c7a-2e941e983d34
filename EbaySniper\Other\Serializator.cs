﻿using System;
using System.Collections.Concurrent;
using System.IO;
using System.IO.Compression;
using System.Runtime.Serialization;
using System.Runtime.Serialization.Formatters.Binary;
using System.Security.Cryptography;
using System.Text;
using System.Windows.Forms;
using System.Xml;
using System.Xml.Serialization;
using Newtonsoft.Json;
using uBuyFirst.Tools;

namespace uBuyFirst.Other
{
    public static class Serializator
    {
        /// <summary>
        /// Takes a byte array and deserializes it back to its type of <see cref="T"/>
        /// </summary>
        /// <typeparam name="T">The Type to deserialize to</typeparam>
        /// <param name="serializedType">The object as a byte array</param>
        /// <returns>The deserialized type</returns>
        public static T BinaryDeserializeObject<T>(byte[] serializedType)
        {
            if (serializedType == null)
                throw new ArgumentNullException(nameof(serializedType));

            if (serializedType.Length.Equals(0))
                throw new ArgumentException("serializedType");

            T deserializedObject;

            using (MemoryStream memoryStream = new MemoryStream(serializedType))
            {
                BinaryFormatter deserializer = new BinaryFormatter();
                deserializer.AssemblyFormat = System.Runtime.Serialization.Formatters.FormatterAssemblyStyle.Simple;

                deserializedObject = (T)deserializer.Deserialize(memoryStream);
            }

            return deserializedObject;
        }

        /// <summary>
        /// Takes an object and serializes it into a byte array
        /// </summary>
        /// <param name="objectToSerialize">The object to serialize</param>
        /// <returns>The object as a <see cref="byte"/> array</returns>
        public static byte[] BinarySerializeObject(object objectToSerialize)
        {
            if (objectToSerialize == null)
                throw new ArgumentNullException(nameof(objectToSerialize));

            byte[] serializedObject;

            using (MemoryStream stream = new MemoryStream())
            {
                BinaryFormatter formatter = new BinaryFormatter();
                formatter.Serialize(stream, objectToSerialize);
                serializedObject = stream.ToArray();
            }

            return serializedObject;
        }

        public static string SerializeToString(object obj)
        {
            byte[] byteArray = BinarySerializeObject(obj);
            return Convert.ToBase64String(byteArray);
        }

        public static T DeSerializeFromString<T>(string input)
        {
            byte[] byteArray = Convert.FromBase64String(input);
            return BinaryDeserializeObject<T>(byteArray);
        }

        public static MemoryStream Object2Stream(XmlSerializer xml, object obj)
        {
            var memStream = new MemoryStream();
            xml.Serialize(memStream, obj);
            memStream.Flush();
            memStream.Position = 0;
            return memStream;
        }

        public static T Stream2Object<T>(byte[] stream)
        {
            var xmlSerializer = new XmlSerializer(typeof(T));
            T deserialized = default;
            if (stream != null)
            {
                using (var memoryStream = new MemoryStream())
                {
                    memoryStream.Write(stream, 0, stream.Length);
                    memoryStream.Position = 0;
                    var sr = new StreamReader(memoryStream);

                    string xml = sr.ReadToEnd();
                    if (xml.Length > 10)
                    {
                        xmlSerializer.Deserialize(new StringReader(xml));
                        deserialized = (T)xmlSerializer.Deserialize(new StringReader(xml));
                    }
                }
            }

            return deserialized;
        }

        public static T FromXml<T>(string rootName, string fileName) where T : class, new()
        {
            XmlSerializer serializer = new XmlSerializer(typeof(T), new XmlRootAttribute(rootName));
            TextReader reader = new StreamReader(fileName);
            return serializer.Deserialize(reader) as T;
        }

        public static string Encrypt(string toEncrypt, bool useHasing)
        {
            if (toEncrypt != null)
            {
                byte[] keyArray;
                byte[] toEncryptArray = Encoding.UTF8.GetBytes(toEncrypt);
                const string key = "3243243346578543211234";
                if (useHasing)
                {
                    var hashmd5 = new MD5CryptoServiceProvider();
                    keyArray = hashmd5.ComputeHash(Encoding.UTF8.GetBytes(key));
                    hashmd5.Clear();
                }
                else
                {
                    keyArray = Encoding.UTF8.GetBytes(key);
                }

                var tDes = new TripleDESCryptoServiceProvider
                {
                    Key = keyArray,
                    Mode = CipherMode.ECB,
                    Padding = PaddingMode.PKCS7
                };
                ICryptoTransform cTransform = tDes.CreateEncryptor();
                byte[] resultArray = cTransform.TransformFinalBlock(toEncryptArray, 0, toEncryptArray.Length);
                tDes.Clear();
                return Convert.ToBase64String(resultArray, 0, resultArray.Length);
            }

            return "";
        }

        public static string Decrypt(string cypherString, bool useHasing)
        {
            try
            {
                byte[] keyArray;
                if (cypherString == null)
                    return "";

                byte[] toDecryptArray = Convert.FromBase64String(cypherString);
                //byte[] toEncryptArray = Convert.FromBase64String(cypherString);
                //System.Configuration.AppSettingsReader settingReader = new     AppSettingsReader();
                const string key = "3243243346578543211234";
                if (useHasing)
                {
                    var hashmd = new MD5CryptoServiceProvider();
                    keyArray = hashmd.ComputeHash(Encoding.UTF8.GetBytes(key));
                    hashmd.Clear();
                }
                else
                {
                    keyArray = Encoding.UTF8.GetBytes(key);
                }

                var tDes = new TripleDESCryptoServiceProvider
                {
                    Key = keyArray,
                    Mode = CipherMode.ECB,
                    Padding = PaddingMode.PKCS7
                };
                ICryptoTransform cTransform = tDes.CreateDecryptor();
                try
                {
                    byte[] resultArray = cTransform.TransformFinalBlock(toDecryptArray, 0, toDecryptArray.Length);
                    tDes.Clear();
                    return Encoding.UTF8.GetString(resultArray, 0, resultArray.Length);
                }
                catch (Exception ex)
                {
                    MessageBox.Show(@"Decrypting:", ex.Message);
                }
            }
            catch (Exception)
            {
                return "";
            }

            return "";
        }

        public static Settings DeserializeSettings(string configPath)
        {
            var ser = new XmlSerializer(typeof(Settings));
            TextReader reader = new StreamReader(configPath);
            var settings = (Settings)ser.Deserialize(reader);
            reader.Close();
            return settings;
        }

        public static string ObjectToXml(object o)
        {
            StringWriter sw = new StringWriter();
            XmlTextWriter tw = null;
            try
            {
                XmlSerializer serializer = new XmlSerializer(o.GetType());
                tw = new XmlTextWriter(sw);
                serializer.Serialize(tw, o);
            }
            catch (Exception)
            {
                //Handle Exception Code
            }
            finally
            {
                sw.Close();
                tw?.Close();
            }

            return sw.ToString();
        }

        public static object Xml2Object(string xml, Type objectType)
        {
            StringReader strReader = null;
            XmlTextReader xmlReader = null;
            object obj = null;
            try
            {
                strReader = new StringReader(xml);
                var serializer = new XmlSerializer(objectType);
                xmlReader = new XmlTextReader(strReader);
                obj = serializer.Deserialize(xmlReader);
            }
            catch (Exception)
            {
                //Handle Exception Code
            }
            finally
            {
                xmlReader?.Close();
                strReader?.Close();
            }

            return obj;
        }

        public static string CompressObject(object data)
        {
            IFormatter formatter = new BinaryFormatter();
            using (var ms = new MemoryStream())
            {
                formatter.Serialize(ms, data);
                ms.Seek(0, SeekOrigin.Begin);
                MemoryStream compressedStream = new MemoryStream();
                using (var gZipStream = new GZipStream(compressedStream, CompressionMode.Compress, true))
                {
                    ms.CopyTo(gZipStream);
                }

                compressedStream.Seek(0, SeekOrigin.Begin);
                var compressedData = new byte[compressedStream.Length];
                compressedStream.Read(compressedData, 0, compressedData.Length);

                var gZipBuffer = new byte[compressedData.Length + 4];
                Buffer.BlockCopy(compressedData, 0, gZipBuffer, 4, compressedData.Length);
                Buffer.BlockCopy(BitConverter.GetBytes(ms.Length), 0, gZipBuffer, 0, 4);
                var str = Convert.ToBase64String(gZipBuffer);
                return str;
            }
        }

        public static object DecompressObject(string compressedObj)
        {
            byte[] gZipBuffer = Convert.FromBase64String(compressedObj);
            IFormatter formatter = new BinaryFormatter();
            {
                using (MemoryStream compressedStream = new MemoryStream())
                {
                    compressedStream.Write(gZipBuffer, 4, gZipBuffer.Length - 4);
                    compressedStream.Position = 0;

                    using (var uncompressedStream = new MemoryStream())
                    {
                        using (var gZipStream = new GZipStream(compressedStream, CompressionMode.Decompress, true))
                        {
                            gZipStream.CopyTo(uncompressedStream);
                        }

                        uncompressedStream.Seek(0, SeekOrigin.Begin);
                        return formatter.Deserialize(uncompressedStream);
                    }
                }
            }
        }

        public static string CompressString(string text)
        {
            byte[] buffer = Encoding.UTF8.GetBytes(text);
            var memoryStream = new MemoryStream();
            using (var gZipStream = new GZipStream(memoryStream, CompressionMode.Compress, true))
            {
                gZipStream.Write(buffer, 0, buffer.Length);
            }

            memoryStream.Position = 0;

            var compressedData = new byte[memoryStream.Length];
            memoryStream.Read(compressedData, 0, compressedData.Length);

            var gZipBuffer = new byte[compressedData.Length + 4];
            Buffer.BlockCopy(compressedData, 0, gZipBuffer, 4, compressedData.Length);
            Buffer.BlockCopy(BitConverter.GetBytes(buffer.Length), 0, gZipBuffer, 0, 4);
            return Convert.ToBase64String(gZipBuffer);
        }

        /// <summary>
        /// Decompresses the string.
        /// </summary>
        /// <param name="compressedText">The compressed text.</param>
        /// <returns></returns>
        public static string DecompressString(string compressedText)
        {
            byte[] gZipBuffer = Convert.FromBase64String(compressedText);
            using (var memoryStream = new MemoryStream())
            {
                int dataLength = BitConverter.ToInt32(gZipBuffer, 0);
                memoryStream.Write(gZipBuffer, 4, gZipBuffer.Length - 4);

                var buffer = new byte[dataLength];

                memoryStream.Position = 0;
                using (var gZipStream = new GZipStream(memoryStream, CompressionMode.Decompress))
                {
                    gZipStream.Read(buffer, 0, buffer.Length);
                }

                return Encoding.UTF8.GetString(buffer);
            }
        }

        private static readonly byte[] Key = Encoding.UTF8.GetBytes("x1ta5LPSmq");

        public static string SerializeConcurrentDictionary<TKey, TValue>(ConcurrentDictionary<TKey, TValue> dictionary)
        {
            var jsonString = JsonConvert.SerializeObject(dictionary);
            var encryptedBytes = XorEncryptDecrypt(Encoding.UTF8.GetBytes(jsonString), Key);
            return Convert.ToBase64String(encryptedBytes);
        }

        public static ConcurrentDictionary<TKey, TValue> DeserializeConcurrentDictionary<TKey, TValue>(string base64String)
        {
            var encryptedBytes = Convert.FromBase64String(base64String);
            var decryptedBytes = XorEncryptDecrypt(encryptedBytes, Key);
            var jsonString = Encoding.UTF8.GetString(decryptedBytes);
            return JsonConvert.DeserializeObject<ConcurrentDictionary<TKey, TValue>>(jsonString);
        }

        private static byte[] XorEncryptDecrypt(byte[] data, byte[] key)
        {
            var result = new byte[data.Length];

            for (int i = 0; i < data.Length; i++)
            {
                result[i] = (byte)(data[i] ^ key[i % key.Length]);
            }

            return result;
        }
    }
}
