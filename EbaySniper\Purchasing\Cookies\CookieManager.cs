﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Data.SQLite;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Reflection;
using DevExpress.XtraEditors;
using Newtonsoft.Json;
using uBuyFirst.Prefs;

namespace uBuyFirst.Purchasing.Cookies
{
    public static class CookieManager
    {
        private static CookieContainer CachedCookies { get; set; }
        public static CookieProfile? Profile { get; set; }

        public static void ClearCookieCache()
        {
            CachedCookies = null;
        }

        public static void ReadChromeVersion()
        {
            try
            {
                if (!ConnectionConfig.CheckoutEnabled)
                    return;

                var path = Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData) + @"\Google\Chrome\User Data\Last Version";

                if (!File.Exists(path))
                    return;

                var version = File.ReadAllText(path);
                if (!string.IsNullOrEmpty(version))
                    ProgramState.ChromeUA = @$"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/{version} Safari/537.36";
            }
            catch (Exception)
            {
                // ignored
            }
        }

        public static CookieContainer ReadCookiesFirefox(IEnumerable<string> hostNames)
        {
            var hostCookies = new CookieContainer { PerDomainCapacity = 100 };
            if (Profile == null || string.IsNullOrWhiteSpace(Profile.Profile))
            {
                XtraMessageBox.Show("Please, select Firefox profile."); // race condition, but i'll risk it
                return hostCookies;
            }

            var cookiesPath = GetCookiesPathFirefox(Profile.Profile);


            if (!File.Exists(cookiesPath))
            {
                XtraMessageBox.Show("Please, select Firefox profile."); // race condition, but i'll risk it
                return hostCookies;
            }

            var builder = new SQLiteConnectionStringBuilder
            {
                DataSource = cookiesPath,
                FailIfMissing = false,
                ReadOnly = true,
                ForeignKeys = false,
                UseUTF16Encoding = false,
                Pooling = false,
                SyncMode = SynchronizationModes.Off,
                DateTimeKind = DateTimeKind.Utc,
                //DateTimeFormat = SQLiteDateFormats.ISO8601,
                DefaultIsolationLevel = IsolationLevel.ReadCommitted,
                DefaultTimeout = (int)TimeSpan.FromSeconds(15).TotalMilliseconds
            };

            var connectionString = builder.ToString();

            var plainCookies = new List<Tuple<string, string, string>>(); // Store hostKey, encryptedData, and cookie name

            using (var conn = new SQLiteConnection(connectionString))
            {
                conn.Open();
                var inClause = string.Join("', '", hostNames.Select(x => x.Replace("'", "''")));
                using var cmd = conn.CreateCommand();
                cmd.CommandText = $"SELECT name, value, host FROM moz_cookies WHERE host IN ('{inClause}')";

                using var reader = cmd.ExecuteReader();
                while (reader.Read())
                {
                    var hostKey = reader.GetString(2);
                    var encryptedData = reader.GetString(1);
                    var cookieName = reader.GetString(0);

                    plainCookies.Add(Tuple.Create(cookieName, encryptedData, hostKey));
                }

                conn.Close();
            }

            // Decrypt cookies after reading from DB
            foreach (var cookie in plainCookies)
            {
                try
                {
                    var c = new Cookie(cookie.Item1, cookie.Item2, "/", cookie.Item3);
                    // Add special handling for specific domains if needed
                    hostCookies.Add(c);
                }
                catch (Exception ex)
                {
                    // Handle decryption exceptions
                }
            }

            //RebuildCookies(hostCookies);
            CachedCookies = hostCookies;

            return hostCookies;
        }

        internal static string GetCookiesPathFirefox(string? profilePath)
        {
            var localDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);

            // Ensure profilePath doesn't already contain the "Profiles" directory
            var cookiesPath = Path.Combine(localDataPath, "Mozilla", "Firefox", profilePath, "cookies.sqlite");

            // Handle the case where the profilePath might already include a "Profiles" folder
            if (cookiesPath.Contains("Profiles" + Path.DirectorySeparatorChar + "Profiles" + Path.DirectorySeparatorChar))
            {
                cookiesPath = cookiesPath.Replace('/', Path.DirectorySeparatorChar);
            }

            return cookiesPath;
        }

        internal static List<CookieProfile> GetProfileNamesFirefox()
        {
            var profiles = new List<CookieProfile>();
            var localDataPath = Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData);
            var firefoxProfilePath = $@"{localDataPath}\Mozilla\Firefox\profiles.ini";



            if (!File.Exists(firefoxProfilePath))
                return profiles;

            var profileContent = File.ReadAllLines(firefoxProfilePath);
            string? profileName = null;
            string? profilePath = null;

            foreach (var line in profileContent)
            {
                if (line.StartsWith("Name="))
                {
                    profileName = line.Substring("Name=".Length);
                }
                else if (line.StartsWith("Path="))
                {
                    profilePath = line.Substring("Path=".Length);
                }

                if (!string.IsNullOrEmpty(profileName) && !string.IsNullOrEmpty(profilePath))
                {
                    var cookiePath = GetCookiesPathFirefox(profilePath);
                    var signedInUserPath = Path.Combine(Path.GetDirectoryName(cookiePath) ?? string.Empty, "signedInUser.json"); // Path to signedInUser.json

                    string? userEmail = null;

                    // Check if signedInUser.json exists and get the email
                    if (File.Exists(signedInUserPath))
                    {
                        var signedInUserJson = File.ReadAllText(signedInUserPath);
                        var signedInUser = JsonConvert.DeserializeObject<SignedInUser>(signedInUserJson);
                        userEmail = signedInUser?.accountData?.email; // Extract email
                    }
                    profiles.Add(new CookieProfile()
                    {
                        Name = (userEmail != null ? $"{userEmail}" : profileName), // Append email if available
                        Profile = profilePath
                    });
                    profileName = null;
                    profilePath = null; // Reset after adding the profile
                }
            }

            return profiles;
        }

        public class SignedInUser
        {
            public AccountData accountData { get; set; }
        }

        public class AccountData
        {
            public string email { get; set; }
        }
    }

    public class CookieProfile
    {
        public string? Name { get; set; }
        public string? Profile { get; set; }

        public override string? ToString()
        {
            return Name;
        }
    }

    public class LocalState
    {
        [JsonProperty("profile")]
        public Profile? Profile { get; set; }
    }

    public class Profile
    {
        [JsonProperty("info_cache")]
        public Dictionary<string, ProfileInfo>? InfoCache { get; set; }
    }

    public class ProfileInfo
    {
        [JsonProperty("name")]
        public string? Name { get; set; }
    }
}
