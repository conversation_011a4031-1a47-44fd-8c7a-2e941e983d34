﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using ExceptionReporting;
using ExceptionReporting.Core;
using uBuyFirst.Other;
using uBuyFirst.Prefs;

namespace uBuyFirst.Tools
{
    internal static class ExM
    {
        public static ExceptionReporter Reporter;
        public static List<string> ExceptionsList;
        public static SynchronizationContext SynchronizationContextCurrent;

        public static void CreateReport()
        {
            try
            {
                var customReporter = new ExceptionReporter();
                customReporter.Config.MailMethod = ExceptionReportInfo.EmailMethod.SMTP;
                customReporter.Config.SmtpFromAddress = "<EMAIL>";
                customReporter.Config.SmtpServer = "smtp.sparkpostmail.com:587";
                customReporter.Config.SmtpUsername = "SMTP_Injection";
                customReporter.Config.SmtpPassword = "977822b49a3725a00df0930ac1389aa8b6d0bbba";
                customReporter.Config.ContactEmail = "<EMAIL>";
                customReporter.Config.EmailReportAddress = "<EMAIL>";
                customReporter.Config.CustomMessage = "Please write your suggestions and bug reports. If you want an update for your request, include your email also.";

                var attachments = new List<string>();
                string tempConfigPath = null;
                try
                {
                    string originalConfigPath = Path.Combine(Folders.Settings, "config.cfg");
                    string sanitizedContent = GetSanitizedConfigContent(originalConfigPath);

                    if (!string.IsNullOrEmpty(sanitizedContent))
                    {
                        tempConfigPath = Path.Combine(Path.GetTempPath(), "config_sanitized_report.cfg");
                        File.WriteAllText(tempConfigPath, sanitizedContent);
                        attachments.Add(tempConfigPath);
                    }
                }
                catch (Exception)
                {
                }

                if (File.Exists(Path.Combine(Folders.Workspace, "Workspace Active.xml")))
                    attachments.Add(Path.Combine(Folders.Workspace, "Workspace Active.xml"));
                if (File.Exists(Path.Combine(Folders.Layout, "Layout Bid Window.xml")))
                    attachments.Add(Path.Combine(Folders.Layout, "Layout Bid Window.xml"));
                if (File.Exists(Path.Combine(Folders.Layout, "Layout Ribbon.xml")))
                    attachments.Add(Path.Combine(Folders.Layout, "Layout Ribbon.xml"));

                if (attachments.Count > 0)
                    customReporter.Config.FilesToAttach = attachments.ToArray();

                customReporter.Config.ShowAssembliesTab = false;
                customReporter.Config.ShowContactTab = false;
                customReporter.Config.ShowConfigTab = false;
                customReporter.Config.ShowExceptionsTab = false;
                customReporter.Config.ShowGeneralTab = true;
                customReporter.Config.ShowSysInfoTab = false;
                customReporter.Config.ShowFullDetail = true;
                customReporter.Config.UserExplanationLabel = "Your configuration files have been attached.";
                customReporter.Config.TitleText = "Report";
                customReporter.Config.TakeScreenshot = true;
                try
                {
                    customReporter.Show(new ArgumentNullException());
                }
                finally
                {
                    // Clean up the temporary sanitized config file
                    if (tempConfigPath != null && File.Exists(tempConfigPath))
                    {
                        try { File.Delete(tempConfigPath); } catch {}
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        // Helper method to read and sanitize config file content
        internal static string GetSanitizedConfigContent(string configPath) // Changed to internal
        {
            try
            {
                if (!File.Exists(configPath))
                {
                    Loggers.LogError($"Config file not found for sanitization: {configPath}");
                    return null; // Indicate file not found or unreadable
                }

                string configContent = File.ReadAllText(configPath);
                // Remove TList section using Regex, handling multi-line content
                string sanitizedContent = System.Text.RegularExpressions.Regex.Replace(
                    configContent,
                    @"<TList>.*?</TList>",
                    "",
                    System.Text.RegularExpressions.RegexOptions.Singleline | System.Text.RegularExpressions.RegexOptions.IgnoreCase);

                return sanitizedContent;
            }
            catch (Exception ex)
            {
                Loggers.LogError($"Error reading or sanitizing config file '{configPath}': {ex.Message}");
                return null; // Indicate failure during processing
            }
        }

        private static TextBox GetInnerTextBox1(TextEdit editor)
        {
            var textEdit = editor;
            var firstOrDefault = textEdit?.Controls.OfType<TextBox>().FirstOrDefault();
            if (firstOrDefault == null)
            {

            }

            return firstOrDefault;
        }

        public static void ubuyExceptionHandler(string str, Exception ex)
        {
            Stat._errorsCount++;
            try
            {
                Form1.Instance.InvokeIfRequired(() => Form1.Instance.barStaticErrorsVal.Caption = "Errors: " + Stat._errorsCount);
            }
            catch
            {
                // ignored
            }

            if (!string.IsNullOrEmpty(ProgramState.SerialNumber))
            {
                if (ProgramState.SerialNumber.StartsWith("ROMA") || ProgramState.SerialNumber.StartsWith("8139"))
                {
                    try
                    {
                        File.AppendAllText(Path.Combine(Folders.Logs, "debug.log"), str + "\r\n" + ex.Message + "\r\n");
                    }
                    catch
                    {
                        // ignored
                    }
                }
            }

            if (Stat._errorsCount > 0 && Stat._errorsCount % 500 == 0) Form1.ShowTrayBalloon("uBuyFirst Warning", "There are significant amount of errors occurring.\n Please, review Log panel and check your internet connection.", 15);

            if (str == "The underlying connection was closed: Could not establish trust relationship for the SSL/TLS secure channel.")
                return;
            if (ex?.InnerException != null)
            {
                if (ex.InnerException.Message.Contains("Error executing request"))
                    return;
                if (ex.InnerException.Message.Contains("ServerCertificateValidationCallback"))
                    return;
            }

            var exMessage = $"{ex.Message} {ex.InnerException?.Message}";
            //var innerTextBox = GetInnerTextBox(Form1.Instance.memoEditLog);
            if (str.Contains("_(info)_"))
            {
                try
                {
                    if (Form1.Instance.memoEditErrorLog != null && Form1.Instance.IsHandleCreated && !Form1.Instance.IsDisposed)
                    {
                        Form1.Instance.memoEditErrorLog.InvokeIfRequired(() =>
                        {
                            if (Form1.Instance.memoEditErrorLog != null)
                            {
                                Form1.Instance.memoEditErrorLog.Text += ($"\r\n{DateTime.Now.ToString("u").Replace("Z", "")} {str}\t{exMessage}");
                                if (Form1.Instance.memoEditErrorLog.Text.Length > 1000000 || Form1.Instance.memoEditErrorLog.Lines.Length > 500)
                                    Form1.Instance.memoEditErrorLog.Text = "";
                            }

                            Form1.GoogleAnalytics?.TrackException($"{str}|{exMessage}", "Errors", 1);
                        });
                    }
                }
                catch
                {
                }

                return;
            }

            Loggers.LogError(ex);
            if (ex.Message.Contains("by the TaskWrapper") || (ex.InnerException != null && ex.InnerException.Message.Contains("by the TaskWrapper")))
                return;

            Reporter.Config.MailMethod = ExceptionReportInfo.EmailMethod.SMTP;
            Reporter.Config.SmtpFromAddress = "<EMAIL>";
            Reporter.Config.SmtpServer = "smtp.sparkpostmail.com:587";
            Reporter.Config.SmtpUsername = "SMTP_Injection";
            Reporter.Config.SmtpPassword = "977822b49a3725a00df0930ac1389aa8b6d0bbba";
            Reporter.Config.ContactEmail = "<EMAIL>";
            Reporter.Config.EmailReportAddress = "<EMAIL>";

            //    _reporter.Config.CustomMessage = "If you want an update for your request, include your email also.";

            var attachments = new List<string>();
            string tempConfigPathForHandler = null;
            try
            {
                string originalConfigPath = Path.Combine(Folders.Settings, "config.cfg");
                string sanitizedContent = GetSanitizedConfigContent(originalConfigPath);

                if (!string.IsNullOrEmpty(sanitizedContent))
                {
                    tempConfigPathForHandler = Path.Combine(Path.GetTempPath(), $"config_sanitized_ex_{Guid.NewGuid()}.cfg"); // Unique name
                    File.WriteAllText(tempConfigPathForHandler, sanitizedContent);
                    attachments.Add(tempConfigPathForHandler);
                }                
            }
            catch (Exception logEx)
            {                
            }
            if (File.Exists(Path.Combine(Folders.Workspace, "Workspace Active.xml")))
                attachments.Add(Path.Combine(Folders.Workspace, "Workspace Active.xml"));
            if (File.Exists(Path.Combine(Folders.Layout, "Layout Bid Window.xml")))
                attachments.Add(Path.Combine(Folders.Layout, "Layout Bid Window.xml"));
            if (File.Exists(Path.Combine(Folders.Layout, "Layout Ribbon.xml")))
                attachments.Add(Path.Combine(Folders.Layout, "Layout Ribbon.xml"));

            if (attachments.Count > 0)
                Reporter.Config.FilesToAttach = attachments.ToArray();

            Reporter.Config.ShowAssembliesTab = false;
            Reporter.Config.ShowContactTab = false;
            Reporter.Config.ShowConfigTab = false;
            Reporter.Config.ShowExceptionsTab = true;
            Reporter.Config.ShowGeneralTab = true;
            Reporter.Config.ShowSysInfoTab = false;
            Reporter.Config.ShowFullDetail = true;

            Reporter.Config.TakeScreenshot = true;
            if (!ExceptionsList.Contains(exMessage))
            {
                ExceptionsList.Add(exMessage);
                try
                {
                    SynchronizationContextCurrent.Send(state => Reporter.Show(ex), "");
                }
                finally
                {
                    // Clean up the temporary sanitized config file
                    if (tempConfigPathForHandler != null && File.Exists(tempConfigPathForHandler))
                    {
                        try { File.Delete(tempConfigPathForHandler); } catch {}
                    }
                }
            }
            else
            {
                try
                {
                    if (Form1.Instance.memoEditErrorLog != null && Form1.Instance.IsHandleCreated && !Form1.Instance.IsDisposed)
                        Form1.Instance.memoEditErrorLog.InvokeIfRequired(() =>
                        {
                            if (Form1.Instance.memoEditErrorLog != null)
                            {
                                Form1.Instance.memoEditErrorLog.Text += "\r\n" + str + "\t" + exMessage;
                                if (Form1.Instance.memoEditErrorLog.Text.Length > 1000000 || Form1.Instance.memoEditErrorLog.Lines.Length > 500)
                                    Form1.Instance.memoEditErrorLog.Text = "";
                            }
                        });
                }
                catch (Exception)
                {
                    // ignored
                }
            }
        }

        public static void ubuyExceptionHandler(object sender, ThreadExceptionEventArgs e)
        {
            ubuyExceptionHandler("Application.ThreadException: ", e.Exception);
        }

        public static void ubuyExceptionTaskHandler(object sender, UnobservedTaskExceptionEventArgs e)
        {
            ubuyExceptionHandler("Unhandled Task exception: ", e.Exception);
        }

        public static void ubuyExceptionHandler(object sender, UnhandledExceptionEventArgs e)
        {
            ubuyExceptionHandler("AppDomain.CurrentDomain.UnhandledException: ", (Exception)e.ExceptionObject);
        }
    }
}
