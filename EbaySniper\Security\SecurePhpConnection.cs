﻿#pragma warning disable

namespace uBuyFirst.Security
{
    public class SecurePhpConnection
    {
        private string _address;
        private string _asyncResponse;
        private readonly HttpControl _http;
        private readonly RsAtoPhpCryptography _rsa;
        private readonly AeStoPhpCryptography _aes;

        /// <summary>
        /// Create a secure connection with a PHP script.
        /// </summary>
        public SecurePhpConnection()
        {
            _http = new HttpControl();
            _rsa = new RsAtoPhpCryptography();
            _aes = new AeStoPhpCryptography();
        }

        /// <summary>
        /// Set the location of the PHP script to use in this secure connection.
        /// </summary>
        /// <param name="phpScriptLocation">The URL of the php script to contact.</param>
        public void SetRemotePhpScriptLocation(string phpScriptLocation)
        {
            _address = phpScriptLocation;
        }

        public string SendMessage(string message)
        {
            var connected = EstablishConnection();
            if (connected)
            {
                var encrypted = _aes.Encrypt(message);
                var response = _http.Post(_address, "data=" + encrypted);
                if (!string.IsNullOrEmpty(response))
                    return _aes.Decrypt(response);
            }

            return "";
        }

        /// <summary>
        /// Send an encrypted message to the remote PHP script and wait for a secure response.
        /// </summary>
        /// <param name="message">The message to send.</param>
        private bool EstablishConnection()
        {
            // Get the RSA public key that we will use
            var cert = _http.Post(_address, "getkey=y");
            _rsa.LoadCertificateFromString(cert);
            // Generate the AES keys, encrypt them with the RSA public key, then send them to the PHP script.
            _aes.GenerateRandomKeys();
            var key = Utility.ToUrlSafeBase64(_rsa.Encrypt(_aes.EncryptionKey));
            var iv = Utility.ToUrlSafeBase64(_rsa.Encrypt(_aes.EncryptionIv));
            var result = _http.Post(_address, "key=" + key + "&iv=" + iv);
            // If the PHP script sends this message back, then the connection is now good.
            var connected = _aes.Decrypt(result) == "AES OK";
            return connected;
        }
    }
}