﻿using System;
using System.Windows.Forms;
using DevExpress.XtraGrid.Views.Grid;
using uBuyFirst.Grid;
using uBuyFirst.Prefs;

namespace uBuyFirst.GUI
{
    internal class FocusRouter
    {
        public static GridView FocusedGridView;
        private readonly WebBrowser _webBrowser;

        public FocusRouter(WebBrowser webBrowser)
        {
            _webBrowser = webBrowser;
        }

        public static void WebBrowserKeyDown(object sender, PreviewKeyDownEventArgs e)
        {
            if (FocusedGridView?.GridControl == null)
                return;

            var keyEventArgs = new KeyEventArgs(e.KeyData);
            e.IsInputKey = true;
            switch (e.KeyCode)
            {
                case Keys.Down:
                    MoveDownRowFocus();

                    break;
                case Keys.Up:
                    MoveUpRowFocus(keyEventArgs);

                    break;
                default:
                    GridBuilder.GridViewKeyDown(FocusedGridView, keyEventArgs);

                    break;
            }

            //AutoMeasurement.Client.TrackEvent(e.KeyData.ToString(), "Grid keypress", Analytics.GAid);
            ProgramState.Idlesw.Restart();
            keyEventArgs.Handled = true;
            FocusedGridView.GridControl.Focus();
        }

        public void FocusBrowser(object sender, EventArgs e)
        {
            var topMostWindow = Form.ActiveForm != null;

            if (!topMostWindow)
                return;

            if (_webBrowser.IsDisposed)
                return;
            _webBrowser?.Document?.Body?.Focus();
        }

        private static void MoveUpRowFocus(KeyEventArgs kd)
        {
            if (FocusedGridView.FocusedRowHandle - 1 >= 0 && FocusedGridView.RowCount != 0)
            {
                FocusedGridView.FocusedRowHandle--;
                FocusedGridView.ClearSelection(); //NEW CODE
                FocusedGridView.SelectRow(FocusedGridView.FocusedRowHandle);
                kd.Handled = true;
            }
        }

        private static void MoveDownRowFocus()
        {
            if (FocusedGridView.FocusedRowHandle < FocusedGridView.RowCount - 1)
            {
                FocusedGridView.FocusedRowHandle++;
                FocusedGridView.ClearSelection(); //NEW CODE
                FocusedGridView.SelectRow(FocusedGridView.FocusedRowHandle);
            }
        }
    }
}