﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using DevExpress.XtraBars.Docking;
using GoogleAnalytics;
using uBuyFirst.Filters;
using uBuyFirst.Item;
using uBuyFirst.Prefs;
using uBuyFirst.Time;
using uBuyFirst.Tools;

namespace uBuyFirst.Stats;

public class Analytics
{
    private static DateTime s_timeStatsSent;

    public static string GAid => s_gAid;

    private int SendStatsInterval = 15;
    private static string s_gAid = "";
    private static GoogleAnalytics.Analytics _analytics;
    private const string AccountId = @"G-G7Z9PT8ER7";
    private const string AppSecret = @"R8rPL4lTQnSRqrcOGqkIHA";
    private static string session_id = Guid.NewGuid().ToString();

    public Analytics(string userId, UserProperties userProperties)
    {
        _analytics = new GoogleAnalytics.Analytics { MeasurementId = AccountId, ApiSecret = AppSecret, ClientId = userId, UserProperties = userProperties };
    }

    public async Task ReportProgramConfig(List<Keyword2Find> kw2FindList)
    {
        var keywordCount = kw2FindList.Sum(keyword2Find => keyword2Find.ChildrenCore.Count);
        var highlightedWordCount = UserSettings.Highlightsvalues.Words1.Length + UserSettings.Highlightsvalues.Words2.Length + UserSettings.Highlightsvalues.Words3.Length;

        _analytics.Events.Add(CreateEvent("Keywords", "Configuration", kw2FindList.Count(kw => !kw. ListingType.Contains(ListingType.OutOfStock))));
        _analytics.Events.Add(CreateEvent("SubKeywords", "Configuration", keywordCount));
        _analytics.Events.Add(CreateEvent("Filters", "Configuration", XFilterManager.FilterCount() ?? 0));
        _analytics.Events.Add(CreateEvent("eBay_Accounts", "Configuration", Form1.EBayAccountsList.Count));
        _analytics.Events.Add(CreateEvent("Highlighted_Words", "Configuration", highlightedWordCount));
        _analytics.Events.Add(CreateEvent("Custom_columns", "Configuration", ItemSpecifics.CategorySpecificsList.Count));
        var outOfStockItemIdCount = kw2FindList.Where(kw => kw.ListingType.Contains(ListingType.OutOfStock)).Sum(kw => kw.Kws.Split(',').Length);
        _analytics.Events.Add(CreateEvent("OutOfStock", "Configuration", outOfStockItemIdCount));

        await PostReport();
    }

    public async Task ReportCounters()
    {
        var secondsSinceLastUpdate = (DateTime.UtcNow - s_timeStatsSent).TotalSeconds;
        s_timeStatsSent = DateTime.UtcNow;
        Stat.Call_SpeedCounter = (int)Math.Round((Stat.FindApiCounter + Stat.BrowseApiCounter + 1) / secondsSinceLastUpdate, 1);

        var analyticsEvents = new Dictionary<string, int>
        {
            { nameof(Stat.PlaceOfferWonCounter), Stat.PlaceOfferWonCounter },
            { nameof(Stat.PlaceOfferWonAmount), Stat.PlaceOfferWonAmount },
            { nameof(Stat.PlaceOfferEmptyCounter), Stat.PlaceOfferEmptyCounter },
            { nameof(Stat.MakeOfferOKCounter), Stat.MakeOfferOKCounter },
            { nameof(Stat.MakeOfferEmptyCounter), Stat.MakeOfferEmptyCounter },
            { nameof(Stat.WebCheckoutWonCounter), Stat.WebCheckoutWonCounter },
            { nameof(Stat.WebCheckoutLostCounter), Stat.WebCheckoutLostCounter },
            { nameof(Stat.Call_SpeedCounter), Stat.Call_SpeedCounter },
            { nameof(Stat.FindApiCounter), Stat.FindApiCounter },
            { nameof(Stat.BrowseApiCounter), Stat.BrowseApiCounter },
            { nameof(Stat.GetItemsCounter), Stat.GetItemsCounter },
            { nameof(Stat.ExternalPanelCounter), Stat.ExternalPanelCounter },
            { nameof(Stat.ItemsFoundCounter), Stat.ItemsFoundCounter },
            { nameof(Stat.GetItemShippingCounter), Stat.GetItemShippingCounter },
            { nameof(Stat.PushBulletCounter), Stat.PushBulletCounter },
            { nameof(Stat.TelegramCounter), Stat.TelegramCounter },
            { nameof(Stat.TitleMatchCounter), Stat.TitleMatchCounter },
            { nameof(Stat.TitleNotMatchCounter), Stat.TitleNotMatchCounter },
            { nameof(Stat.ApiItem_Source), Stat.ApiItem_Source },
            { nameof(Stat.BrowseApiItem_Source), Stat.BrowseApiItem_Source },
            { nameof(Stat.Rss3Item_Source), Stat.Rss3Item_Source },
            { nameof(Stat.S4Item_Source), Stat.S4Item_Source },
            { nameof(Stat.OpenToBrowserCounter), Stat.OpenToBrowserCounter },
            { nameof(Stat.WatchlistApiCounter), Stat.WatchlistApiCounter },
        };

        //Track(nameof(Stat.StatusUpdateCounter),  Stat.StatusUpdateCounter);
        //Track(nameof(Stat.OutOfStockCounter),  Stat.OutOfStockCounter);
        Stat.Call_SpeedCounter = 0;
        Stat.FindApiCounter = 0;
        Stat.BrowseApiCounter = 0;
        Stat.GetItemsCounter = 0;
        Stat.ItemsFoundCounter = 0;
        Stat.GetItemShippingCounter = 0;
        Stat.PushBulletCounter = 0;
        Stat.TelegramCounter = 0;
        Stat.ApiItem_Source = 0;
        Stat.BrowseApiItem_Source = 0;
        Stat.Rss3Item_Source = 0;
        Stat.S4Item_Source = 0;
        Stat.PlaceOfferWonCounter = 0;
        Stat.PlaceOfferWonAmount = 0;
        Stat.PlaceOfferEmptyCounter = 0;
        Stat.MakeOfferOKCounter = 0;
        Stat.MakeOfferEmptyCounter = 0;
        Stat.WebCheckoutWonCounter = 0;
        Stat.WebCheckoutLostCounter = 0;
        Stat.StatusUpdateCounter = 0;
        Stat.OutOfStockCounter = 0;
        Stat.OpenToBrowserCounter = 0;
        Stat.WatchlistApiCounter = 0;
        Stat.ExternalPanelCounter = 0;
        foreach (var analyticsEvent in analyticsEvents)
        {
            if (analyticsEvent.Value == 0)
            {
                continue;
            }

            var trackingEvent = CreateCounterEvent(analyticsEvent.Key, analyticsEvent.Value);
            _analytics.Events.Add(trackingEvent);
        }

        var engagementEvent = CreateEvent("", "Searching", 1);
        engagementEvent.engagement_time_msec = ProgramState.SearchingStopwatchGA4.ElapsedMilliseconds;
        if (ProgramState.SearchingStopwatchGA4.IsRunning)
            ProgramState.SearchingStopwatchGA4.Restart();
        _analytics.Events.Add(engagementEvent);

        await PostReport();
    }

    private static void WriteAnalyticsEventsToCsv(Dictionary<string, int> analyticsEvents, string filePath)
    {
        //Write header line
        if (!File.Exists(filePath))
        {
            var headers = String.Join(",", analyticsEvents.Keys);
            File.AppendAllText(filePath, headers + "\r\n");
        }

        //Write data lines
        var values = String.Join(",", analyticsEvents.Values);
        File.AppendAllText(filePath, values + "\r\n");
    }

    private static Measurement CreateCounterEvent(string trackerName, int trackerValue)
    {
        var actionName = "";
        var categoryName = "";

        var trackerTypes = new[] { "Counter", "Item_Source", "PlaceOfferWonAmount" };
        foreach (var trackerType in trackerTypes)
        {
            if (!trackerName.Contains(trackerType))
            {
                continue;
            }

            actionName = trackerName.Replace(trackerType, "");
            categoryName = trackerType;
            break;
        }

        var counterEvent = CreateEvent(actionName, categoryName, trackerValue);
        return counterEvent;
    }

    public static EventMeasurement CreateEvent(string category, string action, int value)
    {
        string name;
        if (string.IsNullOrEmpty(category))
            name = action;
        else
            name = category + "_" + action;

        var m = new EventMeasurement(name);
        m.Label = s_gAid;
        m.Value = value.ToString();
        m.session_id = session_id;
        m.engagement_time_msec = 0;
        m.debug_mode = Debugger.IsAttached ? 1 : 0;
        return m;
    }

    public static void AddEvent(string category, string action, int value)
    {
        var eventMeasurement = CreateEvent(category, action, value);
        _analytics.Events.Add(eventMeasurement);
    }

    public static void ReportSoldTime(DateTimeWithDiff dSoldTime)
    {
        //if (soldTimeOffset == null)
        //  return;

        //if (ProgramState.InitialSearchCompleted && ProgramState.TotalRunningStopwatch.Elapsed.TotalMinutes > 10 && Math.Abs((int)soldTimeOffset.TimeDiff) < 600)
        //if (soldTimeOffset.TimeDiff > 0)
        //AutoMeasurement.Client.TrackTimedEvent("Performance", "Sold_Time_Positive", new TimeSpan(0, 0, (int)soldTimeOffset.TimeDiff), GAid);
        //else
        //AutoMeasurement.Client.TrackTimedEvent("Performance", "Sold_Time_Negative", new TimeSpan(0, 0, Math.Abs((int)soldTimeOffset.TimeDiff)), GAid);

    }

    public static void SendPanelList(ReadOnlyPanelCollection panelList)
    {
        foreach (DockPanel panel in panelList)
        {
            if (panel.Text.Contains(@"ontainer") || panel.Text.Contains("Keywords"))
            {
                continue;
            }

            var panelName = panel.Header;
            if (string.IsNullOrEmpty(panelName))
                panelName = panel.Text;
            panelName += panel.Visibility == DockVisibility.Visible ? "_Visible" : "_Hidden";
            //AutoMeasurement.Client.TrackEvent("Panel_" + panelName, "Configuration", GAid, 1);
        }
    }

    public async void TrackException(string message, string category, int value)
    {
        return;
        var m = new EventMeasurement(category + "_" + message)
        {
            Label = s_gAid,
            Value = value.ToString(),
            session_id = session_id,
            engagement_time_msec = 1,
            debug_mode = Debugger.IsAttached ? 1 : 0
        };
        _analytics.Events.Add(m);
        await PostReport();
    }

    public static async Task PostReport()
    {
        var overLimitEvents = new List<Measurement>();
        while (_analytics.Events.Count > 25)
        {
            var eventMeasurement = _analytics.Events.Last();
            overLimitEvents.Add(eventMeasurement);
            _analytics.Events.RemoveAt(overLimitEvents.Count - 1);
        }

        try
        {
            await HttpProtocol.PostMeasurements(_analytics);
            if (overLimitEvents.Count > 0)
            {
                _analytics.Events.Clear();
                _analytics.Events.AddRange(overLimitEvents);
                await HttpProtocol.PostMeasurements(_analytics);
            }
        }
        catch (Exception e)
        {
            Debug.WriteLine(e);
        }

        _analytics.Events.Clear();
    }

    public async void SendSearchStatsPeriodic()
    {
        if (!Form1.Instance.IsHandleCreated)
            return;
        if (Debugger.IsAttached)
            SendStatsInterval = 1;
        var minutesSinceLastUpdate = (DateTime.UtcNow - s_timeStatsSent).TotalMinutes;
        if (minutesSinceLastUpdate < SendStatsInterval)
            return;

        if (ProgramState.TotalRunningStopwatch.Elapsed.TotalMinutes < 1)
            return;

        await ReportCounters();
    }

    public static void SetGAid(string gaId)
    {
        if (gaId.Length <= 25)
            s_gAid = gaId;
        else
            s_gAid = gaId.Substring(gaId.Length - 25, 25);
    }
}
