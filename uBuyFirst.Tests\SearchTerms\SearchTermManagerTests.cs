﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using uBuyFirst;
using uBuyFirst.Prefs;
using uBuyFirst.Search;
using uBuyFirst.SearchTerms;

namespace uBuyFirst.Tests.SearchTerms
{
    [TestClass]
    public class SearchTermManagerTests
    {
        private string _testCsvPath;
        private QueryList _testQueryList;

        [TestInitialize]
        public void TestInitialize()
        {
            _testCsvPath = Path.Combine(Path.GetTempPath(), $"test_searchterms_{Guid.NewGuid()}.csv");
            _testQueryList = new QueryList();
            // Set RestockerEnabled to true by default for most tests
            ConnectionConfig.RestockerEnabled = true;
        }

        [TestCleanup]
        public void TestCleanup()
        {
            if (File.Exists(_testCsvPath))
            {
                File.Delete(_testCsvPath);
            }
            // Reset RestockerEnabled to default state
            ConnectionConfig.RestockerEnabled = false;
        }

        #region Export Tests

        [TestMethod]
        public void ExportSearchesToFile_WithRestockerEnabled_IncludesCorrectHeaders()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = true;
            var keyword = CreateTestKeyword();
            var searchTermList = new List<Keyword2Find> { keyword };

            // Act
            var exportedContent = GetExportedContent(searchTermList);
            var lines = exportedContent.Split(new[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries);
            var headerLine = lines[0];

            // Assert
            Assert.IsTrue(headerLine.Contains("Job ID"), "Header should contain 'Job ID' column when RestockerEnabled is true");
            Assert.IsTrue(headerLine.Contains("Required Quantity"), "Header should contain 'Required Quantity' column when RestockerEnabled is true");
            Assert.IsTrue(headerLine.Contains("Purchased Quantity"), "Header should contain 'Purchased Quantity' column when RestockerEnabled is true");
        }

        [TestMethod]
        public void ExportSearchesToFile_WithRestockerDisabled_ExcludesRestockerHeaders()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = false;
            var keyword = CreateTestKeyword();
            var searchTermList = new List<Keyword2Find> { keyword };

            // Act
            var exportedContent = GetExportedContent(searchTermList);
            var lines = exportedContent.Split(new[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries);
            var headerLine = lines[0];

            // Assert
            Assert.IsFalse(headerLine.Contains("Job ID"), "Header should not contain 'Job ID' column when RestockerEnabled is false");
            Assert.IsFalse(headerLine.Contains("Required Quantity"), "Header should not contain 'Required Quantity' column when RestockerEnabled is false");
            Assert.IsFalse(headerLine.Contains("Purchased Quantity"), "Header should not contain 'Purchased Quantity' column when RestockerEnabled is false");
        }

        [TestMethod]
        public void Export_WithRestockerEnabledAndFieldsPopulated_ExportsCorrectValues()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = true;
            var keyword = CreateTestKeyword();
            keyword.JobId = "TEST-JOB-001";
            keyword.RequiredQuantity = 5;
            keyword.PurchasedQuantity = 2;

            // Act
            var exportedRow = SearchTermManager.Export(keyword);
            var cells = ParseCsvRow(exportedRow);

            // Assert
            Assert.IsTrue(cells.Contains("TEST-JOB-001"), "Exported data should contain JobId value when RestockerEnabled is true");
            Assert.IsTrue(cells.Contains("5"), "Exported data should contain RequiredQuantity value when RestockerEnabled is true");
            Assert.IsTrue(cells.Contains("2"), "Exported data should contain PurchasedQuantity value when RestockerEnabled is true");
        }

        [TestMethod]
        public void Export_WithRestockerDisabled_ExcludesRestockerValues()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = false;
            var keyword = CreateTestKeyword();
            keyword.JobId = "TEST-JOB-001";
            keyword.RequiredQuantity = 5;
            keyword.PurchasedQuantity = 2;

            // Act
            var exportedRow = SearchTermManager.Export(keyword);
            var cells = ParseCsvRow(exportedRow);

            // Assert
            Assert.IsFalse(cells.Contains("TEST-JOB-001"), "Exported data should not contain JobId value when RestockerEnabled is false");
            Assert.IsFalse(cells.Contains("5"), "Exported data should not contain RequiredQuantity value when RestockerEnabled is false");
            Assert.IsFalse(cells.Contains("2"), "Exported data should not contain PurchasedQuantity value when RestockerEnabled is false");
        }

        [TestMethod]
        public void Export_WithRestockerEnabledAndEmptyFields_ExportsEmptyValues()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = true;
            var keyword = CreateTestKeyword();
            keyword.JobId = string.Empty;
            keyword.RequiredQuantity = 0;
            keyword.PurchasedQuantity = 0;

            // Act
            var exportedRow = SearchTermManager.Export(keyword);
            var cells = ParseCsvRow(exportedRow);

            // Assert - Should contain empty string and zeros when RestockerEnabled is true
            var jobIdIndex = GetJobIdColumnIndex();
            var requiredQtyIndex = GetRequiredQuantityColumnIndex();
            var purchasedQtyIndex = GetPurchasedQuantityColumnIndex();

            Assert.AreEqual("", cells[jobIdIndex], "Empty JobId should export as empty string when RestockerEnabled is true");
            Assert.AreEqual("0", cells[requiredQtyIndex], "Zero RequiredQuantity should export as '0' when RestockerEnabled is true");
            Assert.AreEqual("0", cells[purchasedQtyIndex], "Zero PurchasedQuantity should export as '0' when RestockerEnabled is true");
        }

        #endregion

        #region Import Tests

        [TestMethod]
        public void ImportSearchTermsFromFile_WithRestockerFields_SetsCorrectValues()
        {
            // Arrange
            var csvContent = @"Id,eBay Search Alias,Keywords,Job ID,Required Quantity,Purchased Quantity,Keyword enabled,Price Min,Price Max
keyword-1,Test Keyword,test keywords,JOB-001,5,2,true,10.00,100.00";

            File.WriteAllText(_testCsvPath, csvContent);

            // Act
            SearchTermManager.ImportSearchTermsFromFile(_testCsvPath, _testQueryList);

            // Assert
            Assert.AreEqual(1, _testQueryList.ChildrenCore.Count, "Should import one keyword");
            var importedKeyword = _testQueryList.ChildrenCore[0];
            Assert.AreEqual("JOB-001", importedKeyword.JobId, "JobId should be imported correctly");
            Assert.AreEqual(5, importedKeyword.RequiredQuantity, "RequiredQuantity should be imported correctly");
            Assert.AreEqual(2, importedKeyword.PurchasedQuantity, "PurchasedQuantity should be imported correctly");
        }

        [TestMethod]
        public void ImportSearchTermsFromFile_WithMissingRestockerColumns_SetsDefaultValues()
        {
            // Arrange - CSV without Restocker columns
            var csvContent = @"Id,eBay Search Alias,Keywords,Keyword enabled,Price Min,Price Max
keyword-1,Test Keyword,test keywords,true,10.00,100.00";

            File.WriteAllText(_testCsvPath, csvContent);

            // Act
            SearchTermManager.ImportSearchTermsFromFile(_testCsvPath, _testQueryList);

            // Assert
            Assert.AreEqual(1, _testQueryList.ChildrenCore.Count, "Should import one keyword");
            var importedKeyword = _testQueryList.ChildrenCore[0];
            Assert.AreEqual(string.Empty, importedKeyword.JobId, "JobId should default to empty string");
            Assert.AreEqual(0, importedKeyword.RequiredQuantity, "RequiredQuantity should default to 0");
            Assert.AreEqual(0, importedKeyword.PurchasedQuantity, "PurchasedQuantity should default to 0");
        }

        [TestMethod]
        public void ImportSearchTermsFromFile_WithInvalidQuantityValues_HandlesGracefully()
        {
            // Arrange
            var csvContent = @"Id,eBay Search Alias,Keywords,Job ID,Required Quantity,Purchased Quantity,Keyword enabled
keyword-1,Test Keyword,test keywords,JOB-001,invalid,not_a_number,true";

            File.WriteAllText(_testCsvPath, csvContent);

            // Act
            SearchTermManager.ImportSearchTermsFromFile(_testCsvPath, _testQueryList);

            // Assert
            Assert.AreEqual(1, _testQueryList.ChildrenCore.Count, "Should import one keyword despite invalid quantities");
            var importedKeyword = _testQueryList.ChildrenCore[0];
            Assert.AreEqual("JOB-001", importedKeyword.JobId, "JobId should be imported correctly");
            Assert.AreEqual(0, importedKeyword.RequiredQuantity, "Invalid RequiredQuantity should default to 0");
            Assert.AreEqual(0, importedKeyword.PurchasedQuantity, "Invalid PurchasedQuantity should default to 0");
        }

        [TestMethod]
        public void ImportSearchTermsFromFile_WithEmptyRestockerValues_HandlesCorrectly()
        {
            // Arrange
            var csvContent = @"Id,eBay Search Alias,Keywords,Job ID,Required Quantity,Purchased Quantity,Keyword enabled
keyword-1,Test Keyword,test keywords,,,,true";

            File.WriteAllText(_testCsvPath, csvContent);

            // Act
            SearchTermManager.ImportSearchTermsFromFile(_testCsvPath, _testQueryList);

            // Assert
            Assert.AreEqual(1, _testQueryList.ChildrenCore.Count, "Should import one keyword");
            var importedKeyword = _testQueryList.ChildrenCore[0];
            Assert.AreEqual(string.Empty, importedKeyword.JobId, "Empty JobId should remain empty");
            Assert.AreEqual(0, importedKeyword.RequiredQuantity, "Empty RequiredQuantity should default to 0");
            Assert.AreEqual(0, importedKeyword.PurchasedQuantity, "Empty PurchasedQuantity should default to 0");
        }

        #endregion

        #region Round-trip Tests

        [TestMethod]
        public void ExportThenImport_WithRestockerEnabled_PreservesRestockerFields()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = true;
            var originalKeyword = CreateTestKeyword();
            originalKeyword.JobId = "ROUNDTRIP-JOB-001";
            originalKeyword.RequiredQuantity = 10;
            originalKeyword.PurchasedQuantity = 3;

            var searchTermList = new List<Keyword2Find> { originalKeyword };

            // Act - Export
            var exportedContent = GetExportedContent(searchTermList);
            File.WriteAllText(_testCsvPath, exportedContent);

            // Act - Import
            var importQueryList = new QueryList();
            SearchTermManager.ImportSearchTermsFromFile(_testCsvPath, importQueryList);

            // Assert
            Assert.AreEqual(1, importQueryList.ChildrenCore.Count, "Should import one keyword");
            var importedKeyword = importQueryList.ChildrenCore[0];
            Assert.AreEqual(originalKeyword.JobId, importedKeyword.JobId, "JobId should be preserved when RestockerEnabled is true");
            Assert.AreEqual(originalKeyword.RequiredQuantity, importedKeyword.RequiredQuantity, "RequiredQuantity should be preserved when RestockerEnabled is true");
            Assert.AreEqual(originalKeyword.PurchasedQuantity, importedKeyword.PurchasedQuantity, "PurchasedQuantity should be preserved when RestockerEnabled is true");
        }

        [TestMethod]
        public void ExportThenImport_WithRestockerDisabled_DoesNotIncludeRestockerFields()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = false;
            var originalKeyword = CreateTestKeyword();
            originalKeyword.JobId = "SHOULD-NOT-EXPORT";
            originalKeyword.RequiredQuantity = 10;
            originalKeyword.PurchasedQuantity = 3;

            var searchTermList = new List<Keyword2Find> { originalKeyword };

            // Act - Export
            var exportedContent = GetExportedContent(searchTermList);
            File.WriteAllText(_testCsvPath, exportedContent);

            // Act - Import
            var importQueryList = new QueryList();
            SearchTermManager.ImportSearchTermsFromFile(_testCsvPath, importQueryList);

            // Assert
            Assert.AreEqual(1, importQueryList.ChildrenCore.Count, "Should import one keyword");
            var importedKeyword = importQueryList.ChildrenCore[0];
            // When RestockerEnabled is false, these fields should have default values since they weren't exported
            Assert.AreEqual(string.Empty, importedKeyword.JobId, "JobId should be empty when RestockerEnabled is false");
            Assert.AreEqual(0, importedKeyword.RequiredQuantity, "RequiredQuantity should be 0 when RestockerEnabled is false");
            Assert.AreEqual(0, importedKeyword.PurchasedQuantity, "PurchasedQuantity should be 0 when RestockerEnabled is false");
        }

        #endregion

        #region Integration Tests

        [TestMethod]
        public void ImportSearchTermsFromFile_WithMixedValidAndInvalidData_ProcessesCorrectly()
        {
            // Arrange
            var csvContent = @"Id,eBay Search Alias,Keywords,Job ID,Required Quantity,Purchased Quantity,Keyword enabled
keyword-1,Valid Keyword 1,test keywords,JOB-001,5,2,true
keyword-2,Valid Keyword 2,more keywords,JOB-002,10,0,true
keyword-3,Invalid Quantities,invalid data,JOB-003,abc,xyz,true
keyword-4,Empty Values,empty test,,,true
keyword-5,Partial Data,partial test,JOB-005,7,,true";

            File.WriteAllText(_testCsvPath, csvContent);

            // Act
            SearchTermManager.ImportSearchTermsFromFile(_testCsvPath, _testQueryList);

            // Assert
            Assert.AreEqual(5, _testQueryList.ChildrenCore.Count, "Should import all 5 keywords");

            // Check first keyword (valid data)
            var keyword1 = _testQueryList.ChildrenCore[0];
            Assert.AreEqual("JOB-001", keyword1.JobId);
            Assert.AreEqual(5, keyword1.RequiredQuantity);
            Assert.AreEqual(2, keyword1.PurchasedQuantity);

            // Check third keyword (invalid quantities)
            var keyword3 = _testQueryList.ChildrenCore[2];
            Assert.AreEqual("JOB-003", keyword3.JobId);
            Assert.AreEqual(0, keyword3.RequiredQuantity); // Should default to 0
            Assert.AreEqual(0, keyword3.PurchasedQuantity); // Should default to 0

            // Check fourth keyword (empty values)
            var keyword4 = _testQueryList.ChildrenCore[3];
            Assert.AreEqual(string.Empty, keyword4.JobId);
            Assert.AreEqual(0, keyword4.RequiredQuantity);
            Assert.AreEqual(0, keyword4.PurchasedQuantity);

            // Check fifth keyword (partial data)
            var keyword5 = _testQueryList.ChildrenCore[4];
            Assert.AreEqual("JOB-005", keyword5.JobId);
            Assert.AreEqual(7, keyword5.RequiredQuantity);
            Assert.AreEqual(0, keyword5.PurchasedQuantity); // Empty should default to 0
        }

        [TestMethod]
        public void ExportSearchesToFile_WithMultipleKeywords_ExportsAllCorrectly()
        {
            // Arrange
            var keywords = new List<Keyword2Find>
            {
                CreateTestKeywordWithRestocker("keyword-1", "Keyword 1", "JOB-001", 5, 2),
                CreateTestKeywordWithRestocker("keyword-2", "Keyword 2", "JOB-002", 10, 0),
                CreateTestKeywordWithRestocker("keyword-3", "Keyword 3", "", 0, 0)
            };

            // Act
            var exportedContent = GetExportedContent(keywords);
            var lines = exportedContent.Split(new[] { "\r\n" }, StringSplitOptions.RemoveEmptyEntries);

            // Assert
            Assert.AreEqual(4, lines.Length, "Should have header + 3 data rows"); // Header + 3 keywords

            // Check that all keywords are exported
            Assert.IsTrue(lines[1].Contains("JOB-001"), "First keyword should contain JOB-001");
            Assert.IsTrue(lines[2].Contains("JOB-002"), "Second keyword should contain JOB-002");
            Assert.IsTrue(lines[3].Contains("Keyword 3"), "Third keyword should be exported");
        }

        [TestMethod]
        public void ImportSearchTermsFromFile_WithCaseInsensitiveHeaders_WorksCorrectly()
        {
            // Arrange - Test case insensitive column mapping
            var csvContent = @"id,ebay search alias,keywords,job id,required quantity,purchased quantity,keyword enabled
keyword-1,Test Keyword,test keywords,JOB-001,5,2,true";

            File.WriteAllText(_testCsvPath, csvContent);

            // Act
            SearchTermManager.ImportSearchTermsFromFile(_testCsvPath, _testQueryList);

            // Assert
            Assert.AreEqual(1, _testQueryList.ChildrenCore.Count, "Should import one keyword");
            var importedKeyword = _testQueryList.ChildrenCore[0];
            Assert.AreEqual("JOB-001", importedKeyword.JobId, "JobId should be imported correctly with lowercase headers");
            Assert.AreEqual(5, importedKeyword.RequiredQuantity, "RequiredQuantity should be imported correctly");
            Assert.AreEqual(2, importedKeyword.PurchasedQuantity, "PurchasedQuantity should be imported correctly");
        }

        [TestMethod]
        public void ImportSearchTermsFromFile_WithExtraColumns_IgnoresExtraColumns()
        {
            // Arrange
            var csvContent = @"Id,eBay Search Alias,Keywords,Job ID,Required Quantity,Purchased Quantity,Extra Column,Another Extra,Keyword enabled
keyword-1,Test Keyword,test keywords,JOB-001,5,2,extra data,more extra,true";

            File.WriteAllText(_testCsvPath, csvContent);

            // Act
            SearchTermManager.ImportSearchTermsFromFile(_testCsvPath, _testQueryList);

            // Assert
            Assert.AreEqual(1, _testQueryList.ChildrenCore.Count, "Should import one keyword");
            var importedKeyword = _testQueryList.ChildrenCore[0];
            Assert.AreEqual("JOB-001", importedKeyword.JobId, "JobId should be imported correctly");
            Assert.AreEqual(5, importedKeyword.RequiredQuantity, "RequiredQuantity should be imported correctly");
            Assert.AreEqual(2, importedKeyword.PurchasedQuantity, "PurchasedQuantity should be imported correctly");
        }

        #endregion

        #region Edge Case Tests

        [TestMethod]
        public void ImportSearchTermsFromFile_WithNegativeQuantities_HandlesCorrectly()
        {
            // Arrange
            var csvContent = @"Id,eBay Search Alias,Keywords,Job ID,Required Quantity,Purchased Quantity,Keyword enabled
keyword-1,Test Keyword,test keywords,JOB-001,-5,-2,true";

            File.WriteAllText(_testCsvPath, csvContent);

            // Act
            SearchTermManager.ImportSearchTermsFromFile(_testCsvPath, _testQueryList);

            // Assert
            Assert.AreEqual(1, _testQueryList.ChildrenCore.Count, "Should import one keyword");
            var importedKeyword = _testQueryList.ChildrenCore[0];
            Assert.AreEqual("JOB-001", importedKeyword.JobId, "JobId should be imported correctly");
            Assert.AreEqual(-5, importedKeyword.RequiredQuantity, "Negative RequiredQuantity should be preserved");
            Assert.AreEqual(-2, importedKeyword.PurchasedQuantity, "Negative PurchasedQuantity should be preserved");
        }

        [TestMethod]
        public void Export_WithSpecialCharactersInJobId_HandlesCorrectly()
        {
            // Arrange
            var keyword = CreateTestKeyword();
            keyword.JobId = "JOB-001,\"Special\",Characters";
            keyword.RequiredQuantity = 5;
            keyword.PurchasedQuantity = 2;

            // Act
            var exportedRow = SearchTermManager.Export(keyword);

            // Assert
            Assert.IsTrue(exportedRow.Contains("JOB-001,\"Special\",Characters"), "Special characters in JobId should be handled correctly");
        }

        [TestMethod]
        public void ImportSearchTermsFromFile_WithLargeQuantities_HandlesCorrectly()
        {
            // Arrange
            var csvContent = @"Id,eBay Search Alias,Keywords,Job ID,Required Quantity,Purchased Quantity,Keyword enabled
keyword-1,Test Keyword,test keywords,JOB-001,999999,888888,true";

            File.WriteAllText(_testCsvPath, csvContent);

            // Act
            SearchTermManager.ImportSearchTermsFromFile(_testCsvPath, _testQueryList);

            // Assert
            Assert.AreEqual(1, _testQueryList.ChildrenCore.Count, "Should import one keyword");
            var importedKeyword = _testQueryList.ChildrenCore[0];
            Assert.AreEqual(999999, importedKeyword.RequiredQuantity, "Large RequiredQuantity should be imported correctly");
            Assert.AreEqual(888888, importedKeyword.PurchasedQuantity, "Large PurchasedQuantity should be imported correctly");
        }

        #endregion

        #region UpdateSearch Tests

        [TestMethod]
        public void ImportSearchTermsFromFile_WithExistingKeyword_UpdatesRestockerFields()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = true;

            // Create an existing keyword in the QueryList
            var existingKeyword = CreateTestKeywordWithRestocker("keyword-1", "Original Keyword", "OLD-JOB", 1, 1);
            _testQueryList.ChildrenCore.Add(existingKeyword);

            // Create CSV with updated Restocker values for the same keyword ID
            var csvContent = @"Id,eBay Search Alias,Keywords,Job ID,Required Quantity,Purchased Quantity,Keyword enabled
keyword-1,Updated Keyword,updated keywords,NEW-JOB-001,10,5,true";

            File.WriteAllText(_testCsvPath, csvContent);

            // Act
            SearchTermManager.ImportSearchTermsFromFile(_testCsvPath, _testQueryList);

            // Assert
            Assert.AreEqual(1, _testQueryList.ChildrenCore.Count, "Should still have one keyword");
            var updatedKeyword = _testQueryList.ChildrenCore[0];

            // Verify that Restocker fields were updated
            Assert.AreEqual("NEW-JOB-001", updatedKeyword.JobId, "JobId should be updated to new value");
            Assert.AreEqual(10, updatedKeyword.RequiredQuantity, "RequiredQuantity should be updated to new value");
            Assert.AreEqual(5, updatedKeyword.PurchasedQuantity, "PurchasedQuantity should be updated to new value");

            // Verify other fields were also updated
            Assert.AreEqual("Updated Keyword", updatedKeyword.Alias, "Alias should be updated");
            Assert.AreEqual("updated keywords", updatedKeyword.Kws, "Keywords should be updated");
        }

        [TestMethod]
        public void ImportSearchTermsFromFile_WithExistingKeywordRestockerDisabled_DoesNotUpdateRestockerFields()
        {
            // Arrange
            ConnectionConfig.RestockerEnabled = false;

            // Create an existing keyword in the QueryList with Restocker values
            var existingKeyword = CreateTestKeywordWithRestocker("keyword-1", "Original Keyword", "OLD-JOB", 1, 1);
            _testQueryList.ChildrenCore.Add(existingKeyword);

            // Create CSV without Restocker columns (since RestockerEnabled is false)
            var csvContent = @"Id,eBay Search Alias,Keywords,Keyword enabled
keyword-1,Updated Keyword,updated keywords,true";

            File.WriteAllText(_testCsvPath, csvContent);

            // Act
            SearchTermManager.ImportSearchTermsFromFile(_testCsvPath, _testQueryList);

            // Assert
            Assert.AreEqual(1, _testQueryList.ChildrenCore.Count, "Should still have one keyword");
            var updatedKeyword = _testQueryList.ChildrenCore[0];

            // Verify that Restocker fields were reset to defaults (since columns weren't in CSV)
            Assert.AreEqual(string.Empty, updatedKeyword.JobId, "JobId should be reset to empty when not in CSV");
            Assert.AreEqual(0, updatedKeyword.RequiredQuantity, "RequiredQuantity should be reset to 0 when not in CSV");
            Assert.AreEqual(0, updatedKeyword.PurchasedQuantity, "PurchasedQuantity should be reset to 0 when not in CSV");

            // Verify other fields were updated
            Assert.AreEqual("Updated Keyword", updatedKeyword.Alias, "Alias should be updated");
            Assert.AreEqual("updated keywords", updatedKeyword.Kws, "Keywords should be updated");
        }

        #endregion

        #region Helper Methods

        private Keyword2Find CreateTestKeyword()
        {
            return new Keyword2Find
            {
                Id = "test-keyword-1",
                Alias = "Test Keyword",
                Kws = "test keywords",
                KeywordEnabled = CheckState.Checked,
                PriceMin = 10.00,
                PriceMax = 100.00
            };
        }

        private Keyword2Find CreateTestKeywordWithRestocker(string id, string alias, string jobId, int requiredQuantity, int purchasedQuantity)
        {
            return new Keyword2Find
            {
                Id = id,
                Alias = alias,
                Kws = "test keywords",
                KeywordEnabled = CheckState.Checked,
                PriceMin = 10.00,
                PriceMax = 100.00,
                JobId = jobId,
                RequiredQuantity = requiredQuantity,
                PurchasedQuantity = purchasedQuantity
            };
        }

        private string GetExportedContent(List<Keyword2Find> searchTermList)
        {
            // Simulate the export process without writing to file
            var header = new List<string>
            {
                "Id", "Sub Search Id", "eBay Search Alias", "Sub Search Alias", "Keywords",
                "Keyword enabled", "Search in Description", "Price Min", "Price Max",
                "Category ID", "Condition", "Site", "Located in", "Ships to", "Ship Zipcode",
                "Sellers", "Include/Exclude", "Interval", "Listing Type", "Filter", "View"
            };

            // Add Restocker fields only if RestockerEnabled is true
            if (ConnectionConfig.RestockerEnabled)
            {
                header.Add("Job ID");
                header.Add("Required Quantity");
                header.Add("Purchased Quantity");
            }

            var fileContents = uBuyFirst.Tools.Helpers.CreateCSVRow(header) + "\r\n";
            foreach (var item in searchTermList)
            {
                fileContents += SearchTermManager.Export(item);
            }

            return fileContents;
        }

        private List<string> ParseCsvRow(string csvRow)
        {
            // Simple CSV parser for testing - handles basic cases
            var cells = new List<string>();
            var currentCell = "";
            var inQuotes = false;

            for (int i = 0; i < csvRow.Length; i++)
            {
                var c = csvRow[i];
                if (c == '"')
                {
                    inQuotes = !inQuotes;
                }
                else if (c == ',' && !inQuotes)
                {
                    cells.Add(currentCell.Trim());
                    currentCell = "";
                }
                else if (c != '\r' && c != '\n')
                {
                    currentCell += c;
                }
            }

            if (!string.IsNullOrEmpty(currentCell))
            {
                cells.Add(currentCell.Trim());
            }

            return cells;
        }

        private int GetJobIdColumnIndex()
        {
            // Based on the expected header order when RestockerEnabled is true
            return ConnectionConfig.RestockerEnabled ? 21 : -1; // "Job ID" is the 22nd column (0-based index 21) when enabled
        }

        private int GetRequiredQuantityColumnIndex()
        {
            return ConnectionConfig.RestockerEnabled ? 22 : -1; // "Required Quantity" is the 23rd column when enabled
        }

        private int GetPurchasedQuantityColumnIndex()
        {
            return ConnectionConfig.RestockerEnabled ? 23 : -1; // "Purchased Quantity" is the 24th column when enabled
        }

        #endregion
    }
}
