﻿using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using DevExpress.XtraBars;
using DevExpress.XtraBars.Docking;
using uBuyFirst.Prefs;
using uBuyFirst.Views;

namespace uBuyFirst
{
    public partial class Form1
    {
        private void barPanels_CheckedChanged(object sender, ItemClickEventArgs e)
        {
            DockPanel panel = null;
            foreach (DockPanel a in dockManager1.Panels)
            {
                if (a.Text == e.Item.Tag.ToString())
                {
                    panel = a;
                    break;
                }
            }

            if (panel == null) return;

            var panelVisible = ((BarCheckItem) sender).Checked;
            ResultsView.TogglePanelVisibility(panelVisible, panel);
        }

        private void barViews_CheckedChanged(object sender, ItemClickEventArgs e)
        {
            DockPanel clickedPanel = null;
            foreach (DockPanel panel in dockManager1.Panels)
            {
                if (panel.Tag != null && panel.Tag.ToString() == e.Item.Tag.ToString())
                {
                    clickedPanel = panel;
                    break;
                }
            }

            if (clickedPanel == null)
                return;

            if (((BarCheckItem) sender).Checked)
            {
                clickedPanel.Show();
            }
            else
            {
                clickedPanel.Hide();
            }

            if (clickedPanel.FloatLocation.X < 0)
            {
                clickedPanel.FloatLocation = new Point(0, clickedPanel.Location.Y);
            }

            if (clickedPanel.FloatLocation.Y < 0)
            {
                clickedPanel.FloatLocation = new Point(clickedPanel.Location.X, 0);
            }
        }

        private void barButtonItemDockPanels_ItemClick(object sender, ItemClickEventArgs e)
        {
            if (!(e.Item is BarButtonItem button))
                return;

            if (button.Name == barButtonItemDockPanels.Name)
            {
                var menuItems = CreateDockPanelsMenuItems();

                popupMenuDockPanels.BeginUpdate();
                popupMenuDockPanels.ItemLinks.Clear();
                popupMenuDockPanels.ItemLinks.AddRange(menuItems);

                var showSeparator = popupMenuDockPanels.ItemLinks.Count > 0;
                foreach (DockPanel panel in dockManager1.Panels)
                {
                    if (panel.Tag == null || !panel.Name.StartsWith("dockPnl"))
                        continue;

                    var item = CreateViewMenuItem(panel);
                    popupMenuDockPanels.ItemLinks.Add(item).BeginGroup = showSeparator;
                    showSeparator = false;
                }

                popupMenuDockPanels.EndUpdate();
            }

            if (button.Name != barButtonItemViews.Name)
                return;
            popupMenuDockPanels.ItemLinks.Clear();
            popupMenuDockPanels.BeginUpdate();
            foreach (DockPanel panel in dockManager1.Panels)
            {
                if (panel.Tag == null || !panel.Name.StartsWith("dockPnl"))
                    continue;

                var item = CreateViewMenuItem(panel);
                popupMenuDockPanels.ItemLinks.Add(item);
            }

            popupMenuDockPanels.EndUpdate();
        }

        private List<BarCheckItem> CreateDockPanelsMenuItems()
        {
            List<BarCheckItem> menuItems = new List<BarCheckItem>();

            foreach (DockPanel panel in dockManager1.Panels)
            {
                if (panel.Text.Contains("ontainer"))
                    continue;
                if (panel.Tag != null)
                {
                    if (panel.Name.StartsWith("dockPnl"))
                        continue;
                }

                menuItems.Add(CreatePanelMenuItem(panel));
            }

            return menuItems;
        }

        private BarCheckItem CreatePanelMenuItem(DockPanel panel)
        {
            var item = new BarCheckItem();
            item.Caption = panel.Header;
            if (string.IsNullOrEmpty(item.Caption))
            {
                item.Caption = panel.Text;
            }

            item.Tag = panel.Text;
            if (panel.Visibility == DockVisibility.Visible)
            {
                item.Checked = true;
            }

            item.CheckedChanged += barPanels_CheckedChanged;
            return item;
        }

        private BarCheckItem CreateViewMenuItem(DockPanel panel)
        {
            var item = new BarCheckItem();
            item.Caption = panel.Header;
            if (string.IsNullOrEmpty(item.Caption))
            {
                item.Caption = panel.Text;
            }

            item.Tag = panel.Tag;
            if (panel.Visibility == DockVisibility.Visible)
            {
                item.Checked = true;
            }

            item.CheckedChanged += barViews_CheckedChanged;
            return item;
        }

        private void ShowFloatPanel(string panelName)
        {
            var tabDocument = tabbedView1.Documents.FirstOrDefault(d => d.Header == panelName);
            tabbedView1.Controller.Float(tabDocument);
            var panel = dockManager1.Panels.FirstOrDefault(a => a.Name == panelName);
            if (panel == null)
            {
                panel = dockManager1.HiddenPanels.FirstOrDefault(a => a.Name == panelName);
                if (panel == null)
                    return;
            }

            if (panel.FloatLocation.X < 40)
            {
                panel.FloatLocation = new Point(40, panel.Location.Y);
            }

            if (panel.FloatLocation.Y < 40)
            {
                panel.FloatLocation = new Point(panel.Location.X, 40);
            }

            panel.MakeFloat();
            panel.Show();
        }

        private List<BarCheckItem> CreateOpenInBrowserMenuItems()
        {
            var menuItems = new List<BarCheckItem>();
            foreach (var itemCaption in Enum.GetNames(typeof(UserSettings.OpenInBrowserEnum)))
            {
                var item = new BarCheckItem();
                item.Name = itemCaption;
                item.Caption = itemCaption;
                if (itemCaption == UserSettings.OpenInBrowser.ToString())
                {
                    item.Checked = true;
                }
                else
                {
                    item.Checked = false;
                }

                item.CheckedChanged += barOpenInBrowser_CheckedChanged;
                menuItems.Add(item);
            }

            return menuItems;
        }

        private void barOpenInBrowser_CheckedChanged(object sender, ItemClickEventArgs e)
        {
            var clickedItem = (BarCheckItem) sender;
            if (clickedItem.Checked)
                UserSettings.OpenInBrowser = (UserSettings.OpenInBrowserEnum) Enum.Parse(typeof(UserSettings.OpenInBrowserEnum), clickedItem.Name);
        }
    }
}
