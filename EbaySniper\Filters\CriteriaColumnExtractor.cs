﻿using System;
using System.Collections.Generic;
using System.Linq;
using DevExpress.Data.Filtering;

namespace uBuyFirst.Filters
{
    public class CriteriaColumnExtractor
    {
        public static List<string> ExtractColumns(IEnumerable<CriteriaOperator> filters)
        {
            var columns = new HashSet<string>();
            foreach (var filter in filters)
            {
                if (filter == null)
                {
                    continue;
                }
                ExtractColumnsRecursive(filter, columns);
            }

            return columns.ToList();
        }

        private static void ExtractColumnsRecursive(CriteriaOperator criteria, HashSet<string> columns)
        {
            switch (criteria)
            {
                case OperandProperty operandProperty:
                    columns.Add(operandProperty.PropertyName);
                    break;

                case BinaryOperator binaryOperator:
                    ExtractColumnsRecursive(binaryOperator.LeftOperand, columns);
                    ExtractColumnsRecursive(binaryOperator.RightOperand, columns);
                    break;

                case BetweenOperator betweenOperator:
                    ExtractColumnsRecursive(betweenOperator.TestExpression, columns);
                    ExtractColumnsRecursive(betweenOperator.BeginExpression, columns);
                    ExtractColumnsRecursive(betweenOperator.EndExpression, columns);
                    break;

                case UnaryOperator unaryOperator:
                    ExtractColumnsRecursive(unaryOperator.Operand, columns);
                    break;

                case InOperator inOperator:
                    ExtractColumnsRecursive(inOperator.LeftOperand, columns);
                    foreach (var operand in inOperator.Operands)
                    {
                        ExtractColumnsRecursive(operand, columns);
                    }

                    break;

                case GroupOperator groupOperator:
                    foreach (var operand in groupOperator.Operands)
                    {
                        ExtractColumnsRecursive(operand, columns);
                    }

                    break;

                case FunctionOperator functionOperator:
                    foreach (var operand in functionOperator.Operands)
                    {
                        ExtractColumnsRecursive(operand, columns);
                    }

                    break;

                case AggregateOperand aggregateOperand:
                    ExtractColumnsRecursive(aggregateOperand.CollectionProperty, columns);
                    ExtractColumnsRecursive(aggregateOperand.Condition, columns);
                    ExtractColumnsRecursive(aggregateOperand.AggregatedExpression, columns);
                    break;

                case JoinOperand joinOperand:
                    ExtractColumnsRecursive(joinOperand.Condition, columns);
                    ExtractColumnsRecursive(joinOperand.AggregatedExpression, columns);
                    break;

                case OperandValue operandValue:
                    // OperandValue doesn't contain column names
                    break;

                //case ConstantValue constantValue:
                //    // ConstantValue doesn't contain column names
                //    break;

                //case NullValue nullValue:
                //    // NullValue doesn't contain column names
                //    break;

                default:
                    throw new NotSupportedException($"Unsupported criteria operator type: {criteria.GetType()}");
            }
        }
    }
}
