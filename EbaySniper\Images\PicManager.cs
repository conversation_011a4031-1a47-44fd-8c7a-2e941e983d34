﻿using DevExpress.XtraBars.Ribbon;
using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using uBuyFirst.Prefs;
using uBuyFirst.Tools;

namespace uBuyFirst.Images
{
    internal class PicManager
    {
        private readonly GalleryControl _galleryControl;
        private CancellationTokenSource _ctsLargeImage;
        private CancellationTokenSource _ctsSmallImage;

        public PicManager(GalleryControl galleryControl)
        {
            _galleryControl = galleryControl;
            _ctsLargeImage = new CancellationTokenSource();
            _ctsSmallImage = new CancellationTokenSource();
        }

        internal void GetLargeImages(GalleryItem galleryItem, bool bLargeSizeChanged, int sizePercent)
        {
            _ctsLargeImage.Cancel();
            _ctsLargeImage = new CancellationTokenSource();
            var ctLargeImage = _ctsLargeImage.Token;
            GetLargeHoverImage(galleryItem, ctLargeImage, bLargeSizeChanged, sizePercent).ContinueWith(t =>
            {
                var i = 0;
                while (_galleryControl.Gallery.Groups.Last().Items.Count > i)
                {
                    _galleryControl.Gallery.ShowItemText = true;
                    var item = _galleryControl.Gallery.Groups.Last().Items[i];
                    GetLargeHoverImage(item, ctLargeImage, bLargeSizeChanged, sizePercent).ConfigureAwait(true);
                    i++;
                }
            }, ctLargeImage, TaskContinuationOptions.OnlyOnRanToCompletion, TaskScheduler.FromCurrentSynchronizationContext());
        }

        private async Task GetLargeHoverImage(GalleryItem item, CancellationToken ctLargeImage, bool bLargeSizeChanged, int sizePercent)
        {
            if (Form1.Instance.Disposing || Form1.Instance.IsDisposed)
                return;

            _galleryControl.Gallery.ShowItemText = true;
            if (item == null || item.Image == null || item.HoverImage == null)
                return;

            var alreadyLoaded = !bLargeSizeChanged;

            // Take care of case when first load the GUI so ensure first initial download.
            if (item.Image.Size == item.HoverImage.Size)
            {
                alreadyLoaded = false;
            }

            if (alreadyLoaded)
                return;

            if (!(item.Tag is string pictureUrl))
                return;

            pictureUrl = Regex.Replace(pictureUrl, @"\$_[0-9]+", "$$_10");
            pictureUrl = pictureUrl.Replace("s-l500", "s-l1600");

            try
            {
                var imagePath = await ImageTools.GetImageFromDiskOrInternet(pictureUrl);
                if (ctLargeImage.IsCancellationRequested)
                    return;
                if (!File.Exists(imagePath))
                    return;

                // Safely get screen dimensions
                int maximumWidth, maximumHeight;
                if (Form1.Instance.IsHandleCreated)
                {
                    var screen = Screen.FromControl(Form1.Instance);
                    maximumWidth = screen.Bounds.Width;
                    maximumHeight = screen.Bounds.Height - Screen.PrimaryScreen.Bounds.Height / 100;
                }
                else
                {
                    // Fallback to primary screen dimensions
                    maximumWidth = Screen.PrimaryScreen.Bounds.Width;
                    maximumHeight = Screen.PrimaryScreen.Bounds.Height - Screen.PrimaryScreen.Bounds.Height / 100;
                }

                maximumWidth = Convert.ToInt32(maximumWidth / DevExpress.Skins.DpiProvider.Default.DpiScaleFactor);
                maximumHeight = Convert.ToInt32(maximumHeight / DevExpress.Skins.DpiProvider.Default.DpiScaleFactor);
                var percent = (sizePercent / (double)100);
                maximumWidth = Convert.ToInt32(percent * maximumWidth);
                maximumHeight = Convert.ToInt32(percent * maximumHeight);
                item.Description = @"...";

                var largeImage = await ImageTools.ReadFileToBitmap(imagePath);
                if (ctLargeImage.IsCancellationRequested || largeImage == null)
                    return;

                largeImage = await Task.Run(() => ImageProcessor.ResizeImage(largeImage, maximumWidth, maximumHeight, true, false));
                if (ctLargeImage.IsCancellationRequested)
                    return;
                _galleryControl.Gallery.AllowHoverImages = true;
                item.Description = @" ";
                if (largeImage == null || Form1.Instance.Disposing || Form1.Instance.IsDisposed)
                    return;

                item.HoverImage = largeImage;
            }
            catch (Exception ex)
            {
                if (Debugger.IsAttached)
                    ExM.ubuyExceptionHandler("Test exception: ", ex);
                Form1.Log.Error("{0}, {1} {3}Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, ex, ex.Message);
            }
        }

        internal void GetSmallImages(GalleryItem galleryItem)
        {
            _ctsSmallImage.Cancel();
            _ctsSmallImage = new CancellationTokenSource();
            var ctSmallImage = _ctsSmallImage.Token;
            GetSmallHoverImage(galleryItem, ctSmallImage);

            var i = 0;
            while (_galleryControl.Gallery.Groups.Last().Items.Count > i)
            {
                _galleryControl.Gallery.ShowItemText = true;
                var item = _galleryControl.Gallery.Groups.Last().Items[i];
                GetSmallHoverImage(item, ctSmallImage);
                i++;
            }

        }

        private void GetSmallHoverImage(GalleryItem item, CancellationToken ctSmallImage)
        {
            if (Form1.Instance.Disposing || Form1.Instance.IsDisposed)
                return;

            _galleryControl.Gallery.ShowItemText = true;
            if (item == null || item.Image == null || item.HoverImage == null)
                return;

            // If image and hover image are the same it is already set to the original.
            var alreadyLoaded = item.Image.Size == item.HoverImage.Size;
            if (alreadyLoaded)
                return;

            if (!(item.Tag is string pictureUrl))
                return;

            if (ctSmallImage.IsCancellationRequested)
                return;

            // Set hover image to the original.
            item.HoverImage = item.Image;
        }
    }
}
