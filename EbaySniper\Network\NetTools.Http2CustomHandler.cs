﻿using System;
using System.Net.Http;
using System.Net.Security;
using System.Security.Cryptography.X509Certificates;
using System.Threading.Tasks;
using uBuyFirst.Tools;

namespace uBuyFirst.Network
{
    public class Http2CustomHandler : WinHttpHandler
    {
        // Constructor with an optional parameter for the callback
        public Http2CustomHandler(Func<HttpRequestMessage, X509Certificate2, X509Chain, SslPolicyErrors, bool> serverCertificateValidationCallback = null)
        {
            ServerCertificateValidationCallback = serverCertificateValidationCallback;
        }

        // Method to set the callback
        public void SetServerCertificateValidationCallback(Func<HttpRequestMessage, X509Certificate2, X509Chain, SslPolicyErrors, bool> callback)
        {
            ServerCertificateValidationCallback = callback;
        }

        protected override Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, System.Threading.CancellationToken cancellationToken)
        {
            // The callback is now set via constructor or method, so no need for the hardcoded line
            request.Version = new Version(2, 0);
            
            return base.SendAsync(request, cancellationToken);
        }
    }
}
