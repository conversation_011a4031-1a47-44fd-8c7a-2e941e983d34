﻿using System.ComponentModel;
using DevExpress.XtraBars;
using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.DXErrorProvider;
using DevExpress.DataAccess.UI;
using DevExpress.XtraVerticalGrid;
using uBuyFirst.CustomClasses;

namespace uBuyFirst
{
    partial class FormXfilters
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>

        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule conditionValidationRule1 = new DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormXfilters));
            this.formAssistant1 = new DevExpress.XtraBars.FormAssistant();
            this.ribbonStatusBar1 = new DevExpress.XtraBars.Ribbon.RibbonStatusBar();
            this.boxKeyword = new DevExpress.XtraEditors.ComboBoxEdit();
            this.propertyGridControl1 = new DevExpress.XtraVerticalGrid.PropertyGridControl();
            this.boxAction = new DevExpress.XtraEditors.ComboBoxEdit();
            this.gridPanel = new uBuyFirst.CustomClasses.UnboundExpressionPanel();
            this.btnAccept = new DevExpress.XtraEditors.SimpleButton();
            this.filterEditorControl1 = new DevExpress.DataAccess.UI.FilterEditorControl();
            this.boxColumns = new DevExpress.XtraEditors.ComboBoxEdit();
            this.ribbonStatusBar2 = new DevExpress.XtraBars.Ribbon.RibbonStatusBar();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.lblFormatColumn = new DevExpress.XtraEditors.LabelControl();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.groupControl2 = new DevExpress.XtraEditors.GroupControl();
            this.txtAlias = new DevExpress.XtraEditors.TextEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.lblFormatStyle = new DevExpress.XtraEditors.LabelControl();
            this.dxValidationProvider1 = new DevExpress.XtraEditors.DXErrorProvider.DXValidationProvider(this.components);
            this.ribbonControl1 = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.hyperlinkLabelControl1 = new DevExpress.XtraEditors.HyperlinkLabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.boxKeyword.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.propertyGridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.boxAction.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridPanel)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.boxColumns.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).BeginInit();
            this.groupControl2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtAlias.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dxValidationProvider1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).BeginInit();
            this.SuspendLayout();
            // 
            // ribbonStatusBar1
            // 
            this.ribbonStatusBar1.Location = new System.Drawing.Point(0, 241);
            this.ribbonStatusBar1.Name = "ribbonStatusBar1";
            this.ribbonStatusBar1.Size = new System.Drawing.Size(334, 20);
            // 
            // boxKeyword
            // 
            this.boxKeyword.Location = new System.Drawing.Point(219, 12);
            this.boxKeyword.Name = "boxKeyword";
            this.boxKeyword.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.boxKeyword.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.boxKeyword.Size = new System.Drawing.Size(150, 20);
            this.boxKeyword.TabIndex = 0;
            this.boxKeyword.Visible = false;
            // 
            // propertyGridControl1
            // 
            this.propertyGridControl1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left)));
            this.propertyGridControl1.Location = new System.Drawing.Point(6, 108);
            this.propertyGridControl1.Name = "propertyGridControl1";
            this.propertyGridControl1.OptionsView.AllowReadOnlyRowAppearance = DevExpress.Utils.DefaultBoolean.True;
            this.propertyGridControl1.Size = new System.Drawing.Size(229, 192);
            this.propertyGridControl1.TabIndex = 2;
            // 
            // boxAction
            // 
            this.boxAction.Location = new System.Drawing.Point(85, 29);
            this.boxAction.Name = "boxAction";
            this.boxAction.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.boxAction.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.boxAction.Size = new System.Drawing.Size(150, 20);
            this.boxAction.TabIndex = 0;
            this.boxAction.SelectedIndexChanged += new System.EventHandler(this.boxAction_SelectedIndexChanged);
            // 
            // gridPanel
            // 
            this.gridPanel.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.gridPanel.Location = new System.Drawing.Point(717, 38);
            this.gridPanel.Name = "gridPanel";
            this.gridPanel.Size = new System.Drawing.Size(556, 334);
            this.gridPanel.TabIndex = 4;
            this.gridPanel.Visible = false;
            // 
            // btnAccept
            // 
            this.btnAccept.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnAccept.Location = new System.Drawing.Point(493, 413);
            this.btnAccept.Name = "btnAccept";
            this.btnAccept.Size = new System.Drawing.Size(75, 23);
            this.btnAccept.TabIndex = 5;
            this.btnAccept.Text = "Ok";
            this.btnAccept.Click += new System.EventHandler(this.btnAccept_Click);
            this.btnAccept.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.FormXfilters_KeyPress);
            // 
            // filterEditorControl1
            // 
            this.filterEditorControl1.AllowDrop = true;
            this.filterEditorControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.filterEditorControl1.AppearanceEmptyValueColor = System.Drawing.Color.Empty;
            this.filterEditorControl1.AppearanceFieldNameColor = System.Drawing.Color.Empty;
            this.filterEditorControl1.AppearanceGroupOperatorColor = System.Drawing.Color.Empty;
            this.filterEditorControl1.AppearanceOperatorColor = System.Drawing.Color.Empty;
            this.filterEditorControl1.AppearanceValueColor = System.Drawing.Color.Empty;
            this.filterEditorControl1.Location = new System.Drawing.Point(6, 28);
            this.filterEditorControl1.Name = "filterEditorControl1";
            this.filterEditorControl1.NodeSeparatorHeight = 2;
            this.filterEditorControl1.ShowDateTimeFunctions = DevExpress.XtraEditors.DateTimeFunctionsShowMode.Advanced;
            this.filterEditorControl1.ShowGroupCommandsIcon = true;
            this.filterEditorControl1.ShowOperandTypeIcon = true;
            this.filterEditorControl1.Size = new System.Drawing.Size(277, 314);
            this.filterEditorControl1.TabIndex = 6;
            this.filterEditorControl1.Text = "filterEditorControl1";
            this.filterEditorControl1.UseMenuForOperandsAndOperators = false;
            this.filterEditorControl1.FilterChanged += new DevExpress.XtraEditors.FilterChangedEventHandler(this.filterEditorControl1_FilterChanged);
            // 
            // boxColumns
            // 
            this.boxColumns.Location = new System.Drawing.Point(85, 54);
            this.boxColumns.Name = "boxColumns";
            this.boxColumns.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.boxColumns.Properties.DropDownRows = 20;
            this.boxColumns.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.boxColumns.Size = new System.Drawing.Size(150, 20);
            this.boxColumns.TabIndex = 0;
            // 
            // ribbonStatusBar2
            // 
            this.ribbonStatusBar2.Location = new System.Drawing.Point(0, 320);
            this.ribbonStatusBar2.Name = "ribbonStatusBar2";
            this.ribbonStatusBar2.Size = new System.Drawing.Size(1518, 20);
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(6, 31);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(30, 13);
            this.labelControl1.TabIndex = 8;
            this.labelControl1.Text = "Action";
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(167, 16);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(46, 13);
            this.labelControl2.TabIndex = 8;
            this.labelControl2.Text = "Keyword:";
            this.labelControl2.Visible = false;
            // 
            // lblFormatColumn
            // 
            this.lblFormatColumn.Location = new System.Drawing.Point(5, 56);
            this.lblFormatColumn.Name = "lblFormatColumn";
            this.lblFormatColumn.Size = new System.Drawing.Size(70, 13);
            this.lblFormatColumn.TabIndex = 8;
            this.lblFormatColumn.Text = "Format column";
            // 
            // groupControl1
            // 
            this.groupControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupControl1.Controls.Add(this.filterEditorControl1);
            this.groupControl1.CustomHeaderButtonsLocation = DevExpress.Utils.GroupElementLocation.AfterText;
            this.groupControl1.Location = new System.Drawing.Point(12, 38);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(288, 345);
            this.groupControl1.TabIndex = 10;
            this.groupControl1.Text = "If condition is:";
            // 
            // groupControl2
            // 
            this.groupControl2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupControl2.Controls.Add(this.txtAlias);
            this.groupControl2.Controls.Add(this.labelControl3);
            this.groupControl2.Controls.Add(this.propertyGridControl1);
            this.groupControl2.Controls.Add(this.boxColumns);
            this.groupControl2.Controls.Add(this.lblFormatStyle);
            this.groupControl2.Controls.Add(this.lblFormatColumn);
            this.groupControl2.Controls.Add(this.boxAction);
            this.groupControl2.Controls.Add(this.labelControl1);
            this.groupControl2.CustomHeaderButtonsLocation = DevExpress.Utils.GroupElementLocation.AfterText;
            this.groupControl2.Location = new System.Drawing.Point(326, 38);
            this.groupControl2.Name = "groupControl2";
            this.groupControl2.Size = new System.Drawing.Size(242, 345);
            this.groupControl2.TabIndex = 11;
            this.groupControl2.Text = "Then do:";
            // 
            // txtAlias
            // 
            this.txtAlias.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.txtAlias.Location = new System.Drawing.Point(73, 317);
            this.txtAlias.Name = "txtAlias";
            this.txtAlias.Size = new System.Drawing.Size(162, 20);
            this.txtAlias.TabIndex = 15;
            conditionValidationRule1.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank;
            conditionValidationRule1.ErrorText = "Alias should not be empty";
            conditionValidationRule1.ErrorType = DevExpress.XtraEditors.DXErrorProvider.ErrorType.Warning;
            conditionValidationRule1.Value1 = "";
            this.dxValidationProvider1.SetValidationRule(this.txtAlias, conditionValidationRule1);
            this.txtAlias.TextChanged += new System.EventHandler(this.txtAlias_TextChanged);
            // 
            // labelControl3
            // 
            this.labelControl3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.labelControl3.Location = new System.Drawing.Point(6, 321);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(48, 13);
            this.labelControl3.TabIndex = 8;
            this.labelControl3.Text = "Filter alias";
            // 
            // lblFormatStyle
            // 
            this.lblFormatStyle.Location = new System.Drawing.Point(6, 89);
            this.lblFormatStyle.Name = "lblFormatStyle";
            this.lblFormatStyle.Size = new System.Drawing.Size(60, 13);
            this.lblFormatStyle.TabIndex = 8;
            this.lblFormatStyle.Text = "Format style";
            // 
            // dxValidationProvider1
            // 
            this.dxValidationProvider1.ValidationMode = DevExpress.XtraEditors.DXErrorProvider.ValidationMode.Manual;
            // 
            // ribbonControl1
            // 
            this.ribbonControl1.ExpandCollapseItem.Id = 0;
            this.ribbonControl1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.ribbonControl1.ExpandCollapseItem,
            this.ribbonControl1.SearchEditItem});
            this.ribbonControl1.Location = new System.Drawing.Point(0, 0);
            this.ribbonControl1.MaxItemId = 1;
            this.ribbonControl1.Name = "ribbonControl1";
            this.ribbonControl1.OptionsPageCategories.ShowCaptions = false;
            this.ribbonControl1.ShowApplicationButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowDisplayOptionsMenuButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowExpandCollapseButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowPageHeadersMode = DevExpress.XtraBars.Ribbon.ShowPageHeadersMode.Hide;
            this.ribbonControl1.ShowQatLocationSelector = false;
            this.ribbonControl1.ShowToolbarCustomizeItem = false;
            this.ribbonControl1.Size = new System.Drawing.Size(579, 49);
            this.ribbonControl1.Toolbar.ShowCustomizeItem = false;
            this.ribbonControl1.ToolbarLocation = DevExpress.XtraBars.Ribbon.RibbonQuickAccessToolbarLocation.Hidden;
            // 
            // hyperlinkLabelControl1
            // 
            this.hyperlinkLabelControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.hyperlinkLabelControl1.Cursor = System.Windows.Forms.Cursors.Hand;
            this.hyperlinkLabelControl1.Location = new System.Drawing.Point(12, 416);
            this.hyperlinkLabelControl1.Name = "hyperlinkLabelControl1";
            this.hyperlinkLabelControl1.Size = new System.Drawing.Size(21, 13);
            this.hyperlinkLabelControl1.TabIndex = 13;
            this.hyperlinkLabelControl1.Text = "Help";
            this.hyperlinkLabelControl1.Click += new System.EventHandler(this.hyperlinkLabelControl1_Click);
            // 
            // FormXfilters
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(579, 452);
            this.Controls.Add(this.hyperlinkLabelControl1);
            this.Controls.Add(this.groupControl2);
            this.Controls.Add(this.groupControl1);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.btnAccept);
            this.Controls.Add(this.boxKeyword);
            this.Controls.Add(this.ribbonControl1);
            this.IconOptions.Icon = ((System.Drawing.Icon)(resources.GetObject("FormXfilters.IconOptions.Icon")));
            this.MinimumSize = new System.Drawing.Size(560, 349);
            this.Name = "FormXfilters";
            this.Ribbon = this.ribbonControl1;
            this.Text = "Edit filter";
            this.Load += new System.EventHandler(this.FormXfilters_Load);
            this.LocationChanged += new System.EventHandler(this.FormXfilters_LocationChanged);
            this.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.FormXfilters_KeyPress);
            ((System.ComponentModel.ISupportInitialize)(this.boxKeyword.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.propertyGridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.boxAction.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.gridPanel)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.boxColumns.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl2)).EndInit();
            this.groupControl2.ResumeLayout(false);
            this.groupControl2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtAlias.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dxValidationProvider1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private FormAssistant formAssistant1;
        private RibbonStatusBar ribbonStatusBar1;
        private ComboBoxEdit boxKeyword;
        private PropertyGridControl propertyGridControl1;
        private ComboBoxEdit boxAction;
        private UnboundExpressionPanel gridPanel;
        private SimpleButton btnAccept;
        private FilterEditorControl filterEditorControl1;
        private ComboBoxEdit boxColumns;
        private RibbonStatusBar ribbonStatusBar2;
        private LabelControl labelControl1;
        private LabelControl labelControl2;
        private LabelControl lblFormatColumn;
        private GroupControl groupControl1;
        private GroupControl groupControl2;
        private LabelControl lblFormatStyle;
        private DXValidationProvider dxValidationProvider1;
        private TextEdit txtAlias;
        private LabelControl labelControl3;
        private RibbonControl ribbonControl1;
        private HyperlinkLabelControl hyperlinkLabelControl1;
    }
}