namespace uBuyFirst.Restocker.Models
{
    /// <summary>
    /// Represents the result of a synchronization operation
    /// </summary>
    public class SyncResult
    {
        /// <summary>
        /// Whether the operation was successful
        /// </summary>
        public bool Success { get; set; }
        
        /// <summary>
        /// The action that was performed: "Added", "Updated", "Skipped", "Error"
        /// </summary>
        public string Action { get; set; } = string.Empty;
        
        /// <summary>
        /// Additional message about the operation
        /// </summary>
        public string Message { get; set; } = string.Empty;
        
        /// <summary>
        /// Error message if the operation failed
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// Creates a successful sync result
        /// </summary>
        public static SyncResult CreateSuccess(string action, string message = "")
        {
            return new SyncResult
            {
                Success = true,
                Action = action,
                Message = message
            };
        }

        /// <summary>
        /// Creates a failed sync result
        /// </summary>
        public static SyncResult CreateFailure(string errorMessage)
        {
            return new SyncResult
            {
                Success = false,
                Action = "Error",
                ErrorMessage = errorMessage
            };
        }

        /// <summary>
        /// Creates a skipped sync result
        /// </summary>
        public static SyncResult CreateSkipped(string reason)
        {
            return new SyncResult
            {
                Success = true,
                Action = "Skipped",
                Message = reason
            };
        }
    }
}
