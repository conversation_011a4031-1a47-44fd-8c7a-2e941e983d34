﻿using System;
using System.Diagnostics;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.Utils;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.BandedGrid;
using uBuyFirst.Grid;
using uBuyFirst.Prefs;
using uBuyFirst.Search;
using uBuyFirst.Time;
using uBuyFirst.Tools;

namespace uBuyFirst
{
    public partial class Form1
    {
        #region Ribbon clicks

        private void barEditItemTimeZone_EditValueChanged(object sender, EventArgs e)
        {
            if (((BarEditItem)sender).EditValue.ToString().Length > 3)
            {
                UserSettings.CurrentTimeZoneInfo = (TimeZoneInfo)((BarEditItem)sender).EditValue;
                barEditItemTimeZone.Hint = UserSettings.CurrentTimeZoneInfo.DisplayName;
                barEditItemTimeZone.SuperTip.Items.Clear();
                var toolTip = new ToolTipItem
                {
                    Text = UserSettings.CurrentTimeZoneInfo.DisplayName
                };
                barEditItemTimeZone.SuperTip.Items.Add(toolTip);
                //  if (!string.IsNullOrEmpty(barEditItemTimeZone.Tag as string))
                barEditItemTimeZone.EditValue = UserSettings.CurrentTimeZoneInfo.BaseUtcOffset.TotalHours;
            }

            barEditItemTimeZone.Tag = "";
        }

        private void barEditItemTimeZone_ShownEditor(object sender, ItemClickEventArgs e)
        {
            barEditItemTimeZone.Tag = "";
            barEditItemTimeZone.EditValue = UserSettings.CurrentTimeZoneInfo;
        }

        private void barCheckItemStartOnBoot_CheckedChanged(object sender, ItemClickEventArgs e)
        {
            SetRunOnStartupSetting();
        }

        private void barButtonCustomColumns_ItemClick(object sender, ItemClickEventArgs e)
        {
            var dialog = new FormSpecificsColumns();
            dialog.ShowDialog();
        }

        private void barButtonItemGridFont_ItemClick(object sender, ItemClickEventArgs e)
        {
            var ebaySearch = _ebaySearches.ChildrenCore.FirstOrDefault();
            if (ebaySearch != null)
            {
                fontDialog1.Font = ((AdvBandedGridView)ebaySearch.GridControl.MainView).Appearance.Row.Font;
                var result = fontDialog1.ShowDialog();
                if (result == DialogResult.OK)
                {
                    var uniqGrids = GridBuilder.GetUniqGrids(_ebaySearches.ChildrenCore);
                    foreach (var grView in uniqGrids)
                    {
                        grView.Appearance.Row.Font = fontDialog1.Font;
                        grView.Appearance.FocusedRow.Font = fontDialog1.Font;
                        grView.Appearance.SelectedRow.Font = fontDialog1.Font;
                    }

                    SaveSettings();
                }
            }
        }

        private void barEditItemRowHeight_EditValueChanged(object sender, EventArgs e)
        {
            var uniqGrids = GridBuilder.GetUniqGrids(_ebaySearches.ChildrenCore);
            foreach (var grView in uniqGrids)
            {
                grView.RowHeight = int.Parse(barEditItemRowHeight.EditValue.ToString(), CultureInfo.InvariantCulture);
            }
        }

        private void barButtonEbayAccounts_ItemClick(object sender, ItemClickEventArgs e)
        {
            if (!ConnectionConfig.TradingAPIEnabled)
            {
                Process.Start("https://ubuyfirst.com/deprecation");

                return;
            }

            var dialog = new FormEbayAccounts();
            dialog.ShowDialog();
            SaveSettings();
        }

        private void barButtonItemHighlightWords_ItemClick(object sender, ItemClickEventArgs e)
        {
            var dialog = new FormHighlightWords();
            dialog.ShowDialog();
            //AutoMeasurement.Client.TrackScreenView("Screen - Main");
            SaveSettings();
        }

        private void barCheckItemSoundAlert_CheckedChanged(object sender, ItemClickEventArgs e)
        {
            if (barCheckItemSoundAlert.Checked)
            {
                try
                {
                    _myPlayer.Play();
                }
                catch (Exception ex)
                {
                    XtraMessageBox.Show(ex.Message);
                }
            }
        }

        private void barButtonItemSelectSound_ItemClick(object sender, ItemClickEventArgs e)
        {
            openFileDialog1.CheckFileExists = true;
            openFileDialog1.InitialDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "Media");
            openFileDialog1.Filter = @"Wave files only (*.wav)|*.wav";
            if (openFileDialog1.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    if (File.Exists(openFileDialog1.FileName))
                        _myPlayer.SoundLocation = openFileDialog1.FileName;
                    _myPlayer.Play();
                    SaveSettings();
                }
                catch (Exception ex)
                {
                    ExM.ubuyExceptionHandler("SelectSound: ", ex);
                }
            }
        }

        private async void barButtonItemTimeSync_ItemClick(object sender, ItemClickEventArgs e)
        {
            barButtonItemTimeSync.Enabled = false;
            var proc = new ProcessStartInfo();
            proc.UseShellExecute = true;
            proc.WorkingDirectory = Environment.CurrentDirectory;
            proc.FileName = Application.ExecutablePath;
            proc.Arguments = "timesync";
            proc.Verb = "runas";
            var p = new Process
            {
                StartInfo = proc
            };
            try
            {
                p.Start();
                p.WaitForExit();
                barStaticItemTimeDiffText.Caption = $@"eBay - Local time: {await Task.Run(TimeSync.GetTimeSyncDifference)} sec";
            }
            catch
            {
                // The user refused to allow privileges elevation.
                // Do nothing and return directly ...
            }

            barButtonItemTimeSync.Enabled = true;
        }

        private void barCheckItemEnableIdleTimeout_CheckedChanged(object sender, ItemClickEventArgs e)
        {
            if (barEnableIdleTimeout.Checked)
            {
                SearchService.IdleTimeEnabled = true;
                SearchService.IdleTimeMaximum = Convert.ToDecimal(barIdleTimeoutMinutes.EditValue.ToString());
                barIdleTimeoutMinutes.Enabled = true;
            }
            else
            {
                SearchService.IdleTimeEnabled = false;
                barIdleTimeoutMinutes.Enabled = false;
            }
        }

        private void barButtonItemCustomizeLayout_ItemClick(object sender, ItemClickEventArgs e)
        {
            layoutControl1.ShowCustomizationForm();
        }

        private void barButtonItemResetLayout_ItemClick(object sender, ItemClickEventArgs e)
        {
            layoutControl1.RestoreDefaultLayout();
            SetLayoutControlFont(layoutControl1.Root.AppearanceItemCaption.Font, layoutControl1.Root.AppearanceItemCaption.FontSizeDelta);
        }

        private void barButtonItemIncreaseFont_ItemClick(object sender, ItemClickEventArgs e)
        {
            layoutControl1.Root.AppearanceItemCaption.FontSizeDelta += 1;
            SetLayoutControlFont(layoutControl1.Root.AppearanceItemCaption.Font, layoutControl1.Root.AppearanceItemCaption.FontSizeDelta);
        }

        private void barButtonItemDecreaseFont_ItemClick(object sender, ItemClickEventArgs e)
        {
            layoutControl1.Root.AppearanceItemCaption.FontSizeDelta -= 1;
            SetLayoutControlFont(layoutControl1.Root.AppearanceItemCaption.Font, layoutControl1.Root.AppearanceItemCaption.FontSizeDelta);
        }

        private void barStaticBuildVersion_ItemClick(object sender, ItemClickEventArgs e)
        {
            if (barEditItemHwid.Visibility == BarItemVisibility.Always)
            {
                barEditItemHwid.Visibility = BarItemVisibility.Never;
                barEditItemHwid.EditValue = ProgramState.HWID;
            }
            else
            {
                barEditItemHwid.Visibility = BarItemVisibility.Always;
                barEditItemHwid.EditValue = ProgramState.HWID;
            }
        }

        private void barButtonItemKeywords_ItemClick(object sender, ItemClickEventArgs e)
        {
            ShowFloatPanel(dockSearchQueries.Name);
        }

        private void barButtonItemFilters_ItemClick(object sender, ItemClickEventArgs e)
        {
            ShowFloatPanel(dockFilters.Name);
        }

        #endregion
    }
}
