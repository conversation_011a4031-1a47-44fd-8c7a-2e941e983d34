﻿using System;
using uBuyFirst.Pricing;

namespace uBuyFirst.API.ShoppingAPI
{
    public static class PricingServiceShoppingAPI
    {
        public static void GetItemPricing(ShoppingAPIJson.SimpleItemType item, ItemPricing itemPricing)
        {
            try
            {
                var buyItNowPrice = new CurrencyAmount(0, "USD");
                if (item.BuyItNowPrice != null)
                {
                    buyItNowPrice = new CurrencyAmount(item.BuyItNowPrice.Value, item.BuyItNowPrice.CurrencyID);
                }

                var currentPrice = new CurrencyAmount(0, "USD");
                if (item.CurrentPrice != null)
                {
                    currentPrice = new CurrencyAmount(item.CurrentPrice.Value, item.CurrentPrice.CurrencyID);
                }

                itemPricing.ItemPrice = GetBinPrice(item.ListingType, item.BuyItNowAvailable, buyItNowPrice, currentPrice);
                itemPricing.AuctionPrice = GetAuctionPrice(item.ListingType, currentPrice);
            }
            catch (Exception)
            {
                //throw;
            }
        }

        private static CurrencyAmount GetBinPrice(string listingType, bool buyItNowAvailable, CurrencyAmount buyItNowPrice, CurrencyAmount currentPrice)
        {
            var itemPrice = new CurrencyAmount(0, "USD");

            switch (listingType)
            {
                case "FixedPriceItem":
                case "StoresFixedPrice":
                    itemPrice = currentPrice;
                    break;

                case "Auction":
                case "Chinese":
                    if (buyItNowAvailable)
                        itemPrice = buyItNowPrice;
                    break;
                case "PersonalOffer":
                case "LeadGeneration":
                    break;
            }

            return itemPrice;
        }

        private static CurrencyAmount GetAuctionPrice(string listingType, CurrencyAmount currentPrice)
        {
            var itemPrice = new CurrencyAmount(0, "USD");

            if (listingType == "Chinese" || listingType == "Auction")
                // && item.ListingDetails.BuyItNowAvailable)
            {
                itemPrice = new CurrencyAmount(currentPrice.Value, currentPrice.Currency.ToString());
            }

            return itemPrice;
        }
    }
}
