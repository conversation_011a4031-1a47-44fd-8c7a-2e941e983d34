﻿using System;
using System.Diagnostics;
using System.Net.Http;
using System.Runtime.InteropServices;
using uBuyFirst.Tools;

namespace uBuyFirst.Time
{
    internal static class TimeSync
    {
        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool SetSystemTime([In] ref Systemtime st);

        public static async void SetEbayTime()
        {
            var computerTime = DateTime.UtcNow;
            var sw = Stopwatch.StartNew();
            var result =  FetchUtcTime();
            if (result.Success)
            {
                sw.Stop();
                if ((result.UtcTime - computerTime).TotalSeconds < 60 * 30)
                    SetSystemClock(result.UtcTime + sw.Elapsed + new TimeSpan(0, 0, 1));
            }
        }

        public static double GetTimeSyncDifference()
        {
            var pcTime = DateTime.UtcNow;

            var result =  FetchUtcTime();

            if (result.Success)
                return Math.Round((result.UtcTime - pcTime).TotalSeconds, 1);

            return 0;
        }

        private static (bool Success, DateTime UtcTime) FetchUtcTime()
        {
            try
            {
                const string url = "https://www.timeapi.io/api/Time/current/zone?timeZone=UTC";

                using var httpClient = new HttpClient
                {
                    Timeout = TimeSpan.FromSeconds(15)
                };

                // Send GET request
                var response = httpClient.GetAsync(url).Result;
                response.EnsureSuccessStatusCode();

                // Read the JSON response and parse the time
                var jsonResponse = response.Content.ReadAsStringAsync().Result;
                var timeApiResponse = Newtonsoft.Json.JsonConvert.DeserializeObject<TimeApiResponse>(jsonResponse);

                if (timeApiResponse != null && !string.IsNullOrEmpty(timeApiResponse.DateTime))
                {
                    var utcTime = DateTime.Parse(timeApiResponse.DateTime, null, System.Globalization.DateTimeStyles.RoundtripKind);
                    return (true, utcTime);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($@"Error in FetchUtcTime: {ex.Message}");
                // Handle or log the exception as needed
            }

            // Return failure with a default DateTime value
            return (false, DateTime.Now);
        }


        // Class representing the response from timeapi.io
        public class TimeApiResponse
        {
            [Newtonsoft.Json.JsonProperty("dateTime")]
            public string DateTime { get; set; } = string.Empty;

            [Newtonsoft.Json.JsonProperty("timeZone")]
            public string TimeZone { get; set; } = string.Empty;

            [Newtonsoft.Json.JsonProperty("dstActive")]
            public bool DstActive { get; set; }
        }





        [StructLayout(LayoutKind.Sequential)]
        private struct Systemtime
        {
            public short wYear;
            public short wMonth;
            public readonly short wDayOfWeek;
            public short wDay;
            public short wHour;
            public short wMinute;
            public short wSecond;
            public readonly short wMilliseconds;
        }

        private static void SetSystemClock(DateTime datetime)
        {
            try
            {
                {
                    var st = new Systemtime
                    {
                        wYear = (short)DateTime.UtcNow.Year,
                        wMonth = (short)DateTime.UtcNow.Month,
                        wDay = (short)DateTime.UtcNow.Day,
                        wHour = (short)DateTime.UtcNow.Hour,
                        wMinute = (short)datetime.Minute,
                        wSecond = (short)datetime.Second
                    };
                    // must be short
                    SetSystemTime(ref st);
                }
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("SetSystemClock: ", ex);
            }
        }
    }
}
