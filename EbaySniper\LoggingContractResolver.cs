﻿using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using System;
using System.Reflection;

public class LoggingContractResolver : DefaultContractResolver
{
    protected override JsonContract CreateContract(Type objectType)
    {
        try
        {
            return base.CreateContract(objectType);
        }
        catch (Exception ex)
        {
            Console.WriteLine($@"Error creating contract for {objectType}: {ex.Message}");
            throw;
        }
    }

    protected override JsonObjectContract CreateObjectContract(Type objectType)
    {
        try
        {
            return base.CreateObjectContract(objectType);
        }
        catch (Exception ex)
        {
            Console.WriteLine($@"Error creating object contract for {objectType}: {ex.Message}");
            throw;
        }
    }

    protected override JsonProperty CreateProperty(MemberInfo member, MemberSerialization memberSerialization)
    {
        try
        {
            return base.CreateProperty(member, memberSerialization);
        }
        catch (Exception ex)
        {
            Console.WriteLine($@"Error creating property '{member.Name}' for '{member.DeclaringType}': {ex.Message}");
            throw;
        }
    }
}
