﻿using System;
using System.ComponentModel;
using System.Data;
using System.Reflection;
using System.Timers;
using System.Xml.Serialization;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using uBuyFirst.Data;
using uBuyFirst.Other;
using uBuyFirst.Purchasing.OrderApi;
using uBuyFirst.Tools;
using uBuyFirst.Views;

namespace uBuyFirst.Auth
{
    [Obfuscation(Exclude = true)]
    [Serializable]
    public class EbayAccount
    {
        [XmlIgnore] private string _tokenPo;
        public string Avatar;
        public string RefreshToken;
        public string OAuthUserToken;
        public double UserTokenExpiration;


        public string BrowserPath { get; set; } = "";
        public string BrowserName { get; set; } = "";
        public string UserName { get; set; } = "";
        public string Default { get; set; } = "";

        public EbayAccount()
        {
            var tokenRefreshTimer = new Timer();
            tokenRefreshTimer.Interval = 10 * 1000;
            tokenRefreshTimer.Elapsed += TokenRefreshTimer_Elapsed;
            tokenRefreshTimer.Start();
        }

        [XmlIgnore]
        public string TokenPo
        {
            get
            {
                if (string.IsNullOrEmpty(_tokenPo) && !string.IsNullOrEmpty(Avatar))
                {
                    _tokenPo = Serializator.Decrypt(Avatar, true);
                }

                return _tokenPo;
            }
            set
            {
                _tokenPo = value;
                Avatar = Serializator.Encrypt(_tokenPo, true);
            }
        }

        public override string ToString()
        {
            return UserName + " " + BrowserName + " " + Default;
        }

        public void ParseOauthToken(string userOAuthTokenJson)
        {
            JObject userTokenJson = JsonConvert.DeserializeObject<dynamic>(userOAuthTokenJson);

            if (userTokenJson != null)
            {
                OAuthUserToken = userTokenJson["access_token"].Value<string>();
                const int updateSafetyBufferMin = 60 * 30;
                UserTokenExpiration = Helpers.ConvertToUnixTimestamp(DateTime.UtcNow.AddSeconds(userTokenJson["expires_in"].Value<int>() - updateSafetyBufferMin));
            }
        }

        private void TokenRefreshTimer_Elapsed(object sender, ElapsedEventArgs e)
        {
            if (string.IsNullOrEmpty(RefreshToken))
                return;

            if (UserTokenExpiration < Helpers.ConvertToUnixTimestamp(DateTime.UtcNow))
            {
                var updateTokenJson = Authenticator.UpdateUserToken(RefreshToken);
                ParseOauthToken(updateTokenJson);
            }
        }

        public ShippingAddress ShippingAddress { get; set; }

        public static void EBayAccountsList_ListChanged(object sender, ListChangedEventArgs e)
        {
            var accountList = (BindingList<EbayAccount>)sender;
            for (var i = 0; i < accountList.Count; i++)
            {
                if (i == 0)
                {
                    accountList[i].Default = "(default account)";

                    foreach (var view in ResultsView.ViewsDict)
                    {
                        if (view.Value.DataSource is not DataTable datatable)
                            continue;

                        var j = 0;
                        while (j < datatable.Rows.Count)
                        {
                            var d = (DataList)datatable.Rows[j]["Blob"];
                            d.EbayAccount = accountList[i];
                            j++;
                        }
                    }
                }
                else
                    accountList[i].Default = "";
            }
        }
    }
}
