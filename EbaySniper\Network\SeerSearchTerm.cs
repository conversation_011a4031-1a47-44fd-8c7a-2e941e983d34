﻿using System;

namespace uBuyFirst.Network
{
    [Serializable]
    public class SeerSearchTerm
    {
        [NonSerialized] private string _categoriesStr;

        public string[] Categories;
        public string[] ConditionID;
        public decimal PriceMax;
        public decimal PriceMin;
        public bool SearchInDescription;
        public string SellersFilter;
        public int Sent;
        public bool Enabled { get; set; }
        public string Alias { get; set; }
        public string Keyword { get; set; }

        public string LocatedIn { get; set; }
        public string AvailableTo { get; set; }
        public int ItemsSent => Sent;
        public string Zip { get; set; }
        public string EBaySite { get; set; }
        public ListingType[] ListingType { get; set; }
        public string[] SellerList { get; set; }

        public string CategoriesStr()
        {
            return _categoriesStr;
        }

        public override string ToString()
        {
            return Keyword;
        }
    }
}
