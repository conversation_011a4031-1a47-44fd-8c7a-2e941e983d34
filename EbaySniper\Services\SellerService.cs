﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Newtonsoft.Json;
using uBuyFirst.Services.Caching;
using uBuyFirst.Models;
using uBuyFirst.Network;
using uBuyFirst.Prefs;
using uBuyFirst.Seller;
using uBuyFirst.Tools;

namespace uBuyFirst.Services
{
    public class SellerService : ISellerService
    {
        private static readonly HttpClient s_httpClient = new(new Http2CustomHandler(Helpers.BuyItNowConfirmation) { WindowsProxyUsePolicy = WindowsProxyUsePolicy.UseWinInetProxy }) { Timeout = TimeSpan.FromSeconds(120) };
        private readonly ICacheService<string, string> _cacheService;

        /// <summary>
        /// Initializes a new instance of the <see cref="SellerService"/> class.
        /// </summary>
        /// <param name="cacheService">The cache service to use for seller information.</param>
        public SellerService(ICacheService<string, string> cacheService)
        {
            _cacheService = cacheService ?? throw new ArgumentNullException(nameof(cacheService));
        }

        /// <summary>
        /// Parses seller JSON data into a refined model.
        /// </summary>
        /// <param name="jsonData">The JSON string containing seller data.</param>
        /// <returns>A SellerUser object if parsing is successful; otherwise, null.</returns>
        public SellerUser? ParseSellerInfo(string jsonData)
        {
            if (string.IsNullOrWhiteSpace(jsonData))
            {
                return null;
            }

            try
            {
                var deserializeObject = JsonConvert.DeserializeObject<Dictionary<string, SellerUserRaw>>(jsonData);
                var sellerUserRaw = deserializeObject?.Values.FirstOrDefault();

                if (sellerUserRaw == null)
                {
                    // Log info: No seller user found in JSON structure
                    Console.WriteLine(@"Info: No seller user object found within the JSON data structure.");
                    return null;
                }

                // Map to SellerUser
                var sellerUser = new SellerUser
                {
                    // Perform int? (0/1) to bool conversion, treating null or 0 as false, 1 as true
                    AccountActive = sellerUserRaw.AccountActive == 1,
                    Verified = sellerUserRaw.Verified == 1,
                    FeedbackPrivate = sellerUserRaw.FeedbackPrivate == 1,
                    Business = sellerUserRaw.Business == 1,
                    StoreOwner = sellerUserRaw.StoreOwner == 1,
                    TopRated = sellerUserRaw.TopRated == 1,

                    // Copy other properties directly
                    UserName = sellerUserRaw.UserName,
                    TransactionPercent = sellerUserRaw.TransactionPercent,
                    StoreName = sellerUserRaw.StoreName,
                    UserWebCountryId = sellerUserRaw.UserWebCountryId,
                    StoreWebCountryId = sellerUserRaw.StoreWebCountryId,
                    RegDate = sellerUserRaw.RegDate
                };

                return sellerUser;

            }
            catch (JsonException ex)
            {
                // Log JSON parsing error
                Console.WriteLine($@"Error parsing seller info JSON: {ex.Message}");
                return null;
            }
            catch (Exception ex) // Catch any other unexpected errors
            {
                Console.WriteLine($@"An unexpected error occurred while parsing seller info: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Fetches seller information from the API.
        /// </summary>
        /// <param name="sellerName">The name of the seller.</param>
        /// <returns>A JSON string containing seller information if successful; otherwise, null.</returns>
        public async Task<string?> FetchSellerInfo(string sellerName)
        {
            try
            {
                var sellerInfoString = await s_httpClient.GetStringAsync($"{ConnectionConfig.SellerUserInfoUrl}?data={sellerName}");
                return sellerInfoString;
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("_(info)_GetSellerInfo, " + sellerName + " ", ex);
            }

            return null;
        }

        /// <summary>
        /// Gets the seller JSON string, either from cache or by fetching it.
        /// </summary>
        /// <param name="sellerName">The name of the seller.</param>
        /// <returns>A JSON string containing seller information if successful; otherwise, null.</returns>
        public async Task<string?> GetSellerJsonStr(string sellerName)
        {
            // Try to get from cache first
            if (!_cacheService.TryGetValue(sellerName, out var sellerJsonStr))
            {
                // Not in cache or value is invalid (e.g., null/empty if cache stores such states)
                // Fetch from the source
                sellerJsonStr = await FetchSellerInfo(sellerName);

                // If fetched successfully, add/update in cache
                if (!string.IsNullOrEmpty(sellerJsonStr))
                {
                    _cacheService.Set(sellerName, sellerJsonStr);
                }
                // If fetch failed, sellerJsonStr will be null, which is handled by the return type.
                // We don't cache failures here, but could if needed.
            }
            // If TryGetValue returned true, sellerJsonStr already holds the cached value.

            return sellerJsonStr;
        }
    }
}
