﻿using System;
using System.Collections.Generic;
using System.IO;
using System.IO.Compression;
using System.Net.Http;
using System.Threading.Tasks;
using CefSharp;
using CefSharp.WinForms;
using uBuyFirst.Other;
using uBuyFirst.Prefs;

namespace uBuyFirst.CefBrowser
{
    public class CefBrowserManager
    {
        private readonly HttpClient _httpClient;

        public CefBrowserManager()
        {
            _httpClient = new HttpClient();
        }

        /// <summary>
        /// Checks if all required CefSharp files exist.
        /// </summary>
        public bool AreRequiredFilesMissing()
        {
            var fileChecks = new Dictionary<string, string>
            {
                // Root files
                { "CefSharp.BrowserSubprocess.Core.dll", "" },
                { "CefSharp.BrowserSubprocess.exe", "" },
                { "CefSharp.Core.Runtime.dll", "" },
                { "CefSharp.dll", "" },
                { "chrome_elf.dll", "" },
                { "d3dcompiler_47.dll", "" },
                { "dxcompiler.dll", "" },
                { "dxil.dll", "" },
                { "libcef.dll", "" },
                { "libEGL.dll", "" },
                { "libGLESv2.dll", "" },
                { "SQLite.Interop.dll", "" },
                { "vk_swiftshader.dll", "" },
                { "vk_swiftshader_icd.json", "" },
                { "vulkan-1.dll", "" },
                // Resources folder
                { "chrome_100_percent.pak", "" },
                { "chrome_200_percent.pak", "" },
                { "icudtl.dat", "" },
                { "resources.pak", "" },
                { "snapshot_blob.bin", "" },
                { "v8_context_snapshot.bin", "" },
                // Locales folder
                { "en-US.pak", "Locales" }
            };

            foreach (var fileCheck in fileChecks)
            {
                var filePath = string.IsNullOrEmpty(fileCheck.Value)
                    ? Path.Combine(Folders.CefSharpBinaries, fileCheck.Key)
                    : Path.Combine(Folders.CefSharpBinaries, fileCheck.Value, fileCheck.Key);

                if (!File.Exists(filePath))
                {
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// Downloads and extracts required Cef files if missing.
        /// Returns true if extraction succeeded, false otherwise.
        /// </summary>
        public async Task<bool> DownloadAndExtractCefFiles(Action<int> reportProgress = null)
        {
            var zipPath = Path.Combine(Path.GetTempPath(), "Chrome.zip");

            try
            {
                using (var response = await _httpClient.GetAsync(Config.ZipUrl, HttpCompletionOption.ResponseHeadersRead))
                {
                    response.EnsureSuccessStatusCode();
                    var totalBytes = response.Content.Headers.ContentLength ?? -1L;

                    using (var contentStream = await response.Content.ReadAsStreamAsync())
                    using (var fileStream = new FileStream(zipPath, FileMode.Create, FileAccess.Write, FileShare.None, 8192, true))
                    {
                        var buffer = new byte[8192];
                        var totalBytesRead = 0L;
                        int bytesRead;

                        while ((bytesRead = await contentStream.ReadAsync(buffer, 0, buffer.Length)) > 0)
                        {
                            await fileStream.WriteAsync(buffer, 0, bytesRead);
                            totalBytesRead += bytesRead;

                            if (totalBytes <= 0 || reportProgress == null)
                            {
                                continue;
                            }

                            var progressPercentage = (int)((totalBytesRead * 100) / totalBytes);
                            reportProgress(progressPercentage);
                        }
                    }
                }

                // Extract files
                if (File.Exists(zipPath))
                {
                    await Task.Run(() =>
                    {
                        using var archive = ZipFile.OpenRead(zipPath);
                        foreach (var entry in archive.Entries)
                        {
                            // Build the destination path
                            var destinationPath = Path.Combine(Folders.CefSharpBinaries, entry.FullName);

                            // Check if the entry is a directory (indicated by a trailing slash)
                            if (entry.FullName.EndsWith("/")) // ZIP format uses "/" for directories
                            {
                                // Create the directory
                                Directory.CreateDirectory(destinationPath);
                            }
                            else
                            {
                                // Ensure the parent directory exists for file entries
                                var directoryPath = Path.GetDirectoryName(destinationPath);
                                if (!string.IsNullOrEmpty(directoryPath))
                                {
                                    Directory.CreateDirectory(directoryPath);
                                }

                                // Extract the file
                                entry.ExtractToFile(destinationPath, true);
                            }
                        }
                    });

                    File.Delete(zipPath);
                    return true;
                }
            }
            catch (Exception)
            {
                if (File.Exists(zipPath))
                    File.Delete(zipPath);
                return false;
            }

            return false;
        }

        /// <summary>
        /// Initializes the Cef environment if not already done.
        /// </summary>
        public void InitializeCefEngine()
        {
            if (Cef.IsInitialized == null || !(bool)Cef.IsInitialized)
            {
                // Create directories
                
                var rootCachePath = Path.Combine(Folders.CefSharpRootCache, "Profiles");
                var profilePath = Path.Combine(rootCachePath, "Profile");
                var logPath = Path.Combine(Folders.CefSharpRootCache, "Logs");
                var userDataPath = Path.Combine(Folders.CefSharpRootCache, "User Data");
                var extensionsPath = Path.Combine(Folders.CefSharpRootCache, "Extensions");

                Directory.CreateDirectory(profilePath);
                Directory.CreateDirectory(rootCachePath);
                Directory.CreateDirectory(logPath);
                Directory.CreateDirectory(userDataPath);
                Directory.CreateDirectory(extensionsPath);

                var settings = new CefSettings
                {
                    CachePath = profilePath,
                    RootCachePath = rootCachePath,
                    LogFile = Path.Combine(logPath, "cef.log"),
                    PersistSessionCookies = true,
                };

                settings.CefCommandLineArgs.Add("enable-extensions");
                settings.CefCommandLineArgs.Add("user-data-dir", userDataPath);
                settings.CefCommandLineArgs.Add("allow-file-access-from-files");

                Cef.Initialize(settings);
            }
        }

        public async Task<bool> DownloadCefFiles(Action<int> reportProgress)
        {
            if (AreRequiredFilesMissing())
            {
                var downloaded = await DownloadAndExtractCefFiles(reportProgress);
                if (!downloaded)
                    return false;
            }

            return true;
        }

        public ChromiumWebBrowser CreateBrowser()
        {
            return new ChromiumWebBrowser();
        }
    }
}
