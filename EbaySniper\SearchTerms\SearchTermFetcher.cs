﻿using System;
using System.IO;
using System.Net.Http;
using System.Net;
using System.Text.RegularExpressions;
using DevExpress.XtraEditors;
using System.Threading.Tasks;
using System.Windows.Forms;

public static class SearchTermFetcher
{
    public const string GoogleSpreadsheetRegex = @"https:\/\/docs\.[a-zA-Z0-9-.]+\/spreadsheets\/d\/([a-zA-Z0-9-_]+)";

    public static string GetCsvExportUrl(string spreadsheetUrl)
    {
        try
        {
            // Regex to handle standard and non-standard TLDs
            var docIdRegex = new Regex(GoogleSpreadsheetRegex);
            var gidRegex = new Regex(@"gid=(\d+)");

            var docIdMatch = docIdRegex.Match(spreadsheetUrl);
            var gidMatch = gidRegex.Match(spreadsheetUrl);

            if (!docIdMatch.Success)
                throw new ArgumentException("Invalid Google Sheets URL: Missing document ID");

            var docId = docIdMatch.Groups[1].Value;
            var gid = gidMatch.Success ? gidMatch.Groups[1].Value : "0"; // Default to gid=0

            return $"https://docs.google.com/spreadsheets/d/{docId}/export?format=csv&gid={gid}";
        }
        catch (Exception ex)
        {
            return $"Error: {ex.Message}";
        }
    }

    public static async Task<string> DownloadKeywordsFileAsync(string url)
    {
        var tempPath = Path.Combine(Path.GetTempPath(), $"uBuyFirstSearchTerms_{DateTime.Now:s}.csv".Replace(":", "."));

        try
        {
            using var client = new HttpClient();
            var response = await client.GetAsync(url);

            if (!response.IsSuccessStatusCode)
            {
                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    XtraMessageBox.Show($"\"Please make sure your spreadsheet is Shared for \"Anyone with the link.\"", "Authorization Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return "";
                }
                throw new Exception($"HTTP request failed with status code: {response.StatusCode}");
            }


            var fileBytes = await response.Content.ReadAsStringAsync();
            File.WriteAllText(tempPath, fileBytes);

            return tempPath;
        }
        catch (Exception ex)
        {
            // Use your preferred way to handle errors in a UI-independent way
            XtraMessageBox.Show($"Failed to download keywords file: {ex.Message}", "Download Error",
                MessageBoxButtons.OK, MessageBoxIcon.Error);
            return "";
        }
    }
}
