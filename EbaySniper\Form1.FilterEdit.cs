﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using DevExpress.XtraGrid.Views.BandedGrid;
using uBuyFirst.Data;
using uBuyFirst.Filters;
using uBuyFirst.Grid;
using uBuyFirst.Other;
using uBuyFirst.Tools;
using uBuyFirst.Views;
using ItemCheckEventArgs = DevExpress.XtraEditors.Controls.ItemCheckEventArgs;

namespace uBuyFirst
{
    public partial class Form1
    {
        private FormXfilters _dialog = new();

        private void btnAddXfilter_Click(object sender, EventArgs e)
        {
            var filter = new XFilterClass();
            var expr = new FormatConditionRuleExpression();
            filter.GridFormatRule = new GridFormatRule
            {
                Rule = expr
            };
            filter.Enabled = false;

            // New approach: Directly assign ActionHandler and ActionIdentifier
            var availableActions = FilterActionFactory.GetAllAvailableActions().ToList();

            if (availableActions.FirstOrDefault(a => a.ActionTypeIdentifier == FormatCellsAction.Identifier) is FormatCellsAction formatCellsAction)
            {
                filter.ActionHandler = formatCellsAction;
                filter.ActionIdentifier = formatCellsAction.ActionTypeIdentifier;
                filter.Action = formatCellsAction.DisplayName; // Keep for consistency or if UI binds early
            }
            else
            {
                // Fallback or error handling if FormatCellsAction is not found
                // For now, retain legacy behavior as a safety net, though this shouldn't happen if registered correctly.
                filter.Action = "Format cells";
                // Consider logging an error here if formatCellsAction is null
            }
            
            filter.FormatColumn = "Title"; // Default column
            ShowXfilterEditor(filter, -1);
        }

        private void btnEditXfilter_Click(object sender, EventArgs e)
        {
            if (lstchkXfilterList.SelectedItems.Count == 0)
                return;

            var xFilter = (XFilterClass)lstchkXfilterList.GetItem(lstchkXfilterList.SelectedIndex);
            ShowXfilterEditor(xFilter, lstchkXfilterList.SelectedIndex);
        }

        private void btnCopyFilter_Click(object sender, EventArgs e)
        {
            if (lstchkXfilterList.SelectedItems.Count > 0)
            {
                var selectedXFilter = (XFilterClass)lstchkXfilterList.GetItem(lstchkXfilterList.SelectedIndex);
                var newFilterCopy = XFilterManager.DuplicateFilter(selectedXFilter);

                var uniqGrids = GridBuilder.GetUniqGrids(_ebaySearches.ChildrenCore);
                foreach (var grView in uniqGrids)
                {
                    XFilterManager.AddOrReplaceFilter(grView, newFilterCopy, (DataTable)grView.GridControl.DataSource);
                }
            }

            SaveSettings();
        }

        private void btnRemoveXfilter_Click(object sender, EventArgs e)
        {
            var itemsToRemove = lstchkXfilterList.SelectedItems.ToList();
            var i = 0;
            while (itemsToRemove.Count > i)
            {
                if (itemsToRemove[i] is XFilterClass xFilter)
                {
                    xFilter.Enabled = false;

                    XFilterManager.Remove(xFilter);
                }

                i++;
            }

            SaveSettings();
        }

        private void lstchkXfilterList_ItemCheck(object sender, ItemCheckEventArgs e)
        {
            var xFilter = (XFilterClass)((CheckedListBoxControl)sender).GetItemValue(e.Index);
            var xFilterEnabled = e.State == CheckState.Checked;
            xFilter.Enabled = xFilterEnabled;
            
            var uniqGrids = GridBuilder.GetUniqGrids(_ebaySearches.ChildrenCore);
            foreach (var grView in uniqGrids)
            {
                XFilterManager.AddOrReplaceFilter(grView, xFilter, (DataTable)grView.GridControl.DataSource);
            }
        }

        private void ShowXfilterEditor(XFilterClass xFilter, int index)
        {
            var ebaySearch = _ebaySearches.ChildrenCore.FirstOrDefault();
            if (ebaySearch == null)
            {
                XtraMessageBox.Show(this, "Please, create a new eBay Search first. Then try again.");
                return;
            }

            if (_dialog == null)
                _dialog = new FormXfilters();
            
            MakeSureWindowOnWithinScreen();

            _dialog.Keywords = _ebaySearches.ChildrenCore.Select(kw => kw.Alias).ToList();
            _dialog.CurrentGridRule = xFilter;
            _dialog.DataTable = (DataTable)ebaySearch.GridControl.DataSource;
            _dialog.EbayAccountsList = EBayAccountsList;
            _dialog.CurrentGridRule.Rebuild();
            _dialog.ShowDialog(this);

            if (_dialog.DialogResult != DialogResult.OK)
            {
                return;
            }

            var gridViewColumn = ((AdvBandedGridView)ResultsView.ViewsDict["Results"].MainView)?.Columns[xFilter.FormatColumn];
            xFilter.GridFormatRule = xFilter.DeSerializeGridFormatRule(gridViewColumn);
            _dialog.CurrentGridRule.Rebuild();
            if (index > -1)
                XFilterManager.UpdateFilter(index, _dialog.CurrentGridRule);
            else
                XFilterManager.AddFilter(_dialog.CurrentGridRule);

            //Add format rules
            var uniqGrids = GridBuilder.GetUniqGrids(_ebaySearches.ChildrenCore);
            foreach (var grView in uniqGrids)
            {
                XFilterManager.AddOrReplaceFilter(grView, _dialog.CurrentGridRule, (DataTable)grView.GridControl.DataSource);
            }

            SaveSettings();
        }

        private void MakeSureWindowOnWithinScreen()
        {
            _dialog.StartPosition = FormStartPosition.Manual;
            var formRect = new Rectangle(_dialog.FiltersFormLocation, _dialog.Size);
            var isVisible = false;
            foreach (var screen in Screen.AllScreens)
            {
                if (screen.WorkingArea.IntersectsWith(formRect))
                {
                    isVisible = true;
                    break;
                }
            }

            if (isVisible)
            {
                _dialog.Location = _dialog.FiltersFormLocation;
            }
            else
            {
                // If not visible on any screen, center on primary screen
                var primaryScreen = Screen.PrimaryScreen;
                _dialog.Location = new Point(
                    primaryScreen.WorkingArea.X + (primaryScreen.WorkingArea.Width - _dialog.Width) / 2,
                    primaryScreen.WorkingArea.Y + (primaryScreen.WorkingArea.Height - _dialog.Height) / 2
                );
            }
        }

        private void lstchkXfilterList_DoubleClick(object sender, EventArgs e)
        {
            if (lstchkXfilterList.SelectedItems.Count > 0)
            {
                ShowXfilterEditor((XFilterClass)lstchkXfilterList.GetItem(lstchkXfilterList.SelectedIndex), lstchkXfilterList.SelectedIndex);
            }
        }

        #region Filter DragDrop

        private void lstchkXfilterList_MouseDown(object sender, MouseEventArgs e)
        {
            var c = sender as CheckedListBoxControl;
            _filterItemLocationPoint = new Point(e.X, e.Y);

            var selectedIndex = c?.IndexFromPoint(_filterItemLocationPoint);
            if (selectedIndex == -1)
            {
                _filterItemLocationPoint = Point.Empty;
            }
        }

        private void lstchkXfilterList_MouseMove(object sender, MouseEventArgs e)
        {
            if (e.Button != MouseButtons.Left)
            {
                return;
            }

            if (_filterItemLocationPoint != Point.Empty
                && (Math.Abs(e.X - _filterItemLocationPoint.X) > SystemInformation.DragSize.Width || Math.Abs(e.Y - _filterItemLocationPoint.Y) > SystemInformation.DragSize.Height))
                lstchkXfilterList.DoDragDrop(sender, DragDropEffects.Move);
        }

        private void lstchkXfilterList_DragOver(object sender, DragEventArgs e)
        {
            e.Effect = DragDropEffects.Move;
        }

        private void lstchkXfilterList_DragDrop(object sender, DragEventArgs e)
        {
            if (sender is not CheckedListBoxControl checkedListBox)
                return;

            checkedListBox.BeginUpdate();

            var newPoint = new Point(e.X, e.Y);
            newPoint = checkedListBox.PointToClient(newPoint);
            var selectedIndex = checkedListBox.IndexFromPoint(newPoint);
            var prevIndex = checkedListBox.IndexFromPoint(_filterItemLocationPoint);
            XFilterManager.MoveFilter(prevIndex, selectedIndex);

            checkedListBox.EndUpdate();
        }

        #endregion

        private void BtnExportFilters_Click(object sender, EventArgs e)
        {
            try
            {
                var header = new List<string>
                {
                    "Filter Alias",
                    "Enabled",
                    "Action",
                    "Format column",
                    "Condition",
                    "Format style"
                };

                var fileContents = Helpers.CreateCSVRow(header) + "\r\n";

                var allFiltersCellLists = XFilterManager.ExportToCellLists();
                foreach (var filterCellList in allFiltersCellLists)
                {
                    fileContents += Helpers.CreateCSVRow(filterCellList) + "\r\n";
                }

                var fileName = $"FiltersExport_{DateTime.Now.ToString("u").Replace(":", ".").Replace("Z", "")}.csv";
                File.WriteAllText(Path.Combine(Folders.Logs, fileName), fileContents);
                Process.Start(Path.Combine(Folders.Logs, fileName));
            }
            catch (IOException ex)
            {
                XtraMessageBox.Show("Please, close all spreadsheet documents and try again.\n" + ex.Message);
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("Export keywords: ", ex);
            }
        }

        private void BtnImportFilters_Click(object sender, EventArgs e)
        {
            openFileDialog1.Filter = @"Comma separated values (*.csv)|*.csv";
            openFileDialog1.InitialDirectory = Folders.Logs;
            openFileDialog1.FileName = "";
            if (openFileDialog1.ShowDialog() != DialogResult.OK)
                return;

            var importLog = "Skipped rows:\n";
            try
            {
                var fileLocation = openFileDialog1.FileName;
                var csvTable = new List<List<string>>();
                var columns = new List<string>();
                using (var reader = new CsvFileReader(fileLocation))
                {
                    var i = 0;
                    while (reader.ReadRow(columns))
                    {
                        if (i != 0)
                        {
                            var cells = new string[columns.Count];
                            columns.CopyTo(cells);
                            csvTable.Add(cells.ToList());
                        }

                        i++;
                    }
                }

                if (csvTable.Count > 0)
                    for (var i = 0; i < csvTable.Count; i++)
                    {
                        var cells = csvTable[i];
                        if (cells is not { Count: > 5 })
                            continue;

                        var xFilter = new XFilterClass();
                        var importResult = xFilter.Import(cells);
                        if (string.IsNullOrEmpty(importResult))
                        {
                            if (!XFilterManager.ContainsFilter(xFilter.Alias))
                            {
                                XFilterManager.AddFilter(xFilter);
                            }
                        }
                        else
                        {
                            importLog += i + ". [" + xFilter.Alias + "] - Incorrect columns: " + importResult + "\n";
                        }
                    }

                //Add format rules
                var uniqGrids = GridBuilder.GetUniqGrids(_ebaySearches.ChildrenCore);
                foreach (var grView in uniqGrids)
                {
                    FormatRuleManager.ApplyGridFormatRules(grView);
                }

                if (importLog.Length > 20)
                {
                    XtraMessageBox.Show(importLog);
                }
            }
            catch (IOException ex)
            {
                XtraMessageBox.Show("Please, close all spreadsheet documents and try again.\n" + ex.Message);
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("Importing filters: ", ex);
            }

            SaveSettings();
        }
    }
}
