﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Reflection;
using System.Xml;
using System.Xml.Schema;
using System.Xml.Serialization;
using DevExpress.XtraTreeList;

namespace uBuyFirst.Search
{
    [Serializable]
    [Obfuscation(Exclude = true)]
    public class QueryList : TreeList.IVirtualTreeListData, IXmlSerializable
    {
        public QueryList()
        {
        }

        [XmlIgnore]
        public readonly List<Keyword2Find> ChildrenCore = new List<Keyword2Find>();

        public void VirtualTreeGetCellValue(VirtualTreeGetCellValueInfo info)
        {
        }

        public void VirtualTreeGetChildNodes(VirtualTreeGetChildNodesInfo info)
        {
            info.Children = ChildrenCore;
        }

        public void VirtualTreeSetCellValue(VirtualTreeSetCellValueInfo info)
        {
        }

        public XmlSchema GetSchema()
        {
            return null;
        }

        public void ReadXml(XmlReader reader)
        {
        }

        public void WriteXml(XmlWriter writer)
        {
        }

        public IEnumerator GetEnumerator()
        {
            throw new NotImplementedException();
        }
    }
}