﻿using System;
using System.Net;
using System.Threading.Tasks;
using eBay.Service.Call;
using eBay.Service.Core.Sdk;
using eBay.Service.Core.Soap;
using uBuyFirst.Intl;
using uBuyFirst.Prefs;
using uBuyFirst.Tools;

namespace uBuyFirst.Auth
{
    public class Authenticator
    {
        private readonly EbayAccount _ebayAccount;

        public Authenticator(EbayAccount ebayAccount)
        {
            if (ebayAccount == null)
                ebayAccount = new EbayAccount();
            _ebayAccount = ebayAccount;
        }

        public bool IsOAuthTokenValid()
        {
            var tokenNotExpired = _ebayAccount?.UserTokenExpiration > Helpers.ConvertToUnixTimestamp(DateTime.UtcNow);
            return !string.IsNullOrEmpty(_ebayAccount?.OAuthUserToken) && tokenNotExpired;
        }

        public static string GetOAuthUrl(EBaySite eBaySite)
        {
            const string scope = "https://api.ebay.com/oauth/api_scope " +
                                 "https://api.ebay.com/oauth/api_scope/buy.order.readonly " +
                                 "https://api.ebay.com/oauth/api_scope/buy.guest.order " +
                                 "https://api.ebay.com/oauth/api_scope/sell.marketing.readonly " +
                                 "https://api.ebay.com/oauth/api_scope/sell.marketing " +
                                 "https://api.ebay.com/oauth/api_scope/sell.inventory.readonly " +
                                 "https://api.ebay.com/oauth/api_scope/sell.inventory " +
                                 "https://api.ebay.com/oauth/api_scope/sell.account.readonly " +
                                 "https://api.ebay.com/oauth/api_scope/sell.account " +
                                 "https://api.ebay.com/oauth/api_scope/sell.fulfillment.readonly " +
                                 "https://api.ebay.com/oauth/api_scope/sell.fulfillment " +
                                 "https://api.ebay.com/oauth/api_scope/sell.analytics.readonly " +
                                 "https://api.ebay.com/oauth/api_scope/sell.marketplace.insights.readonly " +
                                 "https://api.ebay.com/oauth/api_scope/commerce.catalog.readonly " +
                                 "https://api.ebay.com/oauth/api_scope/buy.shopping.cart " +
                                 "https://api.ebay.com/oauth/api_scope/buy.offer.auction " +
                                 "https://api.ebay.com/oauth/api_scope/commerce.identity.readonly " +
                                 "https://api.ebay.com/oauth/api_scope/commerce.identity.email.readonly " +
                                 "https://api.ebay.com/oauth/api_scope/commerce.identity.phone.readonly " +
                                 "https://api.ebay.com/oauth/api_scope/commerce.identity.address.readonly " +
                                 "https://api.ebay.com/oauth/api_scope/commerce.identity.name.readonly " +
                                 "https://api.ebay.com/oauth/api_scope/commerce.identity.status.readonly " +
                                 "https://api.ebay.com/oauth/api_scope/sell.finances " +
                                 "https://api.ebay.com/oauth/api_scope/sell.item.draft " +
                                 "https://api.ebay.com/oauth/api_scope/sell.payment.dispute " +
                                 "https://api.ebay.com/oauth/api_scope/sell.item " +
                                 "https://api.ebay.com/oauth/api_scope/sell.reputation " +
                                 "https://api.ebay.com/oauth/api_scope/sell.reputation.readonly " +
                                 "https://api.ebay.com/oauth/api_scope/commerce.notification.subscription " +
                                 "https://api.ebay.com/oauth/api_scope/commerce.notification.subscription.readonly";
            var authUrl = $"https://auth.{eBaySite.Domain}/oauth2/authorize?state={ProgramState.HWID}" +
                          $"&client_id={ConnectionConfig.TradingApiKey}" +
                          $"&response_type=code&redirect_uri={ConnectionConfig.RuName}" +
                          $"&scope={scope}";
            return authUrl;
        }

        public void ConfirmOAuth()
        {
            var wc = new WebClient();
            var address = "https://ubuyfirst.com/uBuyFirst/f/oauth.php" +
                          $"?action=refreshtoken&hwid={ProgramState.HWID}&cb={new Random().Next(0, 10000)}";
            var refreshToken = wc.DownloadString(address);
            if (!string.IsNullOrEmpty(refreshToken) && refreshToken.Length > 10)
            {
                _ebayAccount.RefreshToken = refreshToken;
                var userOAuthTokenJson = UpdateUserToken(refreshToken);
                _ebayAccount.ParseOauthToken(userOAuthTokenJson);
            }
        }

        public static async Task<TokenStatusType> GetTokenStatusAsync(ApiContext apiContext)
        {
            try
            {
                var getTokenStatus = new GetTokenStatusCall(apiContext);
                var tokenStatus = await Task.Run(() => getTokenStatus.GetTokenStatus());
                return tokenStatus;
            }
            catch (Exception)
            {
                // ignored
            }

            return null;
        }

        public static string UpdateUserToken(string refreshToken)
        {
            var wc = new WebClient();
            var address = "https://ubuyfirst.com/uBuyFirst/f/oauth.php" +
                          $"?action=usertoken&token={WebUtility.UrlEncode(refreshToken)}&cb={new Random().Next(0, 10000)}";
            var userToken = wc.DownloadString(address);
            return userToken;
        }

        public string GetOAuthUserToken()
        {
            return _ebayAccount.OAuthUserToken;
        }

        public EbayAccount GetEBayAccount()
        {
            return _ebayAccount;
        }
    }
}