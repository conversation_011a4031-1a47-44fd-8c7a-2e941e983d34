﻿using System.Collections.Generic;

namespace uBuyFirst.Search
{
    public static class SearchStuff
    {
        public static readonly Dictionary<string, string> ConditionsDict = new Dictionary<string, string>
        {
            {"1000", "New"},
            {"1500", "New other"},
            {"1750", "New with defects"},
            {"2000", "Manufacturer refurbished"},
            {"2010", "Excellent Refurbished"},
            {"2020", "Very Good Refurbished"},
            {"2030", "Good Refurbished"},
            {"2500", "Seller refurbished"},
            {"2750", "Like new"},
            {"3000", "Used"},
            {"4000", "Very good"},
            {"5000", "Good"},
            {"6000", "Acceptable"},
            {"7000", "For parts or not working"},
            {"Unspecified", "Unspecified"}
        };
    }
}
