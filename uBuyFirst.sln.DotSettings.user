﻿<wpf:ResourceDictionary xml:space="preserve" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml" xmlns:s="clr-namespace:System;assembly=mscorlib" xmlns:ss="urn:shemas-jetbrains-com:settings-storage-xaml" xmlns:wpf="http://schemas.microsoft.com/winfx/2006/xaml/presentation">
	
	
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=API/@EntryIndexedValue">API</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=BIN/@EntryIndexedValue">BIN</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=ID/@EntryIndexedValue">ID</s:String>
	<s:String x:Key="/Default/CodeStyle/Naming/CSharpNaming/Abbreviations/=UPC/@EntryIndexedValue">UPC</s:String>
	<s:String x:Key="/Default/Environment/AssemblyExplorer/XmlDocument/@EntryValue">&lt;AssemblyExplorer&gt;&#xD;
  &lt;Assembly Path="C:\Program Files\DevExpress 23.1\Components\Bin\Framework\DevExpress.Data.v23.1.dll" /&gt;&#xD;
  &lt;Assembly Path="C:\ZOCS\Visual Studio 2010\Projects\EbaySniper\EbaySniper\bin\Debug\SimpleGoogleAnalytics.dll" /&gt;&#xD;
&lt;/AssemblyExplorer&gt;</s:String>
	<s:Boolean x:Key="/Default/UserDictionary/Words/=datatable/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/UserDictionary/Words/=Intl/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/UserDictionary/Words/=Placeoffer/@EntryIndexedValue">True</s:Boolean>
	<s:Boolean x:Key="/Default/UserDictionary/Words/=Pushbullet/@EntryIndexedValue">True</s:Boolean></wpf:ResourceDictionary>