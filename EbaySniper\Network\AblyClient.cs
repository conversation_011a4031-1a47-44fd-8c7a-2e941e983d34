﻿using System;
using System.Diagnostics;
using System.Linq;
using System.Net;
using IO.Ably;
using IO.Ably.Encryption;
using IO.Ably.Realtime;
using uBuyFirst.Other;
using uBuyFirst.Prefs;
using LogLevel = IO.Ably.LogLevel;
using Message = IO.Ably.Message;

namespace uBuyFirst.Network
{
    public class AblyClient3
    {
        internal static string HWID => WebUtility.UrlEncode(ProgramState.HWID);
        public DateTime LastStateCheck;
        private AblyRealtime _realtime;
        private IRealtimeChannel _channelItem;
        private IRealtimeChannel _channelPresence;
        private static int _tokenLifetimeMinutes = 180 * 1;
        private readonly Action<Message> _postAblyItemToResultsGrid;
        private bool _mustBeConnected;
        private SeerUser _seerUser;
        public int ReconnectTimeout = 1;
        public int RetryCount;

        public void ReportAblyConnectionProblems()
        {
            if (RetryCount >= 10 && RetryCount <= 11)
            {
                DefaultLogger.LogLevel = LogLevel.Debug;
            }
            else
            {
                DefaultLogger.LogLevel = LogLevel.Error;
            }
        }

        public AblyClient3(Action<Message> postAblyItemToResultsGrid)
        {
            _postAblyItemToResultsGrid = postAblyItemToResultsGrid;
            DefaultLogger.LoggerSink = new AblyLogger();
        }

        public void BeConnected(SeerUser user)
        {
            try
            {
                ReportAblyConnectionProblems();
                _seerUser = user;
                _mustBeConnected = true;
                KillExistingAblyConnection();
                if (_realtime == null || _realtime.Connection.State == ConnectionState.Failed)
                {
                    _realtime = new AblyRealtime(GetClientOptions());
                    _realtime.Connection.ConnectionStateChanged += Realtime_ConnectionStateChanged;
                }
                else
                    _realtime.Connect();
            }
            catch (Exception ex)
            {
                Loggers.LogError(ex);
            }
        }

        private void KillExistingAblyConnection()
        {
            _realtime?.Close();

            if (_realtime?.Connection?.State == ConnectionState.Failed || _channelPresence?.State == ChannelState.Failed || _channelItem?.State == ChannelState.Failed)
                _realtime = null;

            if (_realtime == null)
            {
                _channelItem = null;
                _channelPresence = null;
            }
        }

        public void BeDisconnected()
        {
            _mustBeConnected = false;
            if (_realtime?.Connection?.State != ConnectionState.Closed && _realtime?.Connection?.State != ConnectionState.Closing)
                _realtime?.Close();
        }

        private void Realtime_ConnectionStateChanged(object sender, ConnectionStateChange e)
        {
            switch (e.Current)
            {
                case ConnectionState.Failed:
                    Loggers.LogError($"Keywords:[{_seerUser.EbaySearches.Count}] {e.Reason}");
                    break;
                case ConnectionState.Connected:
                    SubscribeToPresenceChannel();
                    EnterToPresenceChannel();
                    break;
            }
        }

        private void SubscribeToPresenceChannel()
        {
            try
            {
                if (_realtime == null)
                    return;

                if (_channelPresence != null && _channelPresence.State != ChannelState.Failed)
                {
                    if (_channelPresence.State == ChannelState.Attached)
                    {
                        _channelPresence.Detach();
                    }
                }

                _channelPresence = _realtime.Channels.Get("ubfp:presence", new ChannelOptions(true, Crypto.GetDefaultParams("bXlrZXlkc2RkZGRkZGRkZA==")));
                _channelPresence.StateChanged -= ChannelPresence_StateChanged;
                _channelPresence.StateChanged += ChannelPresence_StateChanged;
                _channelPresence.Attach();
            }
            catch (Exception ex)
            {
                Loggers.LogError(ex);
            }
        }

        private void ChannelPresence_StateChanged(object sender, ChannelStateChange args)
        {
            switch (args.Current)
            {
                case ChannelState.Attached:
                    ReconnectTimeout = 5;
                    break;
            }
        }

        private void AttachToItemChannel()
        {
            try
            {
                if (_realtime.Connection.State != ConnectionState.Connecting && _realtime.Connection.State != ConnectionState.Connected)
                    return;

                var channelName = "ubfi:" + WebUtility.UrlDecode(HWID);
                if (_channelItem == null || _channelItem.State == ChannelState.Failed)
                {
                    _channelItem = _realtime.Channels.Get(channelName, new ChannelOptions(true, Crypto.GetDefaultParams("bXlrZXlkc2RkZGRkZGRkZA==")));

                    if (_channelItem != null)
                    {
                        _channelItem.StateChanged -= ChannelItem_ChannelStateChanged;
                        _channelItem.StateChanged += ChannelItem_ChannelStateChanged;
                    }
                }

                if (_channelItem?.State == ChannelState.Attached)
                    _channelItem?.Detach();

                _channelItem?.Attach();
            }
            catch (Exception ex)
            {
                Loggers.LogError(ex);
            }
        }

        private void ChannelItem_ChannelStateChanged(object sender, ChannelStateChange args)
        {
            if (args.Current != ChannelState.Attached || !_mustBeConnected || _realtime.Connection.State != ConnectionState.Connected)
                return;

            var channel = (IRealtimeChannel)sender;
            channel.Unsubscribe(_postAblyItemToResultsGrid);
            channel.Subscribe(_postAblyItemToResultsGrid);
        }

        private async void EnterToPresenceChannel()
        {
            try
            {
                _seerUser.StartTime = DateTimeOffset.UtcNow.DateTime.AddHours(-7).ToString("s");
                if (_channelPresence?.Presence == null)
                    return;

                var serializedObject = Serializator.BinarySerializeObject(_seerUser);
                if (serializedObject.Length < 40000)
                {
                    await _channelPresence?.Presence.EnterAsync(_seerUser);
                    //return;
                }
                else
                {
                    var compressedString = "packed:" + Serializator.CompressString(Newtonsoft.Json.JsonConvert.SerializeObject(_seerUser));
                    if (compressedString.Length < 40000)
                    {
                        await _channelPresence?.Presence.EnterAsync(compressedString);
                        //return;
                    }
                    else
                    {
                        _seerUser.HWID = $"[{_seerUser.EbaySearches.Count}]" + _seerUser.HWID;
                        _seerUser.EbaySearches = _seerUser.EbaySearches.GetRange(0, 20);
                        await _channelPresence?.Presence.EnterAsync(_seerUser);
                    }
                }

                AttachToItemChannel();
            }
            catch (Exception ex)
            {
                Loggers.LogError(ex);
                _realtime?.Close();
            }
        }

        private static ClientOptions GetClientOptions()
        {
            if (Debugger.IsAttached)
            {
                _tokenLifetimeMinutes = 2;
            }

            var authUrl = new Uri($"https://ubuyfirst.com/uBuyFirst/try/tokens/ubft.php?hwid={HWID}&ttl={_tokenLifetimeMinutes * 60}&cb={new Random().Next(100000)}");
            var options = new ClientOptions()
            {
                UseBinaryProtocol = true,
                UseTokenAuth = true,
                Tls = true,
                AuthUrl = authUrl
            };

            if (Environment.OSVersion.Version.Major == 6 && Environment.OSVersion.Version.Minor < 2 || Environment.OSVersion.Version.Major < 6)
                options.TransportFactory = new WebSocket4NetTransport.WebSocketTransportFactory();
            return options;
        }

        public string GetAblyClientState()
        {
            if (Program.Sandbox)
                return "";

            var channelStates = _realtime?.Channels?.Select(c => c.State);
            string states = "Null";
            if (channelStates != null)
                states = string.Join(",", channelStates);

            return $"{_realtime?.Connection?.State},{states};";
        }

        public ConnectionState GetConnectionState()
        {
            if (_realtime?.Connection == null)
                return ConnectionState.Disconnected;

            return _realtime.Connection.State;
        }

        public bool NeedRestart()
        {
            if (!_mustBeConnected)
                return false;

            var timeSinceLastStateCheck = (DateTime.UtcNow - LastStateCheck).TotalSeconds;
            if (timeSinceLastStateCheck < ReconnectTimeout)
                return false;

            LastStateCheck = DateTime.UtcNow;

            switch (_realtime?.Connection?.State)
            {
                case ConnectionState.Failed:
                case ConnectionState.Closed:
                case ConnectionState.Closing:
                case ConnectionState.Disconnected:
                case ConnectionState.Initialized:
                case ConnectionState.Suspended:
                    IncreaseReconnectPeriod();
                    return true;

                case ConnectionState.Connected:
                    var channelsAttached = (_channelItem?.State == ChannelState.Attaching || _channelItem?.State == ChannelState.Attached)
                                           && (_channelPresence?.State == ChannelState.Attaching || _channelPresence?.State == ChannelState.Attached);
                    var isConnectionError = _realtime?.Connection?.ErrorReason != null;
                    if (channelsAttached && !isConnectionError)
                    {
                        RetryCount = 0;
                        return false;
                    }
                    else
                    {
                        IncreaseReconnectPeriod();
                        return true;
                    }
                case ConnectionState.Connecting:
                    return false;
            }

            return false;
        }

        private void IncreaseReconnectPeriod()
        {
            ReconnectTimeout *= 2;
            if (ReconnectTimeout > 300)
            {
                ReconnectTimeout = 300;
            }

            RetryCount++;
        }
    }
}
