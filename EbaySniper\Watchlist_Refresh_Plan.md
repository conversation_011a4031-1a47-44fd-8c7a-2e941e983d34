# Plan: Integrating Watchlist Refresh Controls

This document outlines the plan to modify the `WatchlistManager` and related UI components to support timed auto-refresh, manual refresh, and configurable refresh intervals for the eBay watchlist feature.

**Assumptions:**

1.  UI Controls (`btnRefreshWatchlist`, `timeSpanWatchlistRefreshInterval`, `chkRefreshAndNotify`) are located on the main form (likely `Form1.cs`).
2.  Default state for auto-refresh (`chkRefreshAndNotify`) is **disabled** (unchecked).
3.  Default refresh interval is **5 minutes**.
4.  The active `EbayAccount` can be retrieved from within the form's context (e.g., via a property or method like `GetActiveAccount()`) when needed.

**Implementation Steps:**

1.  **Modify `WatchlistManager.cs`:**
    *   **Fields:**
        *   Add `private System.Threading.Timer _refreshTimer;`
        *   Add `private bool _isAutoRefreshEnabled = false;` (or based on default)
        *   Add `private TimeSpan _currentInterval;`
        *   Add `private EbayAccount _activeAccount;` (Consider if storing it here is best, or if it should always be passed in).
        *   Add `private readonly SynchronizationContext _synchronizationContext;` (Already exists, ensure usage).
        *   Add `private readonly Action<FoundItem> _handleNewItemAction;` (Already exists).
        *   Add `private readonly Dictionary<string, int> _itemQuantities = new Dictionary<string, int>();` (Already exists).
    *   **Timer Logic:**
        *   Implement `private void StartTimer()`: Configures and starts `_refreshTimer` if `_isAutoRefreshEnabled` is true. Uses `_currentInterval`. Handles potential race conditions if called multiple times.
        *   Implement `private void StopTimer()`: Stops and disposes of the current `_refreshTimer` instance safely.
        *   Implement `private async void TimerCallback(object state)`: The method executed by the timer. It should retrieve the `_activeAccount` (if stored) or handle cases where it's not available, then call `await RefreshWatchlistAsync(_activeAccount);`. Ensure error handling within the callback.
    *   **Core Refresh Logic:**
        *   Create `private async Task RefreshWatchlistAsync(EbayAccount account)`: This method will contain the core logic currently in `UpdateWatchlistItems` (fetching from eBay API, processing items, updating `_itemQuantities`, removing stale items, and posting UI updates via `_synchronizationContext`). It needs the `EbayAccount` passed to it.
    *   **Public Control Methods:**
        *   Implement `public void SetAutoRefresh(bool enabled, EbayAccount currentAccount)`: Updates `_isAutoRefreshEnabled`, updates `_activeAccount` (if storing), and calls `StartTimer()` or `StopTimer()`. Requires the current account to start the timer immediately if enabling.
        *   Implement `public void SetRefreshInterval(TimeSpan interval)`: Updates `_currentInterval`. If `_isAutoRefreshEnabled` is true, restarts the timer with the new interval using `_refreshTimer.Change(TimeSpan.Zero, _currentInterval);`.
        *   Implement `public async Task TriggerManualRefreshAsync(EbayAccount account)`: Directly calls `await RefreshWatchlistAsync(account);`. Consider adding a flag to prevent concurrent manual and timed refreshes if necessary.
    *   **Constructor & Disposal:**
        *   Modify the constructor `public WatchlistManager(SynchronizationContext synchronizationContext, Action<FoundItem> handleNewItemAction, TimeSpan defaultInterval)`: Initialize `_synchronizationContext`, `_handleNewItemAction`, `_currentInterval`. Do *not* start the timer here unless auto-refresh defaults to true.
        *   Implement `IDisposable`: Ensure `StopTimer()` is called in the `Dispose` method to clean up the timer resources.
    *   **Remove Old Method:** The existing `internal void UpdateWatchlistItems(EbayAccount ebayAccount)` can likely be removed or made private after its logic is moved to `RefreshWatchlistAsync`.

2.  **Modify `Form1.cs` (or relevant form):**
    *   **Instance:** Ensure a `WatchlistManager` instance exists (e.g., `private WatchlistManager _watchlistManager;`).
    *   **Initialization:** Instantiate `_watchlistManager` in the form's constructor or Load event, passing the `SynchronizationContext.Current`, the item handling action, and the default interval.
        ```csharp
        // Example Initialization
        TimeSpan defaultInterval = TimeSpan.FromMinutes(5); // Or load from settings
        _watchlistManager = new WatchlistManager(SynchronizationContext.Current, HandleNewWatchlistItem, defaultInterval);
        // Initialize UI controls with default values
        timeSpanWatchlistRefreshInterval.EditValue = defaultInterval;
        chkRefreshAndNotify.Checked = false; // Default state
        ```
    *   **Event Handlers:**
        *   `btnRefreshWatchlist_Click`:
            ```csharp
            private async void btnRefreshWatchlist_Click(object sender, EventArgs e)
            {
                EbayAccount currentAccount = GetActiveAccount(); // Implement this method
                if (currentAccount != null && _watchlistManager != null)
                {
                    // Consider adding UI feedback (e.g., disable button, show progress)
                    await _watchlistManager.TriggerManualRefreshAsync(currentAccount);
                    // Restore UI state
                }
                else
                {
                    // Handle case where account or manager is not available
                }
            }
            ```
        *   `timeSpanWatchlistRefreshInterval_EditValueChanged`:
            ```csharp
            private void timeSpanWatchlistRefreshInterval_EditValueChanged(object sender, EventArgs e)
            {
                if (timeSpanWatchlistRefreshInterval.EditValue is TimeSpan interval && _watchlistManager != null)
                {
                    _watchlistManager.SetRefreshInterval(interval);
                    // Optionally save this interval to user settings
                }
            }
            ```
        *   `chkRefreshAndNotify_EditValueChanged`:
            ```csharp
            private void chkRefreshAndNotify_EditValueChanged(object sender, EventArgs e)
            {
                EbayAccount currentAccount = GetActiveAccount(); // Implement this method
                if (_watchlistManager != null)
                {
                     bool isEnabled = chkRefreshAndNotify.Checked;
                     _watchlistManager.SetAutoRefresh(isEnabled, currentAccount);
                     // Optionally save this state to user settings
                }
            }
            ```
    *   **Account Retrieval:** Implement `private EbayAccount GetActiveAccount()` to safely retrieve the currently selected/active eBay account instance.
    *   **Disposal:** In the form's `Dispose` method or `FormClosing` event, call `_watchlistManager?.Dispose();`.

**Visual Plan (Mermaid Diagram):**

```mermaid
graph LR
    subgraph UI Controls [Form1 UI]
        direction LR
        RefreshIntervalControl[timeSpanWatchlistRefreshInterval]
        ManualRefreshButton[btnRefreshWatchlist]
        AutoRefreshCheckbox[chkRefreshAndNotify]
    end

    subgraph Form1 Logic
        direction TB
        GetAccount[GetActiveAccount()]
        ManagerInstance[_watchlistManager]

        EventHandler1[btnRefreshWatchlist_Click]
        EventHandler2[chkRefreshAndNotify_EditValueChanged]
        EventHandler3[timeSpanWatchlistRefreshInterval_EditValueChanged]

        ManualRefreshButton -- triggers --> EventHandler1
        AutoRefreshCheckbox -- triggers --> EventHandler2
        RefreshIntervalControl -- triggers --> EventHandler3

        EventHandler1 -- calls --> ManagerInstance.TriggerManualRefreshAsync()
        EventHandler2 -- calls --> ManagerInstance.SetAutoRefresh()
        EventHandler3 -- calls --> ManagerInstance.SetRefreshInterval()

        EventHandler1 -- uses --> GetAccount
        EventHandler2 -- uses --> GetAccount
    end

    subgraph WatchlistManager
        direction TB
        Timer[System.Threading.Timer]
        IsAutoRefreshEnabled[bool _isAutoRefreshEnabled]
        CurrentInterval[TimeSpan _currentInterval]
        ActiveAccount[EbayAccount _activeAccount] -- Optional storage

        CoreRefreshLogic[RefreshWatchlistAsync()]
        StartTimer[StartTimer()]
        StopTimer[StopTimer()]

        SetAutoRefresh[SetAutoRefresh(bool, account)]
        SetRefreshInterval[SetRefreshInterval(TimeSpan)]
        TriggerManualRefresh[TriggerManualRefreshAsync(account)]

        Timer -- triggers --> CoreRefreshLogic
        TriggerManualRefresh -- calls --> CoreRefreshLogic
        SetAutoRefresh -- controls --> StartTimer & StopTimer
        SetRefreshInterval -- modifies --> CurrentInterval & Timer

        CoreRefreshLogic -- calls --> EbayAPI[eBay GetMyeBayBuying API]
        EbayAPI -- response --> CoreRefreshLogic
        CoreRefreshLogic -- updates UI via --> SyncContext[SynchronizationContext]
    end

    ManagerInstance -- contains --> WatchlistManager

    classDef ui fill:#ccf,stroke:#333,stroke-width:2px;
    classDef formlogic fill:#eef,stroke:#333,stroke-width:2px;
    classDef manager fill:#f9f,stroke:#333,stroke-width:2px;
    classDef external fill:#cfc,stroke:#333,stroke-width:2px;
    classDef internal fill:#eee,stroke:#666;

    class UI Controls ui;
    class Form1 Logic formlogic;
    class WatchlistManager manager;
    class EbayAPI,SyncContext external;
    class CoreRefreshLogic,StartTimer,StopTimer,Timer internal;