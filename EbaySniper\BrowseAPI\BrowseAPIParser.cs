﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Globalization;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using BrowseAPI;
using eBay.Service.Core.Soap;
using Fonlow.Net.Http;
using uBuyFirst.Data;
using uBuyFirst.Item;
using uBuyFirst.Parsing;
using uBuyFirst.Prefs;
using uBuyFirst.Pricing;
using uBuyFirst.Search;
using uBuyFirst.Tools;

namespace uBuyFirst.BrowseAPI
{
    public static class BrowseAPIParser
    {

        internal static async Task<ItemGroup?> FetchBrowseAPIItem2Datalist(DataList dataList, SearchService searchService, string zip, string availableTo, string marketplaceID)
        {
            try
            {
                // Use RequestQueueManager for queue tracking and semaphore management
                // Use the BrowseAPI-specific methods
                await RequestQueueManager.Instance.WaitForBrowseAPIQueueAsync();

                if (dataList.Source != SearchSource.WAT)
                    if (searchService is { Running: false })
                    {
                        RequestQueueManager.Instance.ForceReleaseAll(100);
                        return null;
                    }

                var token = string.IsNullOrEmpty(ConnectionConfig.Token) ? ProgramState.HWID : ConnectionConfig.Token;

                var requestParams = new BrowseAPINetwork.RequestParams
                {
                    ItemID = dataList.ItemID,
                    MarketplaceID = marketplaceID,
                    Country = availableTo,
                    Zip = zip,
                    Token = token
                };

                ItemGroup? itemGroup;
                if (dataList.GroupItemID == null)
                {
                    itemGroup = await AddDataFromBrowseAPI(requestParams);
                }
                else
                {
                    itemGroup = await GroupItemRequest(requestParams);
                }

                if (itemGroup == null && dataList.GroupItemID == null)
                {
                    itemGroup = await GroupItemRequest(requestParams);
                }

                RequestQueueManager.Instance.ReleaseBrowseAPIQueue();
                return itemGroup;
            }
            catch (Exception e)
            {
                Debug.WriteLine("BrowseAPIItem2Datalist: " + e);
                return null;
            }
        }

        internal static Tuple<string, DataRow?>? ParseGroupItem2Datalist(DataList dataList, ItemGroup itemGroup)
        {
            if (ParseBrowseItem2UbfItem(itemGroup, dataList))
            {
                return new Tuple<string, DataRow?>("Convert Browse api GetItem fail", null);
            }

            var pricingServiceBrowseAPI = new PricingServiceBrowseAPI();
            PricingServiceBrowseAPI.ParseItemPricing(itemGroup.Items[0], dataList.ItemPricing);
            pricingServiceBrowseAPI.ParseItemShipping(itemGroup.Items[0].ShippingOptions, dataList);

            var item = itemGroup.Items[0];
            ItemParser.ParsePicturesBrowseAPI(dataList, item.Image, item.AdditionalImages);

            dataList.ItemSpecifics = ItemSpecifics.ParseItemAspectsBrowseAPI(item.Product?.AspectGroups, item.LocalizedAspects);
            return null;
        }

        private static async Task<ItemGroup?> AddDataFromBrowseAPI(BrowseAPINetwork.RequestParams requestParams)
        {
            try
            {
                return await BrowseAPINetwork.GetItem(requestParams);
            }
            catch (AggregateException ex)
            {
                if (ex.InnerException is WebApiRequestException exc)
                    if (exc.Response.Contains("Invalid access token"))
                    {
                        ConnectionConfig.SetBrowseAPIUrl(ConnectionConfig.BrowseAPIUrlFallback);
                        return null;
                    }

                ExM.ubuyExceptionHandler($"_(info)_GetItemBrowse: {requestParams.ItemID} ", ex);
            }
            catch (Exception ex)
            {
                if (ex is WebApiRequestException exc)
                {
                    if (exc.Response.Contains("Invalid access token"))
                    {
                        ConnectionConfig.SetBrowseAPIUrl(ConnectionConfig.BrowseAPIUrlFallback);
                        return null;
                    }

                    if (exc.Response.Contains("The specified item Id was not found."))
                    {
                        return null;
                    }
                }

                if (!ex.Message.Contains("Not Found"))
                    if (!ex.Message.Contains("Gateway Timeout"))
                        if (!ex.Message.Contains("Bad Request"))
                            ExM.ubuyExceptionHandler($"_(info)_GetItemBrowse: {requestParams.ItemID} ", ex);
            }

            return null;
        }

        private static async Task<ItemGroup?> GroupItemRequest(BrowseAPINetwork.RequestParams requestParams)
        {
            try
            {
                return await BrowseAPINetwork.GetGroupItem(requestParams);
            }
            catch (AggregateException ex)
            {
                if (ex.InnerException is WebApiRequestException exc)
                    if (exc.Response.Contains("Invalid access token"))
                    {
                        ConnectionConfig.SetBrowseAPIUrl(ConnectionConfig.BrowseAPIUrlFallback);
                        return null;
                    }

                ExM.ubuyExceptionHandler($"_(info)_GroupItemRequest: {requestParams.ItemID} ", ex);
            }
            catch (Exception ex)
            {
                if (ex is WebApiRequestException exc)
                    if (exc.Response.Contains("Invalid access token"))
                    {
                        ConnectionConfig.SetBrowseAPIUrl(ConnectionConfig.BrowseAPIUrlFallback);
                        return null;
                    }

                if (!ex.Message.Contains("Not Found"))
                    if (!ex.Message.Contains("Gateway Timeout"))
                        if (!ex.Message.Contains("Bad Request"))
                            ExM.ubuyExceptionHandler($"_(info)_GroupItemRequest: {requestParams.ItemID} ", ex);
            }

            return null;
        }

        private static bool ParseBrowseItem2UbfItem(ItemGroup groupItem, DataList dataList)
        {
            try
            {
                var browseItem = groupItem.Items[0];
                if (browseItem == null)
                {
                    return true;
                }

                var authenticity = new List<string>();
                if (!string.IsNullOrEmpty(browseItem.AuthenticityGuarantee?.Description))
                {
                    if (browseItem.AuthenticityGuarantee?.Description == "This item's authenticity will be verified by experts before delivery")
                    {
                        authenticity.Add("Guarantee");
                    }
                }


                if (!string.IsNullOrEmpty(browseItem.AuthenticityVerification?.Description))
                {
                    if (!string.IsNullOrEmpty(browseItem.AuthenticityVerification?.Description))
                    {
                        authenticity.Add(browseItem.AuthenticityVerification?.Description);
                    }
                }

                dataList.Authenticity = string.Join(", ", authenticity);


                if (browseItem.ShipToLocations != null)
                {
                    if (browseItem.ShipToLocations.RegionIncluded != null)
                        dataList.ShipToLocations = browseItem.ShipToLocations.RegionIncluded.Select(r => r.RegionId.ToString()).ToList();

                    if (browseItem.ShipToLocations.RegionExcluded != null)
                        dataList.ExcludeShipToLocation = browseItem.ShipToLocations.RegionExcluded.Select(r => r.RegionId.ToString()).ToList();
                }

                var estimatedAvailability = browseItem.EstimatedAvailabilities.FirstOrDefault();
                if (estimatedAvailability != null)
                {
                    int quantityAvailable;
                    if (estimatedAvailability.EstimatedAvailableQuantity == null)
                        quantityAvailable = (int)estimatedAvailability.EstimatedRemainingQuantity;
                    else
                        quantityAvailable = (int)estimatedAvailability.EstimatedAvailableQuantity;
                    var quantitySold = estimatedAvailability.EstimatedSoldQuantity ?? 0;
                    var totalQuantity = quantityAvailable + quantitySold;

                    dataList.QuantityTotal = totalQuantity;
                    dataList.QuantityAvailable = quantityAvailable;
                }

                var deliveryOptions = new List<string>();
                if (browseItem.EstimatedAvailabilities != null)
                {
                    foreach (var availability in browseItem.EstimatedAvailabilities)
                    {
                        if (availability.DeliveryOptions != null)
                            deliveryOptions.AddRange(availability.DeliveryOptions);
                    }
                }

                deliveryOptions = deliveryOptions.Distinct().ToList();
                dataList.ShippingDelivery = string.Join(", ", deliveryOptions).Replace("_", " ").ToLower().ToTitleCase().Replace("Seller Arranged ", "");

                dataList.BuyItNowAvailable = browseItem.BuyingOptions.Contains("FIXED_PRICE");

                var buyingOptions = browseItem.BuyingOptions;
                for (var i = 0; i < buyingOptions.Length; i++)
                {
                    buyingOptions[i] = buyingOptions[i].Replace("FIXED_PRICE", "Buy it Now").Replace("AUCTION", "Auction");
                }

                dataList.ListingCodeType = buyingOptions;

                //dataList.RelistParentID = ""; //missing

                dataList.SellerName = browseItem.Seller?.Username ?? "";
                if (browseItem.Seller?.SellerAccountType == "BUSINESS")
                {
                    dataList.SellerName = browseItem.Seller.Username;
                    dataList.StoreName = dataList.SellerName;
                    //dataList.SellerStore = "Business";
                }

                if (Enum.TryParse(browseItem.ListingMarketplaceId, out SiteCodeType parsedSite))
                {
                    dataList.Site = parsedSite;
                }
                else
                {
                    dataList.Site = dataList.EBaySite.SiteCode;
                }

                dataList.FeedbackRating = decimal.Parse(browseItem.Seller?.FeedbackPercentage ?? "0", CultureInfo.InvariantCulture);
                dataList.FeedbackScore = browseItem.Seller?.FeedbackScore ?? 0;
                dataList.FromCountry = browseItem.ItemLocation?.Country ?? "" + "US";
                dataList.BestOffer = browseItem.BuyingOptions.Contains("BEST_OFFER");
                dataList.Variation = groupItem.Items.Length > 1;

                var listLocation = GetLocation(browseItem.ItemLocation);

                dataList.Location = listLocation;
                dataList.Title = browseItem.Title;
                //ubfItem.StoreName = ItemParser.GetStoreName(theItem);//missing
                dataList.Bids = browseItem.BidCount ?? 0;

                DateParser.SetPostedTime(dataList, DateTimeOffset.Parse(browseItem.ItemCreationDate));
                DateParser.SetEndTime_Timeleft(dataList, browseItem.ItemEndDate);
                dataList.EndTime = DateParser.ParseEndTime(browseItem.ItemEndDate);
                dataList.CategoryName = browseItem.CategoryPath; //change to last category name
                dataList.CategoryID = browseItem.CategoryId;

                if (browseItem.ConditionId != null && SearchStuff.ConditionsDict.TryGetValue(browseItem.ConditionId, out var conditionDisplayName))
                    dataList.Condition = conditionDisplayName;

                if (browseItem.ConditionId != null)
                    dataList.ConditionID = browseItem.ConditionId;
                else
                    dataList.ConditionID = "";

                //ubfItem.BestOfferCount = theItem.BestOfferDetails?.BestOfferCount;//missing
                //ubfItem.HitCount = theItem.HitCount;//missing
                dataList.ConditionDescription = browseItem.ConditionDescription;

                var paymentMethods = new List<string>();
                if (browseItem.PaymentMethods != null)
                    foreach (var paymentMethod in browseItem.PaymentMethods)
                    {
                        if (paymentMethod.PaymentMethodBrands == null)
                            paymentMethods.Add(paymentMethod.PaymentMethodType);
                        else
                        {
                            var brandTypes = paymentMethod.PaymentMethodBrands.Select(p => p.PaymentMethodBrandType);
                            paymentMethods.AddRange(brandTypes);
                        }
                    }

                dataList.Payment = string.Join(",", paymentMethods.ToArray());
                var returnsAccepted = "";
                var returnsWithin = "";
                if (browseItem.ReturnTerms != null)
                {
                    returnsAccepted = (bool)browseItem.ReturnTerms.ReturnsAccepted ? "Returns accepted" : "No returns accepted";
                    returnsWithin = "";
                    if (browseItem.ReturnTerms.ReturnPeriod != null)
                        returnsWithin = browseItem.ReturnTerms.ReturnPeriod.Value + " " + browseItem.ReturnTerms.ReturnPeriod.Unit.Replace("BUSINESS_", "").Replace("CALENDAR_", "").ToLower();
                }

                dataList.Returns = returnsAccepted;
                if (!string.IsNullOrEmpty(returnsAccepted))
                    dataList.Returns += " / " + returnsWithin;

                var pidList = new List<string>();
                if (browseItem.Gtin != null)
                    pidList.Add(browseItem.Gtin);

                dataList.MPN = browseItem.Mpn ?? "";
                dataList.Brand = browseItem.Brand ?? "";
                dataList.ProductReferenceID = browseItem.Epid ?? "";
                var model = browseItem.LocalizedAspects?.Where(a => a.Name == "Model").Select(a => a.Value).ToList();
                dataList.Model = string.Join(",", model ?? new List<string>());

                if (browseItem.Product != null)
                {
                    dataList.Brand = browseItem.Product.Brand ?? "";

                    if (browseItem.Product.Mpns != null)
                    {
                        var mpns = new List<string>();
                        mpns.AddRange(dataList.MPN.Split(','));
                        mpns.AddRange(browseItem.Product.Mpns);
                        dataList.MPN = string.Join(",", mpns);
                    }

                    if (browseItem.Product.Gtins != null)
                        pidList.AddRange(browseItem.Product.Gtins);

                    if (browseItem.Product.AdditionalProductIdentities != null)
                    {
                        var additionalPIDs = browseItem.Product.AdditionalProductIdentities.Select(i => i.ProductIdentity);

                        foreach (var pid in additionalPIDs)
                        {
                            pidList.AddRange(pid.Select(p => p.IdentifierValue));
                        }
                    }

                    //ubfItem.ISBN = theItem.ProductListingDetails?.ISBN;//missing
                }

                if (browseItem.LocalizedAspects != null)
                {
                    var ids = browseItem.LocalizedAspects.Where(a => a.Name == "ISBN" || a.Name == "ISBN-10").Select(a => a.Value).ToList();
                    if (ids.Any())
                    {
                        pidList.AddRange(ids);
                    }
                }
                dataList.UPC = string.Join(",", pidList);
                Helpers.CleanPIDs(dataList);

                var vatDetail = browseItem.Seller?.SellerLegalInfo?.VatDetails?.FirstOrDefault();
                if (vatDetail != null)
                    dataList.VATNumber = $"{vatDetail.IssuingCountry} {vatDetail.VatId}".Trim();
                dataList.Description = browseItem.Description;
                if (dataList.Description == null)
                {
                    if (groupItem.CommonDescriptions != null)
                    {
                        dataList.Description = groupItem.CommonDescriptions.FirstOrDefault()?.Description ?? "";
                    }
                }

                //ubfItem.AutoPay = //missing
                //ubfItem.StoreName = ItemParser.GetStoreName(theItem);//missing
                //var shippingTimeMax = theItem.ShippingDetails.ShippingServiceOptions.ToArray().ToList().FirstOrDefault()?.ShippingTimeMax ?? 0;
                //ubfItem.DispatchTimeMax = theItem.DispatchTimeMax + shippingTimeMax; //missing
                dataList.SetStatus(ParseInitialItemStatus(browseItem.ItemEndDate, browseItem.EstimatedAvailabilities.FirstOrDefault()));

                return false;
            }
            catch (Exception e)
            {
                Debug.WriteLine("GroupItem2UbfItem: " + e.Message);
                return true;
            }
        }

        public static string GetLocation(Address itemLocation)
        {
            if (itemLocation == null)
            {
                return "";
            }

            var locationList = new List<string> { itemLocation.Country };

            if (itemLocation.StateOrProvince != null)
                locationList.Add(itemLocation.StateOrProvince);

            if (itemLocation.County != null)
                locationList.Add(itemLocation.County);

            if (itemLocation.City != null)
                locationList.Add(itemLocation.City);

            if (itemLocation.PostalCode != null)
                locationList.Add(itemLocation.PostalCode);
            var listLocation = string.Join(", ", locationList);
            return listLocation;
        }

        public static ItemStatus ParseInitialItemStatus(string itemEndDate, EstimatedAvailability estimatedAvailability)
        {
            var isListingEnded = !string.IsNullOrEmpty(itemEndDate) && DateTime.Parse(itemEndDate, CultureInfo.InvariantCulture).ToUniversalTime() < DateTime.UtcNow;
            if (isListingEnded)
            {
                if (estimatedAvailability != null && estimatedAvailability.EstimatedAvailabilityStatus.Contains("OUT_OF_STOCK"))
                {
                }

                if (estimatedAvailability?.EstimatedSoldQuantity > 0)
                {
                    return ItemStatus.Sold;
                }
                else
                {
                    return ItemStatus.Ended;
                }
            }
            else
            {
                if (estimatedAvailability != null && estimatedAvailability.EstimatedAvailabilityStatus.Contains("OUT_OF_STOCK"))
                {
                    if (estimatedAvailability?.EstimatedSoldQuantity > 0)
                    {
                        return ItemStatus.Sold;
                    }

                    return ItemStatus.NotAvailable;
                }

                return ItemStatus.Active;
            }

            //switch (theItem.SellingStatus.ListingStatus)
            //{
            //    case ListingStatusCodeType.Active:
            //        datalist.Status = Status.Active;
            //        break;

            //    //case ListingStatusCodeType.Ended:
            //    //case ListingStatusCodeType.Completed:

            //    //if (theItem.SellingStatus.QuantitySold == 0)
            //    //    datalist.Status = Status.Ended;
            //    //else
            //    //    datalist.Status = Status.Sold;

            //    //UpdateData(datalist, theItem);
            //    //break;

            //    //default:
            //    //    datalist.Status = Status.Ended;
            //    //    break;
            //}
        }
    }
}
