# Keyword Sync Extension API Documentation

## Overview

The Keyword Sync Extension provides functionality to synchronize purchase requirements from CSV spreadsheets into the Restocker database. This extension integrates seamlessly with the existing SearchTermManager while maintaining full backward compatibility.

## Key Features

- **Optional Columns**: "Job ID" and "Required Quantity" columns are completely optional
- **Backward Compatibility**: Existing spreadsheets work unchanged
- **Error Handling**: Comprehensive validation and graceful error handling
- **Sync History**: Complete tracking of synchronization operations
- **Integration Ready**: Easy to integrate with existing keyword import process

## Spreadsheet Format

### Required Columns (Existing)
- `Id` - Unique identifier for the keyword
- `eBay Search Alias` - Display name for the search term

### Optional Restocker Columns (New)
- `Job ID` - Identifier for the purchase job/requirement
- `Required Quantity` - Number of items to purchase
- `Max Price` - Optional maximum price per item (can be empty)

### Example CSV
```csv
Id,eBay Search Alias,Job ID,Required Quantity,Max Price,Keywords,Keyword enabled,Price Min,Price Max
keyword-1,Gaming Laptop,JOB-001,5,999.99,gaming laptop,true,500,1000
keyword-2,Office Chair,JOB-002,3,,office chair,true,100,300
keyword-3,Regular Search,,,desk lamp,true,20,50
```

## API Reference

### SearchTermManagerExtension

#### SyncRestockerRequirementsFromFileAsync

Processes a spreadsheet file and syncs any Restocker purchase requirements.

```csharp
public static async Task<RestockerSyncSummary> SyncRestockerRequirementsFromFileAsync(
    string fileLocation, 
    IPurchaseTrackerRepository repository)
```

**Parameters:**
- `fileLocation` - Path to the CSV spreadsheet file
- `repository` - Repository for storing purchase requirements

**Returns:** `RestockerSyncSummary` with operation results

**Example Usage:**
```csharp
using var repository = SearchTermManagerExtension.GetDefaultRepository();
var result = await SearchTermManagerExtension.SyncRestockerRequirementsFromFileAsync(
    @"C:\path\to\keywords.csv", 
    repository);

if (result.Success)
{
    Console.WriteLine($"Added: {result.RequirementsAdded}, Updated: {result.RequirementsUpdated}");
}
else
{
    Console.WriteLine($"Error: {result.ErrorMessage}");
}
```

#### GetDefaultRepository

Gets the default repository instance for Restocker operations.

```csharp
public static IPurchaseTrackerRepository GetDefaultRepository()
```

**Returns:** Default `PurchaseTrackerRepository` instance

### RestockerSyncSummary

Contains the results of a synchronization operation.

```csharp
public class RestockerSyncSummary
{
    public bool Success { get; set; }
    public string FileName { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public int RequirementsAdded { get; set; }
    public int RequirementsUpdated { get; set; }
    public int RequirementsSkipped { get; set; }
    public int RequirementsErrors { get; set; }
    public string Message { get; set; }
    public string ErrorMessage { get; set; }
    public List<string> ErrorMessages { get; set; }
    
    // Calculated Properties
    public TimeSpan Duration { get; }
    public int TotalProcessed { get; }
}
```

### KeywordSyncService

Core service for processing individual spreadsheet rows.

#### ProcessSpreadsheetRowAsync

Processes a single spreadsheet row for Restocker requirements.

```csharp
public async Task<SyncResult> ProcessSpreadsheetRowAsync(
    List<string> cells, 
    Dictionary<string, int> columnMapping)
```

**Parameters:**
- `cells` - Row data as list of strings
- `columnMapping` - Column name to index mapping

**Returns:** `SyncResult` indicating the action taken

#### SyncPurchaseRequirementAsync

Syncs a single purchase requirement to the database.

```csharp
public async Task<SyncResult> SyncPurchaseRequirementAsync(
    string keywordId,
    string keywordAlias, 
    string jobId,
    int requiredQuantity,
    decimal? maxPrice = null)
```

### SyncResult

Contains the result of processing a single requirement.

```csharp
public class SyncResult
{
    public bool Success { get; set; }
    public string Action { get; set; } // "Added", "Updated", "Skipped", "Error"
    public string Message { get; set; }
    public string ErrorMessage { get; set; }
    
    // Static factory methods
    public static SyncResult CreateAdded(string message = null)
    public static SyncResult CreateUpdated(string message = null)
    public static SyncResult CreateSkipped(string reason)
    public static SyncResult CreateError(string error)
}
```

## Integration Examples

### Basic Integration with Existing Import

```csharp
public async Task ImportKeywordsWithRestockerAsync(string fileLocation)
{
    try
    {
        // 1. Do normal keyword import (existing functionality)
        SearchTermManager.ImportSearchTermsFromFile(fileLocation, ebaySearches);
        
        // 2. Sync Restocker requirements from same file
        using var repository = SearchTermManagerExtension.GetDefaultRepository();
        var syncResult = await SearchTermManagerExtension
            .SyncRestockerRequirementsFromFileAsync(fileLocation, repository);
        
        // 3. Handle results
        if (syncResult.Success)
        {
            if (syncResult.RequirementsAdded > 0 || syncResult.RequirementsUpdated > 0)
            {
                MessageBox.Show($"Restocker sync completed:\n" +
                    $"Added: {syncResult.RequirementsAdded}\n" +
                    $"Updated: {syncResult.RequirementsUpdated}\n" +
                    $"Skipped: {syncResult.RequirementsSkipped}");
            }
        }
        else
        {
            MessageBox.Show($"Restocker sync failed: {syncResult.ErrorMessage}");
        }
    }
    catch (Exception ex)
    {
        MessageBox.Show($"Import failed: {ex.Message}");
    }
}
```

### Check for Restocker Data

```csharp
public static bool HasRestockerData(string fileLocation)
{
    try
    {
        using var repository = new PurchaseTrackerRepository();
        var task = SearchTermManagerExtension
            .SyncRestockerRequirementsFromFileAsync(fileLocation, repository);
        var result = task.GetAwaiter().GetResult();
        
        return result.Success && !result.Message.Contains("No Restocker columns found");
    }
    catch
    {
        return false;
    }
}
```

## Error Handling

The API provides comprehensive error handling:

1. **File Issues**: Missing files, permission errors, corrupt CSV
2. **Data Validation**: Invalid quantities, malformed data
3. **Database Errors**: Connection issues, constraint violations
4. **Business Logic**: Duplicate requirements, missing required fields

All errors are captured in the `RestockerSyncSummary` with detailed error messages.

## Testing

The extension includes 16 comprehensive tests covering:
- Valid data processing
- Backward compatibility (no Restocker columns)
- Invalid data handling
- Update scenarios
- Error conditions
- Edge cases

Run tests with:
```bash
dotnet test uBuyFirst.Tests --filter "FullyQualifiedName~KeywordSyncService"
dotnet test uBuyFirst.Tests --filter "FullyQualifiedName~SearchTermManagerExtension"
```
