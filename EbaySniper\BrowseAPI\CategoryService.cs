﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using Newtonsoft.Json;
using TaxonomyAPI;
using uBuyFirst.GUI;
using uBuyFirst.Other;
using uBuyFirst.Prefs;
using Category = uBuyFirst.GUI.Category;

namespace uBuyFirst.API.TradingAPI
{
    public class CategoryServiceBrowseAPI
    {
        private readonly Dictionary<string, Categories> _categories;

        public CategoryServiceBrowseAPI()
        {

            _categories = new Dictionary<string, Categories>();
        }
        bool IsFileOlderThanOneMonth(string filePath)
        {
            DateTime lastModified = File.GetLastWriteTime(filePath);
            return (DateTime.Now - lastModified).TotalDays > 30;
        }

        public async Task<Categories> GetSiteCategories(string marketplaceID)
        {
            try
            {
                if (_categories.ContainsKey(marketplaceID))
                    return _categories[marketplaceID];

                var categoryFilePath = Path.Combine(Folders.Settings, "CategoryList." + marketplaceID + ".json");
                if (!File.Exists(categoryFilePath) || IsFileOlderThanOneMonth(categoryFilePath))
                {
                    // Delete the old file if it exists
                    if (File.Exists(categoryFilePath))
                    {
                        File.Delete(categoryFilePath);
                    }

                    // Download a new file
                    await DownloadCategories(categoryFilePath, marketplaceID);
                }

                var categories = ReadCategoriesFromDisk(categoryFilePath);
                _categories.Add(marketplaceID, categories);
                return categories;
            }
            catch (Exception e)
            {
                Loggers.LogError(e);
            }

            return new Categories();
        }

        private static Categories ReadCategoriesFromDisk(string categoryFilePath)
        {
            var fileContent = File.ReadAllText(categoryFilePath);
            var categories = JsonConvert.DeserializeObject<Categories>(fileContent);
            return categories;
        }

        private async Task DownloadCategories(string categoryFilePath, string marketplaceID)
        {
            var client = new HttpClient
            {
                BaseAddress = new Uri(ConnectionConfig.TaxonomyAPIURL)
            };
            client.DefaultRequestHeaders.Add("X-EBAY-C-MARKETPLACE-ID", marketplaceID);
            var taxonomyAPIClass = new TaxonomyAPIClass(client);
            var marketplaceTreeID = await taxonomyAPIClass.GetDefaultCategoryTreeIdAsync(marketplaceID, HandleHeaders);
            var categoryTree = await taxonomyAPIClass.GetCategoryTreeAsync(marketplaceTreeID.CategoryTreeId, HandleHeaders);

            var categories = FastParse(categoryTree);

            var fileContent = JsonConvert.SerializeObject(categories, Formatting.None, new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore
            });
            if (!string.IsNullOrWhiteSpace(fileContent))
                File.WriteAllText(categoryFilePath, fileContent);
        }

        private void HandleHeaders(HttpRequestHeaders headers)
        {
            string token;
            if (!string.IsNullOrEmpty(ConnectionConfig.Token))
                token = ConnectionConfig.Token;
            else
                token = ProgramState.HWID;

            headers.Add("Authorization", "Bearer " + token);
        }

        private Categories FastParse(CategoryTree categories)
        {
            Categories parents = new Categories();
            var sortedNodes = categories.RootCategoryNode.ChildCategoryTreeNodes.OrderBy(node => node.Category.CategoryName);
            foreach (var category in sortedNodes)
            {
                var project = new Category(category.Category.CategoryName, category.Category.CategoryId, category.LeafCategoryTreeNode);
                AddProjectRecursive(project, category.ChildCategoryTreeNodes);
                parents.Add(project);
            }

            return parents;
        }

        private void AddProjectRecursive(Category parent, CategoryTreeNode[]? nodes)
        {
            if (nodes == null)
                return;

            var sortedNodes = nodes.OrderBy(node => node.Category.CategoryName);
            foreach (var category in sortedNodes)
            {
                if (category.Category.CategoryId == "14112")
                    continue;
                if (category.Category.CategoryId == "Test Category")
                    continue;

                var project = new Category(category.Category.CategoryName, category.Category.CategoryId, category.LeafCategoryTreeNode);
                AddProjectRecursive(project, category.ChildCategoryTreeNodes);
                if (parent.Categories == null)
                    parent.Categories = new Categories();
                parent.Categories.Add(project);

            }
        }
    }
}
