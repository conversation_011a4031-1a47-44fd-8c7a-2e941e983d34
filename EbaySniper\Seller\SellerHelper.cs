﻿using System;
using System.Globalization;
using System.Linq;
using eBay.Service.Core.Soap;
using uBuyFirst.API.ShoppingAPI;
using uBuyFirst.Data;
using uBuyFirst.Models;
using uBuyFirst.Prefs;
using uBuyFirst.Services;

namespace uBuyFirst.Seller
{
    /// <summary>
    /// Utility class for seller-related operations including validation, filtering, and business logic
    /// </summary>
    public static class SellerHelper
    {
        #region Seller Validation & Filtering

        /// <summary>
        /// Validates if a seller is allowed based on keyword filters and blocked seller list
        /// </summary>
        /// <param name="sellerName">The seller name to validate</param>
        /// <param name="keyword2Find">The keyword configuration containing seller filters</param>
        /// <returns>Validation result with success status and error message</returns>
        public static SellerValidationResult ValidateSeller(string sellerName, Keyword2Find keyword2Find)
        {
            // Check keyword seller filters
            if (keyword2Find is { SellerType: "Include", Sellers.Length: > 0 })
            {
                if (!keyword2Find.Sellers.Contains(sellerName))
                {
                    return new SellerValidationResult(false, "Seller doesn't match");
                }
            }

            // Check blocked sellers list
            if (UserSettings.BlockedSellers.Contains(sellerName))
            {
                return new SellerValidationResult(false, "Seller in block list");
            }

            return new SellerValidationResult(true, null);
        }

        /// <summary>
        /// Checks if a seller is in the blocked sellers list
        /// </summary>
        /// <param name="sellerName">The seller name to check</param>
        /// <returns>True if seller is blocked</returns>
        public static bool IsSellerBlocked(string sellerName)
        {
            return UserSettings.BlockedSellers.Contains(sellerName);
        }

        #endregion

        #region Seller Country & Location Logic

        /// <summary>
        /// Determines if the seller is from UK/GB based on seller country or item location
        /// </summary>
        /// <param name="datalist">The DataList containing location information</param>
        /// <returns>True if seller is from UK/GB</returns>
        public static bool IsSellerFromUkGb(DataList datalist)
        {
            // Check seller country first
            if (!string.IsNullOrEmpty(datalist.SellerCountry))
            {
                var sellerCountry = datalist.SellerCountry.ToUpperInvariant();
                if (IsUkGbCountry(sellerCountry))
                {
                    return true;
                }
            }

            // Fallback to item location country
            if (!string.IsNullOrEmpty(datalist.FromCountry))
            {
                var fromCountry = datalist.FromCountry.ToUpperInvariant();
                if (IsUkGbCountry(fromCountry))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Checks if a country string represents UK/GB
        /// </summary>
        /// <param name="country">Country string to check (case-insensitive)</param>
        /// <returns>True if country represents UK/GB</returns>
        public static bool IsUkGbCountry(string country)
        {
            if (string.IsNullOrEmpty(country))
                return false;

            var upperCountry = country.ToUpperInvariant();
            return upperCountry == "UNITED KINGDOM" || upperCountry == "UK" || upperCountry == "GB";
        }

        /// <summary>
        /// Gets the seller country from SellerUser object using CountryService
        /// </summary>
        /// <param name="sellerUser">The SellerUser object</param>
        /// <param name="countryService">The CountryService instance</param>
        /// <returns>Seller country name</returns>
        public static string GetSellerCountry(SellerUser sellerUser, ICountryService countryService)
        {
            return countryService.GetSellerCountry(sellerUser);
        }

        #endregion

        #region Seller Business Type Logic

        /// <summary>
        /// Determines if the seller is a UK/GB private seller (requires special pricing handling)
        /// </summary>
        /// <param name="datalist">The DataList containing seller information</param>
        /// <returns>True if the seller is from UK/GB and is a private seller</returns>
        public static bool IsUkGbPrivateSeller(DataList datalist)
        {
            // Check if seller is from UK/GB
            var isUkGbSeller = IsSellerFromUkGb(datalist);

            // Check if seller is a private seller (not a business)
            var isPrivateSeller = !datalist.SellerIsBusiness;

            return isUkGbSeller && isPrivateSeller;
        }

        /// <summary>
        /// Checks if the seller is a business seller
        /// </summary>
        /// <param name="datalist">The DataList containing seller information</param>
        /// <returns>True if seller is a business</returns>
        public static bool IsBusinessSeller(DataList datalist)
        {
            return datalist.SellerIsBusiness;
        }

        /// <summary>
        /// Checks if the seller is a private seller
        /// </summary>
        /// <param name="datalist">The DataList containing seller information</param>
        /// <returns>True if seller is private (not business)</returns>
        public static bool IsPrivateSeller(DataList datalist)
        {
            return !datalist.SellerIsBusiness;
        }

        #endregion

        #region Seller Store Information

        /// <summary>
        /// Extracts store name from ItemType (Trading API)
        /// </summary>
        /// <param name="item">The ItemType object</param>
        /// <returns>Store name or empty string</returns>
        public static string GetStoreName(ItemType item)
        {
            if (!item.Seller.SellerInfo.StoreOwner || item.Storefront == null)
                return "";

            if (item.Storefront.StoreName != null)
                return item.Storefront.StoreName;

            if (item.Storefront.StoreURL != null)
                return System.Text.RegularExpressions.Regex.Replace(item.Storefront.StoreURL, ".*/", "");

            if (item.Seller.SellerInfo.StoreOwner)
                return "Store";

            return "Store";
        }

        /// <summary>
        /// Extracts store name from SimpleItemType (Shopping API)
        /// </summary>
        /// <param name="item">The SimpleItemType object</param>
        /// <returns>Store name or empty string</returns>
        public static string GetStoreName(ShoppingAPIJson.SimpleItemType item)
        {
            if (item.Storefront == null)
                return "";

            if (item.Storefront.StoreName != null)
                return item.Storefront.StoreName;

            if (item.Storefront.StoreURL != null)
                return System.Text.RegularExpressions.Regex.Replace(item.Storefront.StoreURL, ".*/", "");

            return "Store";
        }

        #endregion

        #region Seller Data Population

        /// <summary>
        /// Populates seller information in DataList from SellerUser object
        /// </summary>
        /// <param name="datalist">The DataList to populate</param>
        /// <param name="sellerUser">The SellerUser containing seller information</param>
        /// <param name="countryService">The CountryService for country mapping</param>
        public static void PopulateSellerInfo(DataList datalist, SellerUser sellerUser, ICountryService countryService)
        {
            if (sellerUser == null || string.IsNullOrEmpty(sellerUser.UserName))
                return;

            datalist.SellerCountry = GetSellerCountry(sellerUser, countryService);

            try
            {
                datalist.SellerRegistration = DateTime.ParseExact(sellerUser.RegDate, "yyyyMMdd", CultureInfo.InvariantCulture);
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            datalist.SellerStore = sellerUser.StoreName ?? "";
            datalist.SellerIsBusiness = sellerUser.Business;
        }

        /// <summary>
        /// Populates basic seller information from Trading API ItemType
        /// </summary>
        /// <param name="datalist">The DataList to populate</param>
        /// <param name="item">The ItemType containing seller information</param>
        public static void PopulateSellerInfoFromItem(DataList datalist, ItemType item)
        {
            datalist.SellerName = item.Seller?.UserID ?? "";
            datalist.FeedbackRating = (decimal)(item.Seller?.PositiveFeedbackPercent ?? 0.0);
            datalist.FeedbackScore = item.Seller?.FeedbackScore ?? 0;
            datalist.StoreName = GetStoreName(item);
        }

        /// <summary>
        /// Populates basic seller information from Shopping API SimpleItemType
        /// </summary>
        /// <param name="datalist">The DataList to populate</param>
        /// <param name="item">The SimpleItemType containing seller information</param>
        public static void PopulateSellerInfoFromSimpleItem(DataList datalist, ShoppingAPIJson.SimpleItemType item)
        {
            datalist.SellerName = item.Seller?.UserID ?? "";
            datalist.FeedbackRating = (decimal)(item.Seller?.PositiveFeedbackPercent ?? 0);
            datalist.FeedbackScore = item.Seller?.FeedbackScore ?? 0;
            datalist.StoreName = GetStoreName(item);
        }

        #endregion

        #region Seller Blocking & UI Operations

        /// <summary>
        /// Blocks a seller and removes all their items from grid views
        /// </summary>
        /// <param name="sellerName">The seller name to block</param>
        /// <param name="showConfirmation">Whether to show confirmation dialog</param>
        /// <returns>True if seller was blocked, false if user cancelled</returns>
        public static bool BlockSellerAndRemoveFromGrids(string sellerName, bool showConfirmation = true)
        {
            if (string.IsNullOrEmpty(sellerName))
                return false;

            // Show confirmation dialog if requested
            if (showConfirmation)
            {
                var result = DevExpress.XtraEditors.XtraMessageBox.Show(
                    $"Block '{sellerName}'?",
                    "Block Seller",
                    System.Windows.Forms.MessageBoxButtons.YesNo);

                if (result != System.Windows.Forms.DialogResult.Yes)
                    return false;
            }

            // Add to blocked sellers list
            if (!UserSettings.BlockedSellers.Contains(sellerName))
            {
                UserSettings.BlockedSellers.Add(sellerName);
            }

            // Remove from all grid views
            RemoveSellerFromAllGridViews(sellerName);

            return true;
        }

        /// <summary>
        /// Removes all items from a specific seller from all grid views
        /// </summary>
        /// <param name="sellerName">The seller name to remove</param>
        public static void RemoveSellerFromAllGridViews(string sellerName)
        {
            if (string.IsNullOrEmpty(sellerName))
                return;

            var uniqGrids = uBuyFirst.Grid.GridBuilder.GetUniqGrids(Form1.Instance._ebaySearches.ChildrenCore);
            foreach (var grView in uniqGrids)
            {
                RemoveSellerFromGridView(grView, sellerName);
            }
        }

        /// <summary>
        /// Removes all items from a specific seller from a single grid view
        /// </summary>
        /// <param name="gridView">The grid view to process</param>
        /// <param name="sellerName">The seller name to remove</param>
        public static void RemoveSellerFromGridView(DevExpress.XtraGrid.Views.BandedGrid.AdvBandedGridView gridView, string sellerName)
        {
            if (gridView == null || string.IsNullOrEmpty(sellerName))
                return;

            gridView.BeginDataUpdate();
            try
            {
                var dataTable = (System.Data.DataTable)gridView.GridControl.DataSource;
                if (dataTable == null)
                    return;

                var i = 0;
                while (dataTable.Rows.Count > 0 && i < dataTable.Rows.Count)
                {
                    if (dataTable.Rows[i]["Seller Name"].ToString() == sellerName)
                        dataTable.Rows.RemoveAt(i);
                    else
                        i++;
                }
            }
            finally
            {
                gridView.EndDataUpdate();
            }
        }

        /// <summary>
        /// Formats seller registration date for display
        /// </summary>
        /// <param name="sellerRegistration">The seller registration date</param>
        /// <returns>Formatted date string</returns>
        public static string FormatSellerRegistrationDate(DateTimeOffset? sellerRegistration)
        {
            if (sellerRegistration == null)
                return string.Empty;

            return sellerRegistration.Value.ToString("yyyy-MM-dd");
        }

        /// <summary>
        /// Gets seller name from a DataRow safely
        /// </summary>
        /// <param name="row">The DataRow containing seller information</param>
        /// <returns>Seller name or empty string if not found</returns>
        public static string GetSellerNameFromRow(System.Data.DataRow row)
        {
            if (row == null || row.RowState == System.Data.DataRowState.Deleted || row.RowState == System.Data.DataRowState.Detached)
                return string.Empty;

            try
            {
                return row["Seller Name"]?.ToString() ?? string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }

        /// <summary>
        /// Gets DataList from a DataRow safely
        /// </summary>
        /// <param name="row">The DataRow containing the blob</param>
        /// <returns>DataList or null if not found</returns>
        public static DataList GetDataListFromRow(System.Data.DataRow row)
        {
            if (row == null || row.RowState == System.Data.DataRowState.Deleted || row.RowState == System.Data.DataRowState.Detached)
                return null;

            try
            {
                return row["Blob"] as DataList;
            }
            catch
            {
                return null;
            }
        }

        #endregion
    }

    /// <summary>
    /// Result of seller validation operation
    /// </summary>
    public class SellerValidationResult
    {
        public bool IsValid { get; }
        public string ErrorMessage { get; }

        public SellerValidationResult(bool isValid, string errorMessage)
        {
            IsValid = isValid;
            ErrorMessage = errorMessage;
        }
    }
}
