﻿using System;
using System.Diagnostics;
using System.Windows.Forms;
using DevExpress.Utils;
using DevExpress.XtraBars;
using DevExpress.XtraEditors;
using uBuyFirst.GUI;
using uBuyFirst.Purchasing;

namespace uBuyFirst
{
    public partial class FormBid
    {
        private static void LabelControl_HyperlinkClick(object sender, HyperlinkClickEventArgs e)
        {
            Browser.LaunchBrowser("", e.Link);
        }

        private void lcItemID_Click(object sender, EventArgs e)
        {
            Browser.OpenAffiliateLink(D);
        }

        private void barButtonItemHelp_ItemClick(object sender, ItemClickEventArgs e)
        {
            var hyperLink = e.Item;
            if (hyperLink.Tag != null)
                Process.Start(hyperLink.Tag.ToString());
        }

        private void lcTerm_MouseClick(object sender, MouseEventArgs e)
        {
            if (e.Button == MouseButtons.Right)
            {
                var label = (LabelControl)sender;
                label.ContextMenu = new ContextMenu(new MenuItem[] { new("Copy", OnClickCopy) });
            }
        }

        private void OnClickCopy(object sender, EventArgs e)
        {
            Clipboard.SetData(DataFormats.Text, lcTerm.Text);
        }
    }
}
