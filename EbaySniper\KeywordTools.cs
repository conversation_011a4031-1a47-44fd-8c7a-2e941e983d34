﻿using System.Collections.Generic;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace uBuyFirst
{
    internal static class KeywordTools
    {
        public static List<Keyword2Find> ApplyKeywordLimit(List<Keyword2Find> enabledKeywords)
        {
            if (enabledKeywords.Count <= Form1.LicenseUtility.CurrentLimits.SearchTermsCount)
                return enabledKeywords;

            for (var i = enabledKeywords.Count - 1; i >= Form1.LicenseUtility.CurrentLimits.SearchTermsCount; i--)
            {
                enabledKeywords[i].KeywordEnabled = CheckState.Unchecked;
            }

            var enabledKeywords2 = enabledKeywords.Where(kw => kw.KeywordEnabled == CheckState.Checked).ToList();

            XtraMessageBox.Show(
                $"({Form1.LicenseUtility.CurrentLimits.SearchTermsCount}) search terms left enabled. ({enabledKeywords.Count - Form1.LicenseUtility.CurrentLimits.SearchTermsCount}) was disabled.\n Please upgrade to use more search terms.");
            return enabledKeywords2;
        }
    }
}
