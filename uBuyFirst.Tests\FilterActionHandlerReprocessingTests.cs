using Microsoft.VisualStudio.TestTools.UnitTesting;
using System.Collections.Generic;
using System.ComponentModel;
using uBuyFirst.Filters;
using uBuyFirst.Prefs;

namespace uBuyFirst.Tests
{
    [TestClass]
    public class FilterActionHandlerReprocessingTests
    {
        private BindingList<XFilterClass> _testFilters;

        [TestInitialize]
        public void TestInitialize()
        {
            // Reset ConnectionConfig to default state before each test
            ConnectionConfig.RestockerEnabled = false;

            // Clear the registries to ensure clean state for next test
            ClearFilterActionFactory();

            // Create test filters
            _testFilters = new BindingList<XFilterClass>();
        }

        [TestMethod]
        public void ReprocessFilterActionHandlers_WithRestockFilterAfterConfigChange_ShouldPopulateActionHandler()
        {
            // Arrange - Create a filter with Restock action when RestockerEnabled is false
            ConnectionConfig.RestockerEnabled = false;
            FilterActionFactory.Initialize(null);

            var restockFilter = new XFilterClass
            {
                Alias = "TestRestockFilter",
                Enabled = true,
                Action = "Restock" // Legacy action string
            };

            // Simulate filter loading when RestockFilterAction is not registered
            restockFilter.EnsureModernActionFormat();

            // Verify ActionHandler is null (because RestockFilterAction wasn't registered)
            Assert.IsNull(restockFilter.ActionHandler, "ActionHandler should be null when RestockerEnabled is false");

            // Act - Change configuration and re-process filters
            ConnectionConfig.RestockerEnabled = true;
            FilterActionFactory.Reinitialize(null);

            // Simulate the fix: re-process the filter after factory re-initialization
            restockFilter.EnsureModernActionFormat();

            // Assert - ActionHandler should now be populated
            Assert.IsNotNull(restockFilter.ActionHandler, "ActionHandler should be populated after re-processing");
            Assert.IsInstanceOfType(restockFilter.ActionHandler, typeof(RestockFilterAction), "ActionHandler should be RestockFilterAction");
            Assert.AreEqual("RESTOCK", restockFilter.ActionIdentifier, "ActionIdentifier should be set correctly");
        }

        [TestMethod]
        public void ReprocessFilterActionHandlers_WithMultipleFilters_ShouldUpdateOnlyRestockFilters()
        {
            // Arrange - Create multiple filters with different actions
            ConnectionConfig.RestockerEnabled = false;
            FilterActionFactory.Initialize(null);

            var formatFilter = new XFilterClass
            {
                Alias = "FormatFilter",
                Enabled = true,
                Action = "Format cells"
            };

            var restockFilter = new XFilterClass
            {
                Alias = "RestockFilter",
                Enabled = true,
                Action = "Restock"
            };

            var removeFilter = new XFilterClass
            {
                Alias = "RemoveFilter",
                Enabled = true,
                Action = "Remove rows"
            };

            // Process filters when RestockFilterAction is not registered
            formatFilter.EnsureModernActionFormat();
            restockFilter.EnsureModernActionFormat();
            removeFilter.EnsureModernActionFormat();

            // Verify initial state
            Assert.IsNotNull(formatFilter.ActionHandler, "FormatFilter should have ActionHandler");
            Assert.IsNull(restockFilter.ActionHandler, "RestockFilter should not have ActionHandler initially");
            Assert.IsNotNull(removeFilter.ActionHandler, "RemoveFilter should have ActionHandler");

            // Act - Enable Restocker and re-process
            ConnectionConfig.RestockerEnabled = true;
            FilterActionFactory.Reinitialize(null);

            // Re-process all filters
            formatFilter.EnsureModernActionFormat();
            restockFilter.EnsureModernActionFormat();
            removeFilter.EnsureModernActionFormat();

            // Assert - Only RestockFilter should be updated
            Assert.IsNotNull(formatFilter.ActionHandler, "FormatFilter should still have ActionHandler");
            Assert.IsNotNull(restockFilter.ActionHandler, "RestockFilter should now have ActionHandler");
            Assert.IsNotNull(removeFilter.ActionHandler, "RemoveFilter should still have ActionHandler");

            Assert.IsInstanceOfType(restockFilter.ActionHandler, typeof(RestockFilterAction), "RestockFilter should have RestockFilterAction");
        }

        private void ClearFilterActionFactory()
        {
            try
            {
                // Use Reinitialize method instead of manually clearing fields
                // This ensures proper cleanup and re-initialization
                FilterActionFactory.Reinitialize(null);
            }
            catch
            {
                // Ignore errors in cleanup
            }
        }

        [TestCleanup]
        public void TestCleanup()
        {
            // Reset ConnectionConfig to default state after each test
            ConnectionConfig.RestockerEnabled = false;

            // Clear the registries to ensure clean state for next test
            ClearFilterActionFactory();
        }
    }
}
