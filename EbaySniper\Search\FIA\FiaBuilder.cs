﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using DevExpress.Data.Extensions;
using eBay.Services.Finding;
using uBuyFirst.Prefs;

namespace uBuyFirst.Search.FIA
{
    internal static class FiaBuilder
    {
        public static FindItemsAdvancedRequest CreateBINAuctionRequest(Keyword2Find keyword2Find)
        {
            var listingTypeList = new List<string>();
            var findItemsAdvancedRequest = CreateFindItemsAdvancedRequest(keyword2Find);

            if (keyword2Find.ListingType.Contains(ListingType.BuyItNow) && keyword2Find.ListingType.Contains(ListingType.AuctionsStartedNow))
            {
                listingTypeList.AddRange(new[] { "All" });
            }
            else
            {
                if (keyword2Find.ListingType.Contains(ListingType.BuyItNow))
                    listingTypeList.AddRange(new[] { "AuctionWithBIN", "StoreInventory", "FixedPrice" });

                if (keyword2Find.ListingType.Contains(ListingType.AuctionsStartedNow))
                {
                    listingTypeList.Add("Auction");
                    if (!listingTypeList.Contains("AuctionWithBIN"))
                        listingTypeList.Add("AuctionWithBIN");
                }
            }

            findItemsAdvancedRequest.itemFilter = findItemsAdvancedRequest.itemFilter.Where(f => f.name != ItemFilterType.EndTimeTo).ToArray();
            findItemsAdvancedRequest.itemFilter = findItemsAdvancedRequest.itemFilter.Where(f => f.name != ItemFilterType.EndTimeFrom).ToArray();
            var listingTypeFilterIndex = findItemsAdvancedRequest.itemFilter.FindIndex(filter => filter.name == ItemFilterType.ListingType);
            findItemsAdvancedRequest.itemFilter[listingTypeFilterIndex].value = listingTypeList.ToArray();

            return findItemsAdvancedRequest;
        }

        public static FindItemsAdvancedRequest CreateEndingNowRequest(Keyword2Find keyword2Find)
        {
            var request = CreateFindItemsAdvancedRequest(keyword2Find);
            var findIndex = request.itemFilter.FindIndex(filter => filter.name == ItemFilterType.ListingType);
            request.itemFilter[findIndex].value = new[] { "Auction", "AuctionWithBIN" };

            request.sortOrder = SortOrderType.EndTimeSoonest;

            return request;
        }

        private static FindItemsAdvancedRequest CreateFindItemsAdvancedRequest(Keyword2Find keyword2Find)
        {
            var request = new FindItemsAdvancedRequest();
            request.outputSelector = GetOutputSelector();
            request.sortOrder = SortOrderType.StartTimeNewest;
            request.sortOrderSpecified = true;
            int resultsPerSearch = 100;
            request.paginationInput = GetPaginationSettings(resultsPerSearch);
            request.itemFilter = GetFilters(keyword2Find);
            request.keywords = GetKeywords(keyword2Find);
            request.descriptionSearch = keyword2Find.SearchInDescription;
            SetZipcode(keyword2Find, request);
            SetCategories(keyword2Find, request);

            return request;
        }

        private static ItemFilter[] GetFilters(Keyword2Find keyword2Find)
        {
            var filters = new List<ItemFilter>();
            var listingTypeList = new List<string>();
            if (keyword2Find.ListingType.Contains(ListingType.BuyItNow))
                listingTypeList.AddRange(new[] { "AuctionWithBIN", "StoreInventory", "FixedPrice" });

            if (keyword2Find.ListingType.Contains(ListingType.AuctionsStartedNow) || keyword2Find.ListingType.Contains(ListingType.AuctionsEndingNow))
                listingTypeList.AddRange(new[] { "Auction" });

            if (keyword2Find.ListingType.Contains(ListingType.AuctionsEndingNow))
            {
                var endTimeTo = new ItemFilter();
                endTimeTo.name = ItemFilterType.EndTimeTo;
                endTimeTo.value = new[] { DateTime.UtcNow.AddMinutes(15).ToString(@"yyyy-MM-dd\THH:mm:ss.fff\Z") };

                var endTimeFrom = new ItemFilter();
                endTimeFrom.name = ItemFilterType.EndTimeFrom;
                endTimeFrom.value = new[] { DateTime.UtcNow.AddMinutes(80).ToString(@"yyyy-MM-dd\THH:mm:ss.fff\Z") };

                filters.Add(endTimeTo);
                //filters.Add(endTimeFrom); 
            }

            var listingType = new ItemFilter();
            listingType.name = ItemFilterType.ListingType;
            listingType.value = listingTypeList.ToArray();

            var minPrice = new ItemFilter();
            var maxPrice = new ItemFilter();
            minPrice.name = ItemFilterType.MinPrice;
            minPrice.value = new[] { keyword2Find.PriceMin.ToString(CultureInfo.InvariantCulture) };

            maxPrice.name = ItemFilterType.MaxPrice;
            maxPrice.value = new[] { keyword2Find.PriceMax.ToString(CultureInfo.InvariantCulture) };

            filters.Add(minPrice);
            filters.Add(maxPrice);
            filters.Add(listingType);

            var keyword2FindCondition = keyword2Find.Condition.Where(c => c != "2010" && c != "2020" && c != "2030").ToArray();

            if (keyword2FindCondition != null && keyword2FindCondition.Length > 0)
            {
                if (keyword2FindCondition.Length != 1 || !string.IsNullOrEmpty(keyword2FindCondition[0]))
                {
                    var conditionFilter = new ItemFilter
                    {
                        name = ItemFilterType.Condition,
                        value = keyword2FindCondition
                    };

                    filters.Add(conditionFilter);
                }
            }

            if (keyword2Find.Sellers?.Any() == true)
            {
                var validsellers = keyword2Find.Sellers.Where(seller => !string.IsNullOrEmpty(seller)).ToList();
                if (validsellers.Count > 0 && !string.IsNullOrEmpty(keyword2Find.SellerType))
                {
                    var includeSellersFilter = new ItemFilter
                    {
                        name = keyword2Find.SellerType == "Include" ? ItemFilterType.Seller : ItemFilterType.ExcludeSeller,
                        value = validsellers.ToArray()
                    };

                    filters.Add(includeSellersFilter);
                }
            }

            if (keyword2Find.LocatedIn != "Any")
            {
                var locatedIn = new ItemFilter
                {
                    name = ItemFilterType.LocatedIn,
                    value = new[] { keyword2Find.LocatedIn }
                };

                filters.Add(locatedIn);
            }

            if (keyword2Find.AvailableTo != "Any")
            {
                var availableTo = new ItemFilter
                {
                    name = ItemFilterType.AvailableTo,
                    value = new[] { keyword2Find.AvailableTo }
                };

                filters.Add(availableTo);
            }

            var hideDupes = new ItemFilter
            {
                name = ItemFilterType.HideDuplicateItems,
                value = new[] { "true" }
            };

            filters.Add(hideDupes);

            return filters.ToArray();
        }

        private static OutputSelectorType[] GetOutputSelector()
        {
            return new[] { OutputSelectorType.SellerInfo, OutputSelectorType.UnitPriceInfo, OutputSelectorType.PictureURLLarge };
        }

        private static string GetKeywords(Keyword2Find keyword2Find)
        {
            if (keyword2Find.Kws == null)
                keyword2Find.Kws = "";

            var keywords = keyword2Find.Kws;
            if (keywords.Contains(",") && !keywords.Contains("("))
                keywords = $"({keywords})";

            return keywords;
        }

        private static void SetZipcode(Keyword2Find keyword2Find, FindItemsAdvancedRequest request)
        {
            if (!string.IsNullOrEmpty(keyword2Find.Zip.Trim()) && !keyword2Find.IgnorePostalCodeError)
            {
                request.buyerPostalCode = keyword2Find.Zip.Trim();
            }
        }

        private static void SetCategories(Keyword2Find keyword2Find, FindItemsAdvancedRequest request)
        {
            if (keyword2Find.Categories4Api.Length > 0)
            {
                request.categoryId = keyword2Find.Categories4Api.Split(',');
            }
        }

        private static PaginationInput GetPaginationSettings(int resultsPerSearch)
        {
            var pi = new PaginationInput();
            pi.entriesPerPage = resultsPerSearch;
            pi.pageNumber = 1;
            pi.entriesPerPageSpecified = true;
            pi.pageNumberSpecified = true;

            return pi;
        }

        public static void CreateSearchRequests(List<Keyword2Find> enabledKeywords)
        {
            var resultsPerInitialSearch = CalcResultsPerInitialSearch(enabledKeywords);
            for (var i = 0; i < enabledKeywords.Count; i++)
            {
                var kw = enabledKeywords[i];
                kw.InitialSearchLimit = resultsPerInitialSearch;
                kw.InitialSearchCount = 0;
                kw.BinAuctionRequest = CreateBINAuctionRequest(kw);
                kw.RequestEndNow = CreateEndingNowRequest(kw);
            }
        }

        private static int CalcResultsPerInitialSearch(List<Keyword2Find> enabledKeywords)
        {
            var resultsPerInitialSearch = UserSettings.InitialResultsLimit / enabledKeywords.Count;
            resultsPerInitialSearch = Math.Max(resultsPerInitialSearch, 1);
            resultsPerInitialSearch = Math.Min(resultsPerInitialSearch, 100);

            return resultsPerInitialSearch;
        }
    }
}
