﻿using System.Threading.Tasks;
using uBuyFirst.Models;

namespace uBuyFirst.Services
{
    public interface ISellerService
    {
        /// <summary>
        /// Parses seller JSON data into a refined model.
        /// </summary>
        /// <param name="jsonData">The JSON string containing seller data.</param>
        /// <returns>A SellerUser object if parsing is successful; otherwise, null.</returns>
        SellerUser? ParseSellerInfo(string jsonData);

        /// <summary>
        /// Fetches seller information from the API.
        /// </summary>
        /// <param name="sellerName">The name of the seller.</param>
        /// <param name="searchSource">The source of the search.</param>
        /// <returns>A JSON string containing seller information if successful; otherwise, null.</returns>
        Task<string?> FetchSellerInfo(string sellerName);

        /// <summary>
        /// Gets the seller JSON string, either from cache or by fetching it.
        /// </summary>
        /// <param name="sellerName">The name of the seller.</param>
        /// <param name="searchSource">The source of the search.</param>
        /// <returns>A JSON string containing seller information if successful; otherwise, null.</returns>
        Task<string?> GetSellerJsonStr(string sellerName);
    }
}
