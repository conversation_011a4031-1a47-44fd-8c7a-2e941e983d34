﻿namespace uBuyFirst.Pricing
{
    public class ItemPricing
    {
        private CurrencyAmount _totalPrice;
        public CurrencyAmount? ItemPrice;
        public CurrencyAmount? AuctionPrice;

        /// <summary>
        /// The actual purchase price to use for PlaceOffer calls (without fees for UK/GB private sellers)
        /// If null, falls back to ItemPrice
        /// </summary>
        public CurrencyAmount? PurchasePrice;

        public CurrencyAmount GetTotalPrice(CurrencyAmount? fullSingleShippingPrice)
        {
            if (fullSingleShippingPrice?.Value > 0)
                _totalPrice = new CurrencyAmount(ItemPrice.Value + fullSingleShippingPrice.Value, ItemPrice.Currency);
            else
                _totalPrice = ItemPrice;
            if (ItemPrice.Value.Equals(0.0))
                _totalPrice.Value = 0;
            return _totalPrice;
        }

        /// <summary>
        /// Gets the effective purchase price to use for PlaceOffer calls
        /// Returns PurchasePrice if set, otherwise falls back to ItemPrice
        /// </summary>
        /// <returns>The price to use for purchase operations</returns>
        public CurrencyAmount GetEffectivePurchasePrice()
        {
            return PurchasePrice ?? ItemPrice;
        }
    }
}
