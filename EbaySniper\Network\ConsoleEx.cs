﻿using System;
using System.Diagnostics;
using System.Linq;

namespace uBuyFirst.Network
{
    static class ConsoleEx
    {
        static readonly object SyncRoot = new object();

        public static void WriteLine(this ConsoleColor cc, string message)
        {
            if (silent)
                return;
            lock (SyncRoot)
            {
                ConsoleColor oc = Console.ForegroundColor;
                Console.ForegroundColor = cc;
                Debug.WriteLine(message);
                Console.ForegroundColor = oc;
            }
        }

        public static void WriteLine(this ConsoleColor cc, string message, params object[] args)
        {
            if (silent)
                return;
            lock (SyncRoot)
            {
                ConsoleColor oc = Console.ForegroundColor;
                Console.ForegroundColor = cc;
                Debug.WriteLine(message, args);
                Console.ForegroundColor = oc;
            }
        }

        public static void Write(this ConsoleColor cc, string message)
        {
            if (silent)
                return;
            lock (SyncRoot)
            {
                ConsoleColor oc = Console.ForegroundColor;
                Console.ForegroundColor = cc;
                Console.Write(message);
                Console.ForegroundColor = oc;
            }
        }

        public static void LogError(this Exception ex)
        {
            if (ex is AggregateException)
                ex = (ex as AggregateException).Flatten().InnerExceptions.First();
            lock (SyncRoot)
            {
                WriteLine(ConsoleColor.Red, ex.Message);
                WriteLine(ConsoleColor.DarkRed, ex.ToString());
            }
        }

        public static readonly bool silent = false;
    }
}