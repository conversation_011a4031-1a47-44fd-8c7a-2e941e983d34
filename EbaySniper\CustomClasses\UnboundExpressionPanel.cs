﻿using System;
using System.ComponentModel;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraGrid.Columns;
using uBuyFirst.Filters;

namespace uBuyFirst.CustomClasses
{
    public class UnboundExpressionPanel : PanelControl
    {
        public MyUnboundColumnExpressionEditorForm MyForm;
        private GridColumn _fUnboundColumn;

        public UnboundExpressionPanel()
        {
            BorderStyle = BorderStyles.NoBorder;
            StartEdit(new GridColumn());
        }

        private object[] Arguments { get; set; }

        public GridColumn UnboundColumn
        {
            get => _fUnboundColumn;
            set
            {
                if (_fUnboundColumn == value) return;
                StartEdit(value);
            }
        }

        private static MyUnboundColumnExpressionEditorForm CreateForm(params object[] arguments)
        {
            return new MyUnboundColumnExpressionEditorForm(arguments[0], null);
        }

        private void ApplyExpression(string expression)
        {
            if (Arguments == null) return;
            ((GridColumn) Arguments[0]).UnboundExpression = expression;
        }

        private void StartEdit(params object[] arguments)
        {
            if (arguments.Length < 1) return;
            if (!(arguments[0] is GridColumn unboundColumn)) return;
            _fUnboundColumn = unboundColumn;
            DestroyForm();
            Arguments = arguments;
            MyForm = CreateForm(arguments);
            if (MyForm == null) return;
            MyForm.Dock = DockStyle.Fill;

            MyForm.TopLevel = false;
            MyForm.FormBorderStyle = FormBorderStyle.None;
            MyForm.Closing += MyFormClosing;
            MyForm.buttonCancel.Click += buttonCancel_Click;
            MyForm.buttonOK.Text = @"Apply";
            Controls.Add(MyForm);
            MyForm.Visible = true;
        }

        private void buttonCancel_Click(object sender, EventArgs e)
        {
            MyForm?.Close();
        }

        private void MyFormClosing(object sender, CancelEventArgs e)
        {
            e.Cancel = true;
            if (Arguments == null || MyForm == null) return;
            if (MyForm.DialogResult == DialogResult.OK)
            {
                ApplyExpression(MyForm.Expression);
            }
            else
            {
                MyForm.ResetMemoText();
            }
        }

        private void DestroyForm()
        {
            MyForm?.Dispose();
            MyForm = null;
        }
    }
}