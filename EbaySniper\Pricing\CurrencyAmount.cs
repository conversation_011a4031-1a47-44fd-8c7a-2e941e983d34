﻿namespace uBuyFirst.Pricing
{
    public class CurrencyAmount
    {
        public CurrencyAmount(double value, string currency)
        {
            this.Value = value;
            Currency = currency;
        }

        public string Currency { get; set; } = "";
        public double Value { get; set; }

        public override string ToString()
        {

            var currencySign = CurrencyCodeToSign(Currency);

            return $"{currencySign}{Value} ({Currency})".Replace(" (USD)", "");
        }

        private static string CurrencyCodeToSign(string currency)
        {
            if (currency == "USD")
                return "$";

            if (currency == "GBP")
                return "£";

            if (currency == "EUR")
                return "€";

            return "";
        }
    }
}