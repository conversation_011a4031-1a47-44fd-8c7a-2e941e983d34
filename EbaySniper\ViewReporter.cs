﻿using System;
using uBuyFirst.Search;

namespace uBuyFirst
{
    public class ViewReporter
    {
        private readonly IProgress<string> _reporterLogTxt;
        private readonly IProgress<byte> _reporterDots;
        private readonly IProgress<FoundItem> _reporterItem;

        public ViewReporter(Form1 form1)
        {
            var reporterLogTxt = new Progress<string>();
            reporterLogTxt.ProgressChanged += form1.ReportLogTxt;
            _reporterLogTxt = reporterLogTxt;

            var reporterItem = new Progress<FoundItem>();
            reporterItem.ProgressChanged += form1._reporterItem_ProgressChanged;
            _reporterItem = reporterItem;

            var reporterDots = new Progress<byte>();
            reporterDots.ProgressChanged += form1.ReporterDotsProgressChanged;
            _reporterDots = reporterDots;
        }

        public void ReportDots(byte i)
        {
            if (Form1.Instance.Disposing || Form1.Instance.IsDisposed)
                return;
            _reporterDots.Report(i);
        }

        public void ReportLogTxt(string s)
        {
            _reporterLogTxt.Report(s);
        }

        public void ReportItem(FoundItem arrayListItem)
        {
            _reporterItem.Report(arrayListItem);
        }
    }
}
