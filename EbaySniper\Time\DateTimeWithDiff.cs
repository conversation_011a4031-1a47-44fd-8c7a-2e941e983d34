﻿using System;
using uBuyFirst.Prefs;

namespace uBuyFirst.Time
{
    public class DateTimeWithDiff : IComparable
    {
        public DateTime Utc;
        public double? TimeDiff;
        public DateTime LocalTime => TimeZoneInfo.ConvertTimeFromUtc(Utc, UserSettings.CurrentTimeZoneInfo);

        public DateTimeWithDiff()
        {
        }

        public DateTimeWithDiff(DateTime utc, double? timeDiff)
        {
            Utc = utc;
            TimeDiff = timeDiff;
        }

        public int CompareTo(object obj)
        {
            if (ReferenceEquals(null, obj))
                return 1;

            var datetime = ((DateTimeWithDiff)obj).Utc;

            return Utc.CompareTo(datetime);
        }

        public override string ToString()
        {
            if (TimeDiff == null)
                return LocalTime.ToString("HH:mm:ss dd-MMM-yyyy");

            if (TimeDiff.Equals(-112233D))
                return "";
            var humanTimeDiff = TimeSpan.FromSeconds((int)TimeDiff);
            var DisplayText = "";
            if (humanTimeDiff.TotalSeconds < 0)
            {
                DisplayText = "0";
            }
            else
            {
                if (humanTimeDiff.TotalHours >= 24)
                {
                    try
                    {
                        DisplayText = humanTimeDiff.ToString(@"d\ \d");
                    }
                    catch (Exception ex)
                    {
                    }
                }

                else if (humanTimeDiff.TotalMinutes >= 60)
                    DisplayText = humanTimeDiff.ToString(@"h\ \h");
                else if (humanTimeDiff.TotalSeconds >= 60)
                    DisplayText = humanTimeDiff.ToString(@"m\ \m");
                else
                    DisplayText = TimeDiff.ToString();
            }

            return "(" + DisplayText + ") " + LocalTime.ToString("HH:mm:ss dd-MMM-yyyy");
        }
    }
}