﻿using System;
using System.Diagnostics;
using System.Windows.Forms;
using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;
using uBuyFirst.License;
using uBuyFirst.Prefs;
using uBuyFirst.Properties;
using uBuyFirst.Tools;

namespace uBuyFirst
{
    public partial class FormSubscription : RibbonForm
    {
        public string SerialNumber { get; private set; }

        public FormSubscription()
        {
            InitializeComponent();

            try
            {
                // Handle case when called from Trial Subscription.
                var licenseUtil = Form1.LicenseUtility;
                if (licenseUtil == null)
                    licenseUtil = FormTrialSubscriptionPrompt.LicenseUtility;

                switch (licenseUtil.CurrentSubscriptionType)
                {
                    case LicenseUtility.SubscriptionType.NotActivated:
                        lblActivationStatus.Text = $"{licenseUtil.CurrentSubscriptionType}";

                        lnkReadyToUpgrade.Visible = true;
                        lnkResendLicenseKey.Visible = true;
                        lnkFirstTimeUser.Visible = true;
                        lnkSubscriptionFAQ.Visible = false;
                        lblInfoText.Text = "- Your 30 day trial gives you full access to ALL features with no restrictions.\r\n"
                                           + "- Users upgrading from trial/basic to a subscription will be provided a new license key via email after checkout.\r\n"
                                           + "- After your trial ends you’ll automatically be switched to the free basic plan unless you upgrade to a subscription.";
                        break;
                    case LicenseUtility.SubscriptionType.Basic:
                        lblActivationStatus.Text = $"(Active) [{licenseUtil.CurrentSubscriptionType}]";
                        lnkReadyToUpgrade.Visible = true;
                        lnkResendLicenseKey.Visible = true;
                        lnkFirstTimeUser.Visible = false;
                        lnkSubscriptionFAQ.Visible = false;
                        lblInfoText.Text = "- Your 30 day trial gives you full access to ALL features with no restrictions.\r\n"
                                           + "- Users upgrading from trial/basic to a subscription will be provided a new license key via email after checkout.\r\n"
                                           + "- After your trial ends you’ll automatically be switched to the free basic plan unless you upgrade to a subscription.";

                        break;
                    case LicenseUtility.SubscriptionType.Trial:
                        lnkReadyToUpgrade.Visible = true;
                        lnkResendLicenseKey.Visible = false;
                        lnkFirstTimeUser.Visible = false;
                        lnkSubscriptionFAQ.Visible = false;
                        lblInfoText.Text = "- Your 30 day trial gives you full access to ALL features with no restrictions.\r\n"
                                           + "- Users upgrading from trial/basic to a subscription will be provided a new license key via email after checkout.\r\n"
                                           + "- After your trial ends you’ll automatically be switched to the free basic plan unless you upgrade to a subscription.";

                        if (!licenseUtil.TrialExpired)
                        {
                            lblActivationStatus.Text = En_US.ActivationForm_ActivationForm_Trial__Days_left__ + licenseUtil.TrialDaysLeft;
                        }

                        break;

                    case LicenseUtility.SubscriptionType.Enterprise:
                    case LicenseUtility.SubscriptionType.Elite:
                    case LicenseUtility.SubscriptionType.Pro:
                        lblActivationStatus.Text = $"(Active) [{licenseUtil.CurrentSubscriptionType}]";

                        //this.panel1.Controls.Add(this.lblLicenseStatus);
                        //this.panel1.Controls.Add(this.lnkSubscriptionFAQ);

                        //this.panel1.Controls.Remove(this.lnkReadyToUpgrade);
                        //this.panel1.Controls.Remove(this.lnkResendLicenseKey);
                        //this.panel1.Controls.Remove(this.lnkFirstTimeUser);
                        lnkReadyToUpgrade.Visible = false;
                        lnkResendLicenseKey.Visible = false;
                        lnkFirstTimeUser.Visible = false;
                        lnkSubscriptionFAQ.Visible = true;
                        lblInfoText.Text = "";
                        break;
                }

            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("FormSubscription: ", ex);
            }

            this.StartPosition = FormStartPosition.CenterParent;
        }

        private void ActivateButton_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(SerialNumberTextBox.Text) || SerialNumberTextBox.Text == @"    -    -    -    -    -    -")
            {
                XtraMessageBox.Show(En_US.ActivationForm_ActivateButton_Click_Serial_Number_is_required);
                return;
            }

            SerialNumber = SerialNumberTextBox.Text;
            DialogResult = DialogResult.OK;
            Close();
        }

        private void ActivationForm_Load(object sender, EventArgs e)
        {
            if (!string.IsNullOrEmpty(ProgramState.SerialNumber))
                SerialNumberTextBox.Text = ProgramState.SerialNumber.Split('_')[0];
        }

        private void btnContinueTrial_Click(object sender, EventArgs e)
        {
            Close();
        }

        private void FormSubscription_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == 27)
            {
                Close();
            }
        }

        private void linkLabel_Click(object sender, EventArgs e)
        {
            var linkLabel = (HyperlinkLabelControl)sender;
            Process.Start(linkLabel.Tag.ToString());
        }
    }
}
