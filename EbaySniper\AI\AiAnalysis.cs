﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.Utils.Menu;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Grid;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using uBuyFirst.CustomClasses;
using uBuyFirst.Data;
using uBuyFirst.Prefs;

namespace uBuyFirst.AI
{

    internal class AiAnalysis
    {
        public static CustomBindingList<string> AiColumnsList;

        public const string AiPrefix = "ai ";
        public static async void UpdateAnalysis(object sender, EventArgs e)
        {
            try
            {
                var menuItem = sender as DXMenuItem;
                if (menuItem?.Tag is GridView gridView)
                {
                    if (gridView.GridControl.DataSource is DataTable dataTable)
                    {
                        Debug.WriteLine("Starting URL-based AI analysis of product titles...");

                        var semaphore = new SemaphoreSlim(5); // limit concurrency to 5
                        var tasks = new List<Task>();
                        var uiContext = SynchronizationContext.Current;

                        foreach (DataRow row in dataTable.Rows)
                        {
                            // Wait until there is room in the semaphore.
                            await semaphore.WaitAsync();

                            // Launch a task to process this row.
                            tasks.Add(Task.Run(async () =>
                            {
                                try
                                {
                                    if (!row.RowState.HasFlag(DataRowState.Deleted) && !row.IsNull("Title"))
                                    {
                                        // Use the new single-row analysis method
                                        await PerformSingleRowAnalysisWithUIContext(row, uiContext);
                                    }
                                }
                                finally
                                {
                                    // Always release the semaphore even if an exception occurs.
                                    semaphore.Release();
                                }
                            }));
                        }

                        // Wait for all tasks to complete.
                        await Task.WhenAll(tasks);
                        Debug.WriteLine($"Processed {dataTable.Rows.Count} items with URL-based AI");
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in UpdateAnalysis: {ex.Message}");
                XtraMessageBox.Show($"Error processing grid data: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private static void GetLargePictureUrls(List<string> picturesUrl)
        {
            const int ImageWidthHeight = 1600; // High resolution image size

            for (var i = 0; i < picturesUrl.Count; i++)
            {
                var url = picturesUrl[i];
                string imgID = null;

                // Try to pull ID from the "/z/<ID>/" form
                var match = System.Text.RegularExpressions.Regex.Match(url, @"/z/([^/]+)/");
                if (match.Success)
                {
                    imgID = match.Groups[1].Value;
                }
                else
                {
                    // Otherwise, maybe it's already in the "/images/g/<ID>/" form
                    match = System.Text.RegularExpressions.Regex.Match(url, @"/images/g/([^/]+)/");
                    if (match.Success)
                    {
                        imgID = match.Groups[1].Value;
                    }
                    else
                    {
                        // If no ID found, leave it unchanged
                        continue;
                    }
                }

                // Update the URL to the high-resolution format
                picturesUrl[i] = $"https://i.ebayimg.com/images/g/{imgID}/s-l{ImageWidthHeight}.webp";
            }
        }

        public static void AiColumnsList_ListChanged(object sender, ListChangedEventArgs e)
        {

        }

        public static void AiColumnsList_ItemDeleting(object sender, string e)
        {

        }

        /// <summary>
        /// Core AI analysis logic that fetches and parses AI response for a single row
        /// </summary>
        /// <param name="row">The DataRow to analyze</param>
        /// <returns>Tuple containing the title and parsed AI columns, or null if analysis failed</returns>
        private static async Task<(string title, List<KeyValuePair<string, string>> aiColumns)?> PerformAiAnalysisCore(DataRow row)
        {
            string jsonResponse;
            try
            {
                if (row == null || row.RowState == DataRowState.Deleted || row.IsNull("Title"))
                {
                    return null;
                }

                var d = (DataList)row["Blob"];
                var title = d.Title;

                if (string.IsNullOrEmpty(title))
                {
                    return null;
                }

                // Create an instance of UrlJsonService
                var urlJsonService = new UrlJsonService();

                // Fetch JSON response from URL endpoint
                jsonResponse = await urlJsonService.FetchJsonFromUrl(row);
                jsonResponse = jsonResponse.Trim('`').Replace("json\n", "");
                dynamic json = JsonConvert.DeserializeObject(jsonResponse);

                // Convert dynamic json to a JObject for iteration.
                var jObject = json as JObject;
                var aiColumns = new List<KeyValuePair<string, string>>();

                if (jObject != null)
                {
                    foreach (var property in jObject.Properties())
                    {
                        aiColumns.Add(new KeyValuePair<string, string>(property.Name,
                            property?.Value?.ToString() ?? ""));
                    }
                }

                Debug.WriteLine($"AI Analysis completed for: {title}");
                Debug.WriteLine($"AI Response: {jsonResponse}");

                return (title, aiColumns);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in PerformAiAnalysisCore: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Performs AI analysis on a single DataRow with UI context for bulk updates
        /// </summary>
        /// <param name="row">The DataRow to analyze</param>
        /// <param name="uiContext">UI synchronization context for thread-safe updates</param>
        /// <returns>Task representing the async operation</returns>
        private static async Task PerformSingleRowAnalysisWithUIContext(DataRow row, SynchronizationContext uiContext)
        {
            var result = await PerformAiAnalysisCore(row);
            if (result == null) return;

            var (title, aiColumns) = result.Value;
            Debug.WriteLine($"Processing Title: {title}");

            // Use UI context for thread-safe updates during bulk operations
            uiContext.Post(_ =>
            {
                // This code runs on the UI thread.
                foreach (var responseKeyValue in aiColumns)
                {
                    var columnName = AiPrefix + responseKeyValue.Key;
                    if (row.Table.Columns.Contains(columnName))
                    {
                        row[columnName] = responseKeyValue.Value;
                    }
                }
            }, null);

            Debug.WriteLine("----------------------------------------");
        }

        /// <summary>
        /// Performs AI analysis on a single DataRow for new items before filtering
        /// </summary>
        /// <param name="row">The DataRow to analyze</param>
        /// <returns>Task representing the async operation</returns>
        public static async Task PerformSingleRowAnalysis(DataRow row)
        {
            var result = await PerformAiAnalysisCore(row);
            if (result == null)
            {
                return;
            }

            var (title, aiColumns) = result.Value;
            Debug.WriteLine($"Performing AI analysis for new item: {title}");

            // Populate AI columns directly (no UI context needed since we're in the same thread)
            foreach (var responseKeyValue in aiColumns)
            {
                var columnName = AiPrefix + responseKeyValue.Key;
                if (row.Table.Columns.Contains(columnName))
                {
                    row[columnName] = responseKeyValue.Value;
                }
            }
        }

        /// <summary>
        /// Checks if all AI columns are empty for the given row
        /// </summary>
        /// <param name="row">The DataRow to check</param>
        /// <returns>True if all AI columns are empty or null, false otherwise</returns>
        public static bool AreAiColumnsEmpty(DataRow row)
        {
            try
            {
                if (row == null || row.RowState == DataRowState.Deleted || AiColumnsList == null || AiColumnsList.Count == 0)
                {
                    return false; // Don't consider it empty if we can't check properly
                }

                // Check each AI column to see if it has data
                foreach (var aiColumn in AiColumnsList)
                {
                    if (row.Table.Columns.Contains(aiColumn))
                    {
                        var value = row[aiColumn];
                        if (value != null && value != DBNull.Value && !string.IsNullOrWhiteSpace(value.ToString()))
                        {
                            return false; // Found at least one non-empty AI column
                        }
                    }
                }

                return true; // All AI columns are empty
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error checking if AI columns are empty: {ex.Message}");
                return false; // Don't consider it empty if we can't check properly
            }
        }

        /// <summary>
        /// Updates AI columns for a row when changing rows, if they are empty
        /// </summary>
        /// <param name="row">The DataRow to potentially update</param>
        /// <param name="uiContext">UI synchronization context for thread-safe updates</param>
        /// <returns>Task representing the async operation</returns>
        public static async Task UpdateAiColumnsOnRowChange(DataRow row, SynchronizationContext uiContext = null)
        {
            try
            {
                // Only proceed if AI endpoint is enabled
                if (!UserSettings.IsAIEndpoint)
                {
                    return;
                }

                // Only proceed if we have AI columns configured
                if (AiColumnsList == null || AiColumnsList.Count == 0)
                {
                    return;
                }

                // Only proceed if AI columns are empty
                if (!AreAiColumnsEmpty(row))
                {
                    return;
                }

                Debug.WriteLine("AI columns are empty for current row, attempting to fetch data...");

                // Capture UI context if not provided
                if (uiContext == null)
                {
                    uiContext = SynchronizationContext.Current;
                }

                // Perform AI analysis for this row with proper synchronization context
                await PerformSingleRowAnalysisForRowChange(row, uiContext);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating AI columns on row change: {ex.Message}");
                // Don't throw - we don't want to break row navigation if AI fails
            }
        }

        /// <summary>
        /// Performs AI analysis on a single DataRow with proper synchronization context for row change scenarios
        /// </summary>
        /// <param name="row">The DataRow to analyze</param>
        /// <param name="uiContext">UI synchronization context for thread-safe updates</param>
        /// <returns>Task representing the async operation</returns>
        private static async Task PerformSingleRowAnalysisForRowChange(DataRow row, SynchronizationContext uiContext)
        {
            var result = await PerformAiAnalysisCore(row);
            if (result == null)
            {
                return;
            }

            var (title, aiColumns) = result.Value;
            Debug.WriteLine($"Performing AI analysis for row change: {title}");

            // Use synchronization context to marshal updates back to UI thread
            if (uiContext != null)
            {
                uiContext.Post(_ =>
                {
                    try
                    {
                        // Update AI columns on the UI thread
                        foreach (var responseKeyValue in aiColumns)
                        {
                            var columnName = AiPrefix + responseKeyValue.Key;
                            if (row.Table.Columns.Contains(columnName))
                            {
                                row[columnName] = responseKeyValue.Value;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error updating AI columns on UI thread: {ex.Message}");
                    }
                }, null);
            }
            else
            {
                // No UI context available, update directly (assume we're on the correct thread)
                foreach (var responseKeyValue in aiColumns)
                {
                    var columnName = AiPrefix + responseKeyValue.Key;
                    if (row.Table.Columns.Contains(columnName))
                    {
                        row[columnName] = responseKeyValue.Value;
                    }
                }
            }
        }
    }
}
