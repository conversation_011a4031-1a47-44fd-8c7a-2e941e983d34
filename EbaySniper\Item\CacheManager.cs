﻿using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using uBuyFirst.Data;

namespace uBuyFirst.Item {
    internal static class CacheManager {

        private static readonly ConcurrentDictionary<string, DataList> SubSearchItemCache = new();
        private static int _cacheExpirationMinutes = 10;
        private static readonly TimeSpan CleanupJobInterval = TimeSpan.FromMinutes(1);
        internal static Timer _cleanUpTimer = new Timer(_EvictExpired, null, CleanupJobInterval, CleanupJobInterval);

        private static void _EvictExpired(object state)
        {
            if (Debugger.IsAttached)
                _cacheExpirationMinutes = 10;

            var dataLists = SubSearchItemCache.Values.ToList();
            foreach (var dataList in dataLists)
            {
                var cacheItemAgeMinutes = (DateTime.UtcNow - dataList.FoundTime.Utc).TotalMinutes;
                var cacheItemExpired = cacheItemAgeMinutes > _cacheExpirationMinutes;

                if (!cacheItemExpired)
                    continue;

                if (SubSearchItemCache.TryRemove(dataList.ItemID, out _))
                {
                }
            }
        }

        public static void ClearCache()
        {
            while (SubSearchItemCache.Count > 0)
            {
                var cacheItem = SubSearchItemCache.FirstOrDefault();
                if (SubSearchItemCache.TryRemove(cacheItem.Key, out _))
                {
                }
            }
        }

        public static void AddItem(DataList datalist)
        {
            SubSearchItemCache.TryAdd(datalist.ItemID, datalist);
        }

        public static bool TryGetValue(string itemID, out DataList? datalist)
        {
            return SubSearchItemCache.TryGetValue(itemID, out datalist);
        }
    }
}
