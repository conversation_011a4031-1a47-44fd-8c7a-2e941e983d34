﻿using System;
using System.Data;
using DevExpress.XtraGrid;
using uBuyFirst.Data; // Assuming DataList is in this namespace

namespace uBuyFirst.Watchlist
{
    /// <summary>
    /// Provides data for the ItemBecameAvailable event.
    /// </summary>
    public class NotificationEventArgs : EventArgs
    {
        /// <summary>
        /// Gets the DataRow representing the item in the grid.
        /// </summary>
        public DataRow Row { get; }

        /// <summary>
        /// Gets the DataList object containing detailed item data.
        /// </summary>
        public DataList ItemData { get; }

        /// <summary>
        /// Gets the GridControl where the item is displayed.
        /// </summary>
        public GridControl SourceGrid { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="NotificationEventArgs"/> class.
        /// </summary>
        /// <param name="row">The data row.</param>
        /// <param name="itemData">The item data.</param>
        /// <param name="sourceGrid">The source grid control.</param>
        public NotificationEventArgs(DataRow row, DataList itemData, GridControl sourceGrid)
        {
            Row = row ?? throw new ArgumentNullException(nameof(row));
            ItemData = itemData ?? throw new ArgumentNullException(nameof(itemData));
            SourceGrid = sourceGrid ?? throw new ArgumentNullException(nameof(sourceGrid));
        }
    }
}
