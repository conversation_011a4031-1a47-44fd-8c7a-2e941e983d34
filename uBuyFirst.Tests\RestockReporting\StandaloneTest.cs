using System;
using System.IO;
using System.Threading.Tasks;
using uBuyFirst.RestockReporting.Models;
using uBuyFirst.RestockReporting.Services;
using uBuyFirst.Restocker.Models;
using uBuyFirst.Restocker.Services;

namespace uBuyFirst.Tests.RestockReporting
{
    /// <summary>
    /// Standalone test class that can be run independently to verify the reporting system works
    /// This doesn't require MSTest or other testing frameworks
    /// </summary>
    public static class StandaloneTest
    {
        public static async Task RunAllTests()
        {
            Console.WriteLine("Starting RestockReporting Standalone Tests...");

            try
            {
                await TestItemHistoryLogger();
                await TestItemHistoryExporter();
                await TestDataListMapper();

                Console.WriteLine("✅ All tests passed!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Test failed: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
            }
        }

        private static async Task TestItemHistoryLogger()
        {
            Console.WriteLine("Testing ItemHistoryLogger...");

            // Create temporary test directory
            var testPath = Path.Combine(Path.GetTempPath(), "RestockReportingTest", Guid.NewGuid().ToString());
            Directory.CreateDirectory(testPath);

            try
            {
                var options = new ItemHistoryOptions
                {
                    BasePath = testPath,
                    ErrorLogPath = Path.Combine(testPath, "errors"),
                    EnableLogging = true,
                    CreateDailyFolders = true
                };

                using (var logger = new FileItemHistoryLogger(options))
                {
                    var context = CreateTestContext();
                    await logger.LogItemProcessingAsync(context);

                    // Verify file was created
                    var todayFolder = Path.Combine(testPath, context.Timestamp.ToString("yyyy-MM-dd"));
                    if (!Directory.Exists(todayFolder))
                        throw new Exception("Daily folder was not created");

                    var jsonFiles = Directory.GetFiles(todayFolder, "*.json");
                    if (jsonFiles.Length == 0)
                        throw new Exception("No JSON files were created");

                    var jsonContent = await Task.Run(() => File.ReadAllText(jsonFiles[0]));
                    if (!jsonContent.Contains("\"itemId\": \"123456789\""))
                        throw new Exception("JSON content is incorrect");

                    Console.WriteLine("  ✅ ItemHistoryLogger test passed");
                }
            }
            finally
            {
                // Cleanup
                if (Directory.Exists(testPath))
                    Directory.Delete(testPath, true);
            }
        }

        private static async Task TestItemHistoryExporter()
        {
            Console.WriteLine("Testing ItemHistoryExporter...");

            // Create temporary test directory
            var testPath = Path.Combine(Path.GetTempPath(), "RestockReportingExportTest", Guid.NewGuid().ToString());
            Directory.CreateDirectory(testPath);

            try
            {
                var options = new ItemHistoryOptions
                {
                    BasePath = testPath,
                    CreateDailyFolders = true
                };

                // Create test data
                var testDate = DateTime.Now.Date;
                var dayFolder = Path.Combine(testPath, testDate.ToString("yyyy-MM-dd"));
                Directory.CreateDirectory(dayFolder);

                var context = CreateTestContext();
                var jsonFile = Path.Combine(dayFolder, "test_item.json");
                var json = Newtonsoft.Json.JsonConvert.SerializeObject(context, Newtonsoft.Json.Formatting.Indented);
                await Task.Run(() => File.WriteAllText(jsonFile, json));

                // Test exporter
                var exporter = new ItemHistoryExporter(options);
                var items = await exporter.LoadHistoryAsync(testDate, testDate);

                var itemsList = System.Linq.Enumerable.ToList(items);
                if (itemsList.Count != 1)
                    throw new Exception($"Expected 1 item, got {itemsList.Count}");

                if (itemsList[0].ItemData.ItemId != "123456789")
                    throw new Exception("Item ID mismatch");

                // Test CSV export
                var csvPath = Path.Combine(testPath, "export.csv");
                await exporter.ExportToCsvAsync(testDate, testDate, csvPath);

                if (!File.Exists(csvPath))
                    throw new Exception("CSV file was not created");

                var csvContent = await Task.Run(() => File.ReadAllText(csvPath));
                if (!csvContent.Contains("123456789"))
                    throw new Exception("CSV content is incorrect");

                Console.WriteLine("  ✅ ItemHistoryExporter test passed");
            }
            finally
            {
                // Cleanup
                if (Directory.Exists(testPath))
                    Directory.Delete(testPath, true);
            }
        }

        private static async Task TestDataListMapper()
        {
            Console.WriteLine("Testing DataListMapper...");

            // Test outcome determination
            var outcome1 = DataListMapper.DetermineOutcome(false, null);
            if (outcome1 != "filtered_out")
                throw new Exception($"Expected 'filtered_out', got '{outcome1}'");

            var outcome2 = DataListMapper.DetermineOutcome(true, null);
            if (outcome2 != "no_action")
                throw new Exception($"Expected 'no_action', got '{outcome2}'");

            // Test successful purchase outcome
            var successResult = PurchaseExecutionResult.CreateSuccess("Purchase successful", 2);
            var outcome3 = DataListMapper.DetermineOutcome(true, successResult);
            if (outcome3 != "purchased")
                throw new Exception($"Expected 'purchased', got '{outcome3}'");

            // Test quantity_fulfilled outcome - "Required quantity already reached"
            var quantityReachedResult = PurchaseExecutionResult.CreateSkipped("Required quantity already reached");
            var outcome4 = DataListMapper.DetermineOutcome(true, quantityReachedResult);
            if (outcome4 != "quantity_fulfilled")
                throw new Exception($"Expected 'quantity_fulfilled', got '{outcome4}'");

            // Test quantity_fulfilled outcome - "Purchase requirement already fulfilled"
            var requirementFulfilledResult = PurchaseExecutionResult.CreateSkipped("Purchase requirement already fulfilled");
            var outcome5 = DataListMapper.DetermineOutcome(true, requirementFulfilledResult);
            if (outcome5 != "quantity_fulfilled")
                throw new Exception($"Expected 'quantity_fulfilled', got '{outcome5}'");

            // Test skipped outcome
            var skippedResult = PurchaseExecutionResult.CreateSkipped("Credit card payment is not enabled");
            var outcome6 = DataListMapper.DetermineOutcome(true, skippedResult);
            if (outcome6 != "skipped")
                throw new Exception($"Expected 'skipped', got '{outcome6}'");

            // Test purchase_failed outcome
            var failedResult = PurchaseExecutionResult.CreateFailure("Payment processing failed");
            var outcome7 = DataListMapper.DetermineOutcome(true, failedResult);
            if (outcome7 != "purchase_failed")
                throw new Exception($"Expected 'purchase_failed', got '{outcome7}'");

            // Test reason creation
            var reason1 = DataListMapper.CreateReason(false, null);
            if (!reason1.Contains("did not match"))
                throw new Exception("Reason for filtered out item is incorrect");

            var reason2 = DataListMapper.CreateReason(true, null);
            if (!reason2.Contains("No purchase attempt"))
                throw new Exception("Reason for no action is incorrect");

            // Test filter rule context creation
            var filterContext = DataListMapper.CreateFilterRuleContext("Test Filter", "Price <= 100", true, "Matched");
            if (filterContext.FilterAlias != "Test Filter")
                throw new Exception("Filter alias mismatch");
            if (!filterContext.Matched)
                throw new Exception("Filter matched should be true");

            Console.WriteLine("  ✅ DataListMapper test passed");

            await Task.CompletedTask; // Make method async
        }

        private static ItemProcessingContext CreateTestContext()
        {
            return new ItemProcessingContext
            {
                Timestamp = DateTime.UtcNow,
                Outcome = "purchased",
                Reason = "Test purchase successful",
                ItemData = new ItemHistoryData
                {
                    ItemId = "123456789",
                    Title = "Test iPhone Case",
                    CurrentPrice = 25.99m,
                    Condition = "New",
                    Seller = "test-seller",
                    ShippingCost = 5.99m,
                    Location = "United States",
                    QuantityAvailable = 5,
                    ListingType = "FixedPrice",
                    ItemUrl = "https://ebay.com/itm/123456789",
                    ImageUrl = "https://i.ebayimg.com/test.jpg"
                },
                KeywordState = new KeywordSnapshot
                {
                    KeywordId = "kw-test-123",
                    Alias = "iPhone Cases",
                    JobId = "JOB-001",
                    Keywords = "iphone,case,protective",
                    RequiredQuantity = 10,
                    PurchasedQuantity = 6,
                    PriceMin = 15.0,
                    PriceMax = 40.0,
                    Condition = new[] { "New", "Used" },
                    Sellers = "seller1;seller2",
                    SellerType = "Include",
                    CapturedAt = DateTime.UtcNow
                },
                FilterRule = new FilterRuleContext
                {
                    FilterAlias = "Test Restock Filter",
                    Expression = "Price <= 40 AND Condition = 'New'",
                    Matched = true,
                    EvaluationResult = "Filter matched - proceeding with purchase",
                    EvaluatedAt = DateTime.UtcNow
                },
                TransactionResult = new TransactionResult
                {
                    Attempted = true,
                    Success = true,
                    PurchasePrice = 25.99m,
                    Quantity = 2,
                    CompletedAt = DateTime.UtcNow
                }
            };
        }
    }
}
