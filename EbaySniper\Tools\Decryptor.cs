﻿using System;
using System.IO;
using System.IO.Compression;
using System.Security.Cryptography;
using System.Text;
using Newtonsoft.Json.Linq;

public class Decryptor
{
    public static string DecryptMessage(string encryptedMessage)
    {
        try
        {
            // Base64 decode the input
            byte[] decodedMessageBytes = Convert.FromBase64String(encryptedMessage);

            // Reverse the byte array
            Array.Reverse(decodedMessageBytes);

            // Decompress the reversed byte array
            byte[] decompressedBytes = Decompress(decodedMessageBytes);

            // Convert decompressed bytes to string
            string decompressedMessage = Encoding.UTF8.GetString(decompressedBytes);

            // Parse JSON
            JObject json = JObject.Parse(decompressedMessage);
            string encryptedData = json["data"].ToString();
            string staticKey = json["static_key"].ToString();
            string aesIv = json["aes_iv"].ToString();

            // Base64 decode encrypted data and IV
            byte[] encryptedDataBytes = Convert.FromBase64String(encryptedData);
            byte[] aesIvBytes = Convert.FromBase64String(aesIv);
            byte[] staticKeyBytes = HexStringToByteArray(staticKey);

            // Decrypt the data
            string decryptedData = DecryptWithAes(encryptedDataBytes, staticKeyBytes, aesIvBytes);

            return decryptedData;
        }
        catch (Exception ex)
        {
            return $"Error during decryption: {ex.Message}";
        }
    }

    private static byte[] Decompress(byte[] data)
    {
        using (MemoryStream inputStream = new MemoryStream(data))
        using (GZipStream gzipStream = new GZipStream(inputStream, CompressionMode.Decompress))
        using (MemoryStream outputStream = new MemoryStream())
        {
            gzipStream.CopyTo(outputStream);
            return outputStream.ToArray();
        }
    }

    private static byte[] HexStringToByteArray(string hex)
    {
        int length = hex.Length;
        byte[] bytes = new byte[length / 2];
        for (int i = 0; i < length; i += 2)
        {
            bytes[i / 2] = Convert.ToByte(hex.Substring(i, 2), 16);
        }

        return bytes;
    }

    private static string DecryptWithAes(byte[] encryptedData, byte[] key, byte[] iv)
    {
        using (Aes aes = Aes.Create())
        {
            aes.Key = key;
            aes.IV = iv;
            aes.Mode = CipherMode.CBC;
            aes.Padding = PaddingMode.PKCS7;

            using (ICryptoTransform decryptor = aes.CreateDecryptor(aes.Key, aes.IV))
            using (MemoryStream ms = new MemoryStream(encryptedData))
            using (CryptoStream cs = new CryptoStream(ms, decryptor, CryptoStreamMode.Read))
            using (StreamReader sr = new StreamReader(cs))
            {
                return sr.ReadToEnd();
            }
        }
    }
}
