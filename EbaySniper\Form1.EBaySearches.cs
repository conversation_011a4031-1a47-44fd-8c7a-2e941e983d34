﻿using System;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using DevExpress.XtraTreeList.Nodes;
using uBuyFirst.Filters;
using uBuyFirst.Other;
using uBuyFirst.Prefs;
using uBuyFirst.Properties;
using uBuyFirst.SearchTerms;
using uBuyFirst.SubSearch;
using uBuyFirst.Tools;
using uBuyFirst.Views;

namespace uBuyFirst
{
    public partial class Form1
    {
        #region eBay Search Panel

        private void SetTreeListCheckboxesState()
        {
            for (var i = 0; i < treeList1.Nodes.Count; i++)
            {
                SetNodeChecked(treeList1.Nodes[i]);
                for (var j = 0; j < treeList1.Nodes[i].Nodes.Count; j++)
                {
                    SetNodeChecked(treeList1.Nodes[i].Nodes[j]);
                }
            }
        }

        private void SetNodeChecked(TreeListNode node)
        {
            try
            {
                if (node.Level == 0)
                {
                    if (treeList1.GetDataRecordByNode(node) is Keyword2Find kw)
                        node.CheckState = kw.KeywordEnabled;
                }
                else
                {
                    if (treeList1.GetDataRecordByNode(node) is ChildTerm childTerm)
                        node.Checked = childTerm.Enabled;
                }
            }
            catch
            {
                // ignored
            }
        }

        private void btnRemoveSearch_Click(object sender, EventArgs e)
        {
            RemoveSearchItem();
        }

        private void RemoveSearchItem()
        {
            if (treeList1.Selection.Count == 0)
                return;

            var result = XtraMessageBox.Show(defaultLookAndFeel1.LookAndFeel, this, "Total selected items: " + treeList1.Selection.Count + "\nRemove?", "", MessageBoxButtons.YesNo,
                MessageBoxIcon.Question, MessageBoxDefaultButton.Button1);
            //var result = MessageBox.Show("Total selected items: " + treeList1.Selection.Count + "\nRemove?", "",
            //MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1);
            if (result == DialogResult.Yes)
                treeList1.DeleteSelectedNodes();
            SaveSettings();
        }

        private void btnNewChildTerm_Click(object sender, EventArgs e)
        {
            NewChildTerm();
        }

        private void NewChildTerm()
        {
            if (treeList1.FocusedNode != null)
            {
                Keyword2Find? parentSearchTerm;

                if (treeList1.FocusedNode.Level == 0)
                {
                    parentSearchTerm = treeList1.GetDataRecordByNode(treeList1.FocusedNode) as Keyword2Find;
                }
                else
                {
                    parentSearchTerm = treeList1.GetDataRecordByNode(treeList1.FocusedNode.ParentNode) as Keyword2Find;
                }

                if (parentSearchTerm != null)
                {
                    if (parentSearchTerm.ChildrenCore.Count >= LicenseUtility.CurrentLimits.SearchTermsCount)
                    {
                        MessageBox.Show(string.Format(En_US.Form1_SearchKeywordEditCreate_Your_current_subscription_plan_doesn_t_allow_more_than__0__search_terms__Please_upgrade_,
                            LicenseUtility.CurrentLimits.SearchTermsCount));

                        return;
                    }


                    var term = new ChildTerm(parentSearchTerm, "New Sub Search")
                    {
                        PriceMin = parentSearchTerm.PriceMin,
                        PriceMax = parentSearchTerm.PriceMax
                    };


                    term.SubSearch = new XFilterClassChild
                    {
                        Action = "Keep rows",
                        Alias = term.Alias,
                        Enabled = false
                    };

                    treeList1.BeginUpdate();
                    treeList1.RefreshDataSource();

                    treeList1.FocusedNode.Expanded = true;
                    treeList1.SetFocusedNode(treeList1.FocusedNode.Level == 1 ? treeList1.FocusedNode.ParentNode.LastNode : treeList1.FocusedNode.LastNode);
                    UserSettings.CanShowEbaySearchEditor = true;
                    treeList1.EndUpdate();
                    treeList1.FocusedColumn = treeList1.Columns.ColumnByName("cKeywords");
                    treeList1.ShowEditor();
                }
            }
        }

        private void NewEbaySearch(string alias, string keyword = "", string categoryId = "")
        {
            if (_ebaySearches.ChildrenCore.Count >= LicenseUtility?.CurrentLimits.SearchTermsCount)
            {
                XtraMessageBox.Show(string.Format(En_US.Form1_SearchKeywordEditCreate_Your_current_subscription_plan_doesn_t_allow_more_than__0__search_terms__Please_upgrade_,
                    LicenseUtility.CurrentLimits.SearchTermsCount));

                return;
            }

            var uniqueAliasesDict = Helpers.CountStrings(_ebaySearches.ChildrenCore.Select(k => k.Alias).ToList());
            alias = Helpers.MakeUniqAliasOnAdd(alias, uniqueAliasesDict);
            var newKw = new Keyword2Find
            {
                Alias = alias,
                Categories4Api = categoryId,
                PriceMin = 1,
                PriceMax = 10000,
                SellerType = "",
                Frequency = new TimeSpan(),
                Threads = 1,
                ViewName = "Results"
            };
            if (!ResultsView.ViewsDict.ContainsKey("Results"))
                ResultsView.CreateView("Results");

            newKw.GridControl = ResultsView.ViewsDict["Results"];

            if (!string.IsNullOrEmpty(keyword))
            {
                newKw.Kws = keyword;
                newKw.LocatedIn = "US";
                newKw.AvailableTo = "US";
                newKw.Zip = "90210";
                newKw.KeywordEnabled = CheckState.Checked;
            }

            if (treeList1.GetDataRecordByNode(treeList1.Nodes.LastNode) is Keyword2Find lastQuery)
            {
                newKw.LocatedIn = lastQuery.LocatedIn;
                newKw.AvailableTo = lastQuery.AvailableTo;
                newKw.Zip = lastQuery.Zip;
            }

            newKw.ParentCore = _ebaySearches;

            treeList1.RefreshDataSource();
            for (var i = 0; i < treeList1.Nodes.Count; i++)
            {
                SetNodeChecked(treeList1.Nodes[i]);
            }

            UserSettings.CanShowEbaySearchEditor = true;

            while (treeList1.Selection.Count > 0)
            {
                treeList1.Selection.First().Selected = false;
            }

            var lastId = 0;
            TreeListNode lastNode = null;
            foreach (TreeListNode node in treeList1.Nodes)
            {
                if (node.Id > lastId)
                {
                    lastId = node.Id;
                    lastNode = node;
                }
            }

            treeList1.FocusedNode = lastNode;
            if (lastNode != null)
                treeList1.SelectNode(lastNode);
            // treeList1.FocusedColumn = treeList1.Columns.ColumnByName("cAlias");
            //   treeList1.ShowEditor();

            treeList1.FocusedColumn = treeList1.Columns.ColumnByName("cKeywords");
            treeList1.ShowEditor();
            // treeList1.FocusedColumn = treeList1.Columns.ColumnByName("cAlias");
            //  treeList1.ShowEditor();
        }

        private void NewNodeCopy()
        {
            if (treeList1.FocusedNode != null && treeList1.FocusedNode.Level == 0)
            {
                if (_ebaySearches.ChildrenCore.Count >= LicenseUtility.CurrentLimits.SearchTermsCount)
                {
                    XtraMessageBox.Show(string.Format(En_US.Form1_SearchKeywordEditCreate_Your_current_subscription_plan_doesn_t_allow_more_than__0__search_terms__Please_upgrade_,
                        LicenseUtility.CurrentLimits.SearchTermsCount));

                    return;
                }

                if (treeList1.GetDataRecordByNode(treeList1.FocusedNode) is Keyword2Find kw)
                {
                    var kw2 = new Keyword2Find();
                    var uniqueAliasesDict = Helpers.CountStrings(_ebaySearches.ChildrenCore.Select(k => k.Alias).ToList());
                    kw2.Alias = Helpers.MakeUniqAliasOnAdd(kw.Alias, uniqueAliasesDict);
                    kw2.AvailableTo = kw.AvailableTo;
                    kw2.Condition = kw.Condition;
                    kw2.EBaySite = kw.EBaySite;
                    kw2.EbaySiteName = kw.EbaySiteName;
                    kw2.Frequency = kw.Frequency;
                    kw2.IgnorePostalCodeError = kw.IgnorePostalCodeError;
                    kw2.Kws = kw.Kws;
                    kw2.Categories4Api = kw.Categories4Api;
                    kw2.LocatedIn = kw.LocatedIn;
                    kw2.PriceMax = kw.PriceMax;
                    kw2.PriceMin = kw.PriceMin;
                    kw2.SearchInDescription = kw.SearchInDescription;
                    kw2.SellerType = kw.SellerType;
                    kw2.Sellers = kw.Sellers;
                    kw2.Threads = kw.Threads;
                    kw2.Zip = kw.Zip;
                    kw2.ViewName = "Results";
                    kw2.GridControl = kw.GridControl;
                    kw2.ParentCore = _ebaySearches;
                }
            }
            else
            {
                var termSource = treeList1.GetDataRecordByNode(treeList1.FocusedNode) as ChildTerm;
                var parentSource = termSource?.GetParent();
                if (parentSource != null)
                {
                    if (parentSource.ChildrenCore.Count >= LicenseUtility.CurrentLimits.SearchTermsCount)
                    {
                        XtraMessageBox.Show(string.Format(En_US.Form1_SearchKeywordEditCreate_Your_current_subscription_plan_doesn_t_allow_more_than__0__search_terms__Please_upgrade_,
                            LicenseUtility.CurrentLimits.SearchTermsCount));

                        return;
                    }

                    var term = new ChildTerm(parentSource, termSource.Alias);
                    term.PriceMin = termSource.PriceMin;
                    term.PriceMax = termSource.PriceMax;
                    term.SubSearch = new XFilterClassChild
                    {
                        Action = "Keep rows",
                        Alias = termSource.SubSearch.Alias,
                        Enabled = termSource.SubSearch.Enabled,
                        FilterCriteria = termSource.SubSearch.FilterCriteria
                    };
                    term.Condition = termSource.Condition;
                    var uniqueAliasesDict = Helpers.CountStrings(parentSource.ChildrenCore.Select(subSearch => subSearch.Alias).ToList());
                    term.Alias = Helpers.MakeUniqAliasOnAdd(termSource.Alias, uniqueAliasesDict);

                    term.CategoryIDs = termSource.CategoryIDs;
                    term.Keywords = termSource.Keywords;
                }

                treeList1.RefreshDataSource();
                treeList1.FocusedColumn = treeList1.Columns.ColumnByName("cAlias");
            }

            treeList1.FireChanged();
            treeList1.Refresh();
            treeList1.RefreshDataSource();
            for (var i = 0; i < treeList1.Nodes.Count; i++)
            {
                SetNodeChecked(treeList1.Nodes[i]);
            }
        }

        private void btnExportSearches_Click(object sender, EventArgs e)
        {
            SearchTermManager.ExportSearchesToFile(_ebaySearches.ChildrenCore);
        }

        private void btnImportKeywords_Click(object sender, EventArgs e)
        {
            var filePath = AskUserForFileLocation();
            ImportKeywordsFromFile(filePath);
        }

        public void ImportKeywordsFromFile(string fileLocation)
        {
            if (string.IsNullOrEmpty(fileLocation))
                return;

            treeList1.BeginUpdate();

            SearchTermManager.ImportSearchTermsFromFile(fileLocation, _ebaySearches);

            treeList1.EndUpdate();

            treeList1.RefreshDataSource();
            for (var i = 0; i < treeList1.Nodes.Count; i++)
            {
                SetNodeChecked(treeList1.Nodes[i]);
                for (var j = 0; j < treeList1.Nodes[i].Nodes.Count; j++)
                {
                    SetNodeChecked(treeList1.Nodes[i].Nodes[j]);
                }
            }
            SaveSettings();
        }

        private string AskUserForFileLocation()
        {
            openFileDialog1.Filter = @"Comma separated values (*.csv)|*.csv";
            openFileDialog1.InitialDirectory = Folders.Logs;
            openFileDialog1.FileName = "";
            var fileLocation = "";
            if (openFileDialog1.ShowDialog() == DialogResult.OK)
            {
                fileLocation = openFileDialog1.FileName;
            }

            return fileLocation;
        }

        #endregion
    }
}
