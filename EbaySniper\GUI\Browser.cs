﻿using System.Diagnostics;
using System.IO;
using uBuyFirst.Data;
using uBuyFirst.Stats;
using uBuyFirst.Tools;

namespace uBuyFirst.GUI
{
    internal static class Browser
    {
        public static void OpenAffiliateLink(DataList d)
        {
            LaunchBrowser(d.EbayAccount?.<PERSON>rowser<PERSON>ath, d.GetAffiliateLink());
            Pixel.Track(Pixel.EventType.Browser, d.ItemID);
            Stat.OpenToBrowserCounter++;
        }
        public static void OpenAffiliateLinkVia_uBuyFirstRedirect(DataList d)
        {
            LaunchBrowser(d.EbayAccount?.<PERSON><PERSON>er<PERSON>ath, d.GetAffiliateLinkFor_uBuyFirstRedirect());
            Pixel.Track(Pixel.EventType.Browser, d.ItemID);
            Stat.OpenToBrowserCounter++;
        }

        public static void OpenCheckoutLink(DataList d)
        {
            LaunchBrowser(d.EbayAccount?.<PERSON><PERSON><PERSON><PERSON><PERSON>, d.GetCheckoutLink());
            Pixel.Track(Pixel.EventType.Browser, d.ItemID);
            Stat.OpenToBrowserCounter++;
        }

        public static void LaunchBrowser(string browserPath, string url)
            {
            if (!string.IsNullOrEmpty(browserPath) && File.Exists(browserPath))
                Process.Start(browserPath, url);
            else
                Process.Start(url);
        }
    }
}
