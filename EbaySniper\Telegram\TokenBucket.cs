﻿using System;
using System.Diagnostics;
using System.Threading;

namespace uBuyFirst.Telegram
{
    public class TokenBucket
    {
        private readonly int _bucketCapacity;   // Maximum capacity of the bucket
        private int _bucketContent;             // Current number of tokens in the bucket
        private readonly int _refillRate;       // Number of tokens to add per interval
        private readonly Timer _refillTimer;             // Timer to handle token refilling

        private readonly object _lock = new(); // Lock for thread safety

        public TokenBucket(int bucketCapacity, int refillRate, TimeSpan refillInterval)
        {
            _bucketCapacity = bucketCapacity;
            _refillRate = refillRate;
            _bucketContent = bucketCapacity; // Initially, fill the bucket

            // Start the timer to refill tokens at the defined interval
            _refillTimer = new Timer(Refill, null, TimeSpan.Zero, refillInterval);
        }

        // Method for clients to consume tokens (e.g., sending a message)

        public bool TryConsumeTokens(int amount)
        {
            lock (_lock)
            {
                //Debug.WriteLine($"Bucket Before {_bucketContent}/{_bucketCapacity}");
                if (_bucketContent >= amount)
                {
                    _bucketContent -= amount;
                    //Debug.WriteLine($"Bucket After {_bucketContent}/{_bucketCapacity}");
                    return true; // Successfully consumed a token
                }
                return false; // No tokens left to consume
            }
        }

        // Private method to refill tokens at the defined interval
        private void Refill(object state)
        {
            lock (_lock)
            {
                // Add tokens up to the maximum bucket capacity
                _bucketContent = Math.Min(_bucketCapacity, _bucketContent + _refillRate);
            }
        }

        // Stop the timer when needed (e.g., on application shutdown)
        public void StopRefilling()
        {
            _refillTimer.Change(Timeout.Infinite, 0);
            _refillTimer.Dispose();
        }

        // Optional method to get the current number of tokens (for monitoring/debugging)
        public int GetCurrentTokens()
        {
            lock (_lock)
            {
                return _bucketContent;
            }
        }
    }
}
