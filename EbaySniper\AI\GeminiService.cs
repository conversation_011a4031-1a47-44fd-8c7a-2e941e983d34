﻿/*
using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Mscc.GenerativeAI;
using uBuyFirst.Images;
using uBuyFirst.Other;

namespace uBuyFirst.AI
{
    public class GeminiService
    {
        private readonly GenerativeModel _model;
        private const string DefaultApiKey = "AIzaSyBF_DsEKArCmnOZL3Rl_F_M2x-381ceY9c";

        public GeminiService() : this(DefaultApiKey)
        {
        }

        public GeminiService(string apiKey)
        {
            // Initialize Google AI with API key
            var googleAI = new GoogleAI(apiKey: apiKey);

            // Create a generative model instance
            _model = googleAI.GenerativeModel(model: Model.Gemini20FlashLite);
        }

        public async Task<string> SendHelloMessage()
        {
            try
            {
                // Generate content using the model
                var response = await _model.GenerateContent("hello");

                // Extract and return the text response
                return response.Text;
            }
            catch (Exception ex)
            {
                return $"Error calling Gemini API: {ex.Message}";
            }
        }

        public async Task<string> AnalyzeMultipleImages(string prompt, List<string> imageUrls)
        {
            try
            {
                // Create a request with the prompt
                var request = new GenerateContentRequest(prompt);

                // Add all images to the request
                foreach (var imageUrl in imageUrls)
                {
                    var imagePath = await ImageTools.GetImageFromDiskOrInternet(imageUrl);
                    var base64Image = Convert.ToBase64String(File.ReadAllBytes(imagePath));
                    await request.AddMedia(base64Image, "image/jpeg");
                }

                // Generate content using the model
                var response = await _model.GenerateContent(request);

                // Return analysis
                return response.Text;
            }
            catch (Exception ex)
            {
                return $"Error analyzing images with Gemini API: {ex.Message}";
            }
        }

        public async Task<string> AnalyzeTestStripListing(string title, string conditionDescription, string description, List<string> pictures)
        {
            try
            {
                var folderPath = Folders.SkuManagerScripts;
                var promptsFolder = Path.Combine(folderPath, "_prompts");

                var promptFilePath = promptsFolder + "\\system_message.txt";
                if (!System.IO.File.Exists(promptFilePath))
                    return "";

                // Read system prompt from file
                var systemPrompt = System.IO.File.ReadAllText(promptFilePath);

                // Create prompt combining system prompt and listing data
                var prompt = $"{systemPrompt}\n\nAnalyze this listing:\nTitle: {title}\nCondition: {conditionDescription}\nDescription: {description}";

                // Use AnalyzeMultipleImages to process both text and images
                return await AnalyzeMultipleImages(prompt, pictures);
            }
            catch (Exception ex)
            {
                return $"Error analyzing test strip listing with Gemini API: {ex.Message}";
            }
        }

        public async Task<string> AnalyzeProductTitle(string title)
        {
            try
            {
                // Create prompt for title analysis
                var prompt = $"Analyze this product title and provide key information about the item: {title}";

                // Generate content using the model
                var response = await _model.GenerateContent(prompt);

                // Return analysis
                return response.Text;
            }
            catch (Exception ex)
            {
                return $"Error analyzing title with Gemini API: {ex.Message}";
            }
        }
    }
}
*/
