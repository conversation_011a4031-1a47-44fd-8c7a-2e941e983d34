﻿using System;
using System.Diagnostics;
using System.Linq;
using eBay.Service.Core.Soap;

namespace uBuyFirst.Pricing
{
    static class ShippingParserTradingAPI
    {
        public static void GetBareSingleShippingPrice(ShippingDetailsType shippingDetails, ItemShipping itemShipping, bool isInternational)
        {
            var internationalShipping = shippingDetails.InternationalShippingServiceOption.ToArray().ToList().FirstOrDefault();
            var nativeShipping = shippingDetails.ShippingServiceOptions.ToArray().ToList().FirstOrDefault();

            if (isInternational)
            {
                if (internationalShipping?.ShippingServiceCost == null)
                {
                    itemShipping.ShippingStatusTradingAPI = ItemShipping.ParsingStatus.Fail;
                }
                else
                {
                    itemShipping.BareSingleShippingPrice = new CurrencyAmount(internationalShipping?.ShippingServiceCost.Value ?? 0, internationalShipping?.ShippingServiceCost.currencyID.ToString());
                    itemShipping.ShippingStatusTradingAPI = ItemShipping.ParsingStatus.Success;
                }
            }
            else
            {
                if (nativeShipping?.ShippingServiceCost == null)
                {
                    itemShipping.ShippingStatusTradingAPI = ItemShipping.ParsingStatus.Fail;
                }
                else
                {
                    itemShipping.BareSingleShippingPrice = new CurrencyAmount(nativeShipping.ShippingServiceCost.Value, nativeShipping.ShippingServiceCost.currencyID.ToString());
                    itemShipping.ShippingStatusTradingAPI = ItemShipping.ParsingStatus.Success;
                }
            }
        }

        public static double GetPackagingCost(CalculatedShippingRateType calculatedShippingRate, bool isInternational)
        {
            var packageCost = 0.0;
            if (isInternational)
            {
                if (calculatedShippingRate?.InternationalPackagingHandlingCosts != null)
                    packageCost = calculatedShippingRate.InternationalPackagingHandlingCosts.Value;
            }
            else
            {
                if (calculatedShippingRate?.PackagingHandlingCosts != null)
                    packageCost = calculatedShippingRate.PackagingHandlingCosts.Value;
            }

            return packageCost;
        }

        public static CurrencyAmount GetAdditionalItemShipCost(ShippingDetailsType shippingDetails, bool isInternational)
        {
            try
            {
                CurrencyAmount price;
                if (isInternational)
                {
                    var option = shippingDetails.InternationalShippingServiceOption?.ToArray().ToList().FirstOrDefault();
                    price = new CurrencyAmount(option?.ShippingServiceAdditionalCost?.Value ?? 0, option?.ShippingServiceAdditionalCost?.currencyID.ToString() ?? "");
                }
                else
                {
                    var option = shippingDetails.InternationalShippingServiceOption?.ToArray().ToList().FirstOrDefault();
                    price = new CurrencyAmount(option?.ShippingServiceAdditionalCost?.Value ?? 0, option?.ShippingServiceAdditionalCost?.currencyID.ToString() ?? "");
                }

                if (price != null)
                    return price;
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
            }

            return new CurrencyAmount(0, "USD");
        }

        public static double GetImportCost(ShippingDetailsType shippingDetails, bool isInternational)
        {
            var option = shippingDetails.InternationalShippingServiceOption?.ToArray().ToList().FirstOrDefault();
            var importCost = 0.0;
            if (!isInternational)
                return importCost;

            var importCharge = option?.ImportCharge?.Value;
            if (importCharge != null)
                importCost = (double)importCharge;

            return importCost;
        }

        public static double GetInsuranceCost(ShippingDetailsType shippingDetails, bool isInternational)
        {
            try
            {
                double price = 0;
                if (isInternational)
                {
                    var option = shippingDetails.InternationalShippingServiceOption?.ToArray().ToList().FirstOrDefault();
                    price = option?.ShippingInsuranceCost?.Value ?? 0;
                }
                else
                {
                    var option = shippingDetails.InternationalShippingServiceOption?.ToArray().ToList().FirstOrDefault();
                    price = option?.ShippingInsuranceCost?.Value ?? 0;
                }

                return price;
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
            }

            return 0;
        }

        public static CurrencyAmount GetFullSingleItemShippingPrice(ItemShipping itemShipping)
        {
            var packagingCost = itemShipping.PackageHandlingCost;
            var importCost = itemShipping.ImportCost;
            var insuranceCost = itemShipping.ShippingInsuranceCost;
            var surcharge = 0;
            itemShipping.FullSingleShippingPrice.Value = itemShipping.BareSingleShippingPrice.Value + packagingCost + importCost + insuranceCost + surcharge;
            return itemShipping.FullSingleShippingPrice;
        }
    }
}