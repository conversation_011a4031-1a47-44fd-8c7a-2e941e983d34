﻿using System.ComponentModel;
using DevExpress.XtraTreeList;

namespace uBuyFirst.GUI
{
    public class Categories : BindingList<Category>, TreeList.IVirtualTreeListData
    {
        void TreeList.IVirtualTreeListData.VirtualTreeGetChildNodes(VirtualTreeGetChildNodesInfo info)
        {
            Category obj = info.Node as Category;
            info.Children = obj?.Categories;
        }

        void TreeList.IVirtualTreeListData.VirtualTreeGetCellValue(VirtualTreeGetCellValueInfo info)
        {
            var category = info.Node as Category;
            switch (info.Column?.FieldName)
            {
                case "Name":
                    info.CellData = category?.Name;
                    break;
                case "CategoryID":
                    info.CellData = category?.CategoryID;
                    break;
            }
        }

        void TreeList.IVirtualTreeListData.VirtualTreeSetCellValue(VirtualTreeSetCellValueInfo info)
        {
            var node = info.Node as Category;
            switch (info.Column?.FieldName)
            {
                case "Name":
                    if (node != null) node.Name = info.NewCellData as string;
                    break;
                case "CategoryID":
                    if (node != null) node.CategoryID = (string) info.NewCellData;
                    break;
            }
        }
    }
}