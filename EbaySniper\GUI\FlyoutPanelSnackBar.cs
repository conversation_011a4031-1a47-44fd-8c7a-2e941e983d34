﻿using System;
using System.Drawing;
using System.Windows.Forms;
using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls; // Added for ButtonStyle

namespace uBuyFirst.GUI
{
    public enum FlyoutStyle
    {
        Information,
        Success,
        Failure
    }

    public class FlyoutPanelSnackBar : IDisposable
    {
        public FlyoutPanel Panel;
        public FlyoutPanelControl PanelControl;

        private LabelControl messageLabel;
        private PictureEdit iconPictureEdit;
        private SimpleButton okButton;
        private TableLayoutPanel layoutPanel;
        private System.Windows.Forms.Timer dismissTimer;
        private Control parentControl;

        public FlyoutPanelSnackBar(Control parent)
        {
            this.parentControl = parent;

            // Instantiate core controls
            this.Panel = new DevExpress.Utils.FlyoutPanel();
            this.PanelControl = new DevExpress.Utils.FlyoutPanelControl();
            this.layoutPanel = new TableLayoutPanel();
            this.iconPictureEdit = new PictureEdit();
            this.messageLabel = new LabelControl();
            this.okButton = new SimpleButton();
            this.dismissTimer = new System.Windows.Forms.Timer();

            // --- Begin Configuration ---

            // Configure Layout Panel
            this.layoutPanel.SuspendLayout();
            this.layoutPanel.Dock = DockStyle.Fill;
            //this.layoutPanel.BackColor = Color.Transparent; // Make transparent
            this.layoutPanel.ColumnCount = 3;
            this.layoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Absolute, 0F)); // Icon column starts hidden
            this.layoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.Percent, 100F)); // Label column
            this.layoutPanel.ColumnStyles.Add(new ColumnStyle(SizeType.AutoSize)); // Button column
            this.layoutPanel.RowCount = 1;
            this.layoutPanel.RowStyles.Add(new RowStyle(SizeType.Percent, 100F));
            this.layoutPanel.Name = "layoutPanel";
            this.layoutPanel.Padding = new Padding(5); // Add padding inside layout

            // Configure Icon PictureEdit
            ((System.ComponentModel.ISupportInitialize)(this.iconPictureEdit.Properties)).BeginInit();
            this.iconPictureEdit.Properties.ShowCameraMenuItem = DevExpress.XtraEditors.Controls.CameraMenuItemVisibility.Auto;
            this.iconPictureEdit.Properties.SizeMode = DevExpress.XtraEditors.Controls.PictureSizeMode.Squeeze;
            this.iconPictureEdit.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.NoBorder;
            this.iconPictureEdit.Properties.AllowFocused = false;
            this.iconPictureEdit.Properties.ReadOnly = true;
            this.iconPictureEdit.Dock = DockStyle.Fill;
            this.iconPictureEdit.Name = "iconPictureEdit";
            this.iconPictureEdit.Margin = new Padding(3);
            this.iconPictureEdit.Visible = false; // Start hidden
            ((System.ComponentModel.ISupportInitialize)(this.iconPictureEdit.Properties)).EndInit();

            // Configure Message Label
            //this.messageLabel.Appearance.ForeColor = SnackbarForeColor; // Set text color
            this.messageLabel.Appearance.TextOptions.WordWrap = DevExpress.Utils.WordWrap.Wrap;
            this.messageLabel.AutoSizeMode = LabelAutoSizeMode.Vertical;
            this.messageLabel.Font = new Font(this.messageLabel.Appearance.Font.FontFamily, this.okButton.Appearance.Font.Size + 1, FontStyle.Bold);
            this.messageLabel.Dock = DockStyle.Fill;
            this.messageLabel.Name = "messageLabel";
            // Padding removed, handled by layoutPanel now

            // Configure OK Button
            this.okButton.Text = "OK";
            this.okButton.Name = "okButton";
            this.okButton.Appearance.Font = new Font(this.okButton.Appearance.Font.FontFamily, this.okButton.Appearance.Font.Size + 1, FontStyle.Bold);
            //this.okButton.Appearance.ForeColor = ActionButtonColor; // Set accent color
            this.okButton.Anchor = AnchorStyles.Right | AnchorStyles.Top | AnchorStyles.Bottom;
            this.okButton.AutoSize = false;
            var preferredSize = this.okButton.PreferredSize;
            this.okButton.Size = new Size(preferredSize.Width * 2, preferredSize.Height * 2);
            this.okButton.Margin = new Padding(5, 5, 5, 5); // Standard margin
            this.okButton.Padding = new Padding(10, 0, 10, 0);
            this.okButton.ButtonStyle = BorderStyles.Simple; // Use a flatter style
            this.okButton.Click += OkButton_Click;

            // Add controls to Layout Panel
            this.layoutPanel.Controls.Add(this.iconPictureEdit, 0, 0);
            this.layoutPanel.Controls.Add(this.messageLabel, 1, 0);
            this.layoutPanel.Controls.Add(this.okButton, 2, 0);
            this.layoutPanel.ResumeLayout(false);
            this.layoutPanel.PerformLayout();


            // Configure PanelControl
            ((System.ComponentModel.ISupportInitialize)(this.PanelControl)).BeginInit();
            this.PanelControl.SuspendLayout();
            //this.PanelControl.Appearance.BackColor = SnackbarBackColor; // Set background here
            this.PanelControl.Controls.Add(this.layoutPanel);
            this.PanelControl.Dock = System.Windows.Forms.DockStyle.Fill;
            this.PanelControl.FlyoutPanel = this.Panel;
            this.PanelControl.Location = new System.Drawing.Point(0, 0);
            this.PanelControl.Name = "flyoutPanelManagerControl";
            //this.PanelControl.Size = new System.Drawing.Size(340, 40);
            this.PanelControl.TabIndex = 0;
            this.PanelControl.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.PanelControl)).EndInit();

            // Configure Panel
            ((System.ComponentModel.ISupportInitialize)(this.Panel)).BeginInit();
            this.Panel.SuspendLayout();
            this.Panel.Appearance.BackColor = Color.Transparent; // Make outer panel transparent
            this.Panel.Appearance.Options.UseBackColor = true;
            this.Panel.Controls.Add(this.PanelControl);
            this.Panel.Name = "flyoutPanelManager";
            this.Panel.OptionsButtonPanel.ShowButtonPanel = false;
            this.Panel.Size = new System.Drawing.Size(350, 45);
            this.Panel.Padding = new System.Windows.Forms.Padding(5); // Padding around the PanelControl
            //this.Panel.AutoSize = true; // Let Panel size to PanelControl + Padding
            //this.Panel.AutoSizeMode = AutoSizeMode.GrowAndShrink;
            //this.Panel.MaximumSize = new Size(600, 0); // Set a max width (0 means no height limit)
            this.Panel.TabIndex = 105;
            this.Panel.Options.AnimationType = DevExpress.Utils.Win.PopupToolWindowAnimation.Fade;
            this.Panel.Options.CloseOnOuterClick = false;
            this.Panel.Options.AnchorType = DevExpress.Utils.Win.PopupToolWindowAnchor.Bottom;

            // Timer Configuration
            this.dismissTimer.Interval = (int)TimeSpan.FromSeconds(15).TotalMilliseconds;
            this.dismissTimer.Tick += DismissTimer_Tick;

            this.Panel.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.Panel)).EndInit();

            // --- End Configuration ---

            parentControl?.Controls.Add(this.Panel);
            this.Panel.BringToFront();
            this.Panel.Hide();
        }

        public void ShowPopup(Control ownerControl, string message, FlyoutStyle style = FlyoutStyle.Information)
        {
            if (Panel.IsDisposed) return;

            Panel.HidePopup();

            messageLabel.Text = message;
            Panel.OwnerControl = ownerControl ?? this.parentControl;

            // --- Icon Logic Temporarily Removed ---
            /*
            SvgImage svgImage = null;
            try
            {
                switch (style)
                {
                    case FlyoutStyle.Success:
                        // svgImage = DXImageHelper.GetSvgImage("Svg/Status/Completed.svg"); // Replace with correct loading method
                        break;
                    case FlyoutStyle.Failure:
                        // svgImage = DXImageHelper.GetSvgImage("Svg/Status/Error.svg"); // Replace with correct loading method
                        break;
                    case FlyoutStyle.Information:
                    default:
                        // svgImage = DXImageHelper.GetSvgImage("Svg/Status/Info.svg"); // Replace with correct loading method
                        break;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error loading SVG image for FlyoutPanelSnackBar: {ex.Message}");
                svgImage = null;
            }
            iconPictureEdit.EditValue = svgImage;
            */
            iconPictureEdit.Visible = false;
            layoutPanel.ColumnStyles[0].Width = 0F;
            switch (style)
            {
                case FlyoutStyle.Success:
                    okButton.Appearance.BackColor = Color.FromArgb(40, 167, 69); // Bootstrap Success Green
                    okButton.ForeColor = Color.White;
                    okButton.BackColor = Color.FromArgb(40, 167, 69);
                    messageLabel.ForeColor =Color.Black; // Darker success message color
                    PanelControl.Appearance.BackColor = Color.FromArgb(23, 200, 16); // Lighter green background
                    break;

                case FlyoutStyle.Information:
                    okButton.Appearance.BackColor = Color.FromArgb(0, 123, 255); // Bootstrap Primary Blue
                    okButton.ForeColor = Color.White;
                    okButton.BackColor = Color.FromArgb(0, 123, 255);
                    messageLabel.ForeColor = Color.FromArgb(0, 123, 255);
                    PanelControl.Appearance.BackColor = Color.FromArgb(217, 237, 247); // Lighter blue background
                    break;

                case FlyoutStyle.Failure:
                    okButton.Appearance.BackColor = Color.FromArgb(220, 53, 69); // Bootstrap Danger Red
                    okButton.ForeColor = Color.White;
                    okButton.BackColor = Color.FromArgb(220, 53, 69);
                    messageLabel.ForeColor = Color.FromArgb(220, 53, 69);
                    PanelControl.Appearance.BackColor = Color.FromArgb(248, 215, 218); // Lighter red background
                    break;

            }
            PanelControl.Appearance.Options.UseBackColor = true; // Ensure background color is used

            dismissTimer.Stop();

            dismissTimer.Start();


            Panel.ShowPopup(true);
        }

        public void ShowSuccess(Control owner, string msg) => ShowPopup(owner, msg, FlyoutStyle.Success);
        public void ShowInformation(Control owner, string msg) => ShowPopup(owner, msg, FlyoutStyle.Information);
        public void ShowFailure(Control owner, string msg) => ShowPopup(owner, msg, FlyoutStyle.Failure);

        private void DismissTimer_Tick(object sender, EventArgs e)
        {
            dismissTimer.Stop();
            if (!Panel.IsDisposed)
            {
                Panel.HidePopup();
            }
        }

        private void OkButton_Click(object sender, EventArgs e)
        {
            dismissTimer.Stop();
            if (!Panel.IsDisposed)
            {
                Panel.HidePopup();
            }
        }

        private bool disposedValue = false;
        protected virtual void Dispose(bool disposing)
        {
            if (!disposedValue)
            {
                if (disposing)
                {
                    dismissTimer?.Stop();
                    dismissTimer?.Dispose();
                    okButton?.Dispose();
                    iconPictureEdit?.Dispose();
                    messageLabel?.Dispose();
                    layoutPanel?.Dispose();
                    Panel?.Dispose();
                }
                disposedValue = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}
