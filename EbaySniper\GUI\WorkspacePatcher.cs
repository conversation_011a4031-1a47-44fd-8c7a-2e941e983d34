﻿using System;
using System.Diagnostics;
using System.Xml;

namespace uBuyFirst.GUI
{
    static class WorkspacePatcher
    {
        public static void PatchWorkspace(string workspacePath)
        {
            try
            {
                XmlDocument doc = new XmlDocument();
                doc.Load(workspacePath);
                RemoveKeywordsPanel(doc);
                //FixXtraParentID(doc);
                doc.Save(workspacePath);
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
            }
        }

        private static void FixXtraParentID(XmlDocument doc)
        {
            XmlNode node = doc.SelectSingleNode("//property[@name='#LayoutVersion' and text()='d3']");
            if (node != null)
            {
                return;
            }

            XmlNodeList nodes = doc.SelectNodes("//property[@name='XtraParentID']");
            if (nodes == null)
            {
                return;
            }

            for (int i = nodes.Count - 1; i >= 0; i--)
            {
                nodes[i].InnerText = "-10001";
            }
        }

        private static void RemoveKeywordsPanel(XmlDocument doc)
        {
            XmlNodeList nodes = doc.SelectNodes("//property[@name='Header' and text()='Keywords']");
            if (nodes == null)
            {
                return;
            }

            for (int i = nodes.Count - 1; i >= 0; i--)
            {
                nodes[i]?.ParentNode?.ParentNode?.RemoveChild(nodes[i].ParentNode);
            }
        }
    }
}