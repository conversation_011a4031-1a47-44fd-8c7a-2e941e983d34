﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using DalSoft.RestClient;

namespace uBuyFirst.Purchasing.BuyAPI
{
    public class OrderService
    {
        private readonly EBayRestClient _eBayRestClient;
        public OrderApi.CheckoutSessionResponse CheckoutResponse;

        public OrderService(string oAuthToken)
        {
            _eBayRestClient = new EBayRestClient(oAuthToken);
        }

        public async Task RunCheckoutRequest(string itemID, int quantity, OrderApi.ShippingAddress shippingAddress)
        {
            var checkoutRequest = new OrderApi.CreateSignInCheckoutSessionRequest();
            var lineItemInput = new OrderApi.LineItemInput();
            lineItemInput.itemId = "v1|" + itemID + "|0";
            lineItemInput.quantity = quantity;
            checkoutRequest.lineItemInputs = new List<OrderApi.LineItemInput>();
            checkoutRequest.lineItemInputs.Add(lineItemInput);
            if (shippingAddress == null)
                checkoutRequest.shippingAddress = new OrderApi.ShippingAddress
                {
                    recipient = "Frank Smith",
                    phoneNumber = "************",
                    addressLine1 = "3737 Any St",
                    city = "San Jose",
                    stateOrProvince = "CA",
                    postalCode = "95134",
                    country = "US"
                };
            else
            {
                checkoutRequest.shippingAddress = shippingAddress;
            }

            /*    checkoutRequest.creditCard = new OrderApi.CreditCard()
                {
                    accountHolderName = "Frank Smith",
                    brand = "Visa",
                    cardNumber = "****************",
                    cvvNumber = "222",
                    expireMonth = 10,
                    expireYear = 2020,
                    billingAddress = new OrderApi.BillingAddress()
                    {
                        addressLine1 = "Line 1",
                        city = "Los Angeles",
                        country = CountryCodeType.US.ToString(),
                        firstName = "Frank",
                        lastName = "Smith",
                        postalCode = "90001",
                        stateOrProvince = "Los Angeles"
                    }
                };*/
            CheckoutResponse = await _eBayRestClient.RunCheckoutRequest(checkoutRequest);
        }

        public async Task<OrderApi.PurchaseOrderSummary> PlaceOrder()
        {
            return await _eBayRestClient.PlaceOrderRequest(CheckoutResponse.checkoutSessionId);
        }

        public async Task<OrderApi.CheckoutSessionResponse> UpdateShippingOption(string lineItemId, string shippingOptionId)
        {
            var checkoutResponse = await _eBayRestClient.UpdateShippingOptionRequest(CheckoutResponse.checkoutSessionId, lineItemId, shippingOptionId);
            if (checkoutResponse != null)
                CheckoutResponse = checkoutResponse;
            return CheckoutResponse;
        }
    }

    class EBayRestClient
    {
        private readonly dynamic _client;

        public EBayRestClient(string oAuthToken)
        {
            //var restUrl = "https://apix.ebay.com/buy/order/v1";
            var restUrl = "https://api.sandbox.ebay.com/buy/order/v1";
            _client = new RestClient(restUrl, new Dictionary<string, string>
            {
                {"Authorization", "Bearer " + oAuthToken},
                {"Accept", "application/json"},
                {"Content-Type", "application/json"}
            });
        }

        public async Task<OrderApi.CheckoutSessionResponse> RunCheckoutRequest(OrderApi.CreateSignInCheckoutSessionRequest checkoutRequest)
        {
            OrderApi.CheckoutSessionResponse checkoutResponse = await _client.checkout_session.initiate.Post(checkoutRequest);
            return checkoutResponse;
        }

        public async Task<OrderApi.PurchaseOrderSummary> PlaceOrderRequest(string checkoutSessionId)
        {
            if (checkoutSessionId == null) return null;
            OrderApi.PurchaseOrderSummary placeOrderResponse = await _client.checkout_session.Resource(checkoutSessionId).place_order.Post(new
            {
            });
            return placeOrderResponse;
        }

        public async Task<OrderApi.CheckoutSessionResponse> UpdateShippingOptionRequest(string checkoutSession, string lineItemId, string optionId)
        {
            try
            {
                OrderApi.UpdateShippingOption updateShippingOption = new OrderApi.UpdateShippingOption();
                updateShippingOption.lineItemId = lineItemId;
                updateShippingOption.shippingOptionId = optionId;

                OrderApi.CheckoutSessionResponse placeOrderResponse = await _client.checkout_session.Resource(checkoutSession).update_shipping_option.Post(updateShippingOption);
                return placeOrderResponse;
            }
            catch (Exception)
            {
                // ignored
            }

            return null;
        }
    }
}
