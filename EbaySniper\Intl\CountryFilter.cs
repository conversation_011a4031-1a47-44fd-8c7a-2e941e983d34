﻿using System.Collections.Generic;
using System.Linq;

namespace uBuyFirst.Intl
{
    public static class CountryFilter
    {
        public static bool IsShipToCountryMatch(string userCountry, List<string> shipToLocations, List<string> excludeShipToLocation)
        {
            if ((shipToLocations.Contains("Worldwide") || shipToLocations.Contains("WORLDWIDE")) && !excludeShipToLocation.Contains(userCountry))
            {
                return true;
            }

            if (shipToLocations.Contains(userCountry))
                return true;

            return false;
        }

        public static HashSet<string> ConvertRegionsToCountryCodes(string[] includeRegions, string[] excludedRegions)
        {
            var doesNotShip = includeRegions.FirstOrDefault() != null && includeRegions.FirstOrDefault() == "None";
            if (doesNotShip)
            {
                return new HashSet<string>(includeRegions);
            }

            var includedMain = new HashSet<string>();
            var includedSecondary = new List<string>();
            foreach (string region in includeRegions)
            {
                if (region.Length == 2)
                    includedMain.Add(region);
                else
                    ConvertIncludedRegion(region, includedSecondary);
            }

            var excludeMain = new List<string>();
            var excludeSecondary = new HashSet<string>();
            foreach (string region in excludedRegions)
            {
                if (region.Length == 2)
                    excludeMain.Add(region);
                else
                    ConvertExcludedRegion(region, excludeSecondary);
            }

            excludeMain.ForEach(e => includedSecondary.Remove(e));
            foreach (var e in excludeSecondary)
            {
                includedSecondary.Remove(e);
            }
            includedMain.UnionWith(includedSecondary);

            return includedMain;
        }

        public static List<string> ReplaceRegionsWithCountries(List<string> shipToRegions, List<string> excludeShipToLocation)
        {
            var countryCodes = ConvertRegionsToCountryCodes(shipToRegions.ToArray(), excludeShipToLocation.ToArray());
            var countryCollection = new List<string>();
            countryCollection.AddRange(countryCodes.ToArray());
            return countryCollection.ToArray().ToList();
        }

        public static HashSet<string> ConvertExcludedRegionsToCountries(string[] excludeRegions)
        {
            var countries = new HashSet<string>();
            foreach (var regions in excludeRegions)
            {
                if (regions.Length == 2)
                {
                    countries.Add(regions);
                }
                else
                {
                    ConvertExcludedRegion(regions, countries);
                }
            }

            return countries;
        }

        //"AUSTRALIA","GREATER_CHINA", CHANNEL_ISLANDS?
        private static void ConvertExcludedRegion(string region, HashSet<string> countries)
        {
            switch (region.ToLower())
            {
                case "africa":
                    foreach (var country in Africa)
                        countries.Add(country);
                    break;
                case "americas":
                    foreach (var country in Americas)
                        countries.Add(country);
                    break;
                case "asia":
                    foreach (var country in Asia)
                        countries.Add(country);
                    break;
                case "central america and caribbean":
                case "central_america_and_caribbean":
                    foreach (var country in CentralAmericaandCaribbean)
                        countries.Add(country);
                    break;
                case "europe":
                    foreach (var country in EuropeExclude)
                        countries.Add(country);
                    break;
                case "europeanunion":
                case "european_union":
                    foreach (var country in EuropeanUnion)
                        countries.Add(country);
                    break;
                case "middle east":
                case "middle_east":
                    foreach (var country in MiddleEast)
                        countries.Add(country);
                    break;
                case "north america":
                case "north_america":
                    foreach (var country in NorthAmerica)
                        countries.Add(country);
                    break;
                case "oceania":
                    foreach (var country in Oceania)
                        countries.Add(country);
                    break;
                case "south america":
                case "south_america":
                    foreach (var country in SouthAmerica)
                        countries.Add(country);
                    break;
                case "southeast asia":
                case "southeast_asia":
                    foreach (var country in SoutheastAsia)
                        countries.Add(country);
                    break;
            }
        }

        private static void ConvertIncludedRegion(string region, List<string> countries)
        {
            switch (region.ToLower())
            {
                case "africa":
                    countries.AddRange(Africa);
                    break;
                case "americas":
                    countries.AddRange(Americas);
                    break;
                case "asia":
                    countries.AddRange(Asia);
                    break;
                case "central america and caribbean":
                case "central_america_and_caribbean":
                    countries.AddRange(CentralAmericaandCaribbean);
                    break;
                case "europe":
                    countries.AddRange(Europe);
                    break;
                case "europeanunion":
                case "european_union":
                    countries.AddRange(EuropeanUnion);
                    break;
                case "middle east":
                case "middle_east":
                    countries.AddRange(MiddleEast);
                    break;
                case "north america":
                case "north_america":
                    countries.AddRange(NorthAmerica);
                    break;
                case "oceania":
                    countries.AddRange(Oceania);
                    break;
                case "south america":
                case "south_america":
                    countries.AddRange(SouthAmerica);
                    break;
                case "southeast asia":
                case "southeast_asia":
                    countries.AddRange(SoutheastAsia);
                    break;
                case "rest of asia":
                    countries.AddRange(RestOfAsia);
                    break;
                default:
                    countries.Add(region);
                    break;
            }
        }

        private static readonly string[] Americas =
        {
                "AI", "AG", "AR", "AW", "BS", "BB", "BZ", "BM", "BO", "BR", "VG", "CA", "KY", "CL", "CO", "CR", "DM", "DO", "EC", "SV", "FK", "GF", "GL", "GD", "GP", "GT", "GY", "HT", "HN", "JM",
                "MQ", "MX", "MS", "AN", "NI", "PA", "PY", "PE", "PR", "KN", "LC", "PM", "VC", "SR", "TT", "TC", "US", "UY", "VE", "VI",
            };

        private static readonly string[] EuropeanUnion =
        {
                "AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GR", "HU", "IE", "IT", "LV", "LT", "LU", "MT", "NL", "PL", "PT", "RO", "SK", "SI", "ES", "SE", "GB",
            };

        private static readonly string[] Asia =
        {
                "AF", "AM", "AZ", "BH", "BD", "BT", "BN", "KH", "CN", "GE", "HK", "IN", "ID", "IQ", "IL", "JP", "JO", "KZ", "KR", "KW", "KG", "LA", "LB", "MO", "MY", "MV", "MN", "NP", "OM", "PK",
                "PH", "QA", "RU", "SA", "SG", "LK", "TW", "TJ", "TH", "TR", "TM", "AE", "UZ", "VN", "YE",
            };

        private static readonly string[] RestOfAsia = { "BD", "BN", "KH", "HK", "ID", "JP", "LA", "MO", "MY", "MN", "NP", "PH", "SG", "LK", "TW", "TH", "VN", };

        private static readonly string[] AsiaExclude =
        {
                "VN", "TW", "HK", "BN", "TH", "LA", "PH", "MO", "SG", "MY", "ID", "KH", "TR", "BH", "QA", "YE", "AE", "IQ", "OM", "KW", "SA", "IL", "JO", "LB", "TJ", "BD", "MV", "PK", "KZ", "BT",
                "KR", "KG", "LK", "CN", "JP", "UZ", "TM", "IN", "NP", "AM", "MN", "AZ", "GE", "AF", "RU",
            };

        private static readonly string[] SouthAmerica = { "PY", "FK", "SR", "GY", "GF", "BO", "AR", "EC", "CL", "PE", "UY", "VE", "BR", "CO", };

        private static readonly string[] SoutheastAsia = { "VN", "TW", "HK", "BN", "TH", "LA", "PH", "MO", "SG", "MY", "ID", "KH", };

        private static readonly string[] Oceania = { "FM", "WF", "AU", "KI", "WS", "VU", "AS", "NR", "NC", "NU", "SB", "CK", "NZ", "TV", "PF", "GU", "FJ", "PG", "MH", "TO", "PW", };

        private static readonly string[] NorthAmerica = { "GL", "MX", "CA", "PM", "BM", };

        private static readonly string[] MiddleEast = { "TR", "BH", "QA", "YE", "AE", "IQ", "OM", "KW", "SA", "IL", "JO", "LB", };

        private static readonly string[] Europe =
        {
                "AL", "AD", "AT", "BY", "BE", "BA", "BG", "HR", "CY", "CZ", "DK", "EE", "FI", "FR", "DE", "GI", "GR", "GG", "HU", "IS", "IE", "IT", "JE", "LV", "LI", "LT", "LU", "MK", "MT", "MD",
                "MC", "ME", "NL", "NO", "PL", "PT", "RO", "RU", "SM", "RS", "SK", "SI", "ES", "SE", "CH", "UA", "GB", "VA",
            };

        private static readonly string[] EuropeExclude =
        {
                "PT", "MK", "RO", "IS", "DK", "NO", "DE", "AD", "IT", "FI", "HR", "GI", "ME", "IE", "MC", "PL", "JE", "BG", "AT", "VA", "EE", "SM", "ES", "RS", "GR", "FR", "LU", "HU", "GB", "BY",
                "SJ", "CH", "LI", "MD", "LV", "AL", "SE", "CZ", "LT", "NL", "CY", "GG", "RU", "MT", "SK", "UA", "BE", "SI", "BA",
            };

        private static readonly string[] CentralAmericaandCaribbean =
        {
                "PA", "VI", "JM", "VG", "GD", "TT", "PR", "BS", "DM", "BZ", "HT", "KN", "TC", "SV", "MS", "AG", "KY", "AW", "LC", "GT", "AN", "VC", "DO", "GP", "MQ", "CR", "AI", "HN", "NI", "BB",
            };

        private static readonly string[] Africa =
        {
                "EH", "CF", "SH", "TZ", "UG", "DJ", "SL", "LS", "GH", "MZ", "CG", "CD", "RW", "TD", "LR", "RE", "GN", "LY", "YT", "SC", "ET", "MW", "SN", "KM", "TN", "BW", "GQ", "GA", "NG", "MR",
                "GW", "KE", "SZ", "ZA", "DZ", "MG", "CV", "EG", "ZW", "AO", "BI", "MU", "CM", "TG", "CI", "ER", "ML", "GM", "NE", "BF", "MA", "SO", "ZM", "BJ", "NA",
            };
    }
}
