﻿using System;
using System.Management;
using System.Security.Cryptography;
using System.Text;

namespace uBuyFirst.Tools
{
    public class MachineProfile
    {
        public static string HardDriveSerialNumber
        {
            get
            {
                var hardDriveSerialNumber = new ManagementObject("Win32_LogicalDisk.DeviceID=\"" + Environment.SystemDirectory.Substring(0, 2) + "\"")
                    .GetPropertyValue("VolumeSerialNumber")
                    .ToString();
                return hardDriveSerialNumber;
            }
        }

        public static string ProcessorSerialNumber
        {
            get
            {
                try
                {
                    using var enumerator = new ManagementObjectSearcher("SELECT * FROM Win32_baseboard").Get().GetEnumerator();
                    if (enumerator.MoveNext())
                    {
                        var processorSerialNumber = enumerator.Current.GetPropertyValue("SerialNumber").ToString();
                        return processorSerialNumber;
                    }
                }
                catch (Exception ex)
                {
                    return string.Empty;
                }

                return string.Empty;
            }
        }

        public static string GetHardwareId()
        {
            var s = HardDriveSerialNumber + ProcessorSerialNumber;
            return Convert.ToBase64String(HashAlgorithm.Create("System.Security.Cryptography.HashAlgorithm").ComputeHash(Encoding.UTF8.GetBytes(s)));
        }

        public static string GetDeviceModel()
        {
            var model = GetWmiProperty("Win32_ComputerSystem", "model");
            var version = GetWmiProperty("Win32_ComputerSystemProduct", "version");
            var processorID = GetWmiProperty("Win32_Processor", "ProcessorId");
            var biosSN = GetWmiProperty("Win32_BIOS", "SerialNumber");
            var harddriveSN = GetWmiProperty("Win32_DiskDrive", "SerialNumber");

            return $"{model}|{processorID}|{harddriveSN}|{version}|{biosSN}";
        }

        internal static string GetWmiProperty(string wmiClass, string wmiProperty)
        {
            var result = "Not Available";

            try
            {
                var searcher = new ManagementObjectSearcher($"SELECT {wmiProperty} FROM {wmiClass}");
                foreach (var obj in searcher.Get())
                {
                    result = obj[wmiProperty]?.ToString();
                    break;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($@"An error occurred: {ex.Message}");
            }

            return result;
        }
    }
}
