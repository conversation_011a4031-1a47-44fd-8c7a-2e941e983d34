# Restocker Module Database Schema

## Overview

This document details the SQLite database schema for the Restocker module. The database will track purchase requirements, transactions, and related data.

## Business Rules

### Over-Purchase Prevention

**Critical Constraint**: The system enforces that `PurchasedQuantity` never exceeds `RequiredQuantity` for any JobId. This prevents over-purchasing and ensures budget control.

**Implementation**:

- Before each purchase attempt, the system checks if `PurchasedQuantity >= RequiredQuantity`
- If the requirement is already fulfilled, the purchase is skipped with message "Purchase requirement already fulfilled"
- The system calculates `quantityToPurchase = Math.Min(remainingQuantity, itemAvailable)` to ensure exact quantity control

## Tables

### 1. PurchaseRequirements

Stores information about what items need to be purchased.

| Column | Type | Description |
|--------|------|-------------|
| Id | INTEGER | Primary key |
| KeywordId | TEXT | References the keyword's unique identifier |
| KeywordAlias | TEXT | The search term alias for reporting |
| JobId | TEXT | Identifier for the purchase requirement |
| RequiredQuantity | INTEGER | Number of items to purchase |
| MaxPrice | DECIMAL | Optional price ceiling |
| CreatedAt | DATETIME | When the requirement was created |
| UpdatedAt | DATETIME | When the requirement was last updated |
| IsActive | BOOLEAN | Whether the requirement is active |

```sql
CREATE TABLE PurchaseRequirements (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    KeywordId TEXT NOT NULL,
    KeywordAlias TEXT NOT NULL,
    JobId TEXT NOT NULL,
    RequiredQuantity INTEGER NOT NULL,
    MaxPrice DECIMAL,
    CreatedAt DATETIME NOT NULL,
    UpdatedAt DATETIME NOT NULL,
    IsActive BOOLEAN NOT NULL DEFAULT 1
);

CREATE INDEX idx_purchase_requirements_keyword_id ON PurchaseRequirements(KeywordId);
CREATE INDEX idx_purchase_requirements_job_id ON PurchaseRequirements(JobId);
```

### 2. PurchaseTransactions

Records of actual purchases made.

| Column | Type | Description |
|--------|------|-------------|
| Id | INTEGER | Primary key |
| KeywordId | TEXT | References the keyword's unique identifier |
| JobId | TEXT | Job ID for grouping related transactions |
| ItemId | TEXT | eBay item ID |
| ItemTitle | TEXT | Title of the purchased item |
| PurchasePrice | DECIMAL | Price paid |
| Quantity | INTEGER | Number of items purchased |
| PurchaseDate | DATETIME | When the purchase was made |
| Status | TEXT | "Completed", "Failed", "Pending" |
| TransactionId | TEXT | eBay transaction ID |
| PaymentMethod | TEXT | Method of payment |
| ShippingAddress | TEXT | Shipping address used |
| Notes | TEXT | Additional notes |

```sql
CREATE TABLE PurchaseTransactions (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    KeywordId TEXT NOT NULL,
    JobId TEXT NOT NULL,
    ItemId TEXT NOT NULL,
    ItemTitle TEXT NOT NULL,
    PurchasePrice DECIMAL NOT NULL,
    Quantity INTEGER NOT NULL,
    PurchaseDate DATETIME NOT NULL,
    Status TEXT NOT NULL,
    TransactionId TEXT,
    PaymentMethod TEXT,
    ShippingAddress TEXT,
    Notes TEXT
);

CREATE INDEX idx_purchase_transactions_keyword_id ON PurchaseTransactions(KeywordId);
CREATE INDEX idx_purchase_transactions_job_id ON PurchaseTransactions(JobId);
CREATE INDEX idx_purchase_transactions_item_id ON PurchaseTransactions(ItemId);
CREATE INDEX idx_purchase_transactions_status ON PurchaseTransactions(Status);
```

### 3. SyncHistory

Records of spreadsheet synchronization events.

| Column | Type | Description |
|--------|------|-------------|
| Id | INTEGER | Primary key |
| SyncDate | DATETIME | When the sync occurred |
| FileName | TEXT | Name of the synced file |
| RequirementsAdded | INTEGER | Number of new requirements added |
| RequirementsUpdated | INTEGER | Number of requirements updated |
| RequirementsDeactivated | INTEGER | Number of requirements deactivated |

```sql
CREATE TABLE SyncHistory (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    SyncDate DATETIME NOT NULL,
    FileName TEXT NOT NULL,
    RequirementsAdded INTEGER NOT NULL,
    RequirementsUpdated INTEGER NOT NULL,
    RequirementsDeactivated INTEGER NOT NULL
);

CREATE INDEX idx_sync_history_sync_date ON SyncHistory(SyncDate);
```

### 4. PurchaseAttempts

Records of attempts to purchase items.

| Column | Type | Description |
|--------|------|-------------|
| Id | INTEGER | Primary key |
| KeywordId | TEXT | References the keyword's unique identifier |
| JobId | TEXT | Job ID for grouping related attempts |
| ItemId | TEXT | eBay item ID |
| AttemptDate | DATETIME | When the attempt was made |
| Result | TEXT | Result of the attempt |
| ErrorMessage | TEXT | Error message if failed |

```sql
CREATE TABLE PurchaseAttempts (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    KeywordId TEXT NOT NULL,
    JobId TEXT NOT NULL,
    ItemId TEXT NOT NULL,
    AttemptDate DATETIME NOT NULL,
    Result TEXT NOT NULL,
    ErrorMessage TEXT
);

CREATE INDEX idx_purchase_attempts_keyword_id ON PurchaseAttempts(KeywordId);
CREATE INDEX idx_purchase_attempts_job_id ON PurchaseAttempts(JobId);
CREATE INDEX idx_purchase_attempts_item_id ON PurchaseAttempts(ItemId);
```

### 5. Configuration

System configuration settings.

| Column | Type | Description |
|--------|------|-------------|
| Id | INTEGER | Primary key |
| Key | TEXT | Setting key |
| Value | TEXT | Setting value |
| UpdatedAt | DATETIME | When the setting was last updated |

```sql
CREATE TABLE Configuration (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    Key TEXT NOT NULL UNIQUE,
    Value TEXT NOT NULL,
    UpdatedAt DATETIME NOT NULL
);

CREATE INDEX idx_configuration_key ON Configuration(Key);
```

## Relationships

- PurchaseTransactions.KeywordId → Keyword2Find.Id (via application logic)
- PurchaseAttempts.KeywordId → Keyword2Find.Id (via application logic)
- PurchaseTransactions.JobId and PurchaseAttempts.JobId group related operations

## Database Location

The database file will be stored at:
```
{Folders.Settings}/Restock/restock.db
```

## Initialization

The database will be initialized on first run with the following default configuration:

```sql
INSERT INTO Configuration (Key, Value, UpdatedAt)
VALUES ('AutoPurchaseEnabled', 'false', datetime('now'));

INSERT INTO Configuration (Key, Value, UpdatedAt)
VALUES ('DefaultMaxPrice', '0', datetime('now'));

INSERT INTO Configuration (Key, Value, UpdatedAt)
VALUES ('NotifyOnPurchase', 'true', datetime('now'));
```

## Backup Strategy

The database will be backed up:
1. Before each sync operation
2. Daily (if changes occurred)
3. Before schema migrations

Backups will be stored in:
```
{Folders.Backup}/PurchaseTracker/
```