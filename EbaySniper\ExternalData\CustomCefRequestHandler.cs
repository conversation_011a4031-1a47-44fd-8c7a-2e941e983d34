﻿using System;
using System.Collections.Generic;
using System.Text;
using CefSharp;
using Newtonsoft.Json;

namespace uBuyFirst.ExternalData
{
    public class CustomRequestHandler : CefSharp.Handler.RequestHandler
    {
        private readonly List<string> _imagePaths;
        private readonly string _description;

        public CustomRequestHandler(List<string> imagePaths, string description)
        {
            _imagePaths = imagePaths;
            _description = description;
        }

        protected override IResourceRequestHandler? GetResourceRequestHandler(
            IWebBrowser chromiumWebBrowser,
            IBrowser browser,
            IFrame frame,
            IRequest request,
            bool isNavigation,
            bool isDownload,
            string requestInitiator,
            ref bool disableDefaultHandling)
        {
            if (request.Method == "GET")
            {
                return new CustomResourceRequestHandler(_imagePaths, _description);
            }
            return null;
        }
    }

    public class CustomResourceRequestHandler : CefSharp.Handler.ResourceRequestHandler
    {
        private readonly List<string> _imagePaths;
        private readonly string _description;

        public CustomResourceRequestHandler(List<string> imagePaths, string description)
        {
            _imagePaths = imagePaths;
            _description = description;
        }

        private Dictionary<string, string> GetQueryParameters(string url)
        {
            var parameters = new Dictionary<string, string>();
            var uri = new Uri(url);
            
            if (!string.IsNullOrEmpty(uri.Query))
            {
                // Remove the leading '?' and split by '&'
                var queryParams = uri.Query.TrimStart('?').Split('&');
                foreach (var param in queryParams)
                {
                    if (!string.IsNullOrEmpty(param))
                    {
                        try
                        {
                            var parts = param.Split(new[] { '=' }, 2);
                            // Decode and sanitize the key
                            var key = Uri.UnescapeDataString(parts[0]).Replace(" ", "_");
                            
                            // Decode the value, if it exists
                            var value = parts.Length > 1 ? Uri.UnescapeDataString(parts[1]) : string.Empty;
                            
                            // Only add if we have a valid key
                            if (!string.IsNullOrEmpty(key))
                            {
                                parameters[key] = value;
                            }
                        }
                        catch (UriFormatException)
                        {
                            // Skip malformed parameters
                            continue;
                        }
                    }
                }
            }
            return parameters;
        }

        protected override CefReturnValue OnBeforeResourceLoad(
            IWebBrowser chromiumWebBrowser,
            IBrowser browser,
            IFrame frame,
            IRequest request,
            IRequestCallback callback)
        {
            try
            {
                // Build JSON payload
                var queryParams = GetQueryParameters(request.Url);
                var payload = new Dictionary<string, object>
                {
                    { "description", _description },
                    { "images", _imagePaths }
                };

                // Add all query parameters as top-level fields
                foreach (var param in queryParams)
                {
                    payload[param.Key] = param.Value;
                }

                // Serialize with settings to ensure proper escaping
                var settings = new JsonSerializerSettings
                {
                    StringEscapeHandling = StringEscapeHandling.EscapeNonAscii
                };
                var jsonData = JsonConvert.SerializeObject(payload, settings);

                // Create PostData and add JSON data
                var postData = new PostData();
                var jsonBytes = Encoding.UTF8.GetBytes(jsonData);
                postData.AddData(jsonBytes);

                // Modify the request: set method, URL (remove query parameters if desired), and headers
                request.Method = "POST";
                var uri = new Uri(request.Url);
                request.Url = uri.GetLeftPart(UriPartial.Path); // Remove query parameters if necessary
                request.PostData = postData;
                request.SetHeaderByName("Content-Type", "application/json", true);

                return CefReturnValue.Continue;
            }
            catch (Exception ex)
            {
                // Handle any exceptions that occur during the request processing
                // You can log the exception or perform any other error handling as needed
                return CefReturnValue.Cancel;
            }
        }
    }
}
