﻿using System;
using System.Collections.Generic;

// ReSharper disable All
#pragma warning disable IDE1006 // Naming Styles
#pragma warning disable RCS1102 // Make class static.
namespace uBuyFirst.Purchasing
{
    namespace OrderApi
    {
        public class BillingAddress
        {
            public string addressLine1 { get; set; }
            public string addressLine2 { get; set; }
            public string city { get; set; }
            public string country { get; set; }
            public string county { get; set; }
            public string firstName { get; set; }
            public string lastName { get; set; }
            public string postalCode { get; set; }
            public string stateOrProvince { get; set; }
        }

        public class CreditCard
        {
            public string accountHolderName { get; set; }
            public BillingAddress billingAddress { get; set; }
            public string brand { get; set; }
            public string cardNumber { get; set; }
            public string cvvNumber { get; set; }
            public int expireMonth { get; set; }
            public int expireYear { get; set; }
        }

        public class LineItemInput
        {
            public string itemId { get; set; }
            public int quantity { get; set; }
        }

        public class CreateSignInCheckoutSessionRequest
        {
            public CreditCard creditCard { get; set; }
            public List<LineItemInput> lineItemInputs { get; set; }
            public ShippingAddress shippingAddress { get; set; }
        }

        public class CreateGuestCheckoutSessionRequest
        {
            public string contactEmail { get; set; }
            public string contactFirstName { get; set; }
            public string contactLastName { get; set; }
            public CreditCard creditCard { get; set; }
            public List<LineItemInput> lineItemInputs { get; set; }
            public ShippingAddress shippingAddress { get; set; }
        }
        /*---------------------------*/

        public class LogoImage
        {
            public int height { get; set; }
            public string imageUrl { get; set; }
            public int width { get; set; }
        }

        public class PaymentMethodBrand
        {
            public LogoImage logoImage { get; set; }
            public string paymentMethodBrandType { get; set; }
        }

        public class PaymentMethodMessage
        {
            public string legalMessage { get; set; }
            public bool requiredForUserConfirmation { get; set; }
        }

        public class AcceptedPaymentMethod
        {
            public string label { get; set; }
            public LogoImage logoImage { get; set; }
            public List<PaymentMethodBrand> paymentMethodBrands { get; set; }
            public List<PaymentMethodMessage> paymentMethodMessages { get; set; }
            public string paymentMethodType { get; set; }
        }

        public class Image
        {
            public int height { get; set; }
            public string imageUrl { get; set; }
            public int width { get; set; }
        }

        public class Seller
        {
            public string feedbackPercentage { get; set; }
            public int feedbackScore { get; set; }
            public string sellerAccountType { get; set; }
            public string username { get; set; }
        }

        public class ShippingOption
        {
            public string maxEstimatedDeliveryDate { get; set; }
            public string minEstimatedDeliveryDate { get; set; }
            public string shippingCarrierCode { get; set; }
            public string shippingServiceCode { get; set; }
            public bool selected { get; set; }
            public Amount baseDeliveryCost { get; set; }
            public Amount deliveryDiscount { get; set; }
            public string shippingOptionId { get; set; }
        }

        public class LineItem
        {
            public Image image { get; set; }
            public string itemId { get; set; }
            public string lineItemId { get; set; }
            public string lineItemPaymentStatus { get; set; }
            public string lineItemStatus { get; set; }
            public Amount netPrice { get; set; }
            public Amount baseUnitPrice { get; set; }
            public int quantity { get; set; }
            public Seller seller { get; set; }
            public ShippingOption[] shippingOptions { get; set; }
            public string shortDescription { get; set; }
            public string title { get; set; }
        }

        public class Amount
        {
            public string currency { get; set; }
            public string value { get; set; }
        }

        public class Adjustment
        {
            public Amount amount { get; set; }
            public string label { get; set; }
        }

        public class DeliveryCost
        {
            public string currency { get; set; }
            public string value { get; set; }
        }

        public class DeliveryDiscount
        {
            public string currency { get; set; }
            public string value { get; set; }
        }

        public class Fee
        {
            public string currency { get; set; }
            public string value { get; set; }
        }

        public class PriceDiscount
        {
            public string currency { get; set; }
            public string value { get; set; }
        }

        public class PriceSubtotal
        {
            public string currency { get; set; }
            public string value { get; set; }
        }

        public class Tax
        {
            public string currency { get; set; }
            public string value { get; set; }
        }

        public class Total
        {
            public string currency { get; set; }
            public string value { get; set; }
        }

        public class PricingSummary
        {
            public Adjustment adjustment { get; set; }
            public DeliveryCost deliveryCost { get; set; }
            public DeliveryDiscount deliveryDiscount { get; set; }
            public Fee fee { get; set; }
            public PriceDiscount priceDiscount { get; set; }
            public PriceSubtotal priceSubtotal { get; set; }
            public Tax tax { get; set; }
            public Total total { get; set; }
        }

        public class PaymentInstrumentReference
        {
            public string lastFourDigitForCreditCard { get; set; }
        }

        public class PaymentMethodBrand2
        {
            public LogoImage logoImage { get; set; }
            public string paymentMethodBrandType { get; set; }
        }

        public class ProvidedPaymentInstrument
        {
            public PaymentInstrumentReference paymentInstrumentReference { get; set; }
            public PaymentMethodBrand2 paymentMethodBrand { get; set; }
            public string paymentMethodType { get; set; }
        }

        [Serializable()]
        public class ShippingAddress
        {
            public string addressLine1 { get; set; }
            public string addressLine2 { get; set; }
            public string city { get; set; }
            public string country { get; set; }
            public string county { get; set; }
            public string phoneNumber { get; set; }
            public string postalCode { get; set; }
            public string recipient { get; set; }
            public string stateOrProvince { get; set; }
        }

        public class Parameter
        {
            public string name { get; set; }
            public string value { get; set; }
        }

        public class Warning
        {
            public string category { get; set; }
            public string domain { get; set; }
            public int errorId { get; set; }
            public List<string> inputRefIds { get; set; }
            public string longMessage { get; set; }
            public string message { get; set; }
            public List<string> outputRefIds { get; set; }
            public List<Parameter> parameters { get; set; }
        }

        public class Error
        {
            public int errorId { get; set; }
            public string domain { get; set; }
            public string category { get; set; }
            public string message { get; set; }
            public string longMessage { get; set; }
            public List<string> inputRefIds { get; set; }
            public List<string> outputRefIds { get; set; }
            public List<Parameter> parameters { get; set; }
        }

        public class CheckoutSessionResponse
        {
            public List<AcceptedPaymentMethod> acceptedPaymentMethods { get; set; }
            public string checkoutSessionId { get; set; }
            public string expirationDate { get; set; }
            public List<LineItem> lineItems { get; set; }
            public PricingSummary pricingSummary { get; set; }
            public ProvidedPaymentInstrument providedPaymentInstrument { get; set; }
            public ShippingAddress shippingAddress { get; set; }
            public List<Warning> warnings { get; set; }
            public List<Error> errors { get; set; }
        }
        //-=-----------------------------------------------------------        

        public class PurchaseOrderSummary
        {
            public string purchaseOrderHref { get; set; }
            public string purchaseOrderId { get; set; }
            public string purchaseOrderPaymentStatus { get; set; }
            public List<Warning> warnings { get; set; }
        }

        public class UpdateShippingOption
        {
            public string lineItemId { get; set; }
            public string shippingOptionId { get; set; }
        }
    }
}
#pragma warning restore IDE1006 // Naming Styles
#pragma warning restore RCS1102 // Make class static.