﻿using System;
using System.Diagnostics;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraBars.Ribbon;
using uBuyFirst.Prefs;

namespace uBuyFirst
{
    public partial class FormShortcuts : RibbonForm
    {
        public FormShortcuts()
        {
            InitializeComponent();
            LoadShortcutSettings();
            StartPosition = FormStartPosition.CenterParent;
        }

        private void LoadShortcutSettings()
        {
            var modifiers = new[] { "None", "Control", "Alt", "Shift" }.ToList();
            cbBuyModifier.Properties.Items.AddRange(modifiers);
            cbGoToCheckoutModifier.Properties.Items.AddRange(modifiers);

            cbMakeOfferModifier.Properties.Items.AddRange(modifiers);

            var keyList = Enum.GetNames(typeof(Keys)).ToList();
            cbBuyKey.Properties.Items.AddRange(keyList);
            cbGoToCheckoutKey.Properties.Items.AddRange(keyList);
            cbQuickBuyModifier.Properties.Items.AddRange(modifiers);
            cbQuickBuyKey.Properties.Items.AddRange(keyList);
            cbImmediateBuyKey.Properties.Items.AddRange(keyList);
            cbImmediateBuyModifier.Properties.Items.AddRange(modifiers);
            cbMakeOfferKey.Properties.Items.AddRange(keyList);

            cbBuyModifier.SelectedItem = Int2StrKey(UserSettings.Shortcuts.BuyModifier);
            cbBuyKey.SelectedItem = Int2StrKey(UserSettings.Shortcuts.BuyKey);

            cbGoToCheckoutModifier.SelectedItem = Int2StrKey(UserSettings.Shortcuts.GoToCheckoutModifier);
            cbGoToCheckoutKey.SelectedItem = Int2StrKey(UserSettings.Shortcuts.GoToCheckoutKey);

            cbQuickBuyModifier.SelectedItem = Int2StrKey(UserSettings.Shortcuts.QuickBuyModifier);
            cbQuickBuyKey.SelectedItem = Int2StrKey(UserSettings.Shortcuts.QuickBuyKey);
            cbImmediateBuyModifier.SelectedItem = Int2StrKey(UserSettings.Shortcuts.ImmediateBuyModifier);
            cbImmediateBuyKey.SelectedItem = Int2StrKey(UserSettings.Shortcuts.ImmediateBuyKey);

            cbMakeOfferModifier.SelectedItem = Int2StrKey(UserSettings.Shortcuts.MakeOfferModifier);
            cbMakeOfferKey.SelectedItem = Int2StrKey(UserSettings.Shortcuts.MakeOfferKey);
        }

        private void SaveKeyboardShortcuts()
        {
            UserSettings.Shortcuts.BuyModifier = Str2IntKey(cbBuyModifier.SelectedItem.ToString());
            UserSettings.Shortcuts.BuyKey = Str2IntKey(cbBuyKey.SelectedItem.ToString());

            UserSettings.Shortcuts.GoToCheckoutModifier = Str2IntKey(cbGoToCheckoutModifier.SelectedItem.ToString());
            UserSettings.Shortcuts.GoToCheckoutKey = Str2IntKey(cbGoToCheckoutKey.SelectedItem.ToString());

            UserSettings.Shortcuts.QuickBuyModifier = Str2IntKey(cbQuickBuyModifier.SelectedItem.ToString());
            UserSettings.Shortcuts.QuickBuyKey = Str2IntKey(cbQuickBuyKey.SelectedItem.ToString());

            UserSettings.Shortcuts.ImmediateBuyModifier = Str2IntKey(cbImmediateBuyModifier.SelectedItem.ToString());
            UserSettings.Shortcuts.ImmediateBuyKey = Str2IntKey(cbImmediateBuyKey.SelectedItem.ToString());

            UserSettings.Shortcuts.MakeOfferModifier = Str2IntKey(cbMakeOfferModifier.SelectedItem.ToString());
            UserSettings.Shortcuts.MakeOfferKey = Str2IntKey(cbMakeOfferKey.SelectedItem.ToString());
        }

        private static int Str2IntKey(string strKey)
        {
            return (int)Enum.Parse(typeof(Keys), strKey);
        }

        private static string Int2StrKey(int intKey)
        {
            return ((Keys)Enum.ToObject(typeof(Keys), intKey)).ToString();
        }

        private void FormHighlightWords_FormClosing(object sender, FormClosingEventArgs e)
        {
            SaveKeyboardShortcuts();
        }

        private void FormHighlightWords_Load(object sender, EventArgs e)
        {
            //AutoMeasurement.Client.TrackScreenView("Screen - " + Text);
        }

        private void FormHighlightWords_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == 27)
            {
                Close();
            }
        }

        private void textEdit1_KeyDown(object sender, KeyEventArgs e)
        {
            textEdit1.Text = "";
            textEdit1.Text = e.KeyData.ToString();
            e.Handled = true;
            e.SuppressKeyPress = true;
            Debug.WriteLine(e.KeyData + "\t" + e.KeyCode);
        }

        private void lblQuickBuy_Click(object sender, EventArgs e)
        {
            lblImmediateBuy.Visible = !lblImmediateBuy.Visible;
            cbImmediateBuyModifier.Visible = !cbImmediateBuyModifier.Visible;
            cbImmediateBuyKey.Visible = !cbImmediateBuyKey.Visible;
        }
    }
}