﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.ServiceModel.Security;
using System.Threading;
using System.Threading.Tasks;
using BrowseAPI;
using DevExpress.XtraEditors;
using Fonlow.Net.Http;
using uBuyFirst.Network;
using uBuyFirst.Other;
using uBuyFirst.Prefs;
using uBuyFirst.Search;
using uBuyFirst.Tools;

namespace uBuyFirst.BrowseAPI
{
    public class BrowseAPINetwork
    {
        private readonly ViewReporter _viewReporter;

        private static BrowseAPIClass s_browseAPI = new(new HttpClient(new Http2CustomHandler(Helpers.BuyItNowConfirmation)
        {
            WindowsProxyUsePolicy = WindowsProxyUsePolicy.UseWinInetProxy,
            AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate
        })
        { BaseAddress = new Uri(ConnectionConfig.BrowseAPIUrl) });

        public BrowseAPINetwork(ViewReporter viewReporter)
        {
            _viewReporter = viewReporter;
            ConnectionConfig.BrowseAPIUrlChanged += BrowseAPIUrlChangedHandler;
        }

        private static void BrowseAPIUrlChangedHandler(object sender, string url)
        {
            s_browseAPI = new BrowseAPIClass(new HttpClient(new Http2CustomHandler(Helpers.BuyItNowConfirmation)
            {
                WindowsProxyUsePolicy = WindowsProxyUsePolicy.UseWinInetProxy,
                AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate,
            })
            { BaseAddress = new Uri(url) });
        }

        public void FindRequestsApi(CancellationToken ct, Keyword2Find kw2Find, string categoryID, bool isEndingNow, bool isInitialSearch)
        {
            if (ct.IsCancellationRequested || !Form1.Instance.IsHandleCreated)
                return;

            Interlocked.Increment(ref Stat.FindReqCount);
            Interlocked.Increment(ref Stat.BrowseApiCounter);
            Interlocked.Increment(ref Stat.FindActiveThreads);

            var searchTerm = new SearchTermBrowseAPI
            {
                Kws = kw2Find.Kws,
                SearchInDescription = kw2Find.SearchInDescription,
                PriceMin = kw2Find.PriceMin,
                PriceMax = kw2Find.PriceMax,
                Condition = kw2Find.Condition,
                ListingType = kw2Find.ListingType,
                LocatedIn = kw2Find.LocatedIn,
                AvailableTo = kw2Find.AvailableTo,
                SellerType = kw2Find.SellerType,
                Sellers = kw2Find.Sellers,
                CategoryID = categoryID,
                ResultsLimit = 100
            };

            var searchUrl = BrowseAPISearchBuilder.CreateBrowseAPISearchRequestUrl(searchTerm, isEndingNow);

            SearchPagedCollection? response = null;
            try
            {
                var token = string.IsNullOrEmpty(ConnectionConfig.Token) ? ProgramState.HWID : ConnectionConfig.Token;

                var requestParams = new RequestParams { MarketplaceID = kw2Find.EBaySite.BrowseAPIID, Token = token };
                searchUrl = ConnectionConfig.BrowseAPIUrl + searchUrl;
                response = Search(searchUrl, requestParams);
            }
            catch (SecurityNegotiationException ex)
            {
                if (!Loggers.BadCertificateSent)
                {
                    Loggers.LogCertError($"{Loggers.BadCertificate};{ex.Message}");
                    Loggers.BadCertificateSent = true;
                    XtraMessageBox.Show("Certificate error #443.\r\nPlease, contact uBuyFirst support.");
                }
            }

            catch (AggregateException ex)
            {
                if (ex.InnerException is WebApiRequestException exc)
                {
                    if (exc.Response.Contains("Invalid access token"))
                    {
                        ConnectionConfig.SetBrowseAPIUrl(ConnectionConfig.BrowseAPIUrlFallback);
                    }
                    else if (!exc.Response.Contains("The call must have a valid 'q'"))
                        if (!exc.Message.Contains("Gateway Timeout"))
                            if (!exc.Message.Contains("Bad Request") || exc.Response.Contains("This keyword search results in a response that is too large to return"))
                                ExM.ubuyExceptionHandler($"_(info)_BrowseAPI_Search: Alias [{kw2Find.Alias}]" + exc.Response, exc);
                }
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("_(info)_BrowseAPI_Search: ", ex);
            }
            finally
            {
                Interlocked.Decrement(ref Stat.FindActiveThreads);
            }

            if (response?.ItemSummaries != null)
            {
                if (response.Warnings != null && response.Warnings.Any(w => w.Message.Contains("query parameter is invalid")))
                    return;

                if (ct.IsCancellationRequested)
                    return;

                var itemSummaries = response.ItemSummaries;

                var itemIDs = itemSummaries.Select(item => item.ItemId.Replace("v1|", "").Replace("|0", "")).ToList();
                List<string> addedItems;
                if (kw2Find.ChildrenCore.Count == 0)
                {
                    addedItems = SearchService.AddItemsToStorage(itemIDs);
                }
                else
                {
                    addedItems = SearchService.AddItemsToStorage(kw2Find, itemIDs);
                }

                var foundItems = new List<FoundItem>();

                foreach (var addedItem in addedItems)
                {
                    var itemSummary = itemSummaries.FirstOrDefault(summary => summary.ItemId.Contains(addedItem));
                    if (itemSummary == null)
                    {
                        continue;
                    }

                    var isMultiVariation = itemSummary.ItemGroupType != null;
                    DateTime startTime = DateTime.SpecifyKind(DateTime.MinValue, DateTimeKind.Utc);
                    if (DateTime.TryParse(itemSummary.ItemCreationDate, CultureInfo.InvariantCulture, DateTimeStyles.AdjustToUniversal, out var itemCreationDate))
                    {
                        startTime = itemCreationDate;
                    }

                    if (itemSummary.Categories != null)
                    {
                        var categoryPath = itemSummary.Categories.Select(c=>c.CategoryId).ToArray();
                        var foundItem = new FoundItem(null, kw2Find, addedItem, startTime, SearchSource.API2, isMultiVariation, isInitialSearch, itemSummary.Seller.Username, null, null,categoryPath);
                        foundItems.Add(foundItem);
                    }
                    else
                    {
                        
                    }
                }

                SearchService.SendItemsForProcessing(foundItems, _viewReporter);
                uBuyFirst.Search.Status.RowStatusUpdater.SetStatusActive(itemIDs.Except(addedItems).ToList());
            }
            else
            {
                if (response?.Warnings?.Any() != true || ct.IsCancellationRequested)
                    return;

                Stat._errorsCount++;
                _viewReporter.ReportLogTxt(DateTime.Now.ToString("d MMM HH:mm:ss")
                                           + " \t "
                                           + kw2Find.Alias
                                           + " \t "
                                           + response.Warnings.FirstOrDefault()?.Message
                                           + " "
                                           + response.Warnings.FirstOrDefault()?.LongMessage);
            }
        }

        private static SearchPagedCollection Search(string searchUrl, RequestParams requestParams)
        {
            var searchResult = s_browseAPI.SearchByUrlAsync(searchUrl, headers => AddRequestHeaders(headers, requestParams)).Result;
            return searchResult;
        }

        public static async Task<ItemGroup?> GetItem(RequestParams requestParams)
        {
            var v1ItemId = "v1|" + requestParams.ItemID + "|0";

            var item = await s_browseAPI.GetItemAsync("PRODUCT", v1ItemId, "1", headers => AddRequestHeaders(headers, requestParams));
            var itemGroup = new ItemGroup
            {
                Items = new[] { item }
            };
            Interlocked.Increment(ref Stat.BrowseApiCounter);
            return itemGroup;
        }

        public static async Task<Items> GetItems(IEnumerable<string> itemIds, RequestParams requestParams)
        {
            var browseApiItemIds = itemIds.Select(t => "v1|" + t + "|0").ToList();
            var response = await s_browseAPI.GetItemsAsync(string.Join(",", browseApiItemIds), "", "1", headers => AddRequestHeaders(headers, requestParams));
            Interlocked.Increment(ref Stat.GetItemsCounter);
            return response;
        }

        public static async Task<ItemGroup?> GetGroupItem(RequestParams requestParams)
        {
            Interlocked.Increment(ref Stat.BrowseApiCounter);

            var itemGroup = await s_browseAPI.GetItemsByItemGroupAsync("", requestParams.ItemID, "1", headers => AddRequestHeaders(headers, requestParams));
            return itemGroup;
        }

        private static void AddRequestHeaders(HttpHeaders headers, RequestParams requestParams)
        {
            headers.Add("Authorization", "Bearer " + requestParams.Token);
            headers.Add("X-EBAY-C-MARKETPLACE-ID", requestParams.MarketplaceID);

            if (requestParams.Country == null)
            {
                return;
            }

            if (requestParams.Zip == null)
            {
                return;
            }

            var locationHeader = "contextualLocation=" + WebUtility.UrlEncode($"country={requestParams.Country},zip={requestParams.Zip}");
            headers.Add("X-EBAY-C-ENDUSERCTX", locationHeader);
        }

        public class RequestParams
        {
            public string? ItemID { get; set; }
            public string MarketplaceID { get; set; }
            public string? Country { get; set; }
            public string? Zip { get; set; }
            public string Token { get; set; }
        }
    }
}
