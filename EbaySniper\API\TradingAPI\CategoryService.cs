﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using eBay.Service.Core.Soap;
using Newtonsoft.Json;
using uBuyFirst.GUI;
using uBuyFirst.Network;
using uBuyFirst.Other;

namespace uBuyFirst.API.TradingAPI
{
    public class CategoryService
    {
        private readonly ApiService _apiService;
        private readonly Dictionary<SiteCodeType, Categories> _categories;

        public CategoryService(ApiService apiService)
        {
            _apiService = apiService;
            _categories = new Dictionary<SiteCodeType, Categories>();
        }

        public Categories GetSiteCategories(SiteCodeType siteCodeType)
        {
            try
            {
                if (_categories.ContainsKey(siteCodeType))
                    return _categories[siteCodeType];

                var categoryFilePath = Path.Combine(Folders.Settings, "Categories." + siteCodeType + ".json");
                if (!File.Exists(categoryFilePath))
                    DownloadCategories(siteCodeType);

                var categories = ReadCategoriesFromDisk(categoryFilePath);
                _categories.Add(siteCodeType, categories);
                return categories;
            }
            catch (Exception e)
            {
                Loggers.LogError(e);
            }

            return new Categories();
        }

        private static Categories ReadCategoriesFromDisk(string categoryFilePath)
        {
            var fileContent = File.ReadAllText(categoryFilePath);
            var categories = JsonConvert.DeserializeObject<Categories>(fileContent);
            return categories;
        }

        public void DownloadCategories(SiteCodeType siteCodeType)
        {
            var categoriesCollection = _apiService.DownloadCategories(siteCodeType);

            var categories = FastParse(categoriesCollection);
            var categoryFilePath = Path.Combine(Folders.Settings, "Categories." + siteCodeType + ".json");
            var fileContent = JsonConvert.SerializeObject(categories, Formatting.None, new JsonSerializerSettings
            {
                NullValueHandling = NullValueHandling.Ignore
            });
            if (!string.IsNullOrWhiteSpace(fileContent))
                File.WriteAllText(categoryFilePath, fileContent);
        }

        private Categories FastParse(CategoryTypeCollection categories)
        {
            Categories parents = new Categories();
            foreach (CategoryType category in categories)
            {
                if (category.CategoryLevel == 1)
                {
                    var project = new Category(category.CategoryName, category.CategoryID, category.LeafCategory);
                    parents.Add(project);
                }
            }

            var childsHash = categories.ToArray().ToLookup(category => category.CategoryParentID.ItemAt(0));
            AddProjectRecursive(parents, childsHash);

            return parents;
        }

        private void AddProjectRecursive(Categories parents, ILookup<string, CategoryType> childsHash)
        {
            foreach (var parent in parents)
            {
                var children = childsHash[parent.CategoryID];
                foreach (CategoryType child in children)
                {
                    if (child.CategoryParentID.ItemAt(0) == child.CategoryID)
                        continue;
                    if (child.CategoryID == "14112")
                        continue;
                    if (child.CategoryName == "Test Category")
                        continue;
                    var p = new Category(child.CategoryName, child.CategoryID, child.LeafCategory);

                    if (parent.Categories == null)
                        parent.Categories = new Categories();
                    parent.Categories.Add(p);
                }

                if (parent.Categories != null)
                    AddProjectRecursive(parent.Categories, childsHash);
            }
        }
    }
}