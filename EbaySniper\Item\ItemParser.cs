﻿using eBay.Service.Core.Soap;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using BrowseAPI;
using uBuyFirst.API.ShoppingAPI;
using uBuyFirst.Data;
using uBuyFirst.Filters;
using uBuyFirst.Images;
using uBuyFirst.Intl;
using uBuyFirst.Prefs;
using uBuyFirst.Search;

namespace uBuyFirst.Item
{
    internal static class ItemParser
    {
        public static void ParseSimpleFields(DataList datalist)
        {

            if (datalist.ShipToLocations?.Count > 0)
                datalist.ToCountry = datalist.ShipToLocations.ToArray().Aggregate((current, next) => current + "," + next);
            else
                datalist.ToCountry = "";

            datalist.ListingType = datalist.ListingTypeToString();

            //No matches in FindItemsAdvanced

        }

        public static void ParsePictures(DataList datalist, PictureDetailsType pictureDetails)
        {
            ImageProcessor.FixBlurryImages(pictureDetails);
            var thumbnail = pictureDetails?.Any?.ToArray().ToList().FirstOrDefault()?.InnerText;
            if (pictureDetails?.PictureURL?.ToArray().ToList().Count > 0)
                datalist.GalleryUrl = null;
            datalist.Pictures = new List<string>();
            if (!string.IsNullOrEmpty(thumbnail) && thumbnail.StartsWith("http"))
                datalist.GalleryUrl = thumbnail;

            if (pictureDetails?.PictureURL != null)
            {
                datalist.Pictures = pictureDetails.PictureURL.ToArray().ToList();
                datalist.Pictures.Remove(thumbnail);
            }

            if (string.IsNullOrEmpty(datalist.GalleryUrl) && datalist.Pictures?.Count > 0)
            {
                datalist.GalleryUrl = datalist.Pictures?[0];
                datalist.Pictures.Remove(datalist.GalleryUrl);
            }
        }

        public static void ParsePicturesFromWebPage(DataList datalist, List<string> pictureDetails)
        {
            //ImageProcessor.FixBlurryImages(pictureDetails);
            if (pictureDetails.Count > 0)
            {
                datalist.GalleryUrl = pictureDetails.First();
                datalist.Pictures = pictureDetails;
                datalist.Pictures.Remove(datalist.GalleryUrl);
            }
        }

        public static bool IsOutOfStock(ReasonHideFromSearchCodeType reasonHideFromSearch)
        {
            if (reasonHideFromSearch == ReasonHideFromSearchCodeType.OutOfStock)
            {
                return true;
            }

            return false;
        }

        public static bool IsCountryMatch(DataList dataList, string availableTo)
        {
            var isShipToCountryMatch = CountryFilter.IsShipToCountryMatch(availableTo, dataList.ShipToLocations, dataList.ExcludeShipToLocation);

            if (isShipToCountryMatch)
                return true;

            var localPickupAvailable = dataList.ShipToLocations?.Contains("None") == true;

            if (localPickupAvailable)
                return true;

            return false;
        }

        public static bool IsRowCellNull(DataRow row)
        {
            if (row["ItemID"] == DBNull.Value)
            {
                return true;
            }

            return false;
        }

        public static void AssignAccountToBuyWIth(DataRow row, DataList datalist)
        {
            foreach (var filter in XFilterManager.GetDataSource())
                if (filter.Action.Contains("Buy with") && filter.Enabled)
                {
                    var eBayAccount = Form1.EBayAccountsList.FirstOrDefault(account => filter.Action.Contains(account.UserName));
                    if (eBayAccount != null)
                        if (filter.GetEvaluator().Fit(row))
                            datalist.EbayAccount = eBayAccount;
                }
        }

        public static SearchSource RandomizeSearchSource(SearchSource itemSource)
        {
            if (itemSource == SearchSource.RSS3)
            {
                var sources = new[] { SearchSource.API, SearchSource.API2, SearchSource.RSS2 };
                var source = sources[new Random().Next(sources.Length)];

                if (ProgramState.SerialNumber.StartsWith("ROMA") || ProgramState.SerialNumber.StartsWith("AD3A") || ProgramState.Isdebug)
                    return SearchSource.RSS3;

                return source;
            }

            return itemSource;
        }

        public static void PurgeDatalistDescription(DataList dataList)
        {
            dataList.Description = "                                     ";
        }

        public static void SetNoDescriptionWarning(DataRow row)
        {
            var isDescriptionEmpty = row["Description"] == DBNull.Value || row["Description"].ToString().Length <= 5;
            if (isDescriptionEmpty)
                row["Description"] = "<font color ='red'>NO DESCRIPTION</font>";
        }

        public static bool IsTitleEmpty(DataList datalist)
        {
            if (datalist.Title.Length == 0)
            {
                return true;
            }

            return false;
        }

        public static string GetStoreName(ItemType item)
        {
            if (!item.Seller.SellerInfo.StoreOwner || item.Storefront == null)
                return "";
            if (item.Storefront.StoreName != null)
                return item.Storefront.StoreName;

            if (item.Storefront.StoreURL != null)
                return System.Text.RegularExpressions.Regex.Replace(item.Storefront.StoreURL, ".*/", "");

            if (item.Seller.SellerInfo.StoreOwner)
                return "Store";

            return "Store";
        }

        public static string GetStoreName(ShoppingAPIJson.SimpleItemType item)
        {
            if (item.Storefront == null)
                return "";
            if (item.Storefront.StoreName != null)
                return item.Storefront.StoreName;

            if (item.Storefront.StoreURL != null)
                return System.Text.RegularExpressions.Regex.Replace(item.Storefront.StoreURL, ".*/", "");

            return "Store";
        }

        public static void ParsePicturesBrowseAPI(DataList datalist, Image itemImage, Image[] itemAdditionalImages)
        {
            //ImageProcessor.FixBlurryImages(pictureDetails);
            //var thumbnail = pictureDetails?.Any?.ToArray().ToList().FirstOrDefault()?.InnerText;
            string thumbnail = null;
            if (itemImage != null)
            {
                thumbnail = itemImage.ImageUrl;
                if (!string.IsNullOrEmpty(thumbnail) && thumbnail.StartsWith("http"))
                    datalist.GalleryUrl = thumbnail;
            }

            if (itemAdditionalImages != null)
            {
                datalist.Pictures = itemAdditionalImages.Select(image => image.ImageUrl).ToList();
                if (!string.IsNullOrEmpty(thumbnail))
                    datalist.Pictures.Remove(thumbnail);
            }

            if (string.IsNullOrEmpty(datalist.GalleryUrl) && datalist.Pictures?.Count > 0)
            {
                datalist.GalleryUrl = datalist.Pictures?[0];
                if (datalist.GalleryUrl != null)
                    datalist.Pictures?.Remove(datalist.GalleryUrl);
            }

            if (datalist.GalleryUrl != null)
                datalist.GalleryUrl = datalist.GalleryUrl.Replace("s-l1600", "s-l500");

            if (datalist.Pictures != null)
                datalist.Pictures = datalist.Pictures.Select(p => p.Replace("s-l1600", "s-l500")).ToList();
        }

        public static void ParsePicturesShoppingAPI(DataList datalist, string[] itemAdditionalImages)
        {
            if (itemAdditionalImages != null)
                datalist.Pictures = itemAdditionalImages.Select(image => image).ToList();

            if (string.IsNullOrEmpty(datalist.GalleryUrl) && datalist.Pictures?.Count > 0)
            {
                datalist.GalleryUrl = datalist.Pictures?[0];
                if (datalist.GalleryUrl != null)
                    datalist.Pictures?.Remove(datalist.GalleryUrl);
            }
        }
    }
}
