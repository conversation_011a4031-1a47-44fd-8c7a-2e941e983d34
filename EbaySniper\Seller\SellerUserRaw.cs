﻿
using Newtonsoft.Json;

namespace uBuyFirst.Seller
{
    /// <summary>
    /// Represents a deserialized user object with short property names,
    /// mapped from a JSON structure based on the TypeScript SellerUserRaw type.
    /// </summary>
    public class SellerUserRaw
    {
        /// <summary>
        /// Maps to JSON property "a". Represents User.account_active.
        /// Needs conversion from int (0/1) to bool after deserialization.
        /// </summary>
        [JsonProperty("a")]
        public int? AccountActive { get; set; }

        /// <summary>
        /// Maps to JSON property "u". Represents User.userName.
        /// </summary>
        [JsonProperty("u")]
        public string? UserName { get; set; }

        /// <summary>
        /// Maps to JSON property "v". Represents User.verified.
        /// Needs conversion from int (0/1) to bool after deserialization.
        /// </summary>
        [JsonProperty("v")]
        public int? Verified { get; set; }

        /// <summary>
        /// Maps to JSON property "p". Represents User.feedbackPrivate.
        /// Needs conversion from int (0/1) to bool after deserialization.
        /// </summary>
        [JsonProperty("p")]
        public int? FeedbackPrivate { get; set; }

        /// <summary>
        /// Maps to JSON property "tp". Represents User.transactionPercent.
        /// </summary>
        [JsonProperty("tp")]
        public int? TransactionPercent { get; set; }

        /// <summary>
        /// Maps to JSON property "b". Represents User.business.
        /// Needs conversion from int (0/1) to bool after deserialization.
        /// </summary>
        [JsonProperty("b")]
        public int? Business { get; set; }

        /// <summary>
        /// Maps to JSON property "s". Represents User.storeOwner.
        /// Needs conversion from int (0/1) to bool after deserialization.
        /// </summary>
        [JsonProperty("s")]
        public int? StoreOwner { get; set; }

        /// <summary>
        /// Maps to JSON property "sn". Represents User.storeName.
        /// </summary>
        [JsonProperty("sn")]
        public string? StoreName { get; set; }

        /// <summary>
        /// Maps to JSON property "t". Represents User.topRated.
        /// Needs conversion from int (0/1) to bool after deserialization.
        /// </summary>
        [JsonProperty("t")]
        public int? TopRated { get; set; }

        /// <summary>
        /// Maps to JSON property "uc". Represents User.user_web_country_id.
        /// </summary>
        [JsonProperty("uc")]
        public int? UserWebCountryId { get; set; }

        /// <summary>
        /// Maps to JSON property "sc". Represents User.store_web_country_id.
        /// </summary>
        [JsonProperty("sc")]
        public int? StoreWebCountryId { get; set; }

        /// <summary>
        /// Maps to JSON property "rd". Represents User.regDate.
        /// </summary>
        [JsonProperty("rd")]
        public string? RegDate { get; set; }
    }
}
