﻿using System;
using uBuyFirst.Data;
using uBuyFirst.Prefs;
using uBuyFirst.Time;

namespace uBuyFirst.Parsing
{
    static class DateParser
    {
        public static void SetPostedTime(DataList dataList, DateTimeOffset? postedTime)
        {
            if (postedTime.HasValue)
                dataList.StartTimeLocal = TimeZoneInfo.ConvertTime(postedTime.Value, UserSettings.CurrentTimeZoneInfo);
        }

        public static void SetFoundTime(DataList dataList, DateTime? postedTimeUTC)
        {
            if (dataList.FoundTime?.TimeDiff != null)
                return;

            SetFoundTimeWithDelayForced(dataList, postedTimeUTC);
        }

        internal static void SetFoundTimeWithDelayForced(DataList dataList, DateTime? postedTimeUTC)
        {
            DateTimeWithDiff foundTime;
            if (postedTimeUTC.HasValue)
            {
                var foundDelay = Math.Round((DateTime.UtcNow - postedTimeUTC.Value).TotalSeconds, 0);
                foundTime = new DateTimeWithDiff(DateTime.UtcNow, foundDelay);
                dataList.FoundTime = foundTime;
            }
            else
            {
                foundTime = new DateTimeWithDiff(DateTime.UtcNow, null);
                dataList.FoundTime = foundTime;
            }
        }

        public static void SetEndTime_Timeleft(DataList dataList, string endTime)
        {
            var parseEndTime = ParseEndTime(endTime);
            if (parseEndTime != null)
            {
                dataList.TimeLeft = (TimeSpan)(parseEndTime - DateTimeOffset.UtcNow);
            }
        }

        internal static DateTimeOffset? ParseEndTime(string endTime)
        {
            if (DateTimeOffset.TryParse(endTime, out var parsedDateTime))
            {
                var dateUtcFixed = parsedDateTime.ToUniversalTime();
                var dateSortingFixed = dateUtcFixed.AddTicks(new Random().Next(1, 1000));
                return dateSortingFixed;
            }

            return null;
        }

        public static void SetEndTime_Timeleft(DataList dataList, DateTimeOffset endTime)
        {
            dataList.TimeLeft = endTime - DateTimeOffset.UtcNow;
        }
    }
}
