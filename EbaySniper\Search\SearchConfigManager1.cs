﻿using System;
using System.Threading;
using uBuyFirst.Tools;

namespace uBuyFirst.Search
{
    /// <summary>
    /// Manages search configuration settings using a singleton pattern
    /// </summary>
    public class SearchConfigManager
    {
        // Singleton instance with initialization of dependencies after construction
        private static readonly Lazy<SearchConfigManager> s_instance = new(() => 
        {
            var instance = new SearchConfigManager();
            // Delay the initialization of RequestQueueManager until after our instance is created
            return instance;
        });

        // Static constructor to initialize dependencies after singleton creation
        static SearchConfigManager()
        {
            // Ensure the instance is created
            var unused = s_instance.Value;
            // Now that the singleton is fully constructed, we can initialize the RequestQueueManager
            InitializeRequestQueueManager();
        }

        // Configuration
        private Helpers.AdvancedSearchConfig _config;

        // Thread limiters - now managed by RequestQueueManager

        /// <summary>
        /// Gets the singleton instance of the search configuration manager
        /// </summary>
        public static SearchConfigManager Instance => s_instance.Value;

        /// <summary>
        /// Private constructor for singleton pattern
        /// </summary>
        private SearchConfigManager()
        {
            _config = new Helpers.AdvancedSearchConfig();
            // We'll initialize RequestQueueManager later to avoid circular dependency
        }

        /// <summary>
        /// Initializes the RequestQueueManager with our configuration
        /// Called after singleton instantiation to prevent circular dependency
        /// </summary>
        private static void InitializeRequestQueueManager()
        {
            var instance = s_instance.Value;
            // Use the new dedicated method to initialize RequestQueueManager
            // This avoids circular dependency by using a two-phase initialization
            RequestQueueManager.Instance.InitializeFromConfig(instance._config.GetItemDetailsReqMaxThreads);
        }

        #region Configuration Properties

        /// <summary>
        /// Maximum number of Find request threads
        /// </summary>
        public int FindReqMaxThreads
        {
            get => _config.FindReqMaxThreads;
            set
            {
                // Enforce reasonable limits
                if (value > 25)
                    value = 25;
                _config.FindReqMaxThreads = value;
            }
        }

        /// <summary>
        /// Maximum number of GetItem detail request threads
        /// </summary>
        public int GetItemDetailsReqMaxThreads
        {
            get => _config.GetItemDetailsReqMaxThreads;
            set
            {
                if (_config.GetItemDetailsReqMaxThreads != value)
                {
                    _config.GetItemDetailsReqMaxThreads = value;

                    // Update the queue limiter through RequestQueueManager
                    RequestQueueManager.Instance.UpdateLimiters(value);
                }
            }
        }

        /// <summary>
        /// Gets or sets whether to download avatar images
        /// </summary>
        public bool DownloadAvatars
        {
            get => _config.DownloadAvatars;
            set => _config.DownloadAvatars = value;
        }

        /// <summary>
        /// Gets or sets whether to download other images
        /// </summary>
        public bool DownloadOtherImages
        {
            get => _config.DownloadOtherImages;
            set => _config.DownloadOtherImages = value;
        }

        /// <summary>
        /// Gets or sets whether to download description
        /// </summary>
        public bool DownloadDescription
        {
            get => _config.DownloadDescription;
            set => _config.DownloadDescription = value;
        }

        /// <summary>
        /// Gets or sets whether to update item status
        /// </summary>
        public bool UpdateItemStatus
        {
            get => _config.UpdateItemStatus;
            set => _config.UpdateItemStatus = value;
        }

        /// <summary>
        /// Gets or sets whether API is enabled
        /// </summary>
        public bool EnabledApi
        {
            get => _config.EnabledApi;
            set => _config.EnabledApi = value;
        }

        /// <summary>
        /// Gets or sets whether RSS is enabled
        /// </summary>
        public bool EnabledRss
        {
            get => _config.EnabledRss;
            set => _config.EnabledRss = value;
        }

        /// <summary>
        /// Gets or sets whether RSS2 is enabled
        /// </summary>
        public bool EnabledRss2
        {
            get => _config.EnabledRss2;
            set => _config.EnabledRss2 = value;
        }

        /// <summary>
        /// Gets or sets whether to show sold items
        /// </summary>
        public bool ShowSoldItems
        {
            get => _config.ShowSoldItems;
            set => _config.ShowSoldItems = value;
        }

        /// <summary>
        /// Gets or sets whether to use whitespace analyzer
        /// </summary>
        public bool WhiteSpaceAnalyzer
        {
            get => _config.WhiteSpaceAnalyzer;
            set => _config.WhiteSpaceAnalyzer = value;
        }

        #endregion

        #region Semaphore Access

        /// <summary>
        /// Gets the GetItem queue limiter semaphore
        /// </summary>
        public SemaphoreSlim GetItemQueueLimiter => RequestQueueManager.Instance.GetItemQueueLimiter;

        /// <summary>
        /// Resets all queue limiters to their maximum capacities
        /// </summary>
        public void ResetQueueLimiters()
        {
            // Delegate to RequestQueueManager
            RequestQueueManager.Instance.ResetQueueLimiters(_config.GetItemDetailsReqMaxThreads);
        }

        #endregion

        #region Configuration Management

        /// <summary>
        /// Gets the underlying config object for serialization
        /// </summary>
        /// <returns>The advanced search config</returns>
        public Helpers.AdvancedSearchConfig GetConfigForSerialization()
        {
            return _config;
        }

        /// <summary>
        /// Sets the configuration from a loaded config object
        /// </summary>
        /// <param name="config">The config to load</param>
        public void SetConfig(Helpers.AdvancedSearchConfig config)
        {
            if (config == null)
                return;

            _config = config;

            // Apply limits
            if (_config.FindReqMaxThreads > 25)
                _config.FindReqMaxThreads = 25;

            // Update the semaphore through RequestQueueManager
            RequestQueueManager.Instance.UpdateLimiters(_config.GetItemDetailsReqMaxThreads);

            // Ensure at least one search method is enabled
            if (!_config.EnabledApi && !_config.EnabledRss && !_config.EnabledRss2)
                _config.EnabledApi = true;
        }

        /// <summary>
        /// Validates that the configuration is in a valid state
        /// </summary>
        public void ValidateConfig()
        {
            // Ensure at least one search method is enabled
            if (!_config.EnabledApi && !_config.EnabledRss && !_config.EnabledRss2)
                _config.EnabledApi = true;
        }

        #endregion
    }
}
