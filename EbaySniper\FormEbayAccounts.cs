﻿using System;
using System.Diagnostics;
using System.Windows.Forms;
using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;
using eBay.Service.Core.Sdk;
using uBuyFirst.Auth;
using uBuyFirst.Properties;
using uBuyFirst.Tools;

namespace uBuyFirst
{
    public partial class FormEbayAccounts : RibbonForm
    {
        public FormEbayAccounts()
        {
            InitializeComponent();
        }

        private void btnAddAccount_Click(object sender, EventArgs e)
        {
            AddEbayAccount();
        }

        private static void AddEbayAccount()
        {
            try
            {
                if (Form1.EBayAccountsList.Count > 0 && !Form1.LicenseUtility.CurrentLimits.MultipleEbayAccountsEnabled)
                {
                    XtraMessageBox.Show(En_US.Form1_AddEbayAccount_Your_current_subscription_plan_does_not_allow_adding_multiple_eBay_accounts__Please_upgrade_);
                    return;
                }

                var account = Form1.ShowAuthorizeOnEbay();
                if (account != null)
                {
                    bool isNewAccount = true;
                    for (int i = 0; i < Form1.EBayAccountsList.Count; i++)
                    {
                        if (Form1.EBayAccountsList[i].UserName == account.UserName)
                        {
                            Form1.EBayAccountsList[i] = account;
                            isNewAccount = false;
                            break;
                        }
                    }

                    if (isNewAccount)
                    {
                        Form1.EBayAccountsList.Add(account);
                    }
                }
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("AddEbayAccount: ", ex);
            }
        }

        private void RemoveEbayAccount()
        {
            if (listboxEbayAccounts.SelectedIndex > -1)
            {
                EbayAccount ebayAccount = (EbayAccount) listboxEbayAccounts.SelectedItem;

                string userName = "";
                if (ebayAccount != null)
                    userName = ebayAccount.UserName;
                DialogResult dialog = XtraMessageBox.Show(En_US.Form1_btnRemoveEbayAccount_Click_We_will_remove_uBuyFirst_authorization_from_eBay_account__ + userName + @"'", "", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button2);
                if (dialog == DialogResult.Yes)
                {
                    try
                    {
                        //Form1.EBayAccountsList[listboxEbayAccounts.SelectedIndex] = null;
                        Form1.EBayAccountsList.RemoveAt(listboxEbayAccounts.SelectedIndex);
                        XtraMessageBox.Show(En_US.Form1_btnRemoveEbayAccount_Click_We_successfully_removed_uBuyfirst_authorization_from_account__ + userName + @"'");
                    }
                    catch (ApiException ex)
                    {
                        XtraMessageBox.Show(ex.Message);
                    }
                    catch (Exception ex)
                    {
                        ExM.ubuyExceptionHandler("AddEbayAccount: ", ex);
                    }
                }
            }
        }

        private void btnRemoveAccount_Click(object sender, EventArgs e)
        {
            RemoveEbayAccount();
        }

        private void MoveItem(int direction)
        {
            // Checking selected item
            if (listboxEbayAccounts.SelectedItem == null || listboxEbayAccounts.SelectedIndex < 0)
                return; // No selected item - nothing to do

            // Calculate new index using move direction
            int newIndex = listboxEbayAccounts.SelectedIndex + direction;

            // Checking bounds of the range
            if (newIndex < 0 || newIndex >= listboxEbayAccounts.ItemCount)
                return; // Index out of range - nothing to do

            var selectedAccount = Form1.EBayAccountsList[listboxEbayAccounts.SelectedIndex];

            // Removing removable element
            Form1.EBayAccountsList.Remove(selectedAccount);
            // Insert it in new position
            Form1.EBayAccountsList.Insert(newIndex, selectedAccount);
            // Restore selection
            listboxEbayAccounts.SetSelected(newIndex, true);
        }

        private void btnUp_Click(object sender, EventArgs e)
        {
            MoveItem(-1);
        }

        private void btnDown_Click(object sender, EventArgs e)
        {
            MoveItem(1);
        }

        private void FormEbayAccounts_Load(object sender, EventArgs e)
        {
            listboxEbayAccounts.DataSource = Form1.EBayAccountsList;
        }

        private void btnEditAccount_Click(object sender, EventArgs e)
        {
            try
            {
                if (Form1.EBayAccountsList.Count > 0)
                {
                    EbayAccount ebayAccount = (EbayAccount) listboxEbayAccounts.SelectedItem;
                    if (ebayAccount != null)
                        Form1.ShowAuthorizeOnEbay(ebayAccount);
                }
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("AddEbayAccount: ", ex);
            }
        }

        private void FormEbayAccounts_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == 27)
            {
                Close();
            }
        }

        private void hyperlinkLabelControl1_Click(object sender, EventArgs e)
        {
            var hyperLink = (HyperlinkLabelControl) sender;
            if (hyperLink.Tag != null)
                Process.Start(hyperLink.Tag.ToString());
        }
    }
}
