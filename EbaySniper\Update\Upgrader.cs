﻿using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Text.RegularExpressions;

namespace uBuyFirst.Update
{
    public static class Upgrader
    {
        internal static string[] GetDownloadedUpgradeFiles()
        {
            var downloadedUpgrades = new string[] { };
            try
            {
                var tempPath = Path.GetTempPath();
                downloadedUpgrades = Directory.GetFiles(tempPath, "uBuyFirst_Setup.*.exe");
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
            }

            return downloadedUpgrades;
        }

        public static bool ResumeSearchAfterUpgrade;
        public static bool ImmediateUpgradeAvailable;
        public static UpgradeFile? ImmediateUpgradeFile;

        public static void UpgradeImmediately()
        {
            if (ImmediateUpgradeFile != null)
                Process.Start(ImmediateUpgradeFile.FilePath, ImmediateUpgradeFile.SilentUpgrade ? "/silent" : "");
            //Environment.Exit(0);
        }

        public static bool TryUpgrade()
        {
            var downloadedUpgradeFiles = GetDownloadedUpgradeFiles();
            if (downloadedUpgradeFiles.Length == 0)
                return false;

            var latestUpgradePath = FindLatestUpgradeFilePath(downloadedUpgradeFiles);
            var latestUpgradeFile = UpgradeFile.Parse(latestUpgradePath);
            ImmediateUpgradeFile = latestUpgradeFile;
            if (latestUpgradeFile.ImmediateUpgrade && latestUpgradeFile.Version.CompareTo(Assembly.GetEntryAssembly().GetName().Version) == 0)
            {
                ResumeSearchAfterUpgrade = true;
            }

            var upgradeStarted = false;
            if (latestUpgradeFile.Version.CompareTo(Assembly.GetEntryAssembly().GetName().Version) > 0)
            {
                var process = Process.Start(latestUpgradeFile.FilePath, latestUpgradeFile.SilentUpgrade ? "/silent" : "");
                if (process != null)
                {
                    upgradeStarted = true;
                }
            }

            foreach (var upgrade in downloadedUpgradeFiles)
            {
                try
                {
                    File.Delete(upgrade);
                }
                catch (Exception)
                {
                    //XtraMessageBox.Show("Cannot remove setup file:" + LatestUpgradePath);
                    //ExM.ubuyExceptionHandler("Upgrading", ex);
                }
            }

            return upgradeStarted;
        }

        public static string FindLatestUpgradeFilePath(string[] downloadedUpgradeFiles)
        {
            var latestUpgradeFilePath = "";
            foreach (var upgradePath in downloadedUpgradeFiles)
            {
                var versionString = RegexValue(upgradePath, "([0-9]+.[0-9]+.[0-9]+.[0-9]+)");
                if (Version.TryParse(versionString, out _))
                {
                    latestUpgradeFilePath = upgradePath;
                }
            }

            return latestUpgradeFilePath;
        }

        private static string RegexValue(string partPageHtml, string regex)
        {
            if (partPageHtml != null)
            {
                var match = Regex.Match(partPageHtml, regex);
                var str = match.Success ? match.Groups[1].Value : "";
                return Regex.Unescape(str);
            }

            return "";
        }

        public sealed class UpgradeFile
        {
            public Version Version;
            public string FilePath;
            public bool SilentUpgrade;
            public bool ImmediateUpgrade;

            public static UpgradeFile Parse(string filePath)
            {
                var upgrade = new UpgradeFile
                {
                    Version = Version.Parse(RegexValue(filePath, "([0-9]+.[0-9]+.[0-9]+.[0-9]+)")),
                    FilePath = filePath,
                    SilentUpgrade = filePath.Contains(".full"),
                    ImmediateUpgrade = filePath.Contains(".immediate")
                };
                return upgrade;
            }
        }
    }
}
