﻿namespace uBuyFirst
{
    partial class FormTelegram
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormTelegram));
            DevExpress.Utils.SuperToolTip superToolTip1 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem1 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip2 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipTitleItem toolTipTitleItem1 = new DevExpress.Utils.ToolTipTitleItem();
            DevExpress.Utils.ToolTipItem toolTipItem2 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip3 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem3 = new DevExpress.Utils.ToolTipItem();
            DevExpress.Utils.SuperToolTip superToolTip4 = new DevExpress.Utils.SuperToolTip();
            DevExpress.Utils.ToolTipItem toolTipItem4 = new DevExpress.Utils.ToolTipItem();
            this.formAssistant1 = new DevExpress.XtraBars.FormAssistant();
            this.ribbonControl1 = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.barStaticItem1 = new DevExpress.XtraBars.BarStaticItem();
            this.repositoryItemTextEdit1 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.repositoryItemTextEdit2 = new DevExpress.XtraEditors.Repository.RepositoryItemTextEdit();
            this.memoBodyTemplate = new DevExpress.XtraEditors.MemoEdit();
            this.popupMenuColumnNames = new DevExpress.XtraBars.PopupMenu(this.components);
            this.toolTipController1 = new DevExpress.Utils.ToolTipController(this.components);
            this.lnkTelegram = new DevExpress.XtraEditors.HyperlinkLabelControl();
            this.lblTelegramDescription = new DevExpress.XtraEditors.LabelControl();
            this.lblPushbulletToken = new DevExpress.XtraEditors.HyperlinkLabelControl();
            this.txtToken = new DevExpress.XtraEditors.TextEdit();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.btnSave = new DevExpress.XtraEditors.SimpleButton();
            this.btnTestMessage = new DevExpress.XtraEditors.SimpleButton();
            this.btnConnect = new DevExpress.XtraEditors.SimpleButton();
            this.chkSendNotifications = new DevExpress.XtraEditors.CheckEdit();
            this.lblTelegramAccount = new DevExpress.XtraEditors.LabelControl();
            this.lnkTelegramHelp = new DevExpress.XtraEditors.HyperlinkLabelControl();
            this.lblStatusValue = new DevExpress.XtraEditors.LabelControl();
            this.lblStatus = new DevExpress.XtraEditors.LabelControl();
            this.dropDownButton1 = new DevExpress.XtraEditors.DropDownButton();
            this.txtTelegramAccount = new DevExpress.XtraEditors.TextEdit();
            this.behaviorManager1 = new DevExpress.Utils.Behaviors.BehaviorManager(this.components);
            this.btnTelegramInstruction = new DevExpress.XtraEditors.SimpleButton();
            this.btnTelegramAccount = new DevExpress.XtraEditors.SimpleButton();
            this.lblBotDescription = new DevExpress.XtraEditors.LabelControl();
            this.lblPushNoficationSettings = new DevExpress.XtraEditors.LabelControl();
            this.btnApplyToken = new DevExpress.XtraEditors.SimpleButton();
            this.lblBotStatus = new DevExpress.XtraEditors.LabelControl();
            this.lblBotStatusValue = new DevExpress.XtraEditors.LabelControl();
            this.spinEditNumberOfPicturesToAttach = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.lblMaxMessagesPerMin = new DevExpress.XtraEditors.LabelControl();
            this.spinEditMaxMessagesPerMinute = new DevExpress.XtraEditors.SpinEdit();
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.memoBodyTemplate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenuColumnNames)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtToken.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSendNotifications.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTelegramAccount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.behaviorManager1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditNumberOfPicturesToAttach.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMaxMessagesPerMinute.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // ribbonControl1
            // 
            this.ribbonControl1.EmptyAreaImageOptions.ImagePadding = new System.Windows.Forms.Padding(26, 24, 26, 24);
            this.ribbonControl1.ExpandCollapseItem.Id = 0;
            this.ribbonControl1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.ribbonControl1.ExpandCollapseItem,
            this.barStaticItem1});
            this.ribbonControl1.Location = new System.Drawing.Point(0, 0);
            this.ribbonControl1.MaxItemId = 6;
            this.ribbonControl1.Name = "ribbonControl1";
            this.ribbonControl1.OptionsPageCategories.ShowCaptions = false;
            this.ribbonControl1.RepositoryItems.AddRange(new DevExpress.XtraEditors.Repository.RepositoryItem[] {
            this.repositoryItemTextEdit1,
            this.repositoryItemTextEdit2});
            this.ribbonControl1.ShowApplicationButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowDisplayOptionsMenuButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowExpandCollapseButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowPageHeadersMode = DevExpress.XtraBars.Ribbon.ShowPageHeadersMode.Hide;
            this.ribbonControl1.Size = new System.Drawing.Size(577, 32);
            // 
            // barStaticItem1
            // 
            this.barStaticItem1.Id = 5;
            this.barStaticItem1.Name = "barStaticItem1";
            // 
            // repositoryItemTextEdit1
            // 
            this.repositoryItemTextEdit1.AutoHeight = false;
            this.repositoryItemTextEdit1.Name = "repositoryItemTextEdit1";
            // 
            // repositoryItemTextEdit2
            // 
            this.repositoryItemTextEdit2.AutoHeight = false;
            this.repositoryItemTextEdit2.Name = "repositoryItemTextEdit2";
            // 
            // memoBodyTemplate
            // 
            this.memoBodyTemplate.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.memoBodyTemplate.EditValue = resources.GetString("memoBodyTemplate.EditValue");
            this.memoBodyTemplate.Location = new System.Drawing.Point(136, 236);
            this.memoBodyTemplate.Name = "memoBodyTemplate";
            this.memoBodyTemplate.Properties.NullValuePrompt = "ex. Condition: {Condition}, {Condition Description}";
            this.memoBodyTemplate.Size = new System.Drawing.Size(317, 78);
            this.memoBodyTemplate.TabIndex = 3;
            this.memoBodyTemplate.ToolTip = "Use right click menu to choose available fields.";
            // 
            // popupMenuColumnNames
            // 
            this.popupMenuColumnNames.ItemLinks.Add(this.barStaticItem1);
            this.popupMenuColumnNames.Name = "popupMenuColumnNames";
            this.popupMenuColumnNames.OptionsMultiColumn.ColumnCount = 2;
            this.popupMenuColumnNames.Ribbon = this.ribbonControl1;
            this.popupMenuColumnNames.BeforePopup += new System.ComponentModel.CancelEventHandler(this.popupMenuColumnNames_BeforePopup);
            // 
            // toolTipController1
            // 
            this.toolTipController1.AutoPopDelay = 1800000;
            this.toolTipController1.CloseOnClick = DevExpress.Utils.DefaultBoolean.True;
            this.toolTipController1.KeepWhileHovered = true;
            // 
            // lnkTelegram
            // 
            this.lnkTelegram.Cursor = System.Windows.Forms.Cursors.Hand;
            this.lnkTelegram.Location = new System.Drawing.Point(13, 39);
            this.lnkTelegram.Name = "lnkTelegram";
            this.lnkTelegram.Size = new System.Drawing.Size(64, 13);
            this.lnkTelegram.TabIndex = 3;
            this.lnkTelegram.Text = "Telegram.org";
            this.lnkTelegram.ToolTip = "Telegram Register/Login webpage.";
            this.lnkTelegram.Click += new System.EventHandler(this.lnkTelegram_Click);
            // 
            // lblTelegramDescription
            // 
            this.lblTelegramDescription.AllowHtmlString = true;
            this.lblTelegramDescription.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.lblTelegramDescription.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Horizontal;
            this.lblTelegramDescription.Cursor = System.Windows.Forms.Cursors.Default;
            this.lblTelegramDescription.LineVisible = true;
            this.lblTelegramDescription.Location = new System.Drawing.Point(13, 59);
            this.lblTelegramDescription.Name = "lblTelegramDescription";
            this.lblTelegramDescription.Size = new System.Drawing.Size(335, 13);
            toolTipItem1.Appearance.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image")));
            toolTipItem1.Appearance.Options.UseImage = true;
            toolTipItem1.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image1")));
            superToolTip1.Items.Add(toolTipItem1);
            this.lblTelegramDescription.SuperTip = superToolTip1;
            this.lblTelegramDescription.TabIndex = 4;
            this.lblTelegramDescription.Text = "Receive push notification from uBuyFirst via Telegram messaging app.";
            this.lblTelegramDescription.ToolTipController = this.toolTipController1;
            this.lblTelegramDescription.UseMnemonic = false;
            // 
            // lblPushbulletToken
            // 
            this.lblPushbulletToken.Location = new System.Drawing.Point(13, 117);
            this.lblPushbulletToken.Name = "lblPushbulletToken";
            this.lblPushbulletToken.Size = new System.Drawing.Size(79, 13);
            toolTipTitleItem1.Text = "Telegram instruction";
            toolTipItem2.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image2")));
            superToolTip2.Items.Add(toolTipTitleItem1);
            superToolTip2.Items.Add(toolTipItem2);
            this.lblPushbulletToken.SuperTip = superToolTip2;
            this.lblPushbulletToken.TabIndex = 5;
            this.lblPushbulletToken.Text = "HTTP API token:";
            this.lblPushbulletToken.ToolTip = "Create your telegram bot and paste here generated HTTP API token.\r\nClick here to " +
    "open a chat with a @Botfather bot.";
            this.lblPushbulletToken.Click += new System.EventHandler(this.lblPushbulletToken_Click);
            // 
            // txtToken
            // 
            this.txtToken.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtToken.Location = new System.Drawing.Point(136, 115);
            this.txtToken.MenuManager = this.ribbonControl1;
            this.txtToken.Name = "txtToken";
            this.txtToken.Properties.NullValuePrompt = "ex. 1497237319:AAEVeQtdJMNj1Dm55kwDBTQuwogHpd74rEs";
            this.txtToken.Size = new System.Drawing.Size(317, 20);
            this.txtToken.TabIndex = 0;
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(13, 236);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(73, 13);
            this.labelControl2.TabIndex = 9;
            this.labelControl2.Text = "Body template:";
            this.labelControl2.ToolTip = "Use right click menu to choose available fields.";
            // 
            // btnSave
            // 
            this.btnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSave.Appearance.Font = new System.Drawing.Font("Tahoma", 7.8F, System.Drawing.FontStyle.Bold);
            this.btnSave.Appearance.Options.UseFont = true;
            this.btnSave.Location = new System.Drawing.Point(459, 424);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(106, 23);
            this.btnSave.TabIndex = 7;
            this.btnSave.Text = "Save";
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // btnTestMessage
            // 
            this.btnTestMessage.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnTestMessage.Location = new System.Drawing.Point(459, 291);
            this.btnTestMessage.Name = "btnTestMessage";
            this.btnTestMessage.Size = new System.Drawing.Size(106, 23);
            this.btnTestMessage.TabIndex = 4;
            this.btnTestMessage.Text = "Send test message";
            this.btnTestMessage.Click += new System.EventHandler(this.simpleTestMessage_Click);
            // 
            // btnConnect
            // 
            this.btnConnect.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnConnect.Location = new System.Drawing.Point(459, 162);
            this.btnConnect.Name = "btnConnect";
            this.btnConnect.Size = new System.Drawing.Size(106, 23);
            this.btnConnect.TabIndex = 2;
            this.btnConnect.Text = "Connect";
            this.btnConnect.Click += new System.EventHandler(this.btnConnect_Click);
            // 
            // chkSendNotifications
            // 
            this.chkSendNotifications.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.chkSendNotifications.Location = new System.Drawing.Point(133, 424);
            this.chkSendNotifications.MenuManager = this.ribbonControl1;
            this.chkSendNotifications.Name = "chkSendNotifications";
            this.chkSendNotifications.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold);
            this.chkSendNotifications.Properties.Appearance.Options.UseFont = true;
            this.chkSendNotifications.Properties.Caption = "Telegram notifications enabled";
            this.chkSendNotifications.Properties.GlyphAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.chkSendNotifications.Size = new System.Drawing.Size(216, 20);
            this.chkSendNotifications.TabIndex = 6;
            // 
            // lblTelegramAccount
            // 
            this.lblTelegramAccount.Location = new System.Drawing.Point(13, 167);
            this.lblTelegramAccount.Name = "lblTelegramAccount";
            this.lblTelegramAccount.Size = new System.Drawing.Size(89, 13);
            this.lblTelegramAccount.TabIndex = 9;
            this.lblTelegramAccount.Text = "Telegram account:";
            this.lblTelegramAccount.ToolTip = "Your personal telegram username. It will receive push notifications from uBuyFirs" +
    "t.";
            // 
            // lnkTelegramHelp
            // 
            this.lnkTelegramHelp.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.lnkTelegramHelp.Cursor = System.Windows.Forms.Cursors.Hand;
            this.lnkTelegramHelp.Location = new System.Drawing.Point(13, 434);
            this.lnkTelegramHelp.Name = "lnkTelegramHelp";
            this.lnkTelegramHelp.Size = new System.Drawing.Size(21, 13);
            this.lnkTelegramHelp.TabIndex = 3;
            this.lnkTelegramHelp.Text = "Help";
            this.lnkTelegramHelp.ToolTip = "https://ubuyfirst.com/docs/telegram-setup/";
            this.lnkTelegramHelp.Click += new System.EventHandler(this.lnkTelegramHelp_Click);
            // 
            // lblStatusValue
            // 
            this.lblStatusValue.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.lblStatusValue.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical;
            this.lblStatusValue.Location = new System.Drawing.Point(136, 187);
            this.lblStatusValue.Name = "lblStatusValue";
            this.lblStatusValue.Size = new System.Drawing.Size(317, 13);
            this.lblStatusValue.TabIndex = 21;
            this.lblStatusValue.Text = " ";
            this.lblStatusValue.ToolTip = "Displays Pushbullet limits state";
            // 
            // lblStatus
            // 
            this.lblStatus.Location = new System.Drawing.Point(13, 187);
            this.lblStatus.Name = "lblStatus";
            this.lblStatus.Size = new System.Drawing.Size(86, 13);
            this.lblStatus.TabIndex = 21;
            this.lblStatus.Text = "Connection state:";
            this.lblStatus.ToolTip = "Displays Pushbullet limits state";
            // 
            // dropDownButton1
            // 
            this.dropDownButton1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.dropDownButton1.DropDownArrowStyle = DevExpress.XtraEditors.DropDownArrowStyle.Show;
            this.dropDownButton1.DropDownControl = this.popupMenuColumnNames;
            this.dropDownButton1.Location = new System.Drawing.Point(136, 320);
            this.dropDownButton1.MenuManager = this.ribbonControl1;
            this.dropDownButton1.Name = "dropDownButton1";
            this.dropDownButton1.Size = new System.Drawing.Size(67, 23);
            this.dropDownButton1.TabIndex = 22;
            this.dropDownButton1.Text = "Fields";
            // 
            // txtTelegramAccount
            // 
            this.txtTelegramAccount.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtTelegramAccount.Location = new System.Drawing.Point(136, 166);
            this.txtTelegramAccount.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.txtTelegramAccount.MenuManager = this.ribbonControl1;
            this.txtTelegramAccount.Name = "txtTelegramAccount";
            this.txtTelegramAccount.Properties.NullValuePrompt = "my_user_name";
            this.txtTelegramAccount.Properties.UseMaskAsDisplayFormat = true;
            this.txtTelegramAccount.Properties.EditValueChanged += new System.EventHandler(this.txtTelegramAccount_Properties_EditValueChanged);
            this.txtTelegramAccount.Size = new System.Drawing.Size(317, 20);
            this.txtTelegramAccount.TabIndex = 1;
            // 
            // btnTelegramInstruction
            // 
            this.btnTelegramInstruction.AllowFocus = false;
            this.btnTelegramInstruction.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.btnTelegramInstruction.Appearance.BackColor2 = System.Drawing.Color.Transparent;
            this.btnTelegramInstruction.Appearance.Options.UseBackColor = true;
            this.btnTelegramInstruction.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.UltraFlat;
            this.btnTelegramInstruction.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("btnTelegramInstruction.ImageOptions.SvgImage")));
            this.btnTelegramInstruction.ImageOptions.SvgImageSize = new System.Drawing.Size(16, 16);
            this.btnTelegramInstruction.Location = new System.Drawing.Point(109, 113);
            this.btnTelegramInstruction.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnTelegramInstruction.Name = "btnTelegramInstruction";
            this.btnTelegramInstruction.Size = new System.Drawing.Size(22, 21);
            toolTipItem3.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image3")));
            superToolTip3.Items.Add(toolTipItem3);
            this.btnTelegramInstruction.SuperTip = superToolTip3;
            this.btnTelegramInstruction.TabIndex = 26;
            this.btnTelegramInstruction.ToolTipController = this.toolTipController1;
            this.btnTelegramInstruction.Click += new System.EventHandler(this.btnTelegramInstruction_Click);
            // 
            // btnTelegramAccount
            // 
            this.btnTelegramAccount.AllowFocus = false;
            this.btnTelegramAccount.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.btnTelegramAccount.Appearance.BackColor2 = System.Drawing.Color.Transparent;
            this.btnTelegramAccount.Appearance.Options.UseBackColor = true;
            this.btnTelegramAccount.ButtonStyle = DevExpress.XtraEditors.Controls.BorderStyles.UltraFlat;
            this.btnTelegramAccount.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("btnTelegramAccount.ImageOptions.SvgImage")));
            this.btnTelegramAccount.ImageOptions.SvgImageSize = new System.Drawing.Size(16, 16);
            this.btnTelegramAccount.Location = new System.Drawing.Point(109, 164);
            this.btnTelegramAccount.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnTelegramAccount.Name = "btnTelegramAccount";
            this.btnTelegramAccount.Size = new System.Drawing.Size(22, 21);
            toolTipItem4.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("resource.Image4")));
            superToolTip4.Items.Add(toolTipItem4);
            this.btnTelegramAccount.SuperTip = superToolTip4;
            this.btnTelegramAccount.TabIndex = 26;
            this.btnTelegramAccount.Click += new System.EventHandler(this.btnTelegramAccount_Click);
            // 
            // lblBotDescription
            // 
            this.lblBotDescription.Appearance.Font = new System.Drawing.Font("Tahoma", 7.8F, System.Drawing.FontStyle.Bold);
            this.lblBotDescription.Appearance.Options.UseFont = true;
            this.lblBotDescription.Location = new System.Drawing.Point(13, 93);
            this.lblBotDescription.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.lblBotDescription.Name = "lblBotDescription";
            this.lblBotDescription.Size = new System.Drawing.Size(109, 12);
            this.lblBotDescription.TabIndex = 28;
            this.lblBotDescription.Text = "Telegram bot settings";
            // 
            // lblPushNoficationSettings
            // 
            this.lblPushNoficationSettings.Appearance.Font = new System.Drawing.Font("Tahoma", 7.8F, System.Drawing.FontStyle.Bold);
            this.lblPushNoficationSettings.Appearance.Options.UseFont = true;
            this.lblPushNoficationSettings.Location = new System.Drawing.Point(13, 217);
            this.lblPushNoficationSettings.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.lblPushNoficationSettings.Name = "lblPushNoficationSettings";
            this.lblPushNoficationSettings.Size = new System.Drawing.Size(94, 12);
            this.lblPushNoficationSettings.TabIndex = 29;
            this.lblPushNoficationSettings.Text = "Notification format";
            // 
            // btnApplyToken
            // 
            this.btnApplyToken.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnApplyToken.Location = new System.Drawing.Point(459, 111);
            this.btnApplyToken.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.btnApplyToken.Name = "btnApplyToken";
            this.btnApplyToken.Size = new System.Drawing.Size(106, 23);
            this.btnApplyToken.TabIndex = 31;
            this.btnApplyToken.Text = "Apply";
            this.btnApplyToken.Click += new System.EventHandler(this.btnApplyToken_Click);
            // 
            // lblBotStatus
            // 
            this.lblBotStatus.Location = new System.Drawing.Point(13, 136);
            this.lblBotStatus.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.lblBotStatus.Name = "lblBotStatus";
            this.lblBotStatus.Size = new System.Drawing.Size(48, 13);
            this.lblBotStatus.TabIndex = 32;
            this.lblBotStatus.Text = "Bot state:";
            // 
            // lblBotStatusValue
            // 
            this.lblBotStatusValue.Location = new System.Drawing.Point(136, 136);
            this.lblBotStatusValue.Margin = new System.Windows.Forms.Padding(3, 2, 3, 2);
            this.lblBotStatusValue.Name = "lblBotStatusValue";
            this.lblBotStatusValue.Size = new System.Drawing.Size(24, 13);
            this.lblBotStatusValue.TabIndex = 32;
            this.lblBotStatusValue.Text = "        ";
            // 
            // spinEditNumberOfPicturesToAttach
            // 
            this.spinEditNumberOfPicturesToAttach.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.spinEditNumberOfPicturesToAttach.EditValue = new decimal(new int[] {
            0,
            0,
            0,
            0});
            this.spinEditNumberOfPicturesToAttach.Location = new System.Drawing.Point(301, 391);
            this.spinEditNumberOfPicturesToAttach.MenuManager = this.ribbonControl1;
            this.spinEditNumberOfPicturesToAttach.Name = "spinEditNumberOfPicturesToAttach";
            this.spinEditNumberOfPicturesToAttach.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.spinEditNumberOfPicturesToAttach.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.spinEditNumberOfPicturesToAttach.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.spinEditNumberOfPicturesToAttach.Properties.MaskSettings.Set("mask", "d");
            this.spinEditNumberOfPicturesToAttach.Properties.MaskSettings.Set("autoHideDecimalSeparator", true);
            this.spinEditNumberOfPicturesToAttach.Properties.MaxValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditNumberOfPicturesToAttach.Size = new System.Drawing.Size(48, 20);
            this.spinEditNumberOfPicturesToAttach.TabIndex = 34;
            this.spinEditNumberOfPicturesToAttach.ToolTip = "Set a maximum number of pictures in a Telegram message.";
            this.spinEditNumberOfPicturesToAttach.EditValueChanged += new System.EventHandler(this.spinEditNumberOfPicturesToAttach_EditValueChanged);
            // 
            // labelControl1
            // 
            this.labelControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.labelControl1.Location = new System.Drawing.Point(133, 394);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(100, 13);
            this.labelControl1.TabIndex = 9;
            this.labelControl1.Text = "Attach item pictures:";
            this.labelControl1.ToolTip = "Set a maximum number of pictures in a Telegram message.";
            // 
            // lblMaxMessagesPerMin
            // 
            this.lblMaxMessagesPerMin.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.lblMaxMessagesPerMin.Location = new System.Drawing.Point(133, 364);
            this.lblMaxMessagesPerMin.Name = "lblMaxMessagesPerMin";
            this.lblMaxMessagesPerMin.Size = new System.Drawing.Size(128, 13);
            this.lblMaxMessagesPerMin.TabIndex = 9;
            this.lblMaxMessagesPerMin.Text = "Max messages per minute:";
            this.lblMaxMessagesPerMin.ToolTip = resources.GetString("lblMaxMessagesPerMin.ToolTip");
            // 
            // spinEditMaxMessagesPerMinute
            // 
            this.spinEditMaxMessagesPerMinute.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.spinEditMaxMessagesPerMinute.EditValue = new decimal(new int[] {
            1200,
            0,
            0,
            0});
            this.spinEditMaxMessagesPerMinute.Location = new System.Drawing.Point(301, 360);
            this.spinEditMaxMessagesPerMinute.Name = "spinEditMaxMessagesPerMinute";
            this.spinEditMaxMessagesPerMinute.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.spinEditMaxMessagesPerMinute.Properties.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.spinEditMaxMessagesPerMinute.Properties.EditFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
            this.spinEditMaxMessagesPerMinute.Properties.Increment = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditMaxMessagesPerMinute.Properties.MaskSettings.Set("mask", "d");
            this.spinEditMaxMessagesPerMinute.Properties.MaskSettings.Set("autoHideDecimalSeparator", true);
            this.spinEditMaxMessagesPerMinute.Properties.MaxValue = new decimal(new int[] {
            10000,
            0,
            0,
            0});
            this.spinEditMaxMessagesPerMinute.Properties.MinValue = new decimal(new int[] {
            15,
            0,
            0,
            0});
            this.spinEditMaxMessagesPerMinute.Size = new System.Drawing.Size(48, 20);
            this.spinEditMaxMessagesPerMinute.TabIndex = 34;
            this.spinEditMaxMessagesPerMinute.ToolTip = resources.GetString("spinEditMaxMessagesPerMinute.ToolTip");
            this.spinEditMaxMessagesPerMinute.EditValueChanged += new System.EventHandler(this.spinEditMaxMessagesPerMinute_EditValueChanged);
            // 
            // FormTelegram
            // 
            this.Appearance.BackColor = System.Drawing.Color.Transparent;
            this.Appearance.BorderColor = System.Drawing.Color.Transparent;
            this.Appearance.ForeColor = System.Drawing.Color.Transparent;
            this.Appearance.Options.UseBackColor = true;
            this.Appearance.Options.UseBorderColor = true;
            this.Appearance.Options.UseForeColor = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(577, 461);
            this.Controls.Add(this.spinEditMaxMessagesPerMinute);
            this.Controls.Add(this.spinEditNumberOfPicturesToAttach);
            this.Controls.Add(this.lblBotStatusValue);
            this.Controls.Add(this.lblBotStatus);
            this.Controls.Add(this.btnApplyToken);
            this.Controls.Add(this.lblPushNoficationSettings);
            this.Controls.Add(this.lblBotDescription);
            this.Controls.Add(this.btnTelegramAccount);
            this.Controls.Add(this.btnTelegramInstruction);
            this.Controls.Add(this.txtTelegramAccount);
            this.Controls.Add(this.dropDownButton1);
            this.Controls.Add(this.lblStatus);
            this.Controls.Add(this.lblStatusValue);
            this.Controls.Add(this.chkSendNotifications);
            this.Controls.Add(this.btnConnect);
            this.Controls.Add(this.btnTestMessage);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.lblTelegramAccount);
            this.Controls.Add(this.lblMaxMessagesPerMin);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.memoBodyTemplate);
            this.Controls.Add(this.txtToken);
            this.Controls.Add(this.lblPushbulletToken);
            this.Controls.Add(this.lblTelegramDescription);
            this.Controls.Add(this.lnkTelegramHelp);
            this.Controls.Add(this.lnkTelegram);
            this.Controls.Add(this.ribbonControl1);
            this.FormBorderEffect = DevExpress.XtraEditors.FormBorderEffect.None;
            this.IconOptions.Icon = ((System.Drawing.Icon)(resources.GetObject("FormTelegram.IconOptions.Icon")));
            this.Name = "FormTelegram";
            this.Ribbon = this.ribbonControl1;
            this.Text = "Telegram";
            this.Shown += new System.EventHandler(this.FormTelegram_Shown);
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.repositoryItemTextEdit2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.memoBodyTemplate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.popupMenuColumnNames)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtToken.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.chkSendNotifications.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtTelegramAccount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.behaviorManager1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditNumberOfPicturesToAttach.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditMaxMessagesPerMinute.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.FormAssistant formAssistant1;
        private DevExpress.XtraBars.Ribbon.RibbonControl ribbonControl1;
        private DevExpress.XtraEditors.HyperlinkLabelControl lnkTelegram;
        private DevExpress.XtraEditors.LabelControl lblTelegramDescription;
        private DevExpress.XtraEditors.HyperlinkLabelControl lblPushbulletToken;
        private DevExpress.XtraEditors.TextEdit txtToken;
        private DevExpress.XtraEditors.MemoEdit memoBodyTemplate;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SimpleButton btnSave;
        private DevExpress.XtraEditors.SimpleButton btnTestMessage;
        private DevExpress.XtraEditors.SimpleButton btnConnect;
        private DevExpress.XtraEditors.CheckEdit chkSendNotifications;
        private DevExpress.XtraEditors.LabelControl lblTelegramAccount;
        private DevExpress.Utils.ToolTipController toolTipController1;
        private DevExpress.XtraBars.PopupMenu popupMenuColumnNames;
        private DevExpress.XtraBars.BarStaticItem barStaticItem1;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit1;
        private DevExpress.XtraEditors.Repository.RepositoryItemTextEdit repositoryItemTextEdit2;
        private DevExpress.XtraEditors.HyperlinkLabelControl lnkTelegramHelp;
        private DevExpress.XtraEditors.LabelControl lblStatusValue;
        private DevExpress.XtraEditors.LabelControl lblStatus;
        private DevExpress.XtraEditors.DropDownButton dropDownButton1;
        private DevExpress.XtraEditors.TextEdit txtTelegramAccount;
        private DevExpress.Utils.Behaviors.BehaviorManager behaviorManager1;
        private DevExpress.XtraEditors.SimpleButton btnTelegramInstruction;
        private DevExpress.XtraEditors.SimpleButton btnTelegramAccount;
        private DevExpress.XtraEditors.LabelControl lblBotDescription;
        private DevExpress.XtraEditors.LabelControl lblPushNoficationSettings;
        private DevExpress.XtraEditors.SimpleButton btnApplyToken;
        private DevExpress.XtraEditors.LabelControl lblBotStatus;
        private DevExpress.XtraEditors.LabelControl lblBotStatusValue;
        private DevExpress.XtraEditors.SpinEdit spinEditNumberOfPicturesToAttach;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.LabelControl lblMaxMessagesPerMin;
        private DevExpress.XtraEditors.SpinEdit spinEditMaxMessagesPerMinute;
    }
}
