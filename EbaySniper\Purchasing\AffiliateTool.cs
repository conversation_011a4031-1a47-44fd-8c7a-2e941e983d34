﻿using System.Net;
using uBuyFirst.Intl;
using uBuyFirst.Prefs;
using uBuyFirst.Tools;

namespace uBuyFirst.Purchasing
{
    internal class AffiliateTool
    {
        private static string? GetCustomIDShort(string itemID, string actionType)
        {

            if (!actionType.Contains("Offer"))
            {
                return WebUtility.UrlEncode($"{itemID};{ProgramState.HWID};{ProgramState.SerialNumberShort};{actionType}");
            }

            return $"{itemID};{ProgramState.HWID};{ProgramState.SerialNumberShort};{actionType}";
        }

        public static string? GetCustomIDLicenseLong(string itemID, string affiliateAction)
        {

            if (!affiliateAction.Contains("Offer"))
            {
                return WebUtility.UrlEncode($"{itemID};{ProgramState.HWID};{ProgramState.SerialNumber};{affiliateAction}");
            }

            return $"{itemID};{ProgramState.HWID};{ProgramState.SerialNumber};{affiliateAction}";
        }

        private static string GetToolID(string toolName)
        {
            switch (toolName)
            {
                case "Browser":
                    return "10001";
                
                case "Watch":
                    return "10001";

                case "Checkout":
                    return "10011";

                case "PPCC":
                    return "10003";
            }

            return "10008";
        }

        public static string GetAffiliateParams(string itemID, EBaySite ebaySite, string toolId)
        {
            if (Program.AffiliateOff)
            {
                return "";
            }

            var rotationID = Helpers.RegexValue(ebaySite.Roverlink, "rover/1/([0-9-]+)/");

            var mkevt = "mkevt=1";
            var mkcid = "&mkcid=1";
            var mkrid = "&mkrid=" + rotationID;
            var campid = "&campid=" + Config.CampaignID;
            var toolid = "&toolid=" + GetToolID(toolId);
            var customid = "&customid=" + GetCustomIDShort(itemID, toolId);

            var affiliateParams = "";
            affiliateParams += mkevt;
            affiliateParams += mkcid;
            affiliateParams += mkrid;
            affiliateParams += campid;
            affiliateParams += toolid;
            affiliateParams += customid;

            return affiliateParams;
        }
        public static string GetItemAffiliateLink(string itemID, EBaySite eBaySite, string toolId)
        {
            var url = $"https://www.{eBaySite.Domain}/itm/{itemID}/";

            var affiliateParams = GetAffiliateParams(itemID, eBaySite, toolId);
            if (!string.IsNullOrEmpty(affiliateParams))
                url += "?" + affiliateParams;

            return url;
        }
    }
}
