﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="FormNewKeyword_btnSave_Click_Keyword_with_the_same_Alias_already_exists" xml:space="preserve">
    <value>Keyword with the same Alias already exists. Please rename and try again.</value>
    <comment>Warning in case user tries to create a keyword, but the keyword with the same alias already exists</comment>
  </data>
  <data name="FormNewKeyword_btnSave_Click_test" xml:space="preserve">
    <value>test</value>
  </data>
  <data name="FormNewKeyword_SearchKeywordSave_Please__enter_a_keyword" xml:space="preserve">
    <value>Please, enter a keyword</value>
    <comment>User has't entered keyword</comment>
  </data>
  <data name="FormNewKeyword_SearchKeywordSave_Please__enter_keyword_Name" xml:space="preserve">
    <value>Please, enter Alias name and try again.</value>
  </data>
  <data name="FormNewKeyword_SearchKeywordSave_Please__enter_maximum_Price" xml:space="preserve">
    <value>Please, enter maximum price and try again.</value>
  </data>
  <data name="FormNewKeyword_SearchKeywordSave_Please__enter_minimum_Price" xml:space="preserve">
    <value>Please, enter minimum price and try again.</value>
  </data>
  <data name="FormNewKeyword_SearchKeywordSave_Please__provide_a_Zip_code" xml:space="preserve">
    <value>Please, provide a Zip code</value>
  </data>
  <data name="FormNewKeyword_SearchKeywordSave_Please__use_no_more_than_3_categories" xml:space="preserve">
    <value>Please, use no more than 3 categories</value>
  </data>
  <data name="ActivationForm_ActivationForm_Trial__Days_left__" xml:space="preserve">
    <value>Trial. Days left: </value>
    <comment>Trial status</comment>
  </data>
  <data name="ActivationForm_ActivateButton_Click_Serial_Number_is_required" xml:space="preserve">
    <value>Serial Number is required</value>
    <comment>User hasn't entered serial number</comment>
  </data>
  <data name="Form1_PopupFromTray_Hide" xml:space="preserve">
    <value>Hide</value>
    <comment>Tray icon menu item
</comment>
  </data>
  <data name="Form1_BackgroundWorker1RunWorkerCompleted_Start" xml:space="preserve">
    <value>Start</value>
    <comment>Start button</comment>
  </data>
  <data name="Form1_BtnMainStartClick_Please__add_a_keyword" xml:space="preserve">
    <value>No keywords setup. Create keyword searches and retry.</value>
    <comment>Clicked Start button, but there is no keyword created</comment>
  </data>
  <data name="Form1_BtnMainStartClick_Please__enable_at_least_one_keyword" xml:space="preserve">
    <value>No keywords are active. Activate keywords and retry.</value>
    <comment>Clicked start but there are no keywords enabled</comment>
  </data>
  <data name="Form1_BtnMainStartClick_" xml:space="preserve">
    <value>No valid eBay authorization token found.
Please, link you eBay account with uBuyFirst.</value>
    <comment>Clicked start, but there is no ebay account added to program.</comment>
  </data>
  <data name="Form1_BtnMainStartClick_Stop" xml:space="preserve">
    <value>Stop</value>
    <comment>"Stop" text on a Start button</comment>
  </data>
  <data name="Form1_BtnMainStartClick_Stopping" xml:space="preserve">
    <value>Stopping</value>
    <comment>On a start button</comment>
  </data>
  <data name="Form1_Form1_Build__v_" xml:space="preserve">
    <value>Build: v.</value>
    <comment>Label showing build version</comment>
  </data>
  <data name="Form1_Framework_Required" xml:space="preserve">
    <value>.NET Framework v4.6 required
You can get it at https://www.microsoft.com/en-us/download/details.aspx?id=48130
Open the website now?</value>
    <comment>User doesn't have proper .NET framework version. Showing download dialog</comment>
  </data>
  <data name="Form1MainStart3SearchingPausedDueToInactivityIdleTimeoutSettingContinue" xml:space="preserve">
    <value>Searching paused due to inactivity. (Idle Timeout setting)
Continue?</value>
    <comment>Dialog saying uBuyFirst was paused due inactivity</comment>
  </data>
  <data name="Form1_FindRequestsApi_Provide_a_valid_Zip_code_for_keyword___" xml:space="preserve">
    <value>No Zipcode found. Enter the zipcode where your items will ship to. "</value>
  </data>
  <data name="Form1_ShowPlaceOffer_Listing_is_not_available" xml:space="preserve">
    <value>Listing is no longer available.</value>
    <comment>User tried to purchase item that is not available anymore</comment>
  </data>
  <data name="Form1_isTokenValid_eBay_account_was_removed_from_accounts_list" xml:space="preserve">
    <value>eBay account was removed from accounts list</value>
    <comment>Program removed invalid ebay account from ebay account list</comment>
  </data>
  <data name="Form1_Form1_Shown_License__Trial__Days_left__" xml:space="preserve">
    <value>License: Trial. Days left: </value>
  </data>
  <data name="Form1_SearchKeywordSave_Please__enter_keyword_Name" xml:space="preserve">
    <value>Please, enter Alias name and try again.</value>
  </data>
  <data name="Form1_SearchKeywordSave_Please__enter_a_keyword" xml:space="preserve">
    <value>Please, enter a keyword</value>
  </data>
  <data name="Form1_SearchKeywordSave_Please__enter_minimum_Price" xml:space="preserve">
    <value>Please, enter minimum price and try again.</value>
  </data>
  <data name="Form1_SearchKeywordSave_Please__enter_maximum_Price" xml:space="preserve">
    <value>Please, enter maximum price and try again.</value>
  </data>
  <data name="Form1_SearchKeywordSave_Please__use_no_more_than_3_categories" xml:space="preserve">
    <value>A maximum of 3 categories are allowed for each search term. Please edit and try again.</value>
  </data>
  <data name="Form1_SearchKeywordSave_Your_current_subscription_plan_doesn_t_allow_more_than__0__search_terms__Please_upgrade_" xml:space="preserve">
    <value>Your current subscription plan doesn't allow more than {0} search terms. Please upgrade subscription or try combining searches.</value>
  </data>
  <data name="Form1_SearchKeywordEditCreate_Your_current_subscription_plan_doesn_t_allow_more_than__0__search_terms__Please_upgrade_" xml:space="preserve">
    <value>Your current subscription plan doesn't allow more than {0} search terms. Please upgrade subscription or try combining searches.</value>
  </data>
  <data name="Form1_ShowActivationWindow_Please__restart_the_application__" xml:space="preserve">
    <value>Please, close application and restart for subscription settings to take effect.</value>
  </data>
  <data name="Form1_dgv1_ColumnHeaderMouseClick_Do_you_want_to_sort_this_column_" xml:space="preserve">
    <value>Do you want to sort this column?</value>
  </data>
  <data name="Form1_TrayIcon_Click_Show" xml:space="preserve">
    <value>Show</value>
  </data>
  <data name="Form1_AddEbayAccount_Your_current_subscription_plan_does_not_allow_adding_multiple_eBay_accounts__Please_upgrade_" xml:space="preserve">
    <value>Your current subscription plan does not allow adding multiple eBay accounts. Please upgrade.</value>
  </data>
  <data name="FormAuth_btnEbayDoAuth_Click_You_must_enter_credentials_from_http___sandbox_ebay_com___NOT_from_ebay_com__Username_starts_with_testuser__" xml:space="preserve">
    <value>You must enter credentials from http://sandbox.ebay.com , NOT from ebay.com. Username starts with testuser_*</value>
  </data>
  <data name="FormAuth_btnEbayAuthConfirm_Click_" xml:space="preserve">
    <value>Session ID not found
Please, complete Step 1.</value>
    <comment>Authorizing ebay account</comment>
  </data>
  <data name="FormAuth_FormAuth_Shown_Verifying_token_status___" xml:space="preserve">
    <value>Verifying token status...</value>
    <comment>Authorizing ebay account</comment>
  </data>
  <data name="FormAuth_FormAuth_Shown_Establishing_connection_with_eBay_server___" xml:space="preserve">
    <value>Establishing connection with eBay server...</value>
  </data>
  <data name="FormAuth_FormAuth_Shown_Connection_failed__Couldn_t_fetch_session_ID_" xml:space="preserve">
    <value>Connection failed. Couldn't fetch session ID.</value>
  </data>
  <data name="FormAuth_FormAuth_Shown_Connection_established_" xml:space="preserve">
    <value>Connection established.</value>
  </data>
  <data name="FormBid_btnPurchase_Click_Do_you_want_to_purchase_this_item_" xml:space="preserve">
    <value>Buy this item now?</value>
    <comment>Buying ebay item</comment>
  </data>
  <data name="LicenseUtility_ActivateLicense_Thank_you__Please__restart_the_application_" xml:space="preserve">
    <value>Thank you. Please, restart the application.</value>
  </data>
  <data name="Form1_LoadSettings_" xml:space="preserve">
    <value>Couldn't read config.cfg. Remove it and try again {0}</value>
  </data>
  <data name="Form1_ShowRegDialog_" xml:space="preserve">
    <value>Success! : License key activated</value>
    <comment>License verified</comment>
  </data>
  <data name="Form1_GetCategorySpecifics_" xml:space="preserve">
    <value>No valid eBay authorization token found.
Please, link you eBay account with uBuyFirst.</value>
    <comment>Clicked start without associating ebay account first</comment>
  </data>
  <data name="Form1_btnRemoveEbayAccount_Click_We_successfully_removed_uBuyfirst_authorization_from_account__" xml:space="preserve">
    <value>Your eBay account token has been removed successfully from uBuyFirst - '</value>
    <comment>Confirmation that we removed and dissosiated ubuyfirst from ebay.</comment>
  </data>
  <data name="Form1_btnRemoveEbayAccount_Click_We_will_remove_uBuyFirst_authorization_from_eBay_account__" xml:space="preserve">
    <value>Remove eBay account token from uBuyFirst - '</value>
    <comment>Confirmation window when user removes eBay account from uBuyfirst
</comment>
  </data>
  <data name="FiltersForm_UserClosingForm_Do_you_want_to_save_filters_" xml:space="preserve">
    <value>Do you want to save filters?</value>
  </data>
  <data name="Cant_get_session_id_for_ebay_auth" xml:space="preserve">
    <value>Can't get session ID. 
1. Check your internet connection 
2. Try again later. 
3. Contact support.</value>
    <comment>Can't prepare session id for ebay authorization</comment>
  </data>
  <data name="FormAuth_btnEbayDoAuth_Click_Please__choose_your_eBay_website" xml:space="preserve">
    <value>Please, choose your eBay website</value>
  </data>
  <data name="FormAuth_btnEbayAuthConfirm_Click_2" xml:space="preserve">
    <value>Can't fetch authentication token. 
1. Check your internet connection 
2. Try again later. 
3. Contact support.</value>
  </data>
  <data name="FormNewKeyword_SearchKeywordSave_Please__use_no_more_than_10_sellers__You_have___X" xml:space="preserve">
    <value>Please, use no more than 10 sellers. You have - </value>
  </data>
  <data name="FormCustomColumns_btnFindCustomColumns_Click_Please__provide_a_numeric_Category_ID" xml:space="preserve">
    <value>Please, provide a numeric Category ID</value>
  </data>
  <data name="Form1_Form1_Shown_Can_t_connect_to_ubuyfirst_com_" xml:space="preserve">
    <value>Can't connect to ubuyfirst.com.</value>
  </data>
  <data name="Form1_BtnMainStartClick_Not_Authenticated_Warning" xml:space="preserve">
    <value>You have not authenticated your eBay account with uBuyFirst.
You won't be able to purchase "commit to buy" items directly through the application.
Authenticate now? </value>
    <comment>		&lt;!--It is highly recommended that you add your eBay buying account(s) before searching!--&gt;
		&lt;!-- will still be able to search, but many features will not be available including but not limited to category item specifics, quantity, etc. and your ability to find items first.--&gt;
</comment>
  </data>
  <data name="FormXfilters_btnAccept_Click_Couldn_t_validate_the_condition_rule_Try_to_change_it_" xml:space="preserve">
    <value>Couldn't validate the condition rule. Try to change it.</value>
  </data>
  <data name="FormTrialSubscriptionPrompt_DaysLeft" xml:space="preserve">
    <value>Your Trial Expires in {0} Days</value>
    <comment>Trial Expiration Day Status</comment>
  </data>
  <data name="FormTrialSubscriptionPrompt_ContinueTrial" xml:space="preserve">
    <value>Continue Trial</value>
  </data>
  <data name="FormTrialSubscriptionPrompt_BuyNow" xml:space="preserve">
    <value>Buy Now</value>
  </data>
  <data name="FormTrialSubscriptionPrompt_EnterSoftwareKey" xml:space="preserve">
    <value>Enter Software Key</value>
  </data>
  <data name="FormTrialSubscriptionPrompt_TrialText" xml:space="preserve">
    <value>To get more out of your uBuyFirst trial, view our </value>
  </data>
  <data name="FormTrialSubscriptionPrompt_TrialLinkText" xml:space="preserve">
    <value>Free Tutorials</value>
  </data>
  <data name="TrialSubscriptionStatusLoading" xml:space="preserve">
    <value>Trial Status Loading..</value>
  </data>
</root>