﻿using System;
using System.Data;
using DevExpress.Data.Filtering.Helpers;
using DevExpress.XtraEditors.Filtering;
using DevExpress.XtraEditors.Repository;

namespace uBuyFirst.Filters
{
    internal class CustomFilterColumn : UnboundFilterColumn
    {
        public CustomFilterColumn(string columnCaption, string fieldName, Type columnType, RepositoryItem columnEdit, FilterColumnClauseClass clauseClass) : base(columnCaption, fieldName, columnType, columnEdit, clauseClass)
        {
        }

        public override bool AllowItemCollectionEditor => true;

        public override RepositoryItem CreateItemCollectionEditor()
        {
            DataTable table = new DataTable();
            table.Columns.Add(new DataColumn("Name", typeof(string)));
            table.Columns.Add(new DataColumn("Value", typeof(string)));

            table.Rows.Add("New", "New");
            table.Rows.Add("New other", "New other");
            table.Rows.Add("New with defects", "New with defects");
            table.Rows.Add("Manufacturer refurbished", "Manufacturer refurbished");
            table.Rows.Add("Excellent Good Refurbished", "Excellent Good Refurbished");
            table.Rows.Add("Very Good Refurbished", "Very Good Refurbished");
            table.Rows.Add("Good Refurbished", "Good Refurbished");
            table.Rows.Add("Seller refurbished", "Seller refurbished");
            table.Rows.Add("Used", "Used");
            table.Rows.Add("Very good", "Very good");
            table.Rows.Add("Good", "Good");
            table.Rows.Add("Acceptable", "Acceptable");
            table.Rows.Add("For parts or not working", "For parts or not working");
            table.Rows.Add("Acceptable", "Acceptable");
            table.Rows.Add("Unspecified", "Unspecified");

            var checkEditor = new FilterRepositoryItemCheckedComboBoxEdit();
            for (int i = 0; i < table.Rows.Count; i++)
            {
                checkEditor.Items.Add(table.Rows[i][0], table.Rows[i][0].ToString());
            }

            return checkEditor;
        }
    }
}
