﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Windows.Forms;

namespace uBuyFirst.Watchlist
{
    /// <summary>
    /// Parses and validates eBay item IDs from the system clipboard.
    /// </summary>
    public static class ClipboardItemParser
    {
        // Regex to match exactly 12 digits, allowing for surrounding whitespace which will be trimmed.
        private static readonly Regex s_ebayItemIdRegex = new(@"^\d{12}$", RegexOptions.Compiled);

        /// <summary>
        /// Represents the result of parsing item IDs from the clipboard.
        /// </summary>
        public class ParseResult
        {
            public List<string> ValidItemIds { get; } = new();
            public List<string> InvalidEntries { get; } = new();
            public int TotalAttempted { get; set; }
            public bool Success { get; set; }
            public string? ErrorMessage { get; set; }

            /// <summary>
            /// Provides a preview string of valid item IDs. Shows first 5 and last 5 if more than 10 items.
            /// </summary>
            public string ValidItemIdsPreview => GeneratePreview(ValidItemIds);

            /// <summary>
            /// Provides a preview string of invalid entries. Shows first 5 and last 5 if more than 10 items.
            /// </summary>
            public string InvalidEntriesPreview => GeneratePreview(InvalidEntries);

            /// <summary>
            /// Generates a preview string for a list of strings.
            /// </summary>
            private static string GeneratePreview(List<string> list)
            {
                if (list == null || list.Count == 0)
                {
                    return string.Empty;
                }

                if (list.Count <= 10)
                {
                    return string.Join(", ", list);
                }
                else
                {
                    var firstFive = list.Take(5);
                    var lastFive = list.Skip(list.Count - 5);
                    return string.Join(", ", firstFive) + ", ..., " + string.Join(", ", lastFive);
                }
            }
        }

        /// <summary>
        /// Reads the clipboard, parses text content for potential eBay item IDs,
        /// validates them (12 digits, numeric), and returns the results.
        /// </summary>
        /// <returns>A ParseResult object containing the outcome.</returns>
        public static ParseResult ParseItemIdsFromClipboard()
        {
            var result = new ParseResult();

            try
            {
                if (!Clipboard.ContainsText())
                {
                    result.ErrorMessage = "Clipboard does not contain text data.";
                    return result;
                }

                var clipboardText = Clipboard.GetText();
                if (string.IsNullOrWhiteSpace(clipboardText))
                {
                    result.ErrorMessage = "Clipboard text is empty or whitespace.";
                    return result;
                }

                // Split by comma, newline, or carriage return, removing empty entries
                var potentialIds = clipboardText.Split(new[] { ',', '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                result.TotalAttempted = potentialIds.Length;

                foreach (var entry in potentialIds)
                {
                    var trimmedEntry = entry.Trim();
                    if (string.IsNullOrEmpty(trimmedEntry))
                    {
                        // Should be caught by RemoveEmptyEntries, but double-check
                        result.InvalidEntries.Add(entry + " (Empty after trim)");
                        continue;
                    }

                    if (s_ebayItemIdRegex.IsMatch(trimmedEntry))
                    {
                        // Add only if it's not already in the list (handle duplicates in clipboard)
                        if (!result.ValidItemIds.Contains(trimmedEntry))
                        {
                            result.ValidItemIds.Add(trimmedEntry);
                        }
                    }
                    else
                    {
                        result.InvalidEntries.Add(entry); // Add the original entry for feedback
                    }
                }

                result.Success = true; // Parsing completed (even if no valid IDs were found)
            }
            catch (Exception)
            {
                // Log the exception details if logging is available
                // Logger.LogError("Error parsing clipboard data: " + ex.ToString());
                result.ErrorMessage = "An unexpected error occurred while reading or parsing the clipboard.";
                result.Success = false;
            }

            return result;
        }
    }
}
