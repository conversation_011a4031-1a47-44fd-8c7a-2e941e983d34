﻿using System;
using System.IO;
using System.Xml;
using System.Xml.Serialization;
using eBay.Service.Core.Soap;

// ReSharper disable All

namespace uBuyFirst.CustomClasses
{
    internal static class ParseHelpers
    {
        public static Stream ToStream(this string @this)
        {
            var stream = new MemoryStream();
            var writer = new StreamWriter(stream);
            writer.Write(@this);
            writer.Flush();
            stream.Position = 0;
            return stream;
        }

        public static T ParseXml<T>(this string @this) where T : class
        {
            if (!string.IsNullOrEmpty(@this))
            {
                var reader = XmlReader.Create(@this.Trim().ToStream(), new XmlReaderSettings {ConformanceLevel = ConformanceLevel.Auto});
                return new XmlSerializer(typeof(T)).Deserialize(reader) as T;
            }

            return null;
        }

        public static double ParseDouble(string str)
        {
            double.TryParse(str, out var priceMax);
            return priceMax;
        }

        public static string SerializeObject<T>(this T toSerialize)
        {
            XmlSerializer xmlSerializer = new XmlSerializer(toSerialize.GetType());

            using (StringWriter textWriter = new StringWriter())
            {
                xmlSerializer.Serialize(textWriter, toSerialize);
                return textWriter.ToString();
            }
        }
    }

    /// <remarks/>
    [XmlType(AnonymousType = true, Namespace = "urn:ebay:apis:eBLBaseComponents")]
    public class GetMultipleItemsResponseItemNameValueList
    {
        /// <remarks/>
        public string Name { get; set; }

        /// <remarks/>
        [XmlElement("Value")]
        public string[] Value { get; set; }
    }

    /// <remarks/>
    [XmlType(AnonymousType = true, Namespace = "urn:ebay:apis:eBLBaseComponents")]
    [XmlRoot(Namespace = "urn:ebay:apis:eBLBaseComponents", IsNullable = false)]
    public class GetMultipleItemsResponse
    {
        /// <remarks/>
        public DateTime Timestamp { get; set; }

        /// <remarks/>
        public string Ack { get; set; }

        /// <remarks/>
        public string Build { get; set; }

        /// <remarks/>
        public string Version { get; set; }

        /// <remarks/>
        [XmlElement("Item")]
        public GetMultipleItemsResponseItem[] MultipleItem { get; set; }
    }

    /// <remarks/>
    [XmlType(AnonymousType = true, Namespace = "urn:ebay:apis:eBLBaseComponents")]
    public class GetMultipleItemsResponseItem
    {
        /// <remarks/>
        public string BestOfferEnabled { get; set; }

        /// <remarks/>
        public AmountType BuyItNowPrice { get; set; }

        /// <remarks/>
        public string Description { get; set; }

        /// <remarks/>
        public string ItemID { get; set; }

        /// <remarks/>
        public bool BuyItNowAvailable { get; set; }

        /// <remarks/>
        [XmlIgnore()]
        public bool BuyItNowAvailableSpecified { get; set; }

        /// <remarks/>
        public GetMultipleItemsResponseItemConvertedBuyItNowPrice ConvertedBuyItNowPrice { get; set; }

        /// <remarks/>
        public DateTime EndTime { get; set; }

        /// <remarks/>
        public DateTime StartTime { get; set; }

        /// <remarks/>
        public string ViewItemUrlForNaturalSearch { get; set; }

        /// <remarks/>
        public ListingTypeCodeType ListingType { get; set; }

        /// <remarks/>
        public string Location { get; set; }

        /// <remarks/>
        public string PaymentMethods { get; set; }

        /// <remarks/>
        public string GalleryUrl { get; set; }

        /// <remarks/>
        [XmlElement("PictureURL")]
        public string[] PictureUrl { get; set; }

        /// <remarks/>
        public string PostalCode { get; set; }

        /// <remarks/>
        public string PrimaryCategoryID { get; set; }

        /// <remarks/>
        public string PrimaryCategoryName { get; set; }

        public string SecondaryCategoryID { get; set; }

        /// <remarks/>
        public string SecondaryCategoryName { get; set; }

        /// <remarks/>
        public int Quantity { get; set; }

        /// <remarks/>
        public GetMultipleItemsResponseItemSeller Seller { get; set; }

        /// <remarks/>
        public int BidCount { get; set; }

        /// <remarks/>
        public GetMultipleItemsResponseItemConvertedCurrentPrice ConvertedCurrentPrice { get; set; }

        /// <remarks/>
        public AmountType CurrentPrice { get; set; }

        /// <remarks/>
        public GetMultipleItemsResponseItemHighBidder HighBidder { get; set; }

        /// <remarks/>
        public string ListingStatus { get; set; }

        /// <remarks/>
        public int QuantitySold { get; set; }

        /// <remarks/>
        [XmlElement("ShipToLocations")]
        public string[] ShipToLocations { get; set; }

        /// <remarks/>
        public string Site { get; set; }

        /// <remarks/>
        [XmlElement(DataType = "duration")]
        public string TimeLeft { get; set; }

        /// <remarks/>
        public string Title { get; set; }

        public VariationsType Variations { get; set; }

        /// <remarks/>
        [XmlArrayItem("NameValueList", IsNullable = false)]
        public NameValueListTypeCollection ItemSpecifics { get; set; }

        /// <remarks/>
        public int HitCount { get; set; }

        /// <remarks/>
        [XmlIgnore()]
        public bool HitCountSpecified { get; set; }

        /// <remarks/>
        public string Subtitle { get; set; }

        /// <remarks/>
        public string PrimaryCategoryIDPath { get; set; }

        /// <remarks/>
        public GetMultipleItemsResponseItemStorefront Storefront { get; set; }

        /// <remarks/>
        public string Country { get; set; }

        /// <remarks/>
        public GetMultipleItemsResponseItemReturnPolicy ReturnPolicy { get; set; }

        /// <remarks/>
        public GetMultipleItemsResponseItemMinimumToBid MinimumToBid { get; set; }

        /// <remarks/>
        public GetMultipleItemsResponseItemProductID ProductID { get; set; }

        /// <remarks/>
        public bool AutoPay { get; set; }

        /// <remarks/>
        public bool IntegratedMerchantCreditCardEnabled { get; set; }

        /// <remarks/>
        public int HandlingTime { get; set; }

        /// <remarks/>
        public int ConditionID { get; set; }

        /// <remarks/>
        public string ConditionDisplayName { get; set; }

        /// <remarks/>
        [XmlElement("ExcludeShipToLocation")]
        public string[] ExcludeShipToLocation { get; set; }

        /// <remarks/>
        public bool TopRatedListing { get; set; }

        /// <remarks/>
        [XmlIgnore()]
        public bool TopRatedListingSpecified { get; set; }

        /// <remarks/>
        public bool GlobalShipping { get; set; }

        /// <remarks/>
        public string ConditionDescription { get; set; }

        /// <remarks/>
        public int QuantitySoldByPickupInStore { get; set; }

        /// <remarks/>
        public string Sku { get; set; }

        /// <remarks/>
        [XmlIgnore()]
        public bool SkuSpecified { get; set; }

        /// <remarks/>
        public bool NewBestOffer { get; set; }
    }

    /// <remarks/>
    [XmlType(AnonymousType = true, Namespace = "urn:ebay:apis:eBLBaseComponents")]
    public class GetMultipleItemsResponseItemBuyItNowPrice
    {
        /// <remarks/>
        [XmlAttribute()]
        public string CurrencyID { get; set; }

        /// <remarks/>
        [XmlText()]
        public decimal Value { get; set; }
    }

    /// <remarks/>
    [XmlType(AnonymousType = true, Namespace = "urn:ebay:apis:eBLBaseComponents")]
    public class GetMultipleItemsResponseItemConvertedBuyItNowPrice
    {
        /// <remarks/>
        [XmlAttribute()]
        public string CurrencyID { get; set; }

        /// <remarks/>
        [XmlText()]
        public decimal Value { get; set; }
    }

    /// <remarks/>
    [XmlType(AnonymousType = true, Namespace = "urn:ebay:apis:eBLBaseComponents")]
    public class GetMultipleItemsResponseItemSeller
    {
        /// <remarks/>
        public string UserID { get; set; }

        /// <remarks/>
        public string FeedbackRatingStar { get; set; }

        /// <remarks/>
        public int FeedbackScore { get; set; }

        /// <remarks/>
        public decimal PositiveFeedbackPercent { get; set; }

        /// <remarks/>
        public bool TopRatedSeller { get; set; }

        /// <remarks/>
        [XmlIgnore()]
        public bool TopRatedSellerSpecified { get; set; }
    }

    /// <remarks/>
    [XmlType(AnonymousType = true, Namespace = "urn:ebay:apis:eBLBaseComponents")]
    public class GetMultipleItemsResponseItemConvertedCurrentPrice
    {
        /// <remarks/>
        [XmlAttribute()]
        public string CurrencyID { get; set; }

        /// <remarks/>
        [XmlText()]
        public decimal Value { get; set; }
    }

    /// <remarks/>
    [XmlType(AnonymousType = true, Namespace = "urn:ebay:apis:eBLBaseComponents")]
    public class GetMultipleItemsResponseItemCurrentPrice
    {
        /// <remarks/>
        [XmlAttribute()]
        public string CurrencyID { get; set; }

        /// <remarks/>
        [XmlText()]
        public decimal Value { get; set; }
    }

    /// <remarks/>
    [XmlType(AnonymousType = true, Namespace = "urn:ebay:apis:eBLBaseComponents")]
    public class GetMultipleItemsResponseItemHighBidder
    {
        /// <remarks/>
        public string UserID { get; set; }

        /// <remarks/>
        public bool FeedbackPrivate { get; set; }

        /// <remarks/>
        public string FeedbackRatingStar { get; set; }

        /// <remarks/>
        public int FeedbackScore { get; set; }
    }

    /// <remarks/>
    [XmlType(AnonymousType = true, Namespace = "urn:ebay:apis:eBLBaseComponents")]
    public class GetMultipleItemsResponseItemStorefront
    {
        /// <remarks/>
        public string StoreUrl { get; set; }

        /// <remarks/>
        public string StoreName { get; set; }
    }

    /// <remarks/>
    [XmlType(AnonymousType = true, Namespace = "urn:ebay:apis:eBLBaseComponents")]
    public class GetMultipleItemsResponseItemReturnPolicy
    {
        /// <remarks/>
        public string Refund { get; set; }

        /// <remarks/>
        public string ReturnsWithin { get; set; }

        /// <remarks/>
        public string ReturnsAccepted { get; set; }

        /// <remarks/>
        public string Description { get; set; }

        /// <remarks/>
        public string ShippingCostPaidBy { get; set; }

        /// <remarks/>
        public string RestockingFeeValue { get; set; }

        /// <remarks/>
        public string RestockingFeeValueOption { get; set; }
    }

    /// <remarks/>
    [XmlType(AnonymousType = true, Namespace = "urn:ebay:apis:eBLBaseComponents")]
    public class GetMultipleItemsResponseItemMinimumToBid
    {
        /// <remarks/>
        [XmlAttribute()]
        public string CurrencyID { get; set; }

        /// <remarks/>
        [XmlText()]
        public decimal Value { get; set; }
    }

    /// <remarks/>
    [XmlType(AnonymousType = true, Namespace = "urn:ebay:apis:eBLBaseComponents")]
    public class GetMultipleItemsResponseItemProductID
    {
        /// <remarks/>
        [XmlAttribute()]
        public string Type { get; set; }

        /// <remarks/>
        [XmlText()]
        public string Value { get; set; }
    }
}