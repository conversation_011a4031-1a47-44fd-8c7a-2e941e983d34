﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Net;
using System.Runtime.InteropServices;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.Win32;

namespace EbaySniper
{
    public partial class Form1
    {

        //Main tab clicks
        private void CellDoubleClicked(object sender, DataGridViewCellEventArgs e)
        {

            if (e.RowIndex > -1 && e.RowIndex < dataGridView1.Rows.Count)
            {
                var firstOrDefault = AllItems.FirstOrDefault(x => x.Url == dataGridView1.Rows[e.RowIndex].Cells[0].Value.ToString());
                if (firstOrDefault !=null)
                    Process.Start(firstOrDefault.Url);
            }
        }
        private async void CellButtonClicked(object sender, DataGridViewCellMouseEventArgs e)
        {
            if (e.RowIndex > -1 && e.RowIndex < dataGridView1.Rows.Count && e.ColumnIndex > -1 &&e.ColumnIndex < dataGridView1.Columns.Count)
            {
                var row = AllItems.FirstOrDefault(x => x.Url == dataGridView1.Rows[e.RowIndex].Cells[0].Value.ToString());
                if (row != null)
                {
                    txtItemId.Text = row.ID;
                    linkItemUrl.Text = row.Url;
                    txtItemTitle.Text = row.Title;
                    if (dataGridView1.Columns[e.ColumnIndex].HeaderText == "Price/Page")
                    {
                        if (row.Payment == "Immediate")
                        {
                            Process.Start(dataGridView1.Rows[e.RowIndex].Cells[0].Value.ToString());
                        }
                        return;
                    }

                    if (dataGridView1.Columns[e.ColumnIndex].HeaderText == "Total/BIN")
                    {
                        if (row.Url == dataGridView1.Rows[e.RowIndex].Cells[0].Value.ToString())
                        {
                            if (row.Payment == "Immediate") //commit to buy available
                            {
                                Process.Start(
                                    dataGridView1.Rows[e.RowIndex].Cells[0].Value.ToString()
                                        .Replace("http://cgi.ebay.com/ws/eBayISAPI.dll?ViewItem&item=",
                                            "http://offer.ebay.com/ws/eBayISAPI.dll?BinConfirm&quantity=1&item="));
                                return;
                            }
                            Process.Start(
                                dataGridView1.Rows[e.RowIndex].Cells[0].Value.ToString()
                                    .Replace("http://cgi.ebay.com/ws/eBayISAPI.dll?ViewItem&item=",
                                        "http://offer.ebay.com/ws/eBayISAPI.dll?BinConfirm&quantity=1&rev=5&item="));
                            return;
                        }
                    }
                    if (dataGridView1.Columns[e.ColumnIndex].HeaderText == "AutoBuy")
                    {
                        try
                        {
                            Items item;
                            if (row.Color == Color.Aqua.ToArgb())
                            {
                                row.CheckoutStatus = "Cancelling";
                                _checkoutAbort = "AbortCurrent";
                                if (_paymentNumber != null)
                                {
                                    _api.cancelPayment(_paymentNumber);
                                    _paymentNumber = null;
                                }
                            }
                            if (PurchaseQueue.TryRemove(row.Url, out item))
                            {
                                item.Color = Color.Orange.ToArgb();
                                item.CheckoutStatus = "Canceled";
                            }
                        }
                        catch (Exception ex)
                        {
                            if (Isdebug)
                                MessageBox.Show("Aborting checkout: " + ex.Message);
                        }
                    }
                    else
                    {
                        try
                        {
                            if (e.RowIndex > -1 && e.RowIndex < dataGridView1.Rows.Count &&dataGridView1.SelectedCells.Count > 0)
                            {
                                var shippingstr = row.Price + " (" + row.Shipping + ")";
                                if (row.Shipping.Contains("N/A"))
                                {
                                    shippingstr = "<FONT style=\"BACKGROUND-COLOR: Coral\">" + row.Price +
                                                  " (" +
                                                  row.Shipping + ")</FONT>";
                                }
                                var html =
                                    ("<table border=\"1\" cellpadding=\"1\"><tr><td width=140><p></p><img src=" +
                                     row.GalleryUrl +
                                     "></td><td><table><tr><td><b>Title: </b></td><td bgcolor=" +
                                     HexConverter(Color.FromArgb(row.Color)) + ">" + row.Title +
                                     "</td></tr>" +
                                     "<tr><td><b>Price: </b></td><td>" + shippingstr + "</td></tr>" +
                                     "<tr><td><b>Carrier: </b></td><td>" + row.Carrier + "</td></tr>" +
                                     "<tr><td><b>Capacity: </b></td><td>" + row.Capacity + "</td></tr>" +
                                     "<tr><td><b>Condition: </b></td><td>" + row.Condition + "</td></tr>" +
                                     "<tr><td><b>Seller: </b></td><td>" + row.SellerName + "</td></tr>" +
                                     "<tr><td><b>Returns: </b></td><td>" + row.Returns + "</td></tr>" +
                                     "<tr><td><b>Condition description: </b></td><td> <FONT style=\"BACKGROUND-COLOR: " +
                                     HexConverter(Color.FromArgb(255, 255, 150)) + "\">" +
                                     row.ConditionDescription + " </FONT></td></tr>" +
                                     "</table></td></tr><BR><BR></table><BR>") + "<BR>";

                                if (webBrowser1.DocumentText != (" " + html + row.Description))
                                {
                                    html = html + row.Description;
                                    foreach (var word in _highlights.Words1)
                                    {
                                        html = Regex.Replace(html, "(" + word + ".*?)\\s",
                                            "<font style=\"BACKGROUND-COLOR: " +
                                            HexConverter(_highlights.Color1) + "\">" + "$1" + "</FONT> ",
                                            RegexOptions.IgnoreCase | RegexOptions.Singleline);
                                    }
                                    foreach (var word in _highlights.Words2)
                                    {
                                        html = Regex.Replace(html, "(" + word + ".*?)\\s",
                                            "<font style=\"BACKGROUND-COLOR: " +
                                            HexConverter(_highlights.Color2) + "\">" + "$1" + "</FONT> ",
                                            RegexOptions.IgnoreCase | RegexOptions.Singleline);
                                    }
                                    foreach (var word in _highlights.Words3)
                                    {
                                        html = Regex.Replace(html, "(" + word + ".*?)\\s",
                                            "<font style=\"BACKGROUND-COLOR: " +
                                            HexConverter(_highlights.Color3) + "\">" + "$1" + "</FONT> ",
                                            RegexOptions.IgnoreCase | RegexOptions.Singleline);
                                    }
                                    webBrowser1.DocumentText = html;
                                    await UpdatePicPanel(row);
                                }                                
                            }
                        }
                        catch (Exception ex)
                        {
                            if (Isdebug) MessageBox.Show("Cell click" + ex.Message);
                        }
                    }
                }
            }
        }
        private void RowsAdded(object sender, DataGridViewRowsAddedEventArgs e)
        {
            try
            {
                //check and red added row
                if (e.RowIndex > -1 && e.RowIndex < dataGridView1.Rows.Count)
                {
                    var row =AllItems.FirstOrDefault(x => x.Url == dataGridView1.Rows[e.RowIndex].Cells[0].Value.ToString());
                    if (row != null && row.Shipping.Contains("N/A"))
                    {
                        dataGridView1.Rows[e.RowIndex].Cells[4].Style.BackColor = Color.Coral;                        
                    }
                    if (row != null && !row.Category.Contains("9355"))
                    {
                        dataGridView1.Rows[e.RowIndex].Cells[15].Style.BackColor = Color.PaleVioletRed;                        
                    }
                }
            }
            catch (Exception ex)
            {
                if (Isdebug) MessageBox.Show("rowsadded" + ex.Message);
            }
        }

        private async Task UpdatePicPanel(Items row)
        {
            try
            {
                if (dataGridView1.SelectedCells.Count > 0)
                {
                    if (row.Url == (string)dataGridView1.Rows[dataGridView1.SelectedCells[0].RowIndex].Cells[0].Value)
                    {
                        splitContainerSecondary.Panel2.Controls.Clear();
                        var pictureUrls = row.Pictures.Split(new[] { ";" }, StringSplitOptions.RemoveEmptyEntries);
                        var yoffset = 10;

                        foreach (var url in pictureUrls)
                        {
                            var picbox = new PictureBox();
                            var imagepath = await GetImageAsync(url);
                            if (imagepath != "")
                            {
                                var image = new Bitmap(imagepath);
                                picbox.Image = image;

                                picbox.Height = 200;
                                picbox.Width = 150;
                                if ((splitContainerSecondary.Panel2.Controls.Count + 1) % 2 == 0 &&
                                    splitContainerSecondary.Panel2.Controls.Count > 0)
                                {
                                    picbox.Location = new Point(180, yoffset - 155);
                                }
                                else
                                {
                                    picbox.Location = new Point(10, yoffset);
                                    yoffset += 150 + 5;
                                }

                                picbox.SizeMode = PictureBoxSizeMode.Zoom;
                                splitContainerSecondary.Panel2.Controls.Add(picbox);
                                picbox.MouseEnter += MouseEnterPicture;
                                picbox.MouseWheel += MouseEnterPicture;
                                picbox.Click += MouseExitPicture;

                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (Isdebug) MessageBox.Show("UpdatePicPanel" + ex.Message + "\n" + ex.Source);
            }
        }

        private void UpdateBrowser(Items row)
        {
            try
            {
                if (dataGridView1.SelectedCells.Count > 0)
                {
                    if (row.Url == (string)dataGridView1.Rows[dataGridView1.SelectedCells[0].RowIndex].Cells[0].Value)
                    {
                        var shippingstr = row.Price + " (" + row.Shipping + ")";
                        if (row.Shipping.Contains("N/A"))
                        {
                            shippingstr = "<FONT style=\"BACKGROUND-COLOR: Coral\">" + row.Price + " (" + row.Shipping +
                                          ")</FONT>";
                        }
                        var html = ("<table border=\"1\" cellpadding=\"1\"><tr><td width=140><p></p><img src="
                                    + row.GalleryUrl + "></td><td><table><tr ><td><b>Title: </b></td><td bgcolor=" +
                                    HexConverter(Color.FromArgb(row.Color)) + ">" + row.Title + "</td></tr>" +
                                    "<tr><td><b>Price: </b></td><td>" + shippingstr + "</td></tr>" +
                                    "<tr><td><b>Carrier: </b></td><td>" + row.Carrier + "</td></tr>" +
                                    "<tr><td><b>Capacity: </b></td><td>" + row.Capacity + "</td></tr>" +
                                    "<tr><td><b>Condition: </b></td><td>" + row.Condition + "</td></tr>" +
                                    "<tr><td><b>Seller: </b></td><td>" + row.SellerName + "</td></tr>" +
                                    "<tr><td><b>Returns: </b></td><td>" + row.Returns + "</td></tr>" +
                                    "<tr><td><b>Condition description: </b></td><td> <FONT style=\"BACKGROUND-COLOR: " +
                                    HexConverter(Color.FromArgb(255, 255, 150)) + "\">" + row.ConditionDescription +
                                    " </FONT></td></tr>" +
                                    "</table></td></tr><BR><BR></table><BR>") + "<BR>";
                        html = html + row.Description;
                        foreach (var word in _highlights.Words1)
                        {
                            //html = html.Replace(word,"<font style=\"BACKGROUND-COLOR: " + HexConverter(_highlights.Color1) + "\">" + word + "</FONT>");
                            html = Regex.Replace(html, "(" + word + ".*?)\\s",
                                "<font style=\"BACKGROUND-COLOR: " +
                                HexConverter(_highlights.Color1) + "\">" + "$1" + "</FONT> ",
                                RegexOptions.IgnoreCase | RegexOptions.Singleline);
                        }
                        foreach (var word in _highlights.Words2)
                        {
                            html = Regex.Replace(html, "(" + word + ".*?)\\s",
                                "<font style=\"BACKGROUND-COLOR: " +
                                HexConverter(_highlights.Color2) + "\">" + "$1" + "</FONT> ",
                                RegexOptions.IgnoreCase | RegexOptions.Singleline);
                        }
                        foreach (var word in _highlights.Words3)
                        {
                            html = Regex.Replace(html, "(" + word + ".*?)\\s",
                                "<font style=\"BACKGROUND-COLOR: " +
                                HexConverter(_highlights.Color3) + "\">" + "$1" + "</FONT> ",
                                RegexOptions.IgnoreCase | RegexOptions.Singleline);
                        }
                        //   if (webBrowser1.DocumentText != (html + row.Description))
                        {
                            webBrowser1.DocumentText = html;

                        }
                    }
                }
            }
            catch (Exception ex)
            {
                if (Isdebug) MessageBox.Show("UpdateBrowser: " + ex.Message + "\n" + ex.Source);
            }
        }




        private string GetImage(string url)
        {
            try
            {
                var fileName = Path.GetFileName(url);
                if (fileName != null)
                {
                    var filename = fileName.Replace("?", "");
                    var path = Path.GetTempPath() + filename;
                    if (!File.Exists(path))
                    {
                        var request = WebRequest.Create(url);
                        using (var response = request.GetResponse())
                        using (var stream = response.GetResponseStream())
                        {
                            if (stream != null)
                            {
                                var image = Image.FromStream(stream);
                                image.Save(path);
                            }
                        }
                    }
                    return path;
                }
            }
            catch (Exception ex)
            {
                if (Isdebug) MessageBox.Show("GetImage" + ex.Message);
                return "";
            }
            return "";

        }
        private async Task<string> GetImageAsync(string url)
        {
            return await Task.Run(() =>
            {
                try
                {
                    var fileName = Path.GetFileName(url);
                    if (fileName != null)
                    {
                        var filename = fileName.Replace("?", "");
                        var path = Path.GetTempPath() + filename;
                        if (!File.Exists(path))
                        {
                            var request = WebRequest.Create(url);
                            using (var response = request.GetResponse())
                            using (var stream = response.GetResponseStream())
                            {
                                if (stream != null)
                                {
                                    var image = Image.FromStream(stream);
                                    image.Save(path);
                                }
                            }
                        }
                        return path;
                    }
                }
                catch (Exception ex)
                {
                    if (Isdebug) MessageBox.Show("GetImage" + ex.Message);
                    return "";
                }
                return "";
            });
        }
        private void GetImageAsyncBG(string url)
        {
            Task.Run(() =>
           {
               try
               {
                   var fileName = Path.GetFileName(url);
                   if (fileName != null)
                   {
                       var filename = fileName.Replace("?", "");
                       var path = Path.GetTempPath() + filename;
                       if (!File.Exists(path))
                       {
                           var request = WebRequest.Create(url);
                           using (var response = request.GetResponse())
                           using (var stream = response.GetResponseStream())
                           {
                               if (stream != null)
                               {
                                   var image = Image.FromStream(stream);
                                   image.Save(path);
                               }
                           }
                       }
                       return path;
                   }
               }
               catch (Exception ex)
               {
                   if (Isdebug) MessageBox.Show("GetImage" + ex.Message);
                   return "";
               }
               return "";
           }).ConfigureAwait(false);
        }

        //Mouse movements for focus
        private void MouseEnterPicture(object sender, EventArgs e)
        {
            splitContainerSecondary.Panel2.Focus();
        }
        private void MouseEnterPanel1(object sender, EventArgs e)
        {
            webBrowser1.Focus();
        }
        private void MouseExitPicture(object sender, EventArgs e)
        {
            splitContainerSecondary.Panel2.Focus();
            var picbox = (PictureBox)sender;
            if (picbox.SizeMode != PictureBoxSizeMode.Zoom)
            {
                picbox.SizeMode = PictureBoxSizeMode.Zoom;
                picbox.Height = 200;
                picbox.Width = 150;
                picbox.Location = PicLastPoint;
            }
            else
            {
                picbox.SizeMode = PictureBoxSizeMode.AutoSize;
                PicLastPoint = picbox.Location;
                picbox.Location = new Point(10, 10);
                picbox.BringToFront();
            }
        }
        private void MouseMoved(object sender, EventArgs e)
        {
            splitContainerSecondary.Panel2.Focus();
        }
        private void PanelMouseMove(object sender, MouseEventArgs e)
        {
            Idlesw.Restart();
            splitContainerSecondary.Panel2.Focus();
        }
        private void PanelMouseMove(object sender, EventArgs e)
        {
            Idlesw.Restart();
            splitContainerSecondary.Panel2.Focus();
        }
        private void EnterDG(object sender, EventArgs e)
        {
            dataGridView1.Focus();
        }

        //Clear buttons
        private void BtnLogClearClick(object sender, EventArgs e)
        {
            txtLog.Text = "";
        }
        private void BtnRemoveCheckedProxyClick(object sender, EventArgs e)
        {
            var badproxies = new string[chklstBadProxy.CheckedItems.Count];
            chklstBadProxy.CheckedItems.CopyTo(badproxies, 0);
            var i = 0;
            if (badproxies.Length > 0)
            {
                do
                {
                    var proxy = badproxies[i].Split(')')[1].Trim();
                    txtProxyMain.Text = txtProxyMain.Text.Replace(proxy, "").Replace("\n\n", "\n").Trim();
                    txtProxySecondary.Text = txtProxySecondary.Text.Replace(proxy, "").Replace("\n\n", "\n").Trim();
                    ProxiesMain.TryTake(out proxy);
                    ProxiesSecondary.Remove(proxy);
                    chklstBadProxy.Items.Remove(badproxies[i]);
                    i++;
                } while (i < badproxies.Length);
            }

            SaveSettings();
        }
        private void Button1Click(object sender, EventArgs e)
        {
            itemsBindingSource.Clear();
            webBrowser1.DocumentText = "";
            splitContainerSecondary.Panel2.Controls.Clear();
        }
        private void BtnClearChkBadproxies(object sender, EventArgs e)
        {
            chklstBadProxy.Items.Clear();
        }

        //WebBrowser clicks



        private void SetZoom(int zoom)
        {
            int OLECMDID_ZOOM = 63;
            int OLECMDEXECOPT_DONTPROMPTUSER = 2;
            dynamic obj = webBrowser1.ActiveXInstance;
            //   btnBuyItNow.Text = zoom.ToString();
            obj.ExecWB(OLECMDID_ZOOM, OLECMDEXECOPT_DONTPROMPTUSER, zoom, IntPtr.Zero);

        }
        private void BtnDecreaseFont(object sender, EventArgs e)
        {
            FontSize = FontSize - 10;
            if (FontSize < 10)
                FontSize = 10;

            if (webBrowser1.DocumentText.Length > 0)
            {
                SetZoom(FontSize);              
            }

        }
        private void BtnIncreaseFont(object sender, EventArgs e)
        {
            FontSize = FontSize + 10;

            if (webBrowser1.DocumentText.Length > 0)
            {
                SetZoom(FontSize);
            }


        }

        //Idling
        [DllImport("user32.dll")]
        static extern bool GetLastInputInfo(ref LASTINPUTINFO plii);
        [StructLayout(LayoutKind.Sequential)]
        struct LASTINPUTINFO
        {
            public static readonly int SizeOf = Marshal.SizeOf(typeof(LASTINPUTINFO));

            [MarshalAs(UnmanagedType.U4)]
            public Int32 cbSize;
            [MarshalAs(UnmanagedType.U4)]
            public Int32 dwTime;
        }
    }
}
