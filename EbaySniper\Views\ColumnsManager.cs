﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Columns;
using DevExpress.XtraGrid.Views.Grid;
using uBuyFirst.Prefs;

namespace uBuyFirst.Views
{
    internal class ColumnsManager
    {
        public static List<string> GetAllowedColumns()
        {
            var columnList = new List<string>();
            foreach (var gridControl in ResultsView.ViewsDict.Values)
            {
                foreach (DataColumn column in ((DataTable)gridControl.DataSource).Columns)
                {
                    if (!columnList.Contains(column.Caption))
                        columnList.Add(column.Caption);
                }
            }

            columnList.Sort();
            columnList.Remove("ItemID");
            columnList.Remove("Blob");
            columnList.Remove("Source");

            columnList.Remove("Thumbnail");
            columnList.Remove("Relist Parent ID");

            return columnList;
        }

        public static bool ColumnVisible(string columnName)
        {
            foreach (var gridControl in ResultsView.ViewsDict.Values)
            {
                if (gridControl.MainView is not GridView gridView)
                    continue;

                foreach (GridColumn column in gridView.Columns)
                {
                    if (column.Visible)
                        if (column.FieldName == columnName)
                        {
                            return true;
                        }
                }
            }

            return false;
        }

        public static List<string> GetAllowedColumnsTelegram()
        {
            var columnList = new List<string>();
            foreach (var gridControl in ResultsView.ViewsDict.Values)
            {
                foreach (DataColumn column in ((DataTable)gridControl.DataSource).Columns)
                {
                    if (!columnList.Contains(column.Caption))
                        columnList.Add(column.Caption);
                }
            }

            columnList.Sort();
            columnList.Remove("ItemID");
            columnList.Remove("Blob");
            columnList.Remove("Source");
            columnList.Remove("Thumbnail");
            columnList.Remove("Relist Parent ID");

            return columnList;
        }

        public static bool CheckExistingColumns(List<string> newColumns)
        {
            var existingColumns = GetAllowedColumns();
            existingColumns.Add("Description");
            if (ProgramState.SerialNumber.StartsWith("ROMA") || ProgramState.SerialNumber.StartsWith("1BE9-4A48"))
            {
                existingColumns.Add("ItemID");
            }

            foreach (var columnName in newColumns)
            {
                if (columnName == "ViewUrl" || columnName == "CheckoutUrl" || columnName == "ContactUrl")
                    continue;

                if (!existingColumns.Contains(columnName))
                {
                    XtraMessageBox.Show("Can't find column: '" + columnName + "'");

                    return false;
                }
            }

            return true;
        }
    }
}
