using System;
using System.Collections.Generic;
using System.Linq;

namespace uBuyFirst.Restocker.Services
{
    /// <summary>
    /// Service implementation for accessing keyword data
    /// </summary>
    public class KeywordDataService : IKeywordDataService
    {
        private readonly List<Keyword2Find> _keywords;

        /// <summary>
        /// Initializes a new instance of KeywordDataService
        /// </summary>
        /// <param name="keywords">The collection of keywords to manage</param>
        /// <exception cref="ArgumentNullException">Thrown when keywords is null</exception>
        public KeywordDataService(IEnumerable<Keyword2Find> keywords)
        {
            _keywords = keywords?.ToList() ?? throw new ArgumentNullException(nameof(keywords));
        }

        /// <summary>
        /// Gets a keyword by its unique identifier
        /// </summary>
        /// <param name="keywordId">The unique identifier of the keyword</param>
        /// <returns>The keyword if found, null otherwise</returns>
        public Keyword2Find GetKeywordById(string keywordId)
        {
            if (string.IsNullOrEmpty(keywordId))
                return null;

            return _keywords.FirstOrDefault(k => k.Id == keywordId);
        }

        /// <summary>
        /// Gets multiple keywords by their unique identifiers
        /// </summary>
        /// <param name="keywordIds">Collection of keyword identifiers</param>
        /// <returns>List of found keywords (may be fewer than requested if some IDs are invalid)</returns>
        public List<Keyword2Find> GetKeywordsByIds(IEnumerable<string> keywordIds)
        {
            if (keywordIds == null)
                return new List<Keyword2Find>();

            var validIds = keywordIds.Where(id => !string.IsNullOrEmpty(id)).ToList();
            
            return _keywords.Where(k => validIds.Contains(k.Id)).ToList();
        }

        /// <summary>
        /// Gets all available keywords
        /// </summary>
        /// <returns>List of all keywords</returns>
        public List<Keyword2Find> GetAllKeywords()
        {
            return new List<Keyword2Find>(_keywords);
        }
    }
}
