﻿using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraBars.Docking;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid;
using uBuyFirst.Grid;
using uBuyFirst.Tools;

namespace uBuyFirst.Views
{
    public static class ResultsView
    {
        public static readonly Dictionary<string, GridControl> ViewsDict = new();
        public static DockManager DockManager;
        public static RepositoryItemMRUEdit RepositoryItemViews;

        public static void CreateViewsPanels()
        {
            RepositoryItemViews.Items.Clear();
            foreach (var pair in ViewsDict)
            {
                if (!ViewsDict.ContainsKey(pair.Key))
                    ViewsDict.Add(pair.Key, GridBuilder.CreateGridControl(pair.Key));
                var existingPanel = DockManager.Panels.FirstOrDefault(p => p.Tag?.ToString() == pair.Key);
                if (existingPanel == null)
                {
                    var viewName = pair.Key;
                    AddTabbedPanel(ViewsDict[viewName]);
                }
                MruManager.AddMissingMruItem(pair.Key, RepositoryItemViews);
            }
        }

        public static void CreateView(string viewName)
        {
            if (!ViewsDict.ContainsKey(viewName))
                ViewsDict.Add(viewName, GridBuilder.CreateGridControl(viewName));

            AddTabbedPanel(ViewsDict[viewName]);
        }

        public static void CreateMissingViews(List<Keyword2Find> keyword2FindList)
        {
            var viewsNames = keyword2FindList.Select(k => k.ViewName).Distinct();

            foreach (var viewName in viewsNames)
            {
                if (!ViewsDict.ContainsKey(viewName))
                    ViewsDict.Add(viewName, GridBuilder.CreateGridControl(viewName));
            }
        }

        private static void AddTabbedPanel(GridControl gridControl)
        {
            var dockPanel = DockManager.AddPanel(DockingStyle.Bottom);

            CreateDockPanel(dockPanel, gridControl);

            dockPanel.DockedAsTabbedDocument = true;
        }

        public static void AssignViewsToSearches(List<Keyword2Find> ebaySearchesChildrenCore)
        {
            foreach (var kw2Find in ebaySearchesChildrenCore)
            {
                AssignViewToSearch(kw2Find.ViewName, kw2Find);
            }
        }

        public static void AssignViewToSearch(string viewName, Keyword2Find kw2Find)
        {
            kw2Find.ViewName = viewName;
            kw2Find.GridControl = ViewsDict[kw2Find.ViewName];
            TogglePanelsVisibility();
            TogglePanelVisibility(viewName);
        }

        internal static void TogglePanelsVisibility()
        {
            return;
            foreach (var viewName in ViewsDict.Keys)
            {
                // var panel = DockManager.Panels.FirstOrDefault(p => p.Tag?.ToString() == viewName && p.Name.StartsWith("dockPnl"));
                TogglePanelVisibility(viewName);
            }
        }

        private static void TogglePanelVisibility(string viewName)
        {
            var panel = DockManager.Panels.FirstOrDefault(p => p.Header == viewName);
            var panelVisible = Form1.Instance._ebaySearches.ChildrenCore.Any(kw => kw.ViewName == viewName);
            TogglePanelVisibility(panelVisible, panel);
        }

        private static Control CreateContainer(GridControl gridControl)
        {
            var dockPanelContainer = new ControlContainer();
            dockPanelContainer.SuspendLayout();
            dockPanelContainer.BackColor = Color.Transparent;
            dockPanelContainer.Controls.Add(gridControl);
            dockPanelContainer.Location = new Point(0, 0);
            dockPanelContainer.Name = "dockContainer" + Helpers.Str2Guid(gridControl.Text).ToString().Replace("-", "");
            dockPanelContainer.Size = new Size(512, 201);
            dockPanelContainer.TabIndex = 0;
            dockPanelContainer.Dock = DockStyle.Fill;
            dockPanelContainer.ResumeLayout(false);
            dockPanelContainer.PerformLayout();

            return dockPanelContainer;
        }

        private static void CreateDockPanel(DockPanel dockPanel, GridControl gridControl)
        {
            dockPanel.SuspendLayout();
            dockPanel.ControlContainer.Name = "Container_" + Helpers.Str2Guid(gridControl.Text).ToString().Replace("-", "");
            dockPanel.ControlContainer.Controls.Add(gridControl);
            dockPanel.ID = Helpers.Str2Guid(gridControl.Text);
            dockPanel.Header = gridControl.Text;
            dockPanel.Text = gridControl.Text;
            dockPanel.TabText = gridControl.Text;
            dockPanel.Tag = gridControl.Text;
            dockPanel.Name = "dockPnl_" + Helpers.Str2Guid(gridControl.Text).ToString().Replace("-", "");

            dockPanel.Appearance.BackColor = Color.Transparent;
            dockPanel.Appearance.BackColor2 = Color.Transparent;
            dockPanel.Appearance.Options.UseBackColor = true;
            dockPanel.Appearance.Font = new Font(dockPanel.Appearance.Font, FontStyle.Regular);

            dockPanel.FloatLocation = new Point(600, 413);
            dockPanel.FloatSize = new Size(758, 216);
            dockPanel.FloatVertical = true;
            dockPanel.OriginalSize = new Size(200, 144);
            dockPanel.Options.ShowCloseButton = false;
            dockPanel.ResumeLayout(false);
        }

        public static void RemoveFromViewList(string viewName)
        {
            if (ViewsDict.ContainsKey(viewName))
                ViewsDict.Remove(viewName);
        }

        public static void AssignViewForSearches(string oldView, string newView, List<Keyword2Find> ebaySearches)
        {
            var groupKeywords = ebaySearches.Where(k => k.ViewName == oldView).ToList();
            foreach (var keyword2Find in groupKeywords)
            {
                AssignViewForSearch(newView, keyword2Find);
            }
        }

        private static void AssignViewForSearch(string newView, Keyword2Find keyword2Find)
        {
            keyword2Find.ViewName = newView;
            keyword2Find.GridControl = ViewsDict[newView];
        }

        public static void InitViewDict(List<string> settingsViewsList)
        {
            var index = 0;
            while (ViewsDict.Count > 0 && index < ViewsDict.Count)
            {
                if (ViewsDict.Keys.ElementAt(index) != "Results")
                {
                    ViewsDict.Remove(ViewsDict.Keys.ElementAt(index));
                }
                else
                {
                    index++;
                }

            }
            foreach (var viewName in settingsViewsList)
            {
                if (ViewsDict.ContainsKey(viewName))
                    continue;
                ViewsDict.Add(viewName, GridBuilder.CreateGridControl(viewName));
            }
        }

        public static void TogglePanelVisibility(bool panelVisible, DockPanel panel)
        {
            if (panel == null)
                return;

            if (panelVisible)
                panel.Show();
            else
                panel.Hide();
            //panel.Hide();

            if (panel.FloatLocation.X < 0)
                panel.FloatLocation = new Point(0, panel.Location.Y);

            if (panel.FloatLocation.Y < 0)
                panel.FloatLocation = new Point(panel.Location.X, 0);
        }
    }
}
