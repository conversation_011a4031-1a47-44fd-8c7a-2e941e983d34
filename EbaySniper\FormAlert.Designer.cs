﻿namespace uBuyFirst
{
    partial class FormAlert
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormAlert));
            this.ribbon = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.ceHeight = new DevExpress.XtraEditors.SpinEdit();
            this.ceWidth = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.txtText = new DevExpress.XtraEditors.MemoEdit();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.txtCaption = new DevExpress.XtraEditors.TextEdit();
            this.labelControl4 = new DevExpress.XtraEditors.LabelControl();
            this.btnTestAlert = new DevExpress.XtraEditors.SimpleButton();
            this.btnSave = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.ceAutoHeight = new DevExpress.XtraEditors.CheckEdit();
            this.icbFormLocation = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.labelControl8 = new DevExpress.XtraEditors.LabelControl();
            this.icbFormDisplaySpeed = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.labelControl9 = new DevExpress.XtraEditors.LabelControl();
            this.seAutoFormDelay = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl11 = new DevExpress.XtraEditors.LabelControl();
            this.seFormMaxCount = new DevExpress.XtraEditors.SpinEdit();
            this.labelControl12 = new DevExpress.XtraEditors.LabelControl();
            this.spinEditFontSize = new DevExpress.XtraEditors.SpinEdit();
            this.checkEditShowImage = new DevExpress.XtraEditors.CheckEdit();
            this.checkEditEnable = new DevExpress.XtraEditors.CheckEdit();
            this.icbShowingEffect = new DevExpress.XtraEditors.ImageComboBoxEdit();
            this.hyperlinkLabelControlHelp = new DevExpress.XtraEditors.HyperlinkLabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.ribbon)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceHeight.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceWidth.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtText.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCaption.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceAutoHeight.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.icbFormLocation.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.icbFormDisplaySpeed.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.seAutoFormDelay.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.seFormMaxCount.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditFontSize.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditShowImage.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditEnable.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.icbShowingEffect.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // ribbon
            // 
            this.ribbon.EmptyAreaImageOptions.ImagePadding = new System.Windows.Forms.Padding(26, 24, 26, 24);
            this.ribbon.ExpandCollapseItem.Id = 0;
            this.ribbon.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.ribbon.ExpandCollapseItem});
            this.ribbon.Location = new System.Drawing.Point(0, 0);
            this.ribbon.MaxItemId = 1;
            this.ribbon.Name = "ribbon";
            this.ribbon.OptionsMenuMinWidth = 153;
            this.ribbon.OptionsPageCategories.ShowCaptions = false;
            this.ribbon.ShowApplicationButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbon.ShowDisplayOptionsMenuButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbon.ShowExpandCollapseButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbon.ShowPageHeadersMode = DevExpress.XtraBars.Ribbon.ShowPageHeadersMode.Hide;
            this.ribbon.ShowToolbarCustomizeItem = false;
            this.ribbon.Size = new System.Drawing.Size(691, 49);
            this.ribbon.Toolbar.ShowCustomizeItem = false;
            // 
            // labelControl2
            // 
            this.labelControl2.Location = new System.Drawing.Point(189, 279);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(6, 13);
            this.labelControl2.TabIndex = 16;
            this.labelControl2.Text = "x";
            // 
            // ceHeight
            // 
            this.ceHeight.EditValue = new decimal(new int[] {
            110,
            0,
            0,
            0});
            this.ceHeight.Location = new System.Drawing.Point(204, 276);
            this.ceHeight.Name = "ceHeight";
            this.ceHeight.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.ceHeight.Properties.IsFloatValue = false;
            this.ceHeight.Properties.Mask.EditMask = "N00";
            this.ceHeight.Properties.MaxValue = new decimal(new int[] {
            1500,
            0,
            0,
            0});
            this.ceHeight.Properties.MinValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.ceHeight.Size = new System.Drawing.Size(55, 20);
            this.ceHeight.TabIndex = 15;
            // 
            // ceWidth
            // 
            this.ceWidth.EditValue = new decimal(new int[] {
            250,
            0,
            0,
            0});
            this.ceWidth.Location = new System.Drawing.Point(128, 276);
            this.ceWidth.Name = "ceWidth";
            this.ceWidth.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.ceWidth.Properties.IsFloatValue = false;
            this.ceWidth.Properties.Mask.EditMask = "N00";
            this.ceWidth.Properties.MaxValue = new decimal(new int[] {
            1500,
            0,
            0,
            0});
            this.ceWidth.Properties.MinValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.ceWidth.Size = new System.Drawing.Size(55, 20);
            this.ceWidth.TabIndex = 14;
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(14, 279);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(101, 13);
            this.labelControl1.TabIndex = 13;
            this.labelControl1.Text = "Alert Width x Height:";
            this.labelControl1.Click += new System.EventHandler(this.labelControl1_Click);
            // 
            // txtText
            // 
            this.txtText.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtText.Location = new System.Drawing.Point(345, 150);
            this.txtText.Name = "txtText";
            this.txtText.Size = new System.Drawing.Size(328, 130);
            this.txtText.TabIndex = 18;
            this.txtText.ToolTip = "You may use html and columns names in curly brackets.";
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(298, 149);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(26, 13);
            this.labelControl3.TabIndex = 20;
            this.labelControl3.Text = "Text:";
            // 
            // txtCaption
            // 
            this.txtCaption.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.txtCaption.Location = new System.Drawing.Point(345, 121);
            this.txtCaption.Name = "txtCaption";
            this.txtCaption.Size = new System.Drawing.Size(247, 20);
            this.txtCaption.TabIndex = 17;
            this.txtCaption.ToolTip = "You may use html and columns names in curly brackets.";
            // 
            // labelControl4
            // 
            this.labelControl4.Location = new System.Drawing.Point(298, 123);
            this.labelControl4.Name = "labelControl4";
            this.labelControl4.Size = new System.Drawing.Size(41, 13);
            this.labelControl4.TabIndex = 19;
            this.labelControl4.Text = "Caption:";
            // 
            // btnTestAlert
            // 
            this.btnTestAlert.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnTestAlert.Location = new System.Drawing.Point(598, 118);
            this.btnTestAlert.Name = "btnTestAlert";
            this.btnTestAlert.Size = new System.Drawing.Size(75, 25);
            this.btnTestAlert.TabIndex = 21;
            this.btnTestAlert.Text = "Test Alert";
            this.btnTestAlert.Click += new System.EventHandler(this.btnTestAlert_Click);
            // 
            // btnSave
            // 
            this.btnSave.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSave.Location = new System.Drawing.Point(598, 303);
            this.btnSave.Name = "btnSave";
            this.btnSave.Size = new System.Drawing.Size(75, 25);
            this.btnSave.TabIndex = 21;
            this.btnSave.Text = "Save";
            this.btnSave.Click += new System.EventHandler(this.btnSave_Click);
            // 
            // labelControl7
            // 
            this.labelControl7.Location = new System.Drawing.Point(14, 227);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(93, 13);
            this.labelControl7.TabIndex = 38;
            this.labelControl7.Text = "Max alerts at once:";
            this.labelControl7.ToolTip = "Maximum number of alerts on your screen at any given moment.";
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(14, 201);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(76, 13);
            this.labelControl5.TabIndex = 37;
            this.labelControl5.Text = "Showing Effect:";
            // 
            // ceAutoHeight
            // 
            this.ceAutoHeight.Location = new System.Drawing.Point(12, 94);
            this.ceAutoHeight.Name = "ceAutoHeight";
            this.ceAutoHeight.Properties.AllowHtmlDraw = DevExpress.Utils.DefaultBoolean.True;
            this.ceAutoHeight.Properties.Caption = "Auto Height ";
            this.ceAutoHeight.Properties.GlyphAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.ceAutoHeight.Size = new System.Drawing.Size(133, 19);
            this.ceAutoHeight.TabIndex = 36;
            this.ceAutoHeight.ToolTip = "Auto adjust height to show all text. Otherwise text will be trimmed.";
            // 
            // icbFormLocation
            // 
            this.icbFormLocation.Location = new System.Drawing.Point(128, 172);
            this.icbFormLocation.Name = "icbFormLocation";
            this.icbFormLocation.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.icbFormLocation.Size = new System.Drawing.Size(131, 20);
            this.icbFormLocation.TabIndex = 29;
            this.icbFormLocation.ToolTip = "Alert location on your screen";
            // 
            // labelControl8
            // 
            this.labelControl8.Location = new System.Drawing.Point(14, 175);
            this.labelControl8.Name = "labelControl8";
            this.labelControl8.Size = new System.Drawing.Size(44, 13);
            this.labelControl8.TabIndex = 30;
            this.labelControl8.Text = "Location:";
            this.labelControl8.ToolTip = "Alert location on your screen";
            // 
            // icbFormDisplaySpeed
            // 
            this.icbFormDisplaySpeed.Location = new System.Drawing.Point(128, 146);
            this.icbFormDisplaySpeed.Name = "icbFormDisplaySpeed";
            this.icbFormDisplaySpeed.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.icbFormDisplaySpeed.Size = new System.Drawing.Size(131, 20);
            this.icbFormDisplaySpeed.TabIndex = 27;
            // 
            // labelControl9
            // 
            this.labelControl9.Location = new System.Drawing.Point(14, 149);
            this.labelControl9.Name = "labelControl9";
            this.labelControl9.Size = new System.Drawing.Size(71, 13);
            this.labelControl9.TabIndex = 28;
            this.labelControl9.Text = "Display Speed:";
            // 
            // seAutoFormDelay
            // 
            this.seAutoFormDelay.EditValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.seAutoFormDelay.Location = new System.Drawing.Point(128, 120);
            this.seAutoFormDelay.Name = "seAutoFormDelay";
            this.seAutoFormDelay.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.seAutoFormDelay.Properties.IsFloatValue = false;
            this.seAutoFormDelay.Properties.Mask.EditMask = "N00";
            this.seAutoFormDelay.Properties.MaxValue = new decimal(new int[] {
            84000,
            0,
            0,
            0});
            this.seAutoFormDelay.Properties.MinValue = new decimal(new int[] {
            1,
            0,
            0,
            0});
            this.seAutoFormDelay.Size = new System.Drawing.Size(131, 20);
            this.seAutoFormDelay.TabIndex = 23;
            this.seAutoFormDelay.ToolTip = "Close alert after X seconds.";
            // 
            // labelControl11
            // 
            this.labelControl11.Location = new System.Drawing.Point(14, 123);
            this.labelControl11.Name = "labelControl11";
            this.labelControl11.Size = new System.Drawing.Size(58, 13);
            this.labelControl11.TabIndex = 24;
            this.labelControl11.Text = "Close After:";
            this.labelControl11.ToolTip = "Close alert after X seconds.";
            // 
            // seFormMaxCount
            // 
            this.seFormMaxCount.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.seFormMaxCount.Location = new System.Drawing.Point(128, 224);
            this.seFormMaxCount.Name = "seFormMaxCount";
            this.seFormMaxCount.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.seFormMaxCount.Properties.IsFloatValue = false;
            this.seFormMaxCount.Properties.Mask.EditMask = "N00";
            this.seFormMaxCount.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.None;
            this.seFormMaxCount.Properties.MaxValue = new decimal(new int[] {
            1000,
            0,
            0,
            0});
            this.seFormMaxCount.Size = new System.Drawing.Size(131, 20);
            this.seFormMaxCount.TabIndex = 34;
            this.seFormMaxCount.ToolTip = "Maximum number of alerts on your screen at any given moment.";
            // 
            // labelControl12
            // 
            this.labelControl12.Location = new System.Drawing.Point(14, 253);
            this.labelControl12.Name = "labelControl12";
            this.labelControl12.Size = new System.Drawing.Size(47, 13);
            this.labelControl12.TabIndex = 13;
            this.labelControl12.Text = "Font size:";
            // 
            // spinEditFontSize
            // 
            this.spinEditFontSize.EditValue = new decimal(new int[] {
            10,
            0,
            0,
            0});
            this.spinEditFontSize.Location = new System.Drawing.Point(128, 250);
            this.spinEditFontSize.Name = "spinEditFontSize";
            this.spinEditFontSize.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton()});
            this.spinEditFontSize.Properties.IsFloatValue = false;
            this.spinEditFontSize.Properties.Mask.EditMask = "N00";
            this.spinEditFontSize.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.None;
            this.spinEditFontSize.Properties.MaxValue = new decimal(new int[] {
            100,
            0,
            0,
            0});
            this.spinEditFontSize.Properties.MinValue = new decimal(new int[] {
            5,
            0,
            0,
            0});
            this.spinEditFontSize.Size = new System.Drawing.Size(131, 20);
            this.spinEditFontSize.TabIndex = 34;
            // 
            // checkEditShowImage
            // 
            this.checkEditShowImage.Location = new System.Drawing.Point(12, 69);
            this.checkEditShowImage.Name = "checkEditShowImage";
            this.checkEditShowImage.Properties.Caption = "Show Thumbnail";
            this.checkEditShowImage.Properties.GlyphAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.checkEditShowImage.Size = new System.Drawing.Size(133, 19);
            this.checkEditShowImage.TabIndex = 35;
            this.checkEditShowImage.ToolTip = "Show small picture in notification window.";
            // 
            // checkEditEnable
            // 
            this.checkEditEnable.Location = new System.Drawing.Point(12, 44);
            this.checkEditEnable.Name = "checkEditEnable";
            this.checkEditEnable.Properties.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Bold);
            this.checkEditEnable.Properties.Appearance.Options.UseFont = true;
            this.checkEditEnable.Properties.Caption = "Alerts Enabled";
            this.checkEditEnable.Properties.GlyphAlignment = DevExpress.Utils.HorzAlignment.Far;
            this.checkEditEnable.Size = new System.Drawing.Size(133, 19);
            this.checkEditEnable.TabIndex = 35;
            this.checkEditEnable.ToolTip = "Show notifications about new items.";
            // 
            // icbShowingEffect
            // 
            this.icbShowingEffect.Location = new System.Drawing.Point(128, 198);
            this.icbShowingEffect.Name = "icbShowingEffect";
            this.icbShowingEffect.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.icbShowingEffect.Size = new System.Drawing.Size(131, 20);
            this.icbShowingEffect.TabIndex = 29;
            // 
            // hyperlinkLabelControlHelp
            // 
            this.hyperlinkLabelControlHelp.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.hyperlinkLabelControlHelp.Cursor = System.Windows.Forms.Cursors.Hand;
            this.hyperlinkLabelControlHelp.Location = new System.Drawing.Point(14, 315);
            this.hyperlinkLabelControlHelp.Name = "hyperlinkLabelControlHelp";
            this.hyperlinkLabelControlHelp.Size = new System.Drawing.Size(21, 13);
            this.hyperlinkLabelControlHelp.TabIndex = 40;
            this.hyperlinkLabelControlHelp.Tag = "https://ubuyfirst.com/10-other-settings-and-features-advanced/#Tray Alert";
            this.hyperlinkLabelControlHelp.Text = "Help";
            this.hyperlinkLabelControlHelp.Click += new System.EventHandler(this.hyperlinkLabelControlHelp_Click);
            // 
            // FormAlert
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(691, 340);
            this.Controls.Add(this.hyperlinkLabelControlHelp);
            this.Controls.Add(this.labelControl7);
            this.Controls.Add(this.labelControl5);
            this.Controls.Add(this.ceAutoHeight);
            this.Controls.Add(this.checkEditEnable);
            this.Controls.Add(this.checkEditShowImage);
            this.Controls.Add(this.icbShowingEffect);
            this.Controls.Add(this.icbFormLocation);
            this.Controls.Add(this.labelControl8);
            this.Controls.Add(this.icbFormDisplaySpeed);
            this.Controls.Add(this.labelControl9);
            this.Controls.Add(this.seAutoFormDelay);
            this.Controls.Add(this.labelControl11);
            this.Controls.Add(this.spinEditFontSize);
            this.Controls.Add(this.seFormMaxCount);
            this.Controls.Add(this.btnSave);
            this.Controls.Add(this.btnTestAlert);
            this.Controls.Add(this.txtText);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.txtCaption);
            this.Controls.Add(this.labelControl4);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.ceHeight);
            this.Controls.Add(this.ceWidth);
            this.Controls.Add(this.labelControl12);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.ribbon);
            this.IconOptions.Icon = ((System.Drawing.Icon)(resources.GetObject("FormAlert.IconOptions.Icon")));
            this.Name = "FormAlert";
            this.Ribbon = this.ribbon;
            this.Text = "Alert";
            this.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.FormAlert_KeyPress);
            ((System.ComponentModel.ISupportInitialize)(this.ribbon)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceHeight.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceWidth.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtText.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCaption.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.ceAutoHeight.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.icbFormLocation.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.icbFormDisplaySpeed.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.seAutoFormDelay.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.seFormMaxCount.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.spinEditFontSize.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditShowImage.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.checkEditEnable.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.icbShowingEffect.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private DevExpress.XtraBars.Ribbon.RibbonControl ribbon;
        private DevExpress.XtraEditors.LabelControl labelControl2;
        private DevExpress.XtraEditors.SpinEdit ceHeight;
        private DevExpress.XtraEditors.SpinEdit ceWidth;
        private DevExpress.XtraEditors.LabelControl labelControl1;
        private DevExpress.XtraEditors.MemoEdit txtText;
        private DevExpress.XtraEditors.LabelControl labelControl3;
        private DevExpress.XtraEditors.TextEdit txtCaption;
        private DevExpress.XtraEditors.LabelControl labelControl4;
        private DevExpress.XtraEditors.SimpleButton btnTestAlert;
        private DevExpress.XtraEditors.SimpleButton btnSave;
        private DevExpress.XtraEditors.LabelControl labelControl7;
        private DevExpress.XtraEditors.LabelControl labelControl5;
        private DevExpress.XtraEditors.CheckEdit ceAutoHeight;
        private DevExpress.XtraEditors.ImageComboBoxEdit icbFormLocation;
        private DevExpress.XtraEditors.LabelControl labelControl8;
        private DevExpress.XtraEditors.ImageComboBoxEdit icbFormDisplaySpeed;
        private DevExpress.XtraEditors.LabelControl labelControl9;
        private DevExpress.XtraEditors.SpinEdit seAutoFormDelay;
        private DevExpress.XtraEditors.LabelControl labelControl11;
        private DevExpress.XtraEditors.SpinEdit seFormMaxCount;
        private DevExpress.XtraEditors.LabelControl labelControl12;
        private DevExpress.XtraEditors.SpinEdit spinEditFontSize;
        private DevExpress.XtraEditors.CheckEdit checkEditShowImage;
        private DevExpress.XtraEditors.CheckEdit checkEditEnable;
        private DevExpress.XtraEditors.ImageComboBoxEdit icbShowingEffect;
        private DevExpress.XtraEditors.HyperlinkLabelControl hyperlinkLabelControlHelp;
    }
}