﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace uBuyFirst.Properties {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class En_US {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal En_US() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("uBuyFirst.Properties.En-US", typeof(En_US).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Serial Number is required.
        /// </summary>
        internal static string ActivationForm_ActivateButton_Click_Serial_Number_is_required {
            get {
                return ResourceManager.GetString("ActivationForm_ActivateButton_Click_Serial_Number_is_required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trial. Days left: .
        /// </summary>
        internal static string ActivationForm_ActivationForm_Trial__Days_left__ {
            get {
                return ResourceManager.GetString("ActivationForm_ActivationForm_Trial__Days_left__", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can&apos;t get session ID. 
        ///1. Check your internet connection 
        ///2. Try again later. 
        ///3. Contact support..
        /// </summary>
        internal static string Cant_get_session_id_for_ebay_auth {
            get {
                return ResourceManager.GetString("Cant_get_session_id_for_ebay_auth", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to save filters?.
        /// </summary>
        internal static string FiltersForm_UserClosingForm_Do_you_want_to_save_filters_ {
            get {
                return ResourceManager.GetString("FiltersForm_UserClosingForm_Do_you_want_to_save_filters_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your current subscription plan does not allow adding multiple eBay accounts. Please upgrade..
        /// </summary>
        internal static string Form1_AddEbayAccount_Your_current_subscription_plan_does_not_allow_adding_multiple_eBay_accounts__Please_upgrade_ {
            get {
                return ResourceManager.GetString("Form1_AddEbayAccount_Your_current_subscription_plan_does_not_allow_adding_multipl" +
                        "e_eBay_accounts__Please_upgrade_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Start.
        /// </summary>
        internal static string Form1_BackgroundWorker1RunWorkerCompleted_Start {
            get {
                return ResourceManager.GetString("Form1_BackgroundWorker1RunWorkerCompleted_Start", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No valid eBay authorization token found.
        ///Please, link you eBay account with uBuyFirst..
        /// </summary>
        internal static string Form1_BtnMainStartClick_ {
            get {
                return ResourceManager.GetString("Form1_BtnMainStartClick_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You have not authenticated your eBay account with uBuyFirst.
        ///You won&apos;t be able to purchase &quot;commit to buy&quot; items directly through the application.
        ///Authenticate now? .
        /// </summary>
        internal static string Form1_BtnMainStartClick_Not_Authenticated_Warning {
            get {
                return ResourceManager.GetString("Form1_BtnMainStartClick_Not_Authenticated_Warning", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No keywords setup. Create keyword searches and retry..
        /// </summary>
        internal static string Form1_BtnMainStartClick_Please__add_a_keyword {
            get {
                return ResourceManager.GetString("Form1_BtnMainStartClick_Please__add_a_keyword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No keywords are active. Activate keywords and retry..
        /// </summary>
        internal static string Form1_BtnMainStartClick_Please__enable_at_least_one_keyword {
            get {
                return ResourceManager.GetString("Form1_BtnMainStartClick_Please__enable_at_least_one_keyword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stop.
        /// </summary>
        internal static string Form1_BtnMainStartClick_Stop {
            get {
                return ResourceManager.GetString("Form1_BtnMainStartClick_Stop", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Stopping.
        /// </summary>
        internal static string Form1_BtnMainStartClick_Stopping {
            get {
                return ResourceManager.GetString("Form1_BtnMainStartClick_Stopping", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your eBay account token has been removed successfully from uBuyFirst - &apos;.
        /// </summary>
        internal static string Form1_btnRemoveEbayAccount_Click_We_successfully_removed_uBuyfirst_authorization_from_account__ {
            get {
                return ResourceManager.GetString("Form1_btnRemoveEbayAccount_Click_We_successfully_removed_uBuyfirst_authorization_" +
                        "from_account__", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove eBay account token from uBuyFirst - &apos;.
        /// </summary>
        internal static string Form1_btnRemoveEbayAccount_Click_We_will_remove_uBuyFirst_authorization_from_eBay_account__ {
            get {
                return ResourceManager.GetString("Form1_btnRemoveEbayAccount_Click_We_will_remove_uBuyFirst_authorization_from_eBay" +
                        "_account__", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Do you want to sort this column?.
        /// </summary>
        internal static string Form1_dgv1_ColumnHeaderMouseClick_Do_you_want_to_sort_this_column_ {
            get {
                return ResourceManager.GetString("Form1_dgv1_ColumnHeaderMouseClick_Do_you_want_to_sort_this_column_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Zipcode found. Enter the zipcode where your items will ship to. &quot;.
        /// </summary>
        internal static string Form1_FindRequestsApi_Provide_a_valid_Zip_code_for_keyword___ {
            get {
                return ResourceManager.GetString("Form1_FindRequestsApi_Provide_a_valid_Zip_code_for_keyword___", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Build: v..
        /// </summary>
        internal static string Form1_Form1_Build__v_ {
            get {
                return ResourceManager.GetString("Form1_Form1_Build__v_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can&apos;t connect to ubuyfirst.com..
        /// </summary>
        internal static string Form1_Form1_Shown_Can_t_connect_to_ubuyfirst_com_ {
            get {
                return ResourceManager.GetString("Form1_Form1_Shown_Can_t_connect_to_ubuyfirst_com_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to License: Trial. Days left: .
        /// </summary>
        internal static string Form1_Form1_Shown_License__Trial__Days_left__ {
            get {
                return ResourceManager.GetString("Form1_Form1_Shown_License__Trial__Days_left__", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to .NET Framework v4.6 required
        ///You can get it at https://www.microsoft.com/en-us/download/details.aspx?id=48130
        ///Open the website now?.
        /// </summary>
        internal static string Form1_Framework_Required {
            get {
                return ResourceManager.GetString("Form1_Framework_Required", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No valid eBay authorization token found.
        ///Please, link you eBay account with uBuyFirst..
        /// </summary>
        internal static string Form1_GetCategorySpecifics_ {
            get {
                return ResourceManager.GetString("Form1_GetCategorySpecifics_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to eBay account was removed from accounts list.
        /// </summary>
        internal static string Form1_isTokenValid_eBay_account_was_removed_from_accounts_list {
            get {
                return ResourceManager.GetString("Form1_isTokenValid_eBay_account_was_removed_from_accounts_list", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Couldn&apos;t read config.cfg. Remove it and try again {0}.
        /// </summary>
        internal static string Form1_LoadSettings_ {
            get {
                return ResourceManager.GetString("Form1_LoadSettings_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hide.
        /// </summary>
        internal static string Form1_PopupFromTray_Hide {
            get {
                return ResourceManager.GetString("Form1_PopupFromTray_Hide", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your current subscription plan doesn&apos;t allow more than {0} search terms. Please upgrade subscription or try combining searches..
        /// </summary>
        internal static string Form1_SearchKeywordEditCreate_Your_current_subscription_plan_doesn_t_allow_more_than__0__search_terms__Please_upgrade_ {
            get {
                return ResourceManager.GetString("Form1_SearchKeywordEditCreate_Your_current_subscription_plan_doesn_t_allow_more_t" +
                        "han__0__search_terms__Please_upgrade_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter a keyword.
        /// </summary>
        internal static string Form1_SearchKeywordSave_Please__enter_a_keyword {
            get {
                return ResourceManager.GetString("Form1_SearchKeywordSave_Please__enter_a_keyword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter Alias name and try again..
        /// </summary>
        internal static string Form1_SearchKeywordSave_Please__enter_keyword_Name {
            get {
                return ResourceManager.GetString("Form1_SearchKeywordSave_Please__enter_keyword_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter maximum price and try again..
        /// </summary>
        internal static string Form1_SearchKeywordSave_Please__enter_maximum_Price {
            get {
                return ResourceManager.GetString("Form1_SearchKeywordSave_Please__enter_maximum_Price", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter minimum price and try again..
        /// </summary>
        internal static string Form1_SearchKeywordSave_Please__enter_minimum_Price {
            get {
                return ResourceManager.GetString("Form1_SearchKeywordSave_Please__enter_minimum_Price", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to A maximum of 3 categories are allowed for each search term. Please edit and try again..
        /// </summary>
        internal static string Form1_SearchKeywordSave_Please__use_no_more_than_3_categories {
            get {
                return ResourceManager.GetString("Form1_SearchKeywordSave_Please__use_no_more_than_3_categories", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your current subscription plan doesn&apos;t allow more than {0} search terms. Please upgrade subscription or try combining searches..
        /// </summary>
        internal static string Form1_SearchKeywordSave_Your_current_subscription_plan_doesn_t_allow_more_than__0__search_terms__Please_upgrade_ {
            get {
                return ResourceManager.GetString("Form1_SearchKeywordSave_Your_current_subscription_plan_doesn_t_allow_more_than__0" +
                        "__search_terms__Please_upgrade_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, close application and restart for subscription settings to take effect..
        /// </summary>
        internal static string Form1_ShowActivationWindow_Please__restart_the_application__ {
            get {
                return ResourceManager.GetString("Form1_ShowActivationWindow_Please__restart_the_application__", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Listing is no longer available..
        /// </summary>
        internal static string Form1_ShowPlaceOffer_Listing_is_not_available {
            get {
                return ResourceManager.GetString("Form1_ShowPlaceOffer_Listing_is_not_available", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Success! : License key activated.
        /// </summary>
        internal static string Form1_ShowRegDialog_ {
            get {
                return ResourceManager.GetString("Form1_ShowRegDialog_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show.
        /// </summary>
        internal static string Form1_TrayIcon_Click_Show {
            get {
                return ResourceManager.GetString("Form1_TrayIcon_Click_Show", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Searching paused due to inactivity. (Idle Timeout setting)
        ///Continue?.
        /// </summary>
        internal static string Form1MainStart3SearchingPausedDueToInactivityIdleTimeoutSettingContinue {
            get {
                return ResourceManager.GetString("Form1MainStart3SearchingPausedDueToInactivityIdleTimeoutSettingContinue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Session ID not found
        ///Please, complete Step 1..
        /// </summary>
        internal static string FormAuth_btnEbayAuthConfirm_Click_ {
            get {
                return ResourceManager.GetString("FormAuth_btnEbayAuthConfirm_Click_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Can&apos;t fetch authentication token. 
        ///1. Check your internet connection 
        ///2. Try again later. 
        ///3. Contact support..
        /// </summary>
        internal static string FormAuth_btnEbayAuthConfirm_Click_2 {
            get {
                return ResourceManager.GetString("FormAuth_btnEbayAuthConfirm_Click_2", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, choose your eBay website.
        /// </summary>
        internal static string FormAuth_btnEbayDoAuth_Click_Please__choose_your_eBay_website {
            get {
                return ResourceManager.GetString("FormAuth_btnEbayDoAuth_Click_Please__choose_your_eBay_website", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to You must enter credentials from http://sandbox.ebay.com , NOT from ebay.com. Username starts with testuser_*.
        /// </summary>
        internal static string FormAuth_btnEbayDoAuth_Click_You_must_enter_credentials_from_http___sandbox_ebay_com___NOT_from_ebay_com__Username_starts_with_testuser__ {
            get {
                return ResourceManager.GetString("FormAuth_btnEbayDoAuth_Click_You_must_enter_credentials_from_http___sandbox_ebay_" +
                        "com___NOT_from_ebay_com__Username_starts_with_testuser__", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection established..
        /// </summary>
        internal static string FormAuth_FormAuth_Shown_Connection_established_ {
            get {
                return ResourceManager.GetString("FormAuth_FormAuth_Shown_Connection_established_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Connection failed. Couldn&apos;t fetch session ID..
        /// </summary>
        internal static string FormAuth_FormAuth_Shown_Connection_failed__Couldn_t_fetch_session_ID_ {
            get {
                return ResourceManager.GetString("FormAuth_FormAuth_Shown_Connection_failed__Couldn_t_fetch_session_ID_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Establishing connection with eBay server....
        /// </summary>
        internal static string FormAuth_FormAuth_Shown_Establishing_connection_with_eBay_server___ {
            get {
                return ResourceManager.GetString("FormAuth_FormAuth_Shown_Establishing_connection_with_eBay_server___", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Verifying token status....
        /// </summary>
        internal static string FormAuth_FormAuth_Shown_Verifying_token_status___ {
            get {
                return ResourceManager.GetString("FormAuth_FormAuth_Shown_Verifying_token_status___", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy this item now?.
        /// </summary>
        internal static string FormBid_btnPurchase_Click_Do_you_want_to_purchase_this_item_ {
            get {
                return ResourceManager.GetString("FormBid_btnPurchase_Click_Do_you_want_to_purchase_this_item_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, provide a numeric Category ID.
        /// </summary>
        internal static string FormCustomColumns_btnFindCustomColumns_Click_Please__provide_a_numeric_Category_ID {
            get {
                return ResourceManager.GetString("FormCustomColumns_btnFindCustomColumns_Click_Please__provide_a_numeric_Category_I" +
                        "D", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Keyword with the same Alias already exists. Please rename and try again..
        /// </summary>
        internal static string FormNewKeyword_btnSave_Click_Keyword_with_the_same_Alias_already_exists {
            get {
                return ResourceManager.GetString("FormNewKeyword_btnSave_Click_Keyword_with_the_same_Alias_already_exists", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to test.
        /// </summary>
        internal static string FormNewKeyword_btnSave_Click_test {
            get {
                return ResourceManager.GetString("FormNewKeyword_btnSave_Click_test", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter a keyword.
        /// </summary>
        internal static string FormNewKeyword_SearchKeywordSave_Please__enter_a_keyword {
            get {
                return ResourceManager.GetString("FormNewKeyword_SearchKeywordSave_Please__enter_a_keyword", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter Alias name and try again..
        /// </summary>
        internal static string FormNewKeyword_SearchKeywordSave_Please__enter_keyword_Name {
            get {
                return ResourceManager.GetString("FormNewKeyword_SearchKeywordSave_Please__enter_keyword_Name", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter maximum price and try again..
        /// </summary>
        internal static string FormNewKeyword_SearchKeywordSave_Please__enter_maximum_Price {
            get {
                return ResourceManager.GetString("FormNewKeyword_SearchKeywordSave_Please__enter_maximum_Price", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, enter minimum price and try again..
        /// </summary>
        internal static string FormNewKeyword_SearchKeywordSave_Please__enter_minimum_Price {
            get {
                return ResourceManager.GetString("FormNewKeyword_SearchKeywordSave_Please__enter_minimum_Price", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, provide a Zip code.
        /// </summary>
        internal static string FormNewKeyword_SearchKeywordSave_Please__provide_a_Zip_code {
            get {
                return ResourceManager.GetString("FormNewKeyword_SearchKeywordSave_Please__provide_a_Zip_code", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, use no more than 10 sellers. You have - .
        /// </summary>
        internal static string FormNewKeyword_SearchKeywordSave_Please__use_no_more_than_10_sellers__You_have___X {
            get {
                return ResourceManager.GetString("FormNewKeyword_SearchKeywordSave_Please__use_no_more_than_10_sellers__You_have___" +
                        "X", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please, use no more than 3 categories.
        /// </summary>
        internal static string FormNewKeyword_SearchKeywordSave_Please__use_no_more_than_3_categories {
            get {
                return ResourceManager.GetString("FormNewKeyword_SearchKeywordSave_Please__use_no_more_than_3_categories", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Buy Now.
        /// </summary>
        internal static string FormTrialSubscriptionPrompt_BuyNow {
            get {
                return ResourceManager.GetString("FormTrialSubscriptionPrompt_BuyNow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Continue Trial.
        /// </summary>
        internal static string FormTrialSubscriptionPrompt_ContinueTrial {
            get {
                return ResourceManager.GetString("FormTrialSubscriptionPrompt_ContinueTrial", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Your Trial Expires in {0} Days.
        /// </summary>
        internal static string FormTrialSubscriptionPrompt_DaysLeft {
            get {
                return ResourceManager.GetString("FormTrialSubscriptionPrompt_DaysLeft", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter Software Key.
        /// </summary>
        internal static string FormTrialSubscriptionPrompt_EnterSoftwareKey {
            get {
                return ResourceManager.GetString("FormTrialSubscriptionPrompt_EnterSoftwareKey", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Free Tutorials.
        /// </summary>
        internal static string FormTrialSubscriptionPrompt_TrialLinkText {
            get {
                return ResourceManager.GetString("FormTrialSubscriptionPrompt_TrialLinkText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to To get more out of your uBuyFirst trial, view our .
        /// </summary>
        internal static string FormTrialSubscriptionPrompt_TrialText {
            get {
                return ResourceManager.GetString("FormTrialSubscriptionPrompt_TrialText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Couldn&apos;t validate the condition rule. Try to change it..
        /// </summary>
        internal static string FormXfilters_btnAccept_Click_Couldn_t_validate_the_condition_rule_Try_to_change_it_ {
            get {
                return ResourceManager.GetString("FormXfilters_btnAccept_Click_Couldn_t_validate_the_condition_rule_Try_to_change_i" +
                        "t_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thank you. Please, restart the application..
        /// </summary>
        internal static string LicenseUtility_ActivateLicense_Thank_you__Please__restart_the_application_ {
            get {
                return ResourceManager.GetString("LicenseUtility_ActivateLicense_Thank_you__Please__restart_the_application_", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trial Status Loading...
        /// </summary>
        internal static string TrialSubscriptionStatusLoading {
            get {
                return ResourceManager.GetString("TrialSubscriptionStatusLoading", resourceCulture);
            }
        }
    }
}
