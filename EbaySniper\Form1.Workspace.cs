﻿using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Windows.Forms;
using DevExpress.Utils;
using DevExpress.XtraBars;
using DevExpress.XtraBars.Docking;
using DevExpress.XtraEditors;
using DevExpress.XtraLayout;
using uBuyFirst.Grid;
using uBuyFirst.GUI;
using uBuyFirst.Other;
using uBuyFirst.Tools;
using uBuyFirst.Views;

namespace uBuyFirst
{
    public partial class Form1
    {
        private static bool ShouldCreateMissingPanels;
        private void LoadWorkspacesOnFormLoad()
        {
            TryLoadExistingWorkspace(workspaceManager1);
            workspaceManager1.Workspaces.Clear();
            AddWorkspacesToMenu(workspaceManager1);
        }

        private void ResetGridViewIfNoWorkspacesFound()
        {
            if (!File.Exists(Path.Combine(Folders.Workspace, "Workspace Active.xml"))
                && !File.Exists(Path.Combine(Folders.Workspace, "Basic Layout (Default).xml"))
                && !File.Exists(Path.Combine(Folders.Workspace, "Full Details (Default).xml"))
                || File.Exists(Path.Combine(Folders.Workspace, "DefaultWorkspace.xml")))
            {
                var uniqGrids = GridBuilder.GetUniqGrids(_ebaySearches.ChildrenCore);
                foreach (var grView in uniqGrids)
                {
                    GridBuilder.ResetGridViewLayout(grView);
                }
            }
        }

        private void LoadAndApplyWorkSpace()
        {
            ShouldCreateMissingPanels = true;
            var defaultWorkspaces = new[] { "Workspace Active", "Full Details (Default)", "Basic Layout (Default)" };
            foreach (var workspace in defaultWorkspaces)
            {
                if (File.Exists(Path.Combine(Folders.Workspace, workspace + ".xml")))
                {
                    try
                    {
                        var workspacePath = Path.Combine(Folders.Workspace, workspace + ".xml");
                        WorkspacePatcher.PatchWorkspace(workspacePath);
                        workspaceManager1.LoadWorkspace(workspace, workspacePath);
                        workspaceManager1.ApplyWorkspace(workspace);

                        CreateViewsAfterApplyWorkspace();
                    }
                    catch (Exception ex)
                    {
                        if (Debugger.IsAttached)
                            MessageBox.Show(ex.Message);
                    }

                    break;
                }
            }
        }

        private void CreateViewsAfterApplyWorkspace()
        {
            foreach (var pair in ResultsView.ViewsDict)
            {
                if (dockManager1.Panels.All(p => p.Text != pair.Key))
                {
                    ResultsView.CreateView(pair.Key);
                    foreach (var kw2Find in _ebaySearches.ChildrenCore)
                    {
                        if (kw2Find.ViewName == pair.Key)
                            ResultsView.AssignViewToSearch(pair.Key, kw2Find);
                    }
                }
            }
        }

        private void SaveUserWorkspaces()
        {
            foreach (var workspace in workspaceManager1.Workspaces)
            {
                workspaceManager1.SaveWorkspace(workspace.Name, Path.Combine(Folders.Workspace, workspace.Name + ".xml"));
            }

            if (!_saveActiveWorkspace)
                return;

            workspaceManager1.CaptureWorkspace("Workspace Active");
            workspaceManager1.SaveWorkspace("Workspace Active", Path.Combine(Folders.Workspace, "Workspace Active.xml"));
            workspaceManager1.RemoveWorkspace("Workspace Active");
        }

        private void barButtonItemResetWorkspace_ItemClick(object sender, ItemClickEventArgs e)
        {
            var dialogResult = XtraMessageBox.Show(barButtonResetWorkspace.SuperTip.ToString(), "Continue with layout reset?", MessageBoxButtons.YesNo, MessageBoxIcon.Warning,
                MessageBoxDefaultButton.Button2);
            if (dialogResult != DialogResult.Yes)
                return;

            File.Delete(Path.Combine(Folders.Workspace, "Workspace Active.xml"));
            _saveActiveWorkspace = false;
            Application.Exit();

        }

        private void workspaceManager1_AfterApplyWorkspace(object sender, EventArgs e)
        {
            var obsoletePanel = dockManager1.Panels.FirstOrDefault(p => p.ID == new Guid("92581858-ecdc-4d88-be24-9bb7948442dc"));
            obsoletePanel?.Dispose();
            CreateViewsAfterApplyWorkspace();

            ChangePanelHeader();
            ResultsView.TogglePanelsVisibility();
            ToggleCheckoutPermission();
            ToggleRestockPermission();
            if (ShouldCreateMissingPanels)
            {
                CreateMissingPanel(dockPanelWatchlist, layoutWatchlistControls);
                CreateMissingPanel(dockPanelExternalData, layoutControlCefBrowser);
            }
            ribbonPageSync.Visible = true;
            ribbonPageData.Visible = true;
        }

        private void CreateMissingPanel(DockPanel missingPanel, LayoutControl panelControls)
        {
            if (this.dockPanelWatchlist != null)
            {
                var panelInManager = dockManager1.Panels.FirstOrDefault(p => p.Name == missingPanel.Name);
                if (panelInManager == null)
                {
                    dockManager1.BeginUpdate();

                    var newDockPanel = dockManager1.AddPanel(panelControls, DockingStyle.Bottom);
                    newDockPanel.ControlContainer.Name = missingPanel.ControlContainer.Name;
                    newDockPanel.ControlContainer.Controls.Clear();
                    newDockPanel.ControlContainer.Controls.Add(panelControls);
                    newDockPanel.ID = missingPanel.ID;
                    newDockPanel.Header = missingPanel.Header;
                    newDockPanel.Text = missingPanel.Text;
                    newDockPanel.TabText = missingPanel.TabText;
                    newDockPanel.Tag = missingPanel.Tag;
                    newDockPanel.Name = missingPanel.Name;
                    newDockPanel.Options.ShowCloseButton = missingPanel.Options.ShowCloseButton;
                    dockManager1.EndUpdate();
                    newDockPanel.Close();
                }
            }
        }

        private static void TryLoadExistingWorkspace(WorkspaceManager workspaceManager1)
        {
            var defaultWorkspaces = new[] { "Workspace Active", "Full Details (Default)", "Basic Layout (Default)" };
            foreach (var workspaceName in defaultWorkspaces)
            {
                var isActiveWorkspaceBroken = false;
                if (!File.Exists(Path.Combine(Folders.Workspace, workspaceName + ".xml")))
                    continue;
                var workspacePath = Path.Combine(Folders.Workspace, workspaceName + ".xml");
                try
                {
                    WorkspacePatcher.PatchWorkspace(workspacePath);
                    workspaceManager1.LoadWorkspace(workspaceName, workspacePath);
                    workspaceManager1.ApplyWorkspace(workspaceName);
                    break;
                }
                catch (Exception ex)
                {
                    if (workspaceName == "Workspace Active")
                        isActiveWorkspaceBroken = true;
                    XtraMessageBox.Show("Loading workspace: \n" + ex.Message);
                    ExM.ubuyExceptionHandler("_(info)_Loading workspace: ", ex);
                }

                if (isActiveWorkspaceBroken)
                {
                    File.Delete(workspacePath);
                    Environment.Exit(1);
                }
            }
        }

        private static void AddWorkspacesToMenu(WorkspaceManager workspaceManager1)
        {
            var workspaceFiles = Directory.GetFiles(Folders.Workspace);
            foreach (var workspacePath in workspaceFiles)
            {
                var fileName = Path.GetFileNameWithoutExtension(workspacePath);
                if (!string.IsNullOrEmpty(fileName) && !workspacePath.Contains("Workspace Active"))
                {
                    WorkspacePatcher.PatchWorkspace(workspacePath);
                    workspaceManager1.LoadWorkspace(fileName, workspacePath);
                }
            }
        }

        private static void RemoveNonDefaultWorkspaces()
        {
            try
            {
                var defaultWorkspaces = new[] { "Workspace Active", "Full Details (Default)", "Basic Layout (Default)", "DefaultWorkspace" };
                var workspaceFiles = Directory.GetFiles(Folders.Workspace);
                foreach (var file in workspaceFiles)
                {
                    var isDefault = defaultWorkspaces.Any(defaultWorkspace => defaultWorkspace == Path.GetFileNameWithoutExtension(file));
                    if (!isDefault)
                    {
                        File.Delete(file);
                    }
                }
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
            }
        }
    }
}
