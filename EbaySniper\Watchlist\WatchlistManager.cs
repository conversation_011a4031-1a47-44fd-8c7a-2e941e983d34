﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Timers;
using DevExpress.XtraGrid;
using eBay.Service.Call;
using eBay.Service.Core.Soap;
using uBuyFirst.Auth;
using uBuyFirst.Data;
using uBuyFirst.Parsing;
using uBuyFirst.Prefs;
using uBuyFirst.Search;
using uBuyFirst.Tools;
using DataTable = System.Data.DataTable;


namespace uBuyFirst.Watchlist
{
    public class WatchlistManager : IDisposable
    {
        public static GridControl WatchlistGridControl;
        private readonly SynchronizationContext _synchronizationContext;
        private readonly Action<FoundItem> _handleNewItemAction;
        private readonly Dictionary<string, int> _itemQuantities = new();
        private readonly Dictionary<string, decimal> _itemPrices = new Dictionary<string, decimal>();
        private System.Timers.Timer? _oneSecondTimer;
        private bool _isAutoRefreshEnabled;
        public static TimeSpan AutoRefreshInterval;
        //private EbayAccount _activeAccount;
        private bool _isRefreshing;

        public event EventHandler? RefreshStarted;
        public event EventHandler? RefreshCompleted;
        public event EventHandler<NotificationEventArgs>? ItemBecameAvailable;
        private DateTime _lastRefreshCompletionTime = DateTime.MinValue;
        public bool IsRefreshing => _isRefreshing;
        private CancellationTokenSource? _currentRefreshCts;



        public WatchlistManager(SynchronizationContext synchronizationContext, Action<FoundItem> handleNewItemAction)
        {
            _synchronizationContext = synchronizationContext ?? throw new ArgumentNullException(nameof(synchronizationContext));
            _handleNewItemAction = handleNewItemAction ?? throw new ArgumentNullException(nameof(handleNewItemAction));
            _oneSecondTimer = new System.Timers.Timer(1000); // 1-second interval
            _oneSecondTimer.Elapsed += OneSecondTimerElapsed;
            _oneSecondTimer.AutoReset = true; // Keep ticking
            _oneSecondTimer.Enabled = false; // Start disabled
        }

        private void StartAutoRefreshTimer()
        {
            var ebayAccount = Form1.EBayAccountsList?.FirstOrDefault();
            if (!_isAutoRefreshEnabled || ebayAccount == null) return;

            if (_oneSecondTimer != null)
            {
                _oneSecondTimer.Enabled = true;
            }
        }

        private void StopAutRefreshTimer()
        {
            if (_oneSecondTimer != null)
            {
                _oneSecondTimer.Enabled = false;
            }
        }


        private async void OneSecondTimerElapsed(object sender, ElapsedEventArgs e)
        {
            // Prevent re-entrance if the handler takes longer than 1 second
            if (!_isAutoRefreshEnabled || _isRefreshing)
            {
                return; // Not enabled, no account, or already refreshing
            }

            var timeSinceLastCompletion = GetLastRefreshCompletionTime();
            // Check against both the user-defined interval AND the hard limit
            if (timeSinceLastCompletion >= AutoRefreshInterval && timeSinceLastCompletion >= TimeSpan.FromSeconds(ConnectionConfig.WatchlistUpdateInterval))
            {
                CancellationTokenSource? cts = null; // Declare CTS outside try
                try
                {
                    var ebayAccount = Form1.EBayAccountsList.FirstOrDefault();
                    if (ebayAccount != null)
                    {
                        CancelCurrentRefresh(); // Cancel previous if any
                        cts = new CancellationTokenSource();
                        _currentRefreshCts = cts; // Store the new CTS
                        await RefreshWatchlistAsync(ebayAccount, cts.Token);
                    }
                }
                catch (OperationCanceledException)
                {
                    //"Scheduled watchlist refresh was cancelled."
                    // Optionally dispose CTS here if needed, though finally block in RefreshWatchlistAsync should handle it
                }
                catch (Exception ex)
                {
                    // Dispose CTS in case of other errors before finally block in RefreshWatchlistAsync runs
                    cts?.Dispose();
                }
            }
        }

        public TimeSpan GetHardLimitSecondsRemaining()
        {
            var lastRefreshCompletionTime = TimeSpan.FromSeconds(ConnectionConfig.WatchlistUpdateInterval) - GetLastRefreshCompletionTime();
            if (lastRefreshCompletionTime.TotalSeconds < 0)
                return TimeSpan.FromSeconds(0);
            return lastRefreshCompletionTime;
        }

        public TimeSpan GetAutoRefreshSecondsRemaining()
        {
            return AutoRefreshInterval - GetLastRefreshCompletionTime();
        }

        private TimeSpan GetLastRefreshCompletionTime()
        {
            return DateTime.UtcNow - _lastRefreshCompletionTime;
        }

        public bool SetAutoRefresh(bool enabled)
        {
            _isAutoRefreshEnabled = enabled;

            if (enabled)
            {
                // Check if there's an account before starting timer
                if (Form1.EBayAccountsList.FirstOrDefault() == null)
                {
                    return false; // No account available
                }

                StartAutoRefreshTimer();
                return true;
            }
            else
            {
                StopAutRefreshTimer();
                return true; // Always succeeds when disabling
            }
        }

        public void SetRefreshInterval(TimeSpan interval)
        {
            AutoRefreshInterval = interval;
        }
        public void CancelCurrentRefresh()
        {
            try
            {
                _currentRefreshCts?.Cancel();
            }
            catch (ObjectDisposedException) { /* Ignore if already disposed */ }
            catch (Exception ex)
            {
                LogError($"Error cancelling refresh token: {ex.Message}");
            }
        }

        public async Task TriggerManualRefreshAsync(EbayAccount? account)
        {
            if (DateTime.UtcNow - _lastRefreshCompletionTime < TimeSpan.FromSeconds(ConnectionConfig.WatchlistUpdateInterval))
            {
                return;
            }

            if (_isRefreshing)
            {
                return;
            }

            if (account == null)
            {
                return;
            }

            CancellationTokenSource? cts = null; // Declare CTS outside try
            try
            {
                CancelCurrentRefresh(); // Cancel previous if any
                cts = new CancellationTokenSource();
                _currentRefreshCts = cts; // Store the new CTS
                await RefreshWatchlistAsync(account, cts.Token);
            }
            catch (OperationCanceledException)
            {
                LogMessage("Manual watchlist refresh was cancelled.");
                // Optionally dispose CTS here if needed, though finally block in RefreshWatchlistAsync should handle it
            }
            catch (Exception ex)
            {
                LogError($"Error during manual watchlist refresh: {ex.Message}");
                // Dispose CTS in case of other errors before finally block in RefreshWatchlistAsync runs
                cts?.Dispose();
            }
        }

        private async Task RefreshWatchlistAsync(EbayAccount ebayAccount, CancellationToken cancellationToken)
        {
            // Rate limit check - keep this simple check at the start
            if (DateTime.UtcNow - _lastRefreshCompletionTime < TimeSpan.FromSeconds(ConnectionConfig.WatchlistUpdateInterval))
            {
                return;
            }

            // Concurrency check
            if (_isRefreshing)
            {
                return;
            }

            _isRefreshing = true;
            RefreshStarted?.Invoke(this, EventArgs.Empty);

            try
            {
                cancellationToken.ThrowIfCancellationRequested(); // Check before starting any work

                var apiContext = ConnectionConfig.GetApiContextPlaceOffer(SiteCodeType.US, ebayAccount.TokenPo);
                var apiCall = new GetMyeBayBuyingCall(apiContext);
                var watchListRequest = new ItemListCustomizationType
                {
                    Include = true,
                    Pagination = new PaginationType { EntriesPerPage = 200, PageNumber = 1 }
                };
                apiCall.WatchList = watchListRequest;

                var currentPage = 1;
                int totalPages;
                var fetchedItemIds = new HashSet<string>();

                do
                {
                    cancellationToken.ThrowIfCancellationRequested(); // Check before each API call

                    apiCall.WatchList.Pagination.PageNumber = currentPage;
                    Stat.WatchlistApiCounter++;

                    // Execute API call (Task.Run might not be ideal if apiCall itself is async, but keeping for now)
                    // Note: The Task.Run itself isn't cancellable here, only the operations before/after.
                    await Task.Run(() => apiCall.GetMyeBayBuying(), cancellationToken); // Pass token if Task.Run supports it in your .NET version, otherwise remove

                    cancellationToken.ThrowIfCancellationRequested(); // Check after API call returns

                    var watchList = apiCall.WatchListReturn;
                    if (watchList?.PaginationResult != null)
                    {
                        totalPages = watchList.PaginationResult.TotalNumberOfPages;
                    }
                    else
                    {
                        LogError("PaginationResult missing in API response. Stopping watchlist fetch.");
                        break;
                    }

                    if (watchList is { ItemArray.Count: > 0 })
                    {
                        var items = watchList.ItemArray.ToArray();
                        foreach (var item in items)
                        {
                            cancellationToken.ThrowIfCancellationRequested(); // Check frequently during processing

                            if (item == null) continue;
                            var itemID = item.ItemID;
                            if (string.IsNullOrEmpty(itemID)) continue;

                            var currentQuantity = item.QuantityAvailable;
                            var sellerName = item.Seller?.UserID;

                            decimal currentPrice = 0;
                            if (item.SellingStatus?.CurrentPrice != null)
                            {
                                currentPrice = (decimal)item.SellingStatus.CurrentPrice.Value;
                            }

                            _itemPrices.TryGetValue(itemID, out var previousPrice);
                            _itemQuantities.TryGetValue(itemID, out var previousQuantity);

                            if (_itemQuantities.ContainsKey(itemID))
                            {
                                if (_itemQuantities.ContainsKey(itemID) && currentQuantity != previousQuantity || _itemPrices.ContainsKey(itemID) && currentPrice < previousPrice)
                                {
                                    LogMessage($"Updating quantity for item {itemID} from {previousQuantity} to {currentQuantity}.");
                                    _synchronizationContext.Post(_ => UpdateItemInGrid(item, itemID, currentQuantity, previousQuantity, currentPrice, previousPrice), null);
                                }
                            }
                            else
                            {
                                LogMessage($"Adding new item {itemID} from watchlist.");
                                var categories = new string[] { };
                                var foundItem = new FoundItem(null, null, itemID, item.ListingDetails.StartTime, SearchSource.WAT, false, false, sellerName, null, null, categories);
                                _synchronizationContext.Post(_ => _handleNewItemAction(foundItem), null);
                            }

                            _itemQuantities[itemID] = currentQuantity;
                            _itemPrices[itemID] = currentPrice;


                            fetchedItemIds.Add(itemID);
                        }
                    }
                    currentPage++;
                } while (currentPage <= totalPages);

                cancellationToken.ThrowIfCancellationRequested(); // Check before final removal step
                RemoveOrphanedItemsFromWatchlist(fetchedItemIds);
            }
            catch (OperationCanceledException)
            {
                LogMessage("Watchlist refresh operation was cancelled.");
                // No need to re-throw, just exit gracefully
            }
            catch (Exception ex)
            {
                LogError($"Error during watchlist update: {ex.Message}");
                // Log stack trace if needed: LogError($"Error fetching watchlist: {ex}");
            }
            finally
            {
                // Ensure CTS is disposed and flag is reset regardless of success, failure, or cancellation
                _currentRefreshCts?.Dispose();
                _currentRefreshCts = null;
                _isRefreshing = false;
                _lastRefreshCompletionTime = DateTime.UtcNow; // Update time *after* completion/cancellation/error
                RefreshCompleted?.Invoke(this, EventArgs.Empty); // Notify completion/end
            }
        }

        // Helper method extracted for clarity in Post call
        private void UpdateItemInGrid(ItemType item, string itemID, int currentQuantity, int previousQuantity,
            decimal? previousPrice,
            decimal? currentPrice)
        {
            try
            {
                if (WatchlistGridControl.DataSource is DataTable dataTable)
                {
                    var updatedRow = dataTable.Rows.Find(itemID);
                    if (updatedRow != null)
                    {
                        // Assuming a "Price" column exists in the DataTable.
                        // If item.SellingStatus is null or CurrentPrice is null, currentPrice variable will be null.
                        // Decide how to handle null price in the grid: store null, 0, or a specific string.
                        // For this example, let's assume the column can handle DBNull.Value for null prices.

                        var datalist = (DataList)updatedRow["Blob"];

                        datalist.QuantityAvailable = currentQuantity;
                        var quantityAvailable = currentQuantity;
                        updatedRow["Quantity"] = quantityAvailable;

                        if (item.SellingStatus?.CurrentPrice != null)
                        {
                            datalist.ItemPrice = (double)currentPrice.Value;
                            datalist.ItemPricing.ItemPrice.Value = (double)currentPrice.Value;
                        }

                        datalist.TotalPrice = datalist.ItemPricing.GetTotalPrice(datalist.ItemShipping.FullSingleShippingPrice).Value;
                        updatedRow["Item Price"] = datalist.ItemPrice;
                        updatedRow["Total Price"] = datalist.TotalPrice;

                        DateParser.SetFoundTimeWithDelayForced(datalist, datalist.StartTimeLocal?.UtcDateTime);
                        updatedRow["Found Time"] = datalist.FoundTime.LocalTime;


                        // Raise event if item became available
                        if (currentQuantity > 0 && previousQuantity == 0 || currentPrice < previousPrice)
                        {
                            try
                            {
                                var eventArgs = new NotificationEventArgs(updatedRow, datalist, WatchlistGridControl);
                                ItemBecameAvailable?.Invoke(this, eventArgs);
                            }
                            catch (Exception notificationEx)
                            {
                                LogError($"Error preparing/raising ItemBecameAvailable event for item {itemID}: {notificationEx.Message}");
                            }
                        }
                    }
                }
                else
                {
                    LogError($"Could not find DataTable or GridControl to update item {itemID}. DataSource type: {WatchlistGridControl.DataSource?.GetType().Name}");
                }
            }
            catch (Exception uiEx)
            {
                LogError($"Error updating item {itemID} in GridControl: {uiEx.Message}");
            }
        }

        private void RemoveOrphanedItemsFromWatchlist(HashSet<string> fetchedItemIds)
        {
            // Collect orphaned IDs from the dictionary
            var orphanedIds = _itemQuantities.Keys
                .Where(localId => !fetchedItemIds.Contains(localId))
                .ToList();

            // Remove orphaned items from the in-memory dictionary
            foreach (var orphanedId in orphanedIds)
            {
                _itemQuantities.Remove(orphanedId);
                _itemPrices.Remove(orphanedId);
            }

            // Ensure we have a valid data table from the grid control
            if (WatchlistGridControl is not { DataSource: DataTable dataTable })
            {
                return;
            }

            // Now, update the UI in a synchronous manner
            _synchronizationContext.Send(_ =>
            {
                // Create a list to store rows to remove to avoid modifying collection while iterating
                var rowsToRemove = new List<DataRow>();

                foreach (DataRow row in dataTable.Rows)
                {
                    var itemId = row["ItemID"]?.ToString();
                    if (itemId != null && !fetchedItemIds.Contains(itemId))
                    {
                        rowsToRemove.Add(row);
                    }
                }

                foreach (var row in rowsToRemove)
                {
                    try
                    {
                        // Assuming DataTable.PrimaryKey is set for efficient Find()
                        var foundRow = dataTable.Rows.Find(row["ItemID"]);
                        if (foundRow != null)
                        {
                            dataTable.Rows.Remove(foundRow);
                        }
                    }
                    catch (Exception ex)
                    {
                        LogError($"Error removing item {row["ItemID"]} from GridControl: {ex.Message}");
                    }
                }
            }, null);
        }

        public void Dispose()
        {
            // Cancel any ongoing refresh first
            CancelCurrentRefresh();

            // Dispose the timer and unsubscribe
            if (_oneSecondTimer != null)
            {
                _oneSecondTimer.Elapsed -= OneSecondTimerElapsed;
                _oneSecondTimer.Stop(); // Ensure timer is stopped before disposing
                _oneSecondTimer.Dispose();
                _oneSecondTimer = null;
            }

            // Dispose the CancellationTokenSource if it exists
            try
            {
                _currentRefreshCts?.Dispose();
            }
            catch (ObjectDisposedException) { /* Ignore */ }
            _currentRefreshCts = null;

            GC.SuppressFinalize(this); // Recommended practice for IDisposable
        }


        /// <summary>
        /// Adds multiple items to the user's eBay watchlist by their IDs, processing in batches.
        /// </summary>
        /// <param name="itemIds">A collection of item IDs to add.</param>
        /// <returns>Task representing the asynchronous operation.</returns>
        public async Task<bool> AddItems(IEnumerable<string> itemIds)
        {
            const int BatchSize = 10; // eBay API limit for AddToWatchListCall

            var account = Form1.EBayAccountsList?.FirstOrDefault();
            if (account == null)
            {
                return false;
            }

            if (itemIds == null)
            {
                return false;
            }

            var validItemsToAdd = itemIds.Where(id => !string.IsNullOrWhiteSpace(id)).Distinct().ToList();
            if (!validItemsToAdd.Any())
            {
                return false;
            }

            var allSucceeded = true;
            for (var i = 0; i < validItemsToAdd.Count; i += BatchSize)
            {
                var currentBatch = validItemsToAdd.Skip(i).Take(BatchSize).ToList();
                var batchSuccess = await AddItemsToEbayWatchlistAsync(account, currentBatch);
                allSucceeded = allSucceeded && batchSuccess;
            }

            return allSucceeded;
        }


        /// <summary>
        /// Removes a specific item from the user's eBay watchlist using the Trading API.
        /// </summary>
        /// <param name="account">The eBay account associated with the watchlist.</param>
        /// <param name="itemIDs">A list of item IDs to remove.</param>
        /// <returns>True if all items were successfully removed (or if the API returned a warning for all), false otherwise.</returns>
        public static async Task<bool> RemoveItemsFromEbayWatchlistAsync(EbayAccount account, List<string> itemIDs)
        {
            const int BatchSize = 200;
            const int DelayBetweenBatchesMs = 200;

            if (string.IsNullOrEmpty(account.TokenPo))
            {
                return false;
            }

            if (!itemIDs.Any())
            {
                return false;
            }

            var allBatchesSuccessful = true;
            var totalBatches = (itemIDs.Count + BatchSize - 1) / BatchSize;

            try
            {
                for (var i = 0; i < itemIDs.Count; i += BatchSize)
                {
                    var currentBatch = itemIDs.Skip(i).Take(BatchSize).ToList();
                    var currentBatchNumber = (i / BatchSize) + 1;

                    var apiContext = ConnectionConfig.GetApiContextPlaceOffer(SiteCodeType.US, account.TokenPo);
                    var apiCall = new RemoveFromWatchListCall(apiContext);

                    try
                    {
                        apiCall.ItemIDList = new StringCollection(currentBatch.ToArray());
                        Stat.WatchlistApiCounter++; // Increment counter
                        await Task.Run(() => apiCall.Execute());

                        if (apiCall.ApiResponse.Ack == AckCodeType.Success || apiCall.ApiResponse.Ack == AckCodeType.Warning)
                        {
                        }
                        else
                        {
                            var errors = apiCall.ApiException?.Errors ?? apiCall.ApiResponse.Errors;
                            var errorMessages = string.Join("; ", errors?.ToArray()?.Select(e => $"[{e.SeverityCode}] {e.ShortMessage} ({e.ErrorCode}: {e.LongMessage})") ?? new[] { "Unknown API error" });
                            allBatchesSuccessful = false;
                        }
                    }
                    catch (Exception)
                    {
                        allBatchesSuccessful = false;
                    }

                    // Add delay if this is not the last batch
                    if (i + BatchSize < itemIDs.Count)
                    {
                        await Task.Delay(DelayBetweenBatchesMs);
                    }
                }
            }
            catch (Exception)
            {
                return false;
            }

            return allBatchesSuccessful;
        }


        /// <summary>
        /// Adds a batch of items (up to 10) to the user's eBay watchlist using the Trading API.
        /// </summary>
        /// <param name="account">The eBay account associated with the watchlist.</param>
        /// <param name="itemIDs">A list of item IDs to add (max 10).</param>
        /// <returns>True if the API call was successful or returned a warning, false otherwise.</returns>
        public static async Task<bool> AddItemsToEbayWatchlistAsync(EbayAccount? account, List<string> itemIDs)
        {
            if (account == null || string.IsNullOrEmpty(account.TokenPo))
            {
                return false;
            }

            if (itemIDs == null || !itemIDs.Any())
            {
                return false;
            }

            if (itemIDs.Count > 10)
            {
                return false;
            }

            var apiContext = ConnectionConfig.GetApiContextPlaceOffer(SiteCodeType.US, account.TokenPo); // Assuming US site
            var apiCall = new AddToWatchListCall(apiContext);
            try
            {
                apiCall.ItemIDList = new StringCollection(itemIDs.ToArray());

                Stat.WatchlistApiCounter++; // Increment counter
                await Task.Run(() => apiCall.Execute());

                if (apiCall.ApiResponse.Ack == AckCodeType.Success || apiCall.ApiResponse.Ack == AckCodeType.Warning)
                {
                    return true;
                }
                else
                {
                    var errors = apiCall.ApiException?.Errors ?? apiCall.ApiResponse.Errors;
                    var errorMessages = string.Join("; ", errors?.ToArray().Select(e => $"[{e.SeverityCode}] {e.ShortMessage} ({e.ErrorCode}: {e.LongMessage})") ?? new[] { "Unknown API error" });
                    return false;
                }
            }
            catch (Exception ex)
            {
                return false;
            }
        }


        private void LogMessage(string message)
        {
            Console.WriteLine(@$"[WatchlistManager] {DateTime.Now}: {message}");
        }

        private void LogError(string errorMessage)
        {
            Console.WriteLine($@"[WatchlistManager] ERROR {DateTime.Now}: {errorMessage}");
        }

        public void ClearItems()
        {
            _itemQuantities.Clear();
            _itemPrices.Clear();
            if (WatchlistGridControl is { DataSource: DataTable dataTable })
            {
                dataTable.Rows.Clear();
            }
        }
    }
}
