﻿using System;
using System;
using System.Collections.Specialized;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml;
using DevExpress.XtraEditors;
using LicenseSpot.Framework;
using uBuyFirst.Other;
using uBuyFirst.Prefs;
using uBuyFirst.Properties;
using uBuyFirst.Stats;
using MachineProfile = uBuyFirst.Tools.MachineProfile;

namespace uBuyFirst.License
{
    public class LicenseUtility
    {
        public LicenseUtility()
        {
            _licenseFilePath = Path.Combine(Folders.Settings, "license.lic");
        }

        public bool TrialSubscriptionLaunched { get; set; } = false;

        public DateTime LastLicenseCheck { get; set; } = DateTime.UtcNow;
        private readonly string _licenseFilePath;
        private Limits _currentLimits;
        public SubscriptionType CurrentSubscriptionType { get; private set; } = SubscriptionType.NotActivated;
        private int _licenseCheckFailureCount = 0;
        public static bool LicenseCheckCompleted { get; internal set; }

        public Limits CurrentLimits => _currentLimits ??= new Limits();

        public int TrialDaysLeft { get; private set; } = -1;

        public bool TrialExpired { get; private set; } = true;

        public enum SubscriptionType
        {
            Enterprise,
            Elite,
            Pro,
            Basic,
            Trial,
            NotActivated
        }

        private bool IsTrialExpired(ExtendedLicense license)
        {
            string response;
            var licenseHardwareID = GetHWID();
            var hwid = ProgramState.HWID;

            var fingerprint = GetHardwareFingerprint();

            var message = $"{{\"ver\":\"{ProgramState.UBFVersion}\",\"hwid\":\"{licenseHardwareID}\",\"sn\":\"{license.SerialNumber}\",\"hardware\":\"{fingerprint}\"}}";
            var trialExpired = true;
            try
            {
                var phpScriptLocation = $"{Config.TrialUrl1}?id={hwid}&sn={ProgramState.SerialNumber}&cb={new Random().Next()}";
                Form1.Secure.SetRemotePhpScriptLocation(phpScriptLocation);
                response = Form1.Secure.SendMessage(message);
            }
            catch (Exception ex)
            {
                response = "-99999999";
                XtraMessageBox.Show(@"Error #2515: " + ex.Message);
            }

            if (response == "-99999999")
                try
                {
                    response = "0";
                    var phpScriptLocation = $"{Config.TrialUrl2}?id={hwid}{ProgramState.SerialNumber}&cb={new Random().Next()}";
                    Form1.Secure.SetRemotePhpScriptLocation(phpScriptLocation);
                    response = Form1.Secure.SendMessage(message);
                }
                catch (Exception ex)
                {
                    XtraMessageBox.Show(@"Error #2515: " + ex.Message);
                }

            var num1 = 0;
            if (int.TryParse(response, out var num))
            {
                num1 = num;
            }

            if (num1 - 42 < 0)
            {
            }
            else
            {
                TrialDaysLeft = num1 - 42;
                trialExpired = false;
            }

            return trialExpired;
        }

        public static string GetHardwareFingerprint()
        {
            var deviceModel = MachineProfile.GetDeviceModel();
            var processorSerialNumber = MachineProfile.ProcessorSerialNumber;
            string hardDriveSerialNumber = "Unknown";
            try
            {
                hardDriveSerialNumber = MachineProfile.HardDriveSerialNumber;
            }
            catch (Exception e)
            {
            }

            var fingerprint = processorSerialNumber + "|" + hardDriveSerialNumber + "|" + deviceModel;
            if (fingerprint.Length > 255)
                fingerprint = fingerprint.Substring(0, 255);
            return fingerprint;
        }

        public void TryDownloadExistingLicense(string licenseKey)
        {
            var values = new NameValueCollection
            {
                {
                    "data",
                    $"<parameters><SerialNumber>{licenseKey}</SerialNumber><HardwareId>{GetHWID()}</HardwareId></parameters>"
                }
            };

            var licenseContent = "";
            try
            {
                using (var wc = new WebClient())
                {
                    var response = wc.UploadValues(Config.ExistingLicenseUrl, values);
                    var content = Encoding.UTF8.GetString(response);

                    if (!string.IsNullOrEmpty(content) && content.Length > 100)
                    {
                        licenseContent = ExtractLicenseContent(content);
                    }
                }
            }
            catch (Exception ex)
            {
                Loggers.LogError(ex);
            }

            if (!string.IsNullOrWhiteSpace(licenseContent))
            {
                var licenseFile = new LicenseFile();
                licenseFile.FromXmlString(licenseContent);
                licenseFile.Save(_licenseFilePath);
            }
        }

        private static string ExtractLicenseContent(string content)
        {
            var xmlDocument = new XmlDocument();
            try
            {
                xmlDocument.LoadXml(content);

                if (xmlDocument.DocumentElement?.Attributes["stat"].Value == "ok")
                    return xmlDocument.DocumentElement.InnerXml;
            }
            catch (Exception)
            {
                //   InvalidReason = "The license response has an invalid format";
                //   throw new ExtendedLicenseException(InvalidReason);
            }

            if (xmlDocument.DocumentElement?.Attributes["stat"].Value == "ok")
                return xmlDocument.DocumentElement.InnerXml;

            return "";
        }

        public void ActivateLicense(string licenseKey)
        {
            try
            {
                var license = ExtendedLicenseManager.GetLicense(typeof(Form1), Application.OpenForms["Form1"]);
                var content = license.Activate(licenseKey, false);
                var licenseFile = new LicenseFile();
                licenseFile.FromXmlString(content);
                licenseFile.Save(_licenseFilePath);
                Analytics.AddEvent("", "LicenseActivated", 1);
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);

                return;
            }

            XtraMessageBox.Show(En_US.LicenseUtility_ActivateLicense_Thank_you__Please__restart_the_application_);
        }

        public void SetDefaultLimits()
        {
            _currentLimits = new Limits
            {
                FiltersEnabled = false,
                MultipleEbayAccountsEnabled = false,
                PlaceOfferEnabled = false,
                SearchInterval = 10,
                SearchTermsCount = 5
            };
        }

        public void SetLimits(SubscriptionType currentSubscriptionType)
        {
            _currentLimits = currentSubscriptionType switch
            {
                SubscriptionType.Basic => new Limits
                {
                    FiltersEnabled = false,
                    MultipleEbayAccountsEnabled = false,
                    PlaceOfferEnabled = false,
                    SearchInterval = 10,
                    SearchTermsCount = 5
                },
                SubscriptionType.Pro => new Limits
                {
                    FiltersEnabled = true,
                    MultipleEbayAccountsEnabled = false,
                    PlaceOfferEnabled = false,
                    SearchInterval = 5,
                    SearchTermsCount = 10
                },
                SubscriptionType.Elite => new Limits
                {
                    FiltersEnabled = true,
                    MultipleEbayAccountsEnabled = true,
                    PlaceOfferEnabled = true,
                    SearchInterval = 2,
                    SearchTermsCount = 20
                },
                SubscriptionType.Enterprise => new Limits
                {
                    FiltersEnabled = true,
                    MultipleEbayAccountsEnabled = true,
                    PlaceOfferEnabled = true,
                    SearchInterval = 1,
                    SearchTermsCount = 10000
                },
                SubscriptionType.Trial => new Limits
                {
                    FiltersEnabled = true,
                    MultipleEbayAccountsEnabled = true,
                    PlaceOfferEnabled = true,
                    SearchInterval = 1,
                    SearchTermsCount = 10000
                },
                _ => new Limits
                {
                    FiltersEnabled = false,
                    MultipleEbayAccountsEnabled = false,
                    PlaceOfferEnabled = false,
                    SearchInterval = 10,
                    SearchTermsCount = 5
                }
            };
        }

        public async Task CheckLicense()
        {
            try
            {
                if (!File.Exists(_licenseFilePath))
                {
                    CurrentSubscriptionType = SubscriptionType.NotActivated;
                    return;
                }

                var license = await Task.Run(ReadGenuineLicenseFromFile);

                if (license == null)
                {
                    CurrentSubscriptionType = SubscriptionType.NotActivated;
                    return;
                }

                CurrentSubscriptionType = SetLicenseType(license);
                if (CurrentSubscriptionType == SubscriptionType.Basic)
                {
                    TrialExpired = IsTrialExpired(license);
                    if (!TrialExpired)
                        CurrentSubscriptionType = SubscriptionType.Trial;
                }
                SetLimits(CurrentSubscriptionType);

                ProgramState.SerialNumber = license.SerialNumber;
                if (license.SerialNumber.Length > 10)
                {
                    ProgramState.SerialNumberShort = license.SerialNumber.Substring(0, 9);
                }
            }
            catch (Exception)
            {
                CurrentSubscriptionType = SubscriptionType.NotActivated;
                // Log exception if needed
            }
        }

        internal string GetLicenseStatusText()
        {
            var licenseText = $"License: {CurrentSubscriptionType}";
            if (CurrentSubscriptionType == SubscriptionType.Trial)
            {
                if (!TrialExpired)
                {
                    CurrentSubscriptionType = SubscriptionType.Trial;
                    licenseText = $"{En_US.Form1_Form1_Shown_License__Trial__Days_left__} {TrialDaysLeft}";
                }
            }

            return licenseText;
        }

        internal ExtendedLicense? ReadGenuineLicenseFromFile()
        {
            if (!File.Exists(_licenseFilePath))
                return null;

            var license = CreateLicenseFromFile();

            if (license == null)
                return null;

            var isLicenseValid = ValidateLicense(license);

            if (isLicenseValid)
                try
                {
                    license.ApiUrl = "https://app.licensespot.com/Api/api.ashx";
                    var isGenuine = license.IsGenuine();
                    if (isGenuine)
                        return license;
                    else
                        return null;
                }
                catch (Exception ex)
                {
                    Form1.ShowTrayBalloon("uBuyFirst", "License validation error. " + ex.Message, 60);
                }

            return null;
        }

        private static bool ValidateLicense(ExtendedLicense? license)
        {
            try
            {
                if (license != null)
                    return license.Validate();
            }
            catch (Exception)
            {
                // ignored
            }

            return false;
        }

        private ExtendedLicense? CreateLicenseFromFile()
        {
            var licenseFile = new LicenseFile();
            licenseFile.Load(_licenseFilePath);
            var licenseValidationInfo = new LicenseValidationInfo { LicenseFile = licenseFile };
            ExtendedLicense? license = null;
            try
            {
                var trialForm = Application.OpenForms["FormTrialSubscriptionPrompt"];
                if (trialForm != null)
                {
                    license = ExtendedLicenseManager.GetLicense(typeof(FormTrialSubscriptionPrompt), trialForm, licenseValidationInfo);
                }
                else
                {
                    license = ExtendedLicenseManager.GetLicense(typeof(Form1), Application.OpenForms["Form1"], licenseValidationInfo);
                }
            }
            catch (Exception)
            {
                // ignored
            }

            return license;
        }

        private SubscriptionType SetLicenseType(ExtendedLicense license)
        {
            foreach (var type in Enum.GetValues(typeof(SubscriptionType)))
            {
                var typeName = type.ToString();

                var licenseNameMatches = license.Properties.Any(p => p.Name == typeName);
                var licenseTypeMatches = license.Properties.FirstOrDefault(f => f.Name == "Type")?.Value.ToLower().Contains(typeName.ToLower()) ?? false;
                if (licenseNameMatches || licenseTypeMatches)
                {
                    return (SubscriptionType)type;
                }
            }

            return SubscriptionType.NotActivated;
        }

        public static string GetHWID()
        {
            try
            {
                return MachineProfile.GetHardwareId();
            }
            catch (Exception)
            {
                // ignored
            }

            return "unknownHWID" + Environment.MachineName;
        }

        // Field for tracking consecutive license failures

        public bool HasTrialPromptBeenLaunched()
        {
            return TrialSubscriptionLaunched;
        }

        public void SetTrialPromptLaunched()
        {
            TrialSubscriptionLaunched = true;
        }

        public async void PeriodicLicenseCheck()
        {
            var minValue = 1440; //24h
            var maxValue = 2880; //48h
            var intervalCheckMinutes = new Random().Next(minValue, maxValue);

            // Only check if enough time has elapsed
            if ((DateTime.UtcNow - LastLicenseCheck).TotalMinutes >= intervalCheckMinutes)
            {
                LastLicenseCheck = DateTime.UtcNow;

                var previousLicenseType = CurrentSubscriptionType;

                // Suppose CheckLicense() returns a bool indicating success/failure,
                // or we rely on the updated CurrentSubscriptionType to see if it 'failed'
                // For demonstration, we'll just call it and then check the subscription type.
                await CheckLicense();

                // If the subscription type changed, let's handle it
                //if (CurrentSubscriptionType != previousLicenseType)
                {
                    // Determine if the license is effectively "failed"
                    var subscriptionExpired = CurrentSubscriptionType == SubscriptionType.NotActivated;
                    var trialExpired = previousLicenseType == SubscriptionType.Trial && CurrentSubscriptionType == SubscriptionType.Basic;

                    // If it's expired or downgraded from trial to basic, count that as a failure
                    if (subscriptionExpired || trialExpired)
                    {
                        _licenseCheckFailureCount++;

                        // Once we hit 3 consecutive fails, apply restrictions
                        if (_licenseCheckFailureCount >= 3)
                        {
                            var licenseStatusText = GetLicenseStatusText();
                            Form1.Instance.barStaticLicense.Caption = licenseStatusText;
                            if (Form1.Instance._searchService != null && Form1.Instance._searchService.Running)
                            {
                                await Form1.Instance.StopWorking();
                                licenseStatusText += "\r\nSearch stopped";
                            }
                            Form1.ShowTrayBalloon("uBuyFirst", licenseStatusText, 60);
                        }
                    }
                    else
                    {
                        // If it's valid again, reset the consecutive failure count
                        _licenseCheckFailureCount = 0;
                    }
                }
            }
        }
    }
}

