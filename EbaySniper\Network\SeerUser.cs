﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using uBuyFirst.Prefs;

namespace uBuyFirst.Network
{
    [Serializable]
    public class SeerUser
    {
        public string Version { get; set; }
        public int Sent;
        public bool Enabled { get; set; }
        public string UserName { get; set; }
        public string LicenseKey { get; set; }
        public string HWID { get; set; }
        public string ConnectionID { get; set; }

        public string StartTime { get; set; }

        public List<SeerSearchTerm> EbaySearches { get; set; }
        public int ItemsSent => Sent;
        public HashSet<String> BlockedSellers { get; set; }

        public override string ToString()
        {
            return HWID;
        }

        public static SeerUser CreateSeerUser(List<Keyword2Find> keywords2Find)
        {
            var user = new SeerUser();
            user.Version = ProgramState.UBFVersion;
            user.HWID = ProgramState.HWID;
            user.LicenseKey = Regex.Replace(ProgramState.SerialNumber, "_.*", "");
            user.EbaySearches = new List<SeerSearchTerm>();
            user.BlockedSellers = UserSettings.BlockedSellers;
            var keywords = keywords2Find.Where(kw => kw.KeywordEnabled == CheckState.Checked).ToList();
            foreach (var kw in keywords)
            {
                if (!kw.ListingType.Contains(ListingType.BuyItNow) && !kw.ListingType.Contains(ListingType.AuctionsStartedNow) && !kw.ListingType.Contains(ListingType.OutOfStock))
                    continue;

                var term = new SeerSearchTerm();

                term.Alias = kw.Alias;
                term.Keyword = kw.Kws;
                term.PriceMin = (decimal)kw.PriceMin;
                term.PriceMax = (decimal)kw.PriceMax;
                term.SearchInDescription = kw.SearchInDescription;
                term.Categories = kw.Categories4Api.Split(',');
                term.AvailableTo = kw.AvailableTo;
                term.LocatedIn = kw.LocatedIn;
                term.ConditionID = kw.Condition;
                term.SellerList = kw.Sellers;
                term.SellersFilter = kw.SellerType;
                term.Zip = kw.Zip;
                term.EBaySite = kw.EBaySite.GlobalID;
                term.ListingType = kw.ListingType;
                user.EbaySearches.Add(term);
            }

            return user;
        }
    }
}
