using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Restocker.Services;
using uBuyFirst;

namespace uBuyFirst.Tests.Restocker.Services
{
    [TestClass]
    public class KeywordDataServiceTests
    {
        private IKeywordDataService _keywordDataService;
        private List<Keyword2Find> _testKeywords;

        [TestInitialize]
        public void Setup()
        {
            _testKeywords = CreateTestKeywords();
            _keywordDataService = new KeywordDataService(_testKeywords);
        }

        [TestMethod]
        public void GetKeywordById_WithValidId_ReturnsKeyword()
        {
            // Arrange
            var expectedKeywordId = "keyword-1";

            // Act
            var result = _keywordDataService.GetKeywordById(expectedKeywordId);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(expectedKeywordId, result.Id);
            Assert.AreEqual("Test Product 1", result.Alias);
        }

        [TestMethod]
        public void GetKeywordById_WithInvalidId_ReturnsNull()
        {
            // Arrange
            var invalidKeywordId = "non-existent-keyword";

            // Act
            var result = _keywordDataService.GetKeywordById(invalidKeywordId);

            // Assert
            Assert.IsNull(result);
        }

        [TestMethod]
        public void GetKeywordById_WithNullId_ReturnsNull()
        {
            // Act
            var result = _keywordDataService.GetKeywordById(null);

            // Assert
            Assert.IsNull(result);
        }

        [TestMethod]
        public void GetKeywordById_WithEmptyId_ReturnsNull()
        {
            // Act
            var result = _keywordDataService.GetKeywordById(string.Empty);

            // Assert
            Assert.IsNull(result);
        }

        [TestMethod]
        public void GetKeywordsByIds_WithValidIds_ReturnsMatchingKeywords()
        {
            // Arrange
            var keywordIds = new[] { "keyword-1", "keyword-2" };

            // Act
            var results = _keywordDataService.GetKeywordsByIds(keywordIds);

            // Assert
            Assert.AreEqual(2, results.Count);
            Assert.IsTrue(results.Any(k => k.Id == "keyword-1"));
            Assert.IsTrue(results.Any(k => k.Id == "keyword-2"));
        }

        [TestMethod]
        public void GetKeywordsByIds_WithMixedValidInvalidIds_ReturnsOnlyValidKeywords()
        {
            // Arrange
            var keywordIds = new[] { "keyword-1", "non-existent", "keyword-2" };

            // Act
            var results = _keywordDataService.GetKeywordsByIds(keywordIds);

            // Assert
            Assert.AreEqual(2, results.Count);
            Assert.IsTrue(results.Any(k => k.Id == "keyword-1"));
            Assert.IsTrue(results.Any(k => k.Id == "keyword-2"));
        }

        [TestMethod]
        public void GetKeywordsByIds_WithNullIds_ReturnsEmptyList()
        {
            // Act
            var results = _keywordDataService.GetKeywordsByIds(null);

            // Assert
            Assert.IsNotNull(results);
            Assert.AreEqual(0, results.Count);
        }

        [TestMethod]
        public void GetKeywordsByIds_WithEmptyIds_ReturnsEmptyList()
        {
            // Act
            var results = _keywordDataService.GetKeywordsByIds(new string[0]);

            // Assert
            Assert.IsNotNull(results);
            Assert.AreEqual(0, results.Count);
        }

        [TestMethod]
        public void GetAllKeywords_ReturnsAllKeywords()
        {
            // Act
            var results = _keywordDataService.GetAllKeywords();

            // Assert
            Assert.AreEqual(_testKeywords.Count, results.Count);
            foreach (var keyword in _testKeywords)
            {
                Assert.IsTrue(results.Any(k => k.Id == keyword.Id));
            }
        }

        [TestMethod]
        public void Constructor_WithNullKeywords_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.ThrowsException<ArgumentNullException>(() => 
                new KeywordDataService(null));
        }

        private List<Keyword2Find> CreateTestKeywords()
        {
            return new List<Keyword2Find>
            {
                new Keyword2Find
                {
                    Id = "keyword-1",
                    Alias = "Test Product 1",
                    Kws = "test,product,keywords",
                    JobId = "job-123",
                    RequiredQuantity = 5,
                    PurchasedQuantity = 2,
                    PriceMin = 10.0,
                    PriceMax = 50.0,
                    Categories4Api = "12345",
                    EbaySiteName = "eBay US",
                    LocatedIn = "United States",
                    AvailableTo = "Worldwide",
                    Zip = "12345",
                    SellerType = "Include",
                    ViewName = "TestView",
                    SearchInDescription = true,
                    Condition = new[] { "New", "Used" }
                },
                new Keyword2Find
                {
                    Id = "keyword-2",
                    Alias = "Test Product 2",
                    Kws = "another,test,product",
                    JobId = "job-456",
                    RequiredQuantity = 3,
                    PurchasedQuantity = 1,
                    PriceMin = 20.0,
                    PriceMax = 100.0,
                    Categories4Api = "67890",
                    EbaySiteName = "eBay UK",
                    LocatedIn = "United Kingdom",
                    AvailableTo = "Europe",
                    Zip = "SW1A 1AA",
                    SellerType = "Exclude",
                    ViewName = "TestView2",
                    SearchInDescription = false,
                    Condition = new[] { "New" }
                },
                new Keyword2Find
                {
                    Id = "keyword-3",
                    Alias = "Test Product 3",
                    Kws = "third,test,item",
                    JobId = "job-789",
                    RequiredQuantity = 10,
                    PurchasedQuantity = 0,
                    PriceMin = 5.0,
                    PriceMax = 25.0,
                    Categories4Api = "11111",
                    EbaySiteName = "eBay CA",
                    LocatedIn = "Canada",
                    AvailableTo = "North America",
                    Zip = "K1A 0A6",
                    SellerType = "Include",
                    ViewName = "TestView3",
                    SearchInDescription = true,
                    Condition = new[] { "Used", "Refurbished" }
                }
            };
        }
    }
}
