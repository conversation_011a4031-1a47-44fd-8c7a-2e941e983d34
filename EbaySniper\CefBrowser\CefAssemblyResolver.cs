﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;

namespace uBuyFirst.CefBrowser
{
    public static class AssemblyResolver
    {
        // A directory path where you have placed the downloaded CefSharp assemblies.
        // Update this to your actual path.
        private static string? s_cefBinariesDirectory;

        public static void Initialize(string? binariesPath)
        {
            s_cefBinariesDirectory = binariesPath;

            // Attach the event handler for assembly resolution
            AppDomain.CurrentDomain.AssemblyResolve += OnAssemblyResolve;
        }

        private static Assembly OnAssemblyResolve(object sender, ResolveEventArgs args)
        {
            // Extract the name of the assembly without version or culture
            var assemblyName = new AssemblyName(args.Name).Name;

            // We can specifically handle known CefSharp assemblies or attempt to load any assembly
            // from the cefBinariesDirectory if we trust all assemblies there.
            // For safety, let's only handle known assemblies:
            var knownAssemblies = new List<string>()
            {
                "CefSharp",
                "CefSharp.Core",
                "CefSharp.Core.Runtime",
                "CefSharp.BrowserSubprocess.Core"
            };

            if (knownAssemblies.Contains(assemblyName))
            {
                // Construct full path to the assembly
                var assemblyPath = Path.Combine(s_cefBinariesDirectory, $"{assemblyName}.dll");
                if (File.Exists(assemblyPath))
                {
                    return Assembly.LoadFrom(assemblyPath);
                }
            }

            // If not one of the known assemblies or file not found, return null
            return null;
        }
    }
}
