﻿using System;
using System.Diagnostics;
using System.Threading.Tasks;
using eBay.Service.Core.Soap;
using uBuyFirst.Data;
using uBuyFirst.Network;
using CountryCodeType = eBay.Service.Core.Soap.CountryCodeType;
using ShippingDetailsType = eBay.Service.Core.Soap.ShippingDetailsType;
using uBuyFirst.Models;

namespace uBuyFirst.Pricing
{
    public class PricingService
    {
        private readonly ApiService _apiService;

        public PricingService(ApiService apiService)
        {
            _apiService = apiService;
        }

        public async Task<ItemShipping> GetItemPricing(string itemItemID, string listingTypeCodeType, bool itemBuyItNowAvailable, CurrencyAmount itemConvertedBuyItNowPrice,
            CurrencyAmount itemConvertedCurrentPrice, Location location)
        {
            var ItemShipping = new ItemShipping();
            try
            {
                if (!string.IsNullOrWhiteSpace(location.Zip))
                {
                    // var shippingDetails = await MakeShippingDetailsCall(itemItemID, 1, destinationCountry,location.Zip);
                    //TryParseShipping(shippingDetails, itemPricing, location.ReqAvailableTo != location.ItemLocatedIn.ToString());
                }

                //itemPricing.ItemPrice = GetBinPrice(listingTypeCodeType, itemBuyItNowAvailable, itemConvertedBuyItNowPrice, itemConvertedCurrentPrice);
            }
            catch (Exception ex)
            {
                //if (ex.Message == "Invalid Destination or PostalCode.") itemShipping.FullSingleShippingPrice = new CurrencyAmount(-2, CurrencyCodeType.USD.ToString());
            }

            return ItemShipping;
        }

        public async Task GetItemPricing(ItemType item, DataList datalist, bool isInternational, CountryCodeType destinationCountry, string zip)
        {
            try
            {
                /*
                                if (item.ShipToLocations.Contains("None"))
                                {
                                }
                                if (item.LocalListing)
                                {
                                }
                                if (item.ShippingDetails.ShippingServiceOptions.ToArray().ToList().Any(s => s.LocalPickup))
                                {
                                }
                                */

                TryParseShipping(item.ShippingDetails, datalist.ItemShipping, isInternational);
                if (datalist.ItemShipping.ShippingStatusTradingAPI == ItemShipping.ParsingStatus.Success)
                    return;

                try
                {
                    if (!item.ShipToLocations.Contains("None"))
                    {
                        if (string.IsNullOrEmpty(datalist.Authenticity))
                        {
                            var shippingDetails = await GetItemShipping(destinationCountry, zip, item.ItemID);
                            TryParseShipping(shippingDetails, datalist.ItemShipping, isInternational);
                            return;
                        }
                    }

                }
                catch (Exception ex)
                {
                    if (ex.Message == "Invalid Destination or PostalCode.")
                        datalist.ItemShipping.ShippingStatusTradingAPI = ItemShipping.ParsingStatus.Fail;
                }
            }
            catch (Exception)
            {
                //throw;
            }

            datalist.ItemShipping.ShippingStatusTradingAPI = ItemShipping.ParsingStatus.Fail;
        }

        // Modified to accept DataList to access seller info for UK fee calculation
        internal static void GetBuyItNowAndAuctionPrice(ItemType item, DataList datalist)
        {
            CurrencyAmount currentPrice;
            if (item.SellingStatus.CurrentPrice == null)
                currentPrice = new CurrencyAmount(0, "USD");
            else
                currentPrice = new CurrencyAmount(item.SellingStatus.CurrentPrice.Value, item.SellingStatus.CurrentPrice.currencyID.ToString());
            
            var buyItNowPrice = new CurrencyAmount(item.BuyItNowPrice.Value, item.BuyItNowPrice.currencyID.ToString());
            
            // Get base price
            var baseItemPrice = GetBinPrice(item.ListingType.ToString(), item.ListingDetails.BuyItNowAvailable, buyItNowPrice, currentPrice);

            // Create a temporary SellerUser object from DataList to pass to fee calculation
            var sellerInfo = new SellerUser
            {
                Business = datalist.SellerIsBusiness,
                // Assuming datalist.SellerCountry stores the country ID as a string, needs parsing.
                // Or, if datalist.SellerCountry is the name, we need to get the ID.
                // For now, let's assume it's the name and we'll refine if needed.
                // This part needs careful checking against how SellerCountry is populated in DataList.
                // For the purpose of this step, we'll assume direct use is okay, but it's a potential issue.
                // A better approach would be to pass datalist.SellerCountry (string) and datalist.SellerIsBusiness (bool)
                // directly to CalculateUkBuyerProtectionFee if SellerUser construction is complex here.
                // However, CalculateUkBuyerProtectionFee expects SellerUser.
                // Let's assume datalist.SellerCountry is "United Kingdom" for UK sellers.
                // And that UserWebCountryId or StoreWebCountryId would be set based on this.
                // This is a simplification for now.
                UserWebCountryId = (datalist.SellerCountry == "United Kingdom" || datalist.SellerCountry == "UK" || datalist.SellerCountry == "GB") ? 826 : 0, // Simplified: 0 if not UK
                StoreWebCountryId = (datalist.SellerCountry == "United Kingdom" || datalist.SellerCountry == "UK" || datalist.SellerCountry == "GB") ? 826 : 0 // Simplified
            };
            
            var ukFee = CalculateUkBuyerProtectionFee(baseItemPrice, sellerInfo);

            // Add fee to item price. Assuming baseItemPrice and ukFee are in compatible currencies (e.g., both GBP or converted).
            // If ukFee is in GBP and baseItemPrice is in another currency, conversion is needed here.
            // For now, direct addition is assumed as per "assumed GBP or converted".
            if (ukFee.Value > 0 && baseItemPrice.Currency == ukFee.Currency) // Basic check
            {
                //datalist.ItemPricing.ItemPrice = new CurrencyAmount(baseItemPrice.Value + ukFee.Value, baseItemPrice.Currency);
            }
            else
            {
                // Log if currencies don't match and fee is applicable, or handle conversion
                //datalist.ItemPricing.ItemPrice = baseItemPrice;
            }
            
            datalist.ItemPricing.AuctionPrice = GetAuctionPrice(item.ListingType.ToString(), currentPrice);
        }

        private async Task<ShippingDetailsType> GetItemShipping(CountryCodeType destinationCountry, string zip, string itemItemID)
        {
            var shippingDetails = await _apiService.GetItemShipping(itemItemID, 1, destinationCountry, zip);
            return shippingDetails;
        }

        public static void TryParseShipping(ShippingDetailsType shippingDetails, ItemShipping itemShipping, bool isInternational)
        {
            try
            {
                if (shippingDetails == null)
                    return;

                // var shippingDetailsParser = ShippingDetailsParser.Parse(shippingDetails, isInternational);
                //     if (shippingDetailsParser == null)
                //        return;

                ShippingParserTradingAPI.GetBareSingleShippingPrice(shippingDetails, itemShipping, isInternational);
                if (itemShipping.ShippingStatusTradingAPI != ItemShipping.ParsingStatus.Fail)
                {
                    itemShipping.PackageHandlingCost = ShippingParserTradingAPI.GetPackagingCost(shippingDetails.CalculatedShippingRate, isInternational);
                    itemShipping.ShipAdditionalItem = ShippingParserTradingAPI.GetAdditionalItemShipCost(shippingDetails, isInternational);
                    itemShipping.ShippingInsuranceCost = ShippingParserTradingAPI.GetInsuranceCost(shippingDetails, isInternational);
                    itemShipping.ImportCost = ShippingParserTradingAPI.GetImportCost(shippingDetails, isInternational);
                    itemShipping.FullSingleShippingPrice = ShippingParserTradingAPI.GetFullSingleItemShippingPrice(itemShipping);
                }
            }
            catch (Exception)
            {
                if (Debugger.IsAttached)
                    throw;
            }
        }

        private static CurrencyAmount GetBinPrice(string listingType, bool buyItNowAvailable, CurrencyAmount buyItNowPrice, CurrencyAmount currentPrice)
        {
            var itemPrice = new CurrencyAmount(0, "USD");

            switch (listingType)
            {
                case "FixedPriceItem":
                case "StoresFixedPrice":
                    itemPrice = currentPrice;
                    break;

                case "Auction":
                case "Chinese":
                    if (buyItNowAvailable)
                        itemPrice = buyItNowPrice;
                    break;
                case "PersonalOffer":
                case "LeadGeneration":
                    break;
            }

            return itemPrice;
        }

        private static CurrencyAmount GetAuctionPrice(string listingType, CurrencyAmount currentPrice)
        {
            var itemPrice = new CurrencyAmount(0, "USD");

            if (listingType == "Chinese" || listingType == "Auction")
                // && item.ListingDetails.BuyItNowAvailable)
            {
                itemPrice = new CurrencyAmount(currentPrice.Value, currentPrice.Currency.ToString());
            }

            return itemPrice;
        }

        /// <summary&gt;
        /// Calculates the UK Buyer Protection Fee if applicable.
        /// </summary&gt;
        /// <param name="itemPrice"&gt;The price of the item (assumed GBP or converted).</param&gt;
        /// <param name="sellerUser"&gt;The seller information.</param&gt;
        /// <returns&gt;The calculated fee as a CurrencyAmount in GBP, or zero if not applicable.</returns&gt;
        internal static CurrencyAmount CalculateUkBuyerProtectionFee(CurrencyAmount itemPrice, SellerUser sellerUser)
        {
            const int ukCountryId = 826;
            const double flatFee = 0.75; // Assuming £0.75 based on task description
            const double tier1Limit = 300.00;
            const double tier2Limit = 4000.00;
            const double tier1Rate = 0.04; // 4%
            const double tier2Rate = 0.02; // 2%
            const string feeCurrency = "GBP"; // Fee is in GBP

            // Check if seller is a private seller based in the UK
            bool isUkSeller = sellerUser.UserWebCountryId == ukCountryId || sellerUser.StoreWebCountryId == ukCountryId;
            bool isPrivateSeller = !sellerUser.Business;

            if (isUkSeller && isPrivateSeller)
            {
                // Ensure itemPrice is not null and has a value
                if (itemPrice == null || itemPrice.Value <= 0)
                {
                    return new CurrencyAmount(0, feeCurrency);
                }

                // Assuming itemPrice.Value is the price in GBP for calculation
                double price = itemPrice.Value;
                double variableFee = 0;

                if (price <= tier1Limit)
                {
                    variableFee = price * tier1Rate;
                }
                else // price > tier1Limit
                {
                    variableFee = tier1Limit * tier1Rate; // Fee for the first £300
                    if (price <= tier2Limit)
                    {
                        variableFee += (price - tier1Limit) * tier2Rate; // Fee for the portion between £300.01 and £4000
                    }
                    else // price > tier2Limit
                    {
                        variableFee += (tier2Limit - tier1Limit) * tier2Rate; // Fee for the portion between £300.01 and £4000
                        // No additional fee above £4000 according to rules
                    }
                }

                double totalFee = flatFee + variableFee;
                return new CurrencyAmount(totalFee, feeCurrency);
            }

            // Not applicable
            return new CurrencyAmount(0, feeCurrency);
        }
    }
}
