﻿using System;
using System.Collections;
using System.ComponentModel;
using System.Data;
using System.Linq;
using System.Reflection;
using System.Windows.Forms;
using System.Xml.Serialization;
using DevExpress.Data.Filtering;
using DevExpress.Data.Filtering.Exceptions;
using DevExpress.Data.Filtering.Helpers;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid;
using uBuyFirst.AI;
using uBuyFirst.Grid;
using uBuyFirst.Item;

namespace uBuyFirst.Filters
{
    [Obfuscation(Exclude = true)]
    [Serializable]
    [XmlRoot("XFiltersChild")]
    public class XFilterClassChild
    {
        private string _action;
        private string _alias;
        private bool _enabled;
        private string _expression;
        private CriteriaOperator _filterCriteria;
        private string _formatColumn;

        [NonSerialized]
        private GridFormatRule _gridFormatRule;

        private string _keywordScope;
        private string[] _serializedAppearance;

        [NonSerialized]
        private ExpressionEvaluator _evaluator;

        public XFilterClassChild()
        {
            _serializedAppearance = new[] { "", "", "" };
        }

        public bool Enabled
        {
            get => _enabled;
            set => _enabled = value;
        }

        public string Alias
        {
            get => _alias ?? "";
            set => _alias = value;
        }

        public string Action
        {
            get => _action;
            set => _action = value;
        }

        public CriteriaOperator FilterCriteria
        {
            get => _filterCriteria;
            set => _filterCriteria = value;
        }

        public ExpressionEvaluator GetEvaluator()
        {
            return _evaluator;
        }

        public void Rebuild()
        {
            var dataTable = GridBuilder.GetDefaultDataTable();
            foreach (var specific in ItemSpecifics.CategorySpecificsList)
            {
                if (dataTable.Columns.Contains(specific.CategoryName))
                    continue;

                dataTable.Columns.Add(specific.CategoryName);
            }

            foreach (var aiColumn in AiAnalysis.AiColumnsList)
            {
                if (dataTable.Columns.Contains(aiColumn))
                    continue;

                dataTable.Columns.Add(aiColumn);
            }

            try
            {
                var pds = new PropertyDescriptor[dataTable.Columns.Count];
                for (int i = 0; i < pds.Length; i++)
                {
                    pds[i] = new Filters.MyDescriptor(dataTable.Columns[i].ColumnName, dataTable.Columns[i].DataType, typeof(DataRow));
                }

                var pdc = new PropertyDescriptorCollection(pds);
                _evaluator = new ExpressionEvaluator(pdc, _filterCriteria, false);
            }
            catch (InvalidPropertyPathException ex)
            {
                Enabled = false;
                XtraMessageBox.Show($"Filter \'{Alias}\'\r\n{ex.Message}\r\nPlease edit or remove this filter");
            }
        }

        //Test if rule is valid, all columns exist
        private bool IsCriteriaValid(IList dataSource, string filterString)
        {
            CriteriaOperator op = CriteriaOperator.Parse(filterString, null);
            if (ReferenceEquals(op, null))
                return false;
            PropertyDescriptorCollection pdc = ListBindingHelper.GetListItemProperties(dataSource);
            var dict = CriteriaColumnAffinityResolver.SplitByColumns(op);
            return dict.Keys.All(item => pdc.Find(item.PropertyName, false) != null);
        }

        public override string ToString()
        {
            return Alias;
        }
    }
}
