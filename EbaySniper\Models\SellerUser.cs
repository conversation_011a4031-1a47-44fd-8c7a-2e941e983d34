﻿namespace uBuyFirst.Models
{
    /// <summary>
    /// A refined representation of SellerUserRaw with boolean flags.
    /// </summary>
    public class SellerUser
    {
        public bool AccountActive { get; set; }
        public string? UserName { get; set; }
        public bool Verified { get; set; }
        public bool FeedbackPrivate { get; set; }
        public int? TransactionPercent { get; set; }
        public bool Business { get; set; }
        public bool StoreOwner { get; set; }
        public string? StoreName { get; set; }
        public bool TopRated { get; set; }
        public int? UserWebCountryId { get; set; }
        public int? StoreWebCountryId { get; set; }
        public string? RegDate { get; set; }
    }
}
