﻿// ReSharper disable StringLiteralTypo
using System.Collections.Generic;
using uBuyFirst.Models;
using uBuyFirst.Seller;

namespace uBuyFirst.Services
{
    public class CountryService : ICountryService
    {
        /// <inheritdoc />
        public CountryData? GetCountryById(int id)
        {
            _countriesById.TryGetValue(id, out var country);
            return country;
        }

        /// <inheritdoc />
        public IEnumerable<CountryData> GetAllCountries()
        {
            return _countriesById.Values;
        }
        
        public string GetSellerCountry(SellerUser sellerUser)
        {
            var sellerCountry = "";
            if (sellerUser.StoreWebCountryId.HasValue)
            {
                var storeCountry = GetCountryById(sellerUser.StoreWebCountryId.Value);
                if (storeCountry != null)
                {
                    sellerCountry = storeCountry.Name;
                }
            }
            else if (sellerUser.UserWebCountryId.HasValue) // Use else-if for clarity
            {
                var userCountry = GetCountryById(sellerUser.UserWebCountryId.Value);
                if (userCountry != null)
                {
                    sellerCountry = userCountry.Name;
                }

            }

            return sellerCountry;
        }
        
        private readonly Dictionary<int, CountryData> _countriesById = new()
        {
            [4] = new CountryData
            {
                Name = @"Afghanistan",
                Iso = @"AF",
                Id = 4,
                Region = @"Asia",
                RegionId = 4,
                SubRegion = @"Southern Asia",
                SubRegionId = 34
            },
            [8] = new CountryData
            {
                Name = @"Albania",
                Iso = @"AL",
                Id = 8,
                Region = @"Europe",
                RegionId = 8,
                SubRegion = @"Southern Europe",
                SubRegionId = 39
            },
            [12] = new CountryData
            {
                Name = @"Algeria",
                Iso = @"DZ",
                Id = 12,
                Region = @"Africa",
                RegionId = 12,
                SubRegion = @"Northern Africa",
                SubRegionId = 15
            },
            [16] = new CountryData
            {
                Name = @"American Samoa",
                Iso = @"AS",
                Id = 16,
                Region = @"Oceania",
                RegionId = 16,
                SubRegion = @"Polynesia",
                SubRegionId = 61
            },
            [20] = new CountryData
            {
                Name = @"Andorra",
                Iso = @"AD",
                Id = 20,
                Region = @"Europe",
                RegionId = 20,
                SubRegion = @"Southern Europe",
                SubRegionId = 39
            },
            [24] = new CountryData
            {
                Name = @"Angola",
                Iso = @"AO",
                Id = 24,
                Region = @"Africa",
                RegionId = 24,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [28] = new CountryData
            {
                Name = @"Antigua and Barbuda",
                Iso = @"AG",
                Id = 28,
                Region = @"Americas",
                RegionId = 28,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [31] = new CountryData
            {
                Name = @"Republic of Azerbaijan",
                Iso = @"AZ",
                Id = 31,
                Region = @"Asia",
                RegionId = 31,
                SubRegion = @"Western Asia",
                SubRegionId = 145
            },
            [32] = new CountryData
            {
                Name = @"Argentina",
                Iso = @"AR",
                Id = 32,
                Region = @"Americas",
                RegionId = 32,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [36] = new CountryData
            {
                Name = @"Australia",
                Iso = @"AU",
                Id = 36,
                Region = @"Oceania",
                RegionId = 36,
                SubRegion = @"Australia and New Zealand",
                SubRegionId = 53
            },
            [40] = new CountryData
            {
                Name = @"Austria",
                Iso = @"AT",
                Id = 40,
                Region = @"Europe",
                RegionId = 40,
                SubRegion = @"Western Europe",
                SubRegionId = 155
            },
            [44] = new CountryData
            {
                Name = @"Bahamas",
                Iso = @"BS",
                Id = 44,
                Region = @"Americas",
                RegionId = 44,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [48] = new CountryData
            {
                Name = @"Bahrain",
                Iso = @"BH",
                Id = 48,
                Region = @"Asia",
                RegionId = 48,
                SubRegion = @"Western Asia",
                SubRegionId = 145
            },
            [50] = new CountryData
            {
                Name = @"Bangladesh",
                Iso = @"BD",
                Id = 50,
                Region = @"Asia",
                RegionId = 50,
                SubRegion = @"Southern Asia",
                SubRegionId = 34
            },
            [51] = new CountryData
            {
                Name = @"Armenia",
                Iso = @"AM",
                Id = 51,
                Region = @"Asia",
                RegionId = 51,
                SubRegion = @"Western Asia",
                SubRegionId = 145
            },
            [52] = new CountryData
            {
                Name = @"Barbados",
                Iso = @"BB",
                Id = 52,
                Region = @"Americas",
                RegionId = 52,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [56] = new CountryData
            {
                Name = @"Belgium",
                Iso = @"BE",
                Id = 56,
                Region = @"Europe",
                RegionId = 56,
                SubRegion = @"Western Europe",
                SubRegionId = 155
            },
            [60] = new CountryData
            {
                Name = @"Bermuda",
                Iso = @"BM",
                Id = 60,
                Region = @"Americas",
                RegionId = 60,
                SubRegion = @"Northern America",
                SubRegionId = 21
            },
            [64] = new CountryData
            {
                Name = @"Bhutan",
                Iso = @"BT",
                Id = 64,
                Region = @"Asia",
                RegionId = 64,
                SubRegion = @"Southern Asia",
                SubRegionId = 34
            },
            [68] = new CountryData
            {
                Name = @"Bolivia",
                Iso = @"BO",
                Id = 68,
                Region = @"Americas",
                RegionId = 68,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [70] = new CountryData
            {
                Name = @"Bosnia and Herzegovina",
                Iso = @"BA",
                Id = 70,
                Region = @"Europe",
                RegionId = 70,
                SubRegion = @"Southern Europe",
                SubRegionId = 39
            },
            [72] = new CountryData
            {
                Name = @"Botswana",
                Iso = @"BW",
                Id = 72,
                Region = @"Africa",
                RegionId = 72,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [76] = new CountryData
            {
                Name = @"Brazil",
                Iso = @"BR",
                Id = 76,
                Region = @"Americas",
                RegionId = 76,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [84] = new CountryData
            {
                Name = @"Belize",
                Iso = @"BZ",
                Id = 84,
                Region = @"Americas",
                RegionId = 84,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [90] = new CountryData
            {
                Name = @"Solomon Islands",
                Iso = @"SB",
                Id = 90,
                Region = @"Oceania",
                RegionId = 90,
                SubRegion = @"Melanesia",
                SubRegionId = 54
            },
            [92] = new CountryData
            {
                Name = @"British Virgin Islands",
                Iso = @"VG",
                Id = 92,
                Region = @"Americas",
                RegionId = 92,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [96] = new CountryData
            {
                Name = @"Brunei Darussalam",
                Iso = @"BN",
                Id = 96,
                Region = @"Asia",
                RegionId = 96,
                SubRegion = @"South-eastern Asia",
                SubRegionId = 35
            },
            [100] = new CountryData
            {
                Name = @"Bulgaria",
                Iso = @"BG",
                Id = 100,
                Region = @"Europe",
                RegionId = 100,
                SubRegion = @"Eastern Europe",
                SubRegionId = 151
            },
            [108] = new CountryData
            {
                Name = @"Burundi",
                Iso = @"BI",
                Id = 108,
                Region = @"Africa",
                RegionId = 108,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [112] = new CountryData
            {
                Name = @"Belarus",
                Iso = @"BY",
                Id = 112,
                Region = @"Europe",
                RegionId = 112,
                SubRegion = @"Eastern Europe",
                SubRegionId = 151
            },
            [116] = new CountryData
            {
                Name = @"Cambodia",
                Iso = @"KH",
                Id = 116,
                Region = @"Asia",
                RegionId = 116,
                SubRegion = @"South-eastern Asia",
                SubRegionId = 35
            },
            [120] = new CountryData
            {
                Name = @"Cameroon",
                Iso = @"CM",
                Id = 120,
                Region = @"Africa",
                RegionId = 120,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [124] = new CountryData
            {
                Name = @"Canada",
                Iso = @"CA",
                Id = 124,
                Region = @"Americas",
                RegionId = 124,
                SubRegion = @"Northern America",
                SubRegionId = 21
            },
            [132] = new CountryData
            {
                Name = @"Cape Verde Islands",
                Iso = @"CV",
                Id = 132,
                Region = @"Africa",
                RegionId = 132,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [136] = new CountryData
            {
                Name = @"Cayman Islands",
                Iso = @"KY",
                Id = 136,
                Region = @"Americas",
                RegionId = 136,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [140] = new CountryData
            {
                Name = @"Central African Republic",
                Iso = @"CF",
                Id = 140,
                Region = @"Africa",
                RegionId = 140,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [144] = new CountryData
            {
                Name = @"Sri Lanka",
                Iso = @"LK",
                Id = 144,
                Region = @"Asia",
                RegionId = 144,
                SubRegion = @"Southern Asia",
                SubRegionId = 34
            },
            [148] = new CountryData
            {
                Name = @"Chad",
                Iso = @"TD",
                Id = 148,
                Region = @"Africa",
                RegionId = 148,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [152] = new CountryData
            {
                Name = @"Chile",
                Iso = @"CL",
                Id = 152,
                Region = @"Americas",
                RegionId = 152,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [156] = new CountryData
            {
                Name = @"China",
                Iso = @"CN",
                Id = 156,
                Region = @"Asia",
                RegionId = 156,
                SubRegion = @"Eastern Asia",
                SubRegionId = 30
            },
            [158] = new CountryData
            {
                Name = @"Taiwan",
                Iso = @"TW",
                Id = 158,
                Region = @"Asia",
                RegionId = 158,
                SubRegion = @"Eastern Asia",
                SubRegionId = 35
            },
            [170] = new CountryData
            {
                Name = @"Colombia",
                Iso = @"CO",
                Id = 170,
                Region = @"Americas",
                RegionId = 170,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [174] = new CountryData
            {
                Name = @"Comoros",
                Iso = @"KM",
                Id = 174,
                Region = @"Africa",
                RegionId = 174,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [175] = new CountryData
            {
                Name = @"Mayotte",
                Iso = @"YT",
                Id = 175,
                Region = @"Africa",
                RegionId = 175,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [178] = new CountryData
            {
                Name = @"Congo, Republic of the",
                Iso = @"CG",
                Id = 178,
                Region = @"Africa",
                RegionId = 178,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [180] = new CountryData
            {
                Name = @"Congo, Democratic Republic of the",
                Iso = @"CD",
                Id = 180,
                Region = @"Africa",
                RegionId = 180,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [184] = new CountryData
            {
                Name = @"Cook Islands",
                Iso = @"CK",
                Id = 184,
                Region = @"Oceania",
                RegionId = 184,
                SubRegion = @"Polynesia",
                SubRegionId = 61
            },
            [188] = new CountryData
            {
                Name = @"Costa Rica",
                Iso = @"CR",
                Id = 188,
                Region = @"Americas",
                RegionId = 188,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [191] = new CountryData
            {
                Name = @"Republic of Croatia",
                Iso = @"HR",
                Id = 191,
                Region = @"Europe",
                RegionId = 191,
                SubRegion = @"Southern Europe",
                SubRegionId = 39
            },
            [196] = new CountryData
            {
                Name = @"Cyprus",
                Iso = @"CY",
                Id = 196,
                Region = @"Asia",
                RegionId = 196,
                SubRegion = @"Western Asia",
                SubRegionId = 145
            },
            [203] = new CountryData
            {
                Name = @"Czech Republic",
                Iso = @"CZ",
                Id = 203,
                Region = @"Europe",
                RegionId = 203,
                SubRegion = @"Eastern Europe",
                SubRegionId = 151
            },
            [204] = new CountryData
            {
                Name = @"Benin",
                Iso = @"BJ",
                Id = 204,
                Region = @"Africa",
                RegionId = 204,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [208] = new CountryData
            {
                Name = @"Denmark",
                Iso = @"DK",
                Id = 208,
                Region = @"Europe",
                RegionId = 208,
                SubRegion = @"Northern Europe",
                SubRegionId = 154
            },
            [212] = new CountryData
            {
                Name = @"Dominica",
                Iso = @"DM",
                Id = 212,
                Region = @"Americas",
                RegionId = 212,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [214] = new CountryData
            {
                Name = @"Dominican Republic",
                Iso = @"DO",
                Id = 214,
                Region = @"Americas",
                RegionId = 214,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [218] = new CountryData
            {
                Name = @"Ecuador",
                Iso = @"EC",
                Id = 218,
                Region = @"Americas",
                RegionId = 218,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [222] = new CountryData
            {
                Name = @"El Salvador",
                Iso = @"SV",
                Id = 222,
                Region = @"Americas",
                RegionId = 222,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [226] = new CountryData
            {
                Name = @"Equatorial Guinea",
                Iso = @"GQ",
                Id = 226,
                Region = @"Africa",
                RegionId = 226,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [231] = new CountryData
            {
                Name = @"Ethiopia",
                Iso = @"ET",
                Id = 231,
                Region = @"Africa",
                RegionId = 231,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [232] = new CountryData
            {
                Name = @"Eritrea",
                Iso = @"ER",
                Id = 232,
                Region = @"Africa",
                RegionId = 232,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [233] = new CountryData
            {
                Name = @"Estonia",
                Iso = @"EE",
                Id = 233,
                Region = @"Europe",
                RegionId = 233,
                SubRegion = @"Northern Europe",
                SubRegionId = 154
            },
            [238] = new CountryData
            {
                Name = @"Falkland Islands (Islas Malvinas)",
                Iso = @"FK",
                Id = 238,
                Region = @"Americas",
                RegionId = 238,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [242] = new CountryData
            {
                Name = @"Fiji",
                Iso = @"FJ",
                Id = 242,
                Region = @"Oceania",
                RegionId = 242,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 54
            },
            [246] = new CountryData
            {
                Name = @"Finland",
                Iso = @"FI",
                Id = 246,
                Region = @"Europe",
                RegionId = 246,
                SubRegion = @"Northern Europe",
                SubRegionId = 154
            },
            [250] = new CountryData
            {
                Name = @"France",
                Iso = @"FR",
                Id = 250,
                Region = @"Europe",
                RegionId = 250,
                SubRegion = @"Western Europe",
                SubRegionId = 155
            },
            [254] = new CountryData
            {
                Name = @"French Guiana",
                Iso = @"GF",
                Id = 254,
                Region = @"Americas",
                RegionId = 254,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [258] = new CountryData
            {
                Name = @"Tahiti",
                Iso = @"TA",
                Id = 258,
                Region = @"Oceania",
                RegionId = 258,
                SubRegion = @"Polynesia",
                SubRegionId = 61
            },
            [262] = new CountryData
            {
                Name = @"Djibouti",
                Iso = @"DJ",
                Id = 262,
                Region = @"Africa",
                RegionId = 262,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [266] = new CountryData
            {
                Name = @"Gabon Republic",
                Iso = @"GA",
                Id = 266,
                Region = @"Africa",
                RegionId = 266,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [268] = new CountryData
            {
                Name = @"Georgia",
                Iso = @"GE",
                Id = 268,
                Region = @"Asia",
                RegionId = 268,
                SubRegion = @"Western Asia",
                SubRegionId = 145
            },
            [270] = new CountryData
            {
                Name = @"Gambia",
                Iso = @"GM",
                Id = 270,
                Region = @"Africa",
                RegionId = 270,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [276] = new CountryData
            {
                Name = @"Germany",
                Iso = @"DE",
                Id = 276,
                Region = @"Europe",
                RegionId = 276,
                SubRegion = @"Western Europe",
                SubRegionId = 155
            },
            [288] = new CountryData
            {
                Name = @"Ghana",
                Iso = @"GH",
                Id = 288,
                Region = @"Africa",
                RegionId = 288,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [292] = new CountryData
            {
                Name = @"Gibraltar",
                Iso = @"GI",
                Id = 292,
                Region = @"Europe",
                RegionId = 292,
                SubRegion = @"Southern Europe",
                SubRegionId = 39
            },
            [296] = new CountryData
            {
                Name = @"Kiribati",
                Iso = @"KI",
                Id = 296,
                Region = @"Oceania",
                RegionId = 296,
                SubRegion = @"Micronesia",
                SubRegionId = 57
            },
            [300] = new CountryData
            {
                Name = @"Greece",
                Iso = @"GR",
                Id = 300,
                Region = @"Europe",
                RegionId = 300,
                SubRegion = @"Southern Europe",
                SubRegionId = 39
            },
            [304] = new CountryData
            {
                Name = @"Greenland",
                Iso = @"GL",
                Id = 304,
                Region = @"Americas",
                RegionId = 304,
                SubRegion = @"Northern America",
                SubRegionId = 21
            },
            [308] = new CountryData
            {
                Name = @"Grenada",
                Iso = @"GD",
                Id = 308,
                Region = @"Americas",
                RegionId = 308,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [312] = new CountryData
            {
                Name = @"Guadeloupe",
                Iso = @"GP",
                Id = 312,
                Region = @"Americas",
                RegionId = 312,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [316] = new CountryData
            {
                Name = @"Guam",
                Iso = @"GU",
                Id = 316,
                Region = @"Oceania",
                RegionId = 316,
                SubRegion = @"Micronesia",
                SubRegionId = 57
            },
            [320] = new CountryData
            {
                Name = @"Guatemala",
                Iso = @"GT",
                Id = 320,
                Region = @"Americas",
                RegionId = 320,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [324] = new CountryData
            {
                Name = @"Guinea",
                Iso = @"GN",
                Id = 324,
                Region = @"Africa",
                RegionId = 324,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [328] = new CountryData
            {
                Name = @"Guyana",
                Iso = @"GY",
                Id = 328,
                Region = @"Americas",
                RegionId = 328,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [332] = new CountryData
            {
                Name = @"Haiti",
                Iso = @"HT",
                Id = 332,
                Region = @"Americas",
                RegionId = 332,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [336] = new CountryData
            {
                Name = @"Vatican City",
                Iso = @"VA",
                Id = 336,
                Region = @"Europe",
                RegionId = 336,
                SubRegion = @"Southern Europe",
                SubRegionId = 39
            },
            [340] = new CountryData
            {
                Name = @"Honduras",
                Iso = @"HN",
                Id = 340,
                Region = @"Americas",
                RegionId = 340,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [344] = new CountryData
            {
                Name = @"Hong Kong",
                Iso = @"HK",
                Id = 344,
                Region = @"Asia",
                RegionId = 344,
                SubRegion = @"Eastern Asia",
                SubRegionId = 30
            },
            [348] = new CountryData
            {
                Name = @"Hungary",
                Iso = @"HU",
                Id = 348,
                Region = @"Europe",
                RegionId = 348,
                SubRegion = @"Eastern Europe",
                SubRegionId = 151
            },
            [352] = new CountryData
            {
                Name = @"Iceland",
                Iso = @"IS",
                Id = 352,
                Region = @"Europe",
                RegionId = 352,
                SubRegion = @"Northern Europe",
                SubRegionId = 154
            },
            [356] = new CountryData
            {
                Name = @"India",
                Iso = @"IN",
                Id = 356,
                Region = @"Asia",
                RegionId = 356,
                SubRegion = @"Southern Asia",
                SubRegionId = 34
            },
            [360] = new CountryData
            {
                Name = @"Indonesia",
                Iso = @"ID",
                Id = 360,
                Region = @"Asia",
                RegionId = 360,
                SubRegion = @"South-eastern Asia",
                SubRegionId = 35
            },
            [372] = new CountryData
            {
                Name = @"Ireland",
                Iso = @"IE",
                Id = 372,
                Region = @"Europe",
                RegionId = 372,
                SubRegion = @"Northern Europe",
                SubRegionId = 154
            },
            [376] = new CountryData
            {
                Name = @"Israel",
                Iso = @"IL",
                Id = 376,
                Region = @"Asia",
                RegionId = 376,
                SubRegion = @"Western Asia",
                SubRegionId = 145
            },
            [380] = new CountryData
            {
                Name = @"Italy",
                Iso = @"IT",
                Id = 380,
                Region = @"Europe",
                RegionId = 380,
                SubRegion = @"Southern Europe",
                SubRegionId = 39
            },
            [384] = new CountryData
            {
                Name = @"Cote d Ivoire (Ivory Coast)",
                Iso = @"CI",
                Id = 384,
                Region = @"Africa",
                RegionId = 384,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [388] = new CountryData
            {
                Name = @"Jamaica",
                Iso = @"JM",
                Id = 388,
                Region = @"Americas",
                RegionId = 388,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [392] = new CountryData
            {
                Name = @"Japan",
                Iso = @"JP",
                Id = 392,
                Region = @"Asia",
                RegionId = 392,
                SubRegion = @"Eastern Asia",
                SubRegionId = 30
            },
            [398] = new CountryData
            {
                Name = @"Kazakhstan",
                Iso = @"KZ",
                Id = 398,
                Region = @"Asia",
                RegionId = 398,
                SubRegion = @"Central Asia",
                SubRegionId = 143
            },
            [400] = new CountryData
            {
                Name = @"Jordan",
                Iso = @"JO",
                Id = 400,
                Region = @"Asia",
                RegionId = 400,
                SubRegion = @"Western Asia",
                SubRegionId = 145
            },
            [404] = new CountryData
            {
                Name = @"Kenya",
                Iso = @"KE",
                Id = 404,
                Region = @"Africa",
                RegionId = 404,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [410] = new CountryData
            {
                Name = @"South Korea",
                Iso = @"KR",
                Id = 410,
                Region = @"Asia",
                RegionId = 410,
                SubRegion = @"Eastern Asia",
                SubRegionId = 30
            },
            [414] = new CountryData
            {
                Name = @"Kuwait",
                Iso = @"KW",
                Id = 414,
                Region = @"Asia",
                RegionId = 414,
                SubRegion = @"Western Asia",
                SubRegionId = 145
            },
            [417] = new CountryData
            {
                Name = @"Kyrgyzstan",
                Iso = @"KG",
                Id = 417,
                Region = @"Asia",
                RegionId = 417,
                SubRegion = @"Central Asia",
                SubRegionId = 143
            },
            [418] = new CountryData
            {
                Name = @"Laos",
                Iso = @"LA",
                Id = 418,
                Region = @"Asia",
                RegionId = 418,
                SubRegion = @"South-eastern Asia",
                SubRegionId = 35
            },
            [422] = new CountryData
            {
                Name = @"Lebanon",
                Iso = @"LB",
                Id = 422,
                Region = @"Asia",
                RegionId = 422,
                SubRegion = @"Western Asia",
                SubRegionId = 145
            },
            [428] = new CountryData
            {
                Name = @"Latvia",
                Iso = @"LV",
                Id = 428,
                Region = @"Europe",
                RegionId = 428,
                SubRegion = @"Northern Europe",
                SubRegionId = 154
            },
            [438] = new CountryData
            {
                Name = @"Liechtenstein",
                Iso = @"LI",
                Id = 438,
                Region = @"Europe",
                RegionId = 438,
                SubRegion = @"Western Europe",
                SubRegionId = 155
            },
            [440] = new CountryData
            {
                Name = @"Lithuania",
                Iso = @"LT",
                Id = 440,
                Region = @"Europe",
                RegionId = 440,
                SubRegion = @"Northern Europe",
                SubRegionId = 154
            },
            [442] = new CountryData
            {
                Name = @"Luxembourg",
                Iso = @"LU",
                Id = 442,
                Region = @"Europe",
                RegionId = 442,
                SubRegion = @"Western Europe",
                SubRegionId = 155
            },
            [446] = new CountryData
            {
                Name = @"Macau",
                Iso = @"MO",
                Id = 446,
                Region = @"Asia",
                RegionId = 446,
                SubRegion = @"Eastern Asia",
                SubRegionId = 30
            },
            [450] = new CountryData
            {
                Name = @"Madagascar",
                Iso = @"MG",
                Id = 450,
                Region = @"Africa",
                RegionId = 450,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [454] = new CountryData
            {
                Name = @"Malawi",
                Iso = @"MW",
                Id = 454,
                Region = @"Africa",
                RegionId = 454,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [458] = new CountryData
            {
                Name = @"Malaysia",
                Iso = @"MY",
                Id = 458,
                Region = @"Asia",
                RegionId = 458,
                SubRegion = @"South-eastern Asia",
                SubRegionId = 35
            },
            [462] = new CountryData
            {
                Name = @"Maldives",
                Iso = @"MV",
                Id = 462,
                Region = @"Asia",
                RegionId = 462,
                SubRegion = @"Southern Asia",
                SubRegionId = 34
            },
            [466] = new CountryData
            {
                Name = @"Mali",
                Iso = @"ML",
                Id = 466,
                Region = @"Africa",
                RegionId = 466,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [470] = new CountryData
            {
                Name = @"Malta",
                Iso = @"MT",
                Id = 470,
                Region = @"Europe",
                RegionId = 470,
                SubRegion = @"Southern Europe",
                SubRegionId = 39
            },
            [474] = new CountryData
            {
                Name = @"Martinique",
                Iso = @"MQ",
                Id = 474,
                Region = @"Americas",
                RegionId = 474,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [478] = new CountryData
            {
                Name = @"Mauritania",
                Iso = @"MR",
                Id = 478,
                Region = @"Africa",
                RegionId = 478,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [480] = new CountryData
            {
                Name = @"Mauritius",
                Iso = @"MU",
                Id = 480,
                Region = @"Africa",
                RegionId = 480,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [484] = new CountryData
            {
                Name = @"Mexico",
                Iso = @"MX",
                Id = 484,
                Region = @"Americas",
                RegionId = 484,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [492] = new CountryData
            {
                Name = @"Monaco",
                Iso = @"MC",
                Id = 492,
                Region = @"Europe",
                RegionId = 492,
                SubRegion = @"Western Europe",
                SubRegionId = 155
            },
            [496] = new CountryData
            {
                Name = @"Mongolia",
                Iso = @"MN",
                Id = 496,
                Region = @"Asia",
                RegionId = 496,
                SubRegion = @"Eastern Asia",
                SubRegionId = 30
            },
            [498] = new CountryData
            {
                Name = @"Moldova",
                Iso = @"MD",
                Id = 498,
                Region = @"Europe",
                RegionId = 498,
                SubRegion = @"Eastern Europe",
                SubRegionId = 151
            },
            [499] = new CountryData
            {
                Name = @"Montenegro",
                Iso = @"ME",
                Id = 499,
                Region = @"Europe",
                RegionId = 499,
                SubRegion = @"Southern Europe",
                SubRegionId = 39
            },
            [500] = new CountryData
            {
                Name = @"Montserrat",
                Iso = @"MS",
                Id = 500,
                Region = @"Americas",
                RegionId = 500,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [504] = new CountryData
            {
                Name = @"Morocco",
                Iso = @"MA",
                Id = 504,
                Region = @"Africa",
                RegionId = 504,
                SubRegion = @"Northern Africa",
                SubRegionId = 15
            },
            [508] = new CountryData
            {
                Name = @"Mozambique",
                Iso = @"MZ",
                Id = 508,
                Region = @"Africa",
                RegionId = 508,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [512] = new CountryData
            {
                Name = @"Oman",
                Iso = @"OM",
                Id = 512,
                Region = @"Asia",
                RegionId = 512,
                SubRegion = @"Western Asia",
                SubRegionId = 145
            },
            [516] = new CountryData
            {
                Name = @"Namibia",
                Iso = @"NA",
                Id = 516,
                Region = @"Africa",
                RegionId = 516,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [520] = new CountryData
            {
                Name = @"Nauru",
                Iso = @"NR",
                Id = 520,
                Region = @"Oceania",
                RegionId = 520,
                SubRegion = @"Micronesia",
                SubRegionId = 57
            },
            [524] = new CountryData
            {
                Name = @"Nepal",
                Iso = @"NP",
                Id = 524,
                Region = @"Asia",
                RegionId = 524,
                SubRegion = @"Southern Asia",
                SubRegionId = 34
            },
            [528] = new CountryData
            {
                Name = @"Netherlands",
                Iso = @"NL",
                Id = 528,
                Region = @"Europe",
                RegionId = 528,
                SubRegion = @"Western Europe",
                SubRegionId = 155
            },
            [530] = new CountryData
            {
                Name = @"Netherlands Antilles",
                Iso = @"AN",
                Id = 530,
                Region = @"Americas",
                RegionId = 530,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [531] = new CountryData
            {
                Name = @"Curacao",
                Iso = @"CW",
                Id = 531,
                Region = @"Americas",
                RegionId = 531,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [533] = new CountryData
            {
                Name = @"Aruba",
                Iso = @"AW",
                Id = 533,
                Region = @"Americas",
                RegionId = 533,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [540] = new CountryData
            {
                Name = @"New Caledonia",
                Iso = @"NC",
                Id = 540,
                Region = @"Oceania",
                RegionId = 540,
                SubRegion = @"Melanesia",
                SubRegionId = 54
            },
            [548] = new CountryData
            {
                Name = @"Vanuatu",
                Iso = @"VU",
                Id = 548,
                Region = @"Oceania",
                RegionId = 548,
                SubRegion = @"Melanesia",
                SubRegionId = 54
            },
            [554] = new CountryData
            {
                Name = @"New Zealand",
                Iso = @"NZ",
                Id = 554,
                Region = @"Oceania",
                RegionId = 554,
                SubRegion = @"Australia and New Zealand",
                SubRegionId = 53
            },
            [558] = new CountryData
            {
                Name = @"Nicaragua",
                Iso = @"NI",
                Id = 558,
                Region = @"Americas",
                RegionId = 558,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [562] = new CountryData
            {
                Name = @"Niger",
                Iso = @"NE",
                Id = 562,
                Region = @"Africa",
                RegionId = 562,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [566] = new CountryData
            {
                Name = @"Nigeria",
                Iso = @"NG",
                Id = 566,
                Region = @"Africa",
                RegionId = 566,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [570] = new CountryData
            {
                Name = @"Niue",
                Iso = @"NU",
                Id = 570,
                Region = @"Oceania",
                RegionId = 570,
                SubRegion = @"Polynesia",
                SubRegionId = 61
            },
            [578] = new CountryData
            {
                Name = @"Norway",
                Iso = @"NO",
                Id = 578,
                Region = @"Europe",
                RegionId = 578,
                SubRegion = @"Northern Europe",
                SubRegionId = 154
            },
            [583] = new CountryData
            {
                Name = @"Micronesia",
                Iso = @"FM",
                Id = 583,
                Region = @"Oceania",
                RegionId = 583,
                SubRegion = @"Micronesia",
                SubRegionId = 57
            },
            [584] = new CountryData
            {
                Name = @"Marshall Islands",
                Iso = @"MH",
                Id = 584,
                Region = @"Oceania",
                RegionId = 584,
                SubRegion = @"Micronesia",
                SubRegionId = 57
            },
            [585] = new CountryData
            {
                Name = @"Palau",
                Iso = @"PW",
                Id = 585,
                Region = @"Oceania",
                RegionId = 585,
                SubRegion = @"Micronesia",
                SubRegionId = 57
            },
            [586] = new CountryData
            {
                Name = @"Pakistan",
                Iso = @"PK",
                Id = 586,
                Region = @"Asia",
                RegionId = 586,
                SubRegion = @"Southern Asia",
                SubRegionId = 34
            },
            [591] = new CountryData
            {
                Name = @"Panama",
                Iso = @"PA",
                Id = 591,
                Region = @"Americas",
                RegionId = 591,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [598] = new CountryData
            {
                Name = @"Papua New Guinea",
                Iso = @"PG",
                Id = 598,
                Region = @"Oceania",
                RegionId = 598,
                SubRegion = @"Melanesia",
                SubRegionId = 54
            },
            [600] = new CountryData
            {
                Name = @"Paraguay",
                Iso = @"PY",
                Id = 600,
                Region = @"Americas",
                RegionId = 600,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [604] = new CountryData
            {
                Name = @"Peru",
                Iso = @"PE",
                Id = 604,
                Region = @"Americas",
                RegionId = 604,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [608] = new CountryData
            {
                Name = @"Philippines",
                Iso = @"PH",
                Id = 608,
                Region = @"Asia",
                RegionId = 608,
                SubRegion = @"South-eastern Asia",
                SubRegionId = 35
            },
            [616] = new CountryData
            {
                Name = @"Poland",
                Iso = @"PL",
                Id = 616,
                Region = @"Europe",
                RegionId = 616,
                SubRegion = @"Eastern Europe",
                SubRegionId = 151
            },
            [620] = new CountryData
            {
                Name = @"Portugal",
                Iso = @"PT",
                Id = 620,
                Region = @"Europe",
                RegionId = 620,
                SubRegion = @"Southern Europe",
                SubRegionId = 39
            },
            [624] = new CountryData
            {
                Name = @"Guinea-Bissau",
                Iso = @"GW",
                Id = 624,
                Region = @"Africa",
                RegionId = 624,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [630] = new CountryData
            {
                Name = @"Puerto Rico",
                Iso = @"PR",
                Id = 630,
                Region = @"Americas",
                RegionId = 630,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [634] = new CountryData
            {
                Name = @"Qatar",
                Iso = @"QA",
                Id = 634,
                Region = @"Asia",
                RegionId = 634,
                SubRegion = @"Western Asia",
                SubRegionId = 145
            },
            [638] = new CountryData
            {
                Name = @"Reunion",
                Iso = @"RE",
                Id = 638,
                Region = @"Africa",
                RegionId = 638,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [642] = new CountryData
            {
                Name = @"Romania",
                Iso = @"RO",
                Id = 642,
                Region = @"Europe",
                RegionId = 642,
                SubRegion = @"Eastern Europe",
                SubRegionId = 151
            },
            [643] = new CountryData
            {
                Name = @"Russian Federation",
                Iso = @"RU",
                Id = 643,
                Region = @"Europe",
                RegionId = 643,
                SubRegion = @"Eastern Europe",
                SubRegionId = 151
            },
            [646] = new CountryData
            {
                Name = @"Rwanda",
                Iso = @"RW",
                Id = 646,
                Region = @"Africa",
                RegionId = 646,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [654] = new CountryData
            {
                Name = @"Saint Helena",
                Iso = @"SH",
                Id = 654,
                Region = @"Africa",
                RegionId = 654,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [659] = new CountryData
            {
                Name = @"Saint Kitts-Nevis",
                Iso = @"KN",
                Id = 659,
                Region = @"Americas",
                RegionId = 659,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [660] = new CountryData
            {
                Name = @"Anguilla",
                Iso = @"AI",
                Id = 660,
                Region = @"Americas",
                RegionId = 660,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [662] = new CountryData
            {
                Name = @"Saint Lucia",
                Iso = @"LC",
                Id = 662,
                Region = @"Americas",
                RegionId = 662,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [666] = new CountryData
            {
                Name = @"Saint Pierre and Miquelon",
                Iso = @"PM",
                Id = 666,
                Region = @"Americas",
                RegionId = 666,
                SubRegion = @"Northern America",
                SubRegionId = 21
            },
            [670] = new CountryData
            {
                Name = @"Saint Vincent and the Grenadines",
                Iso = @"VC",
                Id = 670,
                Region = @"Americas",
                RegionId = 670,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [674] = new CountryData
            {
                Name = @"San Marino",
                Iso = @"SM",
                Id = 674,
                Region = @"Europe",
                RegionId = 674,
                SubRegion = @"Southern Europe",
                SubRegionId = 39
            },
            [682] = new CountryData
            {
                Name = @"Saudi Arabia",
                Iso = @"SA",
                Id = 682,
                Region = @"Asia",
                RegionId = 682,
                SubRegion = @"Western Asia",
                SubRegionId = 145
            },
            [686] = new CountryData
            {
                Name = @"Senegal",
                Iso = @"SN",
                Id = 686,
                Region = @"Africa",
                RegionId = 686,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [688] = new CountryData
            {
                Name = @"Serbia",
                Iso = @"RS",
                Id = 688,
                Region = @"Europe",
                RegionId = 688,
                SubRegion = @"Southern Europe",
                SubRegionId = 39
            },
            [690] = new CountryData
            {
                Name = @"Seychelles",
                Iso = @"SC",
                Id = 690,
                Region = @"Africa",
                RegionId = 690,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [694] = new CountryData
            {
                Name = @"Sierra Leone",
                Iso = @"SL",
                Id = 694,
                Region = @"Africa",
                RegionId = 694,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [702] = new CountryData
            {
                Name = @"Singapore",
                Iso = @"SG",
                Id = 702,
                Region = @"Asia",
                RegionId = 702,
                SubRegion = @"South-eastern Asia",
                SubRegionId = 35
            },
            [703] = new CountryData
            {
                Name = @"Slovakia",
                Iso = @"SK",
                Id = 703,
                Region = @"Europe",
                RegionId = 703,
                SubRegion = @"Eastern Europe",
                SubRegionId = 151
            },
            [704] = new CountryData
            {
                Name = @"Vietnam",
                Iso = @"VN",
                Id = 704,
                Region = @"Asia",
                RegionId = 704,
                SubRegion = @"South-eastern Asia",
                SubRegionId = 35
            },
            [705] = new CountryData
            {
                Name = @"Slovenia",
                Iso = @"SI",
                Id = 705,
                Region = @"Europe",
                RegionId = 705,
                SubRegion = @"Southern Europe",
                SubRegionId = 39
            },
            [706] = new CountryData
            {
                Name = @"Somalia",
                Iso = @"SO",
                Id = 706,
                Region = @"Africa",
                RegionId = 706,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [710] = new CountryData
            {
                Name = @"South Africa",
                Iso = @"ZA",
                Id = 710,
                Region = @"Africa",
                RegionId = 710,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [716] = new CountryData
            {
                Name = @"Zimbabwe",
                Iso = @"ZW",
                Id = 716,
                Region = @"Africa",
                RegionId = 716,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [724] = new CountryData
            {
                Name = @"Spain",
                Iso = @"ES",
                Id = 724,
                Region = @"Europe",
                RegionId = 724,
                SubRegion = @"Southern Europe",
                SubRegionId = 39
            },
            [732] = new CountryData
            {
                Name = @"Western Sahara",
                Iso = @"EH",
                Id = 732,
                Region = @"Africa",
                RegionId = 732,
                SubRegion = @"Northern Africa",
                SubRegionId = 15
            },
            [740] = new CountryData
            {
                Name = @"Suriname",
                Iso = @"SR",
                Id = 740,
                Region = @"Americas",
                RegionId = 740,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [744] = new CountryData
            {
                Name = @"Svalbard",
                Iso = @"SJ",
                Id = 744,
                Region = @"Europe",
                RegionId = 744,
                SubRegion = @"Northern Europe",
                SubRegionId = 154
            },
            [748] = new CountryData
            {
                Name = @"Swaziland",
                Iso = @"SZ",
                Id = 748,
                Region = @"Africa",
                RegionId = 748,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [752] = new CountryData
            {
                Name = @"Sweden",
                Iso = @"SE",
                Id = 752,
                Region = @"Europe",
                RegionId = 752,
                SubRegion = @"Northern Europe",
                SubRegionId = 154
            },
            [756] = new CountryData
            {
                Name = @"Switzerland",
                Iso = @"CH",
                Id = 756,
                Region = @"Europe",
                RegionId = 756,
                SubRegion = @"Western Europe",
                SubRegionId = 155
            },
            [762] = new CountryData
            {
                Name = @"Tajikistan",
                Iso = @"TJ",
                Id = 762,
                Region = @"Asia",
                RegionId = 762,
                SubRegion = @"Eastern Asia",
                SubRegionId = 143
            },
            [764] = new CountryData
            {
                Name = @"Thailand",
                Iso = @"TH",
                Id = 764,
                Region = @"Asia",
                RegionId = 764,
                SubRegion = @"South-eastern Asia",
                SubRegionId = 35
            },
            [768] = new CountryData
            {
                Name = @"Togo",
                Iso = @"TG",
                Id = 768,
                Region = @"Africa",
                RegionId = 768,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [776] = new CountryData
            {
                Name = @"Tonga",
                Iso = @"TO",
                Id = 776,
                Region = @"Oceania",
                RegionId = 776,
                SubRegion = @"Polynesia",
                SubRegionId = 61
            },
            [780] = new CountryData
            {
                Name = @"Trinidad and Tobago",
                Iso = @"TT",
                Id = 780,
                Region = @"Americas",
                RegionId = 780,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [784] = new CountryData
            {
                Name = @"United Arab Emirates",
                Iso = @"AE",
                Id = 784,
                Region = @"Asia",
                RegionId = 784,
                SubRegion = @"Western Asia",
                SubRegionId = 145
            },
            [788] = new CountryData
            {
                Name = @"Tunisia",
                Iso = @"TN",
                Id = 788,
                Region = @"Africa",
                RegionId = 788,
                SubRegion = @"Northern Africa",
                SubRegionId = 15
            },
            [792] = new CountryData
            {
                Name = @"Turkey",
                Iso = @"TR",
                Id = 792,
                Region = @"Asia",
                RegionId = 792,
                SubRegion = @"Western Asia",
                SubRegionId = 145
            },
            [795] = new CountryData
            {
                Name = @"Turkmenistan",
                Iso = @"TM",
                Id = 795,
                Region = @"Asia",
                RegionId = 795,
                SubRegion = @"Central Asia",
                SubRegionId = 143
            },
            [796] = new CountryData
            {
                Name = @"Turks and Caicos Islands",
                Iso = @"TC",
                Id = 796,
                Region = @"Americas",
                RegionId = 796,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [798] = new CountryData
            {
                Name = @"Tuvalu",
                Iso = @"TV",
                Id = 798,
                Region = @"Oceania",
                RegionId = 798,
                SubRegion = @"Polynesia",
                SubRegionId = 61
            },
            [800] = new CountryData
            {
                Name = @"Uganda",
                Iso = @"UG",
                Id = 800,
                Region = @"Africa",
                RegionId = 800,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [804] = new CountryData
            {
                Name = @"Ukraine",
                Iso = @"UA",
                Id = 804,
                Region = @"Europe",
                RegionId = 804,
                SubRegion = @"Eastern Europe",
                SubRegionId = 151
            },
            [807] = new CountryData
            {
                Name = @"Macedonia",
                Iso = @"MK",
                Id = 807,
                Region = @"Europe",
                RegionId = 807,
                SubRegion = @"Southern Europe",
                SubRegionId = 39
            },
            [818] = new CountryData
            {
                Name = @"Egypt",
                Iso = @"EG",
                Id = 818,
                Region = @"Africa",
                RegionId = 818,
                SubRegion = @"Northern Africa",
                SubRegionId = 15
            },
            [826] = new CountryData
            {
                Name = @"United Kingdom",
                Iso = @"GB",
                Id = 826,
                Region = @"Europe",
                RegionId = 826,
                SubRegion = @"Northern Europe",
                SubRegionId = 154
            },
            [831] = new CountryData
            {
                Name = @"Guernsey",
                Iso = @"GG",
                Id = 831,
                Region = @"Europe",
                RegionId = 831,
                SubRegion = @"Northern Europe",
                SubRegionId = 154
            },
            [832] = new CountryData
            {
                Name = @"Jersey",
                Iso = @"JE",
                Id = 832,
                Region = @"Europe",
                RegionId = 832,
                SubRegion = @"Northern Europe",
                SubRegionId = 154
            },
            [834] = new CountryData
            {
                Name = @"Tanzania",
                Iso = @"TZ",
                Id = 834,
                Region = @"Africa",
                RegionId = 834,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [840] = new CountryData
            {
                Name = @"United States",
                Iso = @"US",
                Id = 840,
                Region = @"Americas",
                RegionId = 840,
                SubRegion = @"Northern America",
                SubRegionId = 21
            },
            [850] = new CountryData
            {
                Name = @"Virgin Islands",
                Iso = @"VI",
                Id = 850,
                Region = @"Americas",
                RegionId = 850,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [854] = new CountryData
            {
                Name = @"Burkina Faso",
                Iso = @"BF",
                Id = 854,
                Region = @"Africa",
                RegionId = 854,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
            [858] = new CountryData
            {
                Name = @"Uruguay",
                Iso = @"UY",
                Id = 858,
                Region = @"Americas",
                RegionId = 858,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [860] = new CountryData
            {
                Name = @"Uzbekistan",
                Iso = @"UZ",
                Id = 860,
                Region = @"Asia",
                RegionId = 860,
                SubRegion = @"Central Asia",
                SubRegionId = 143
            },
            [862] = new CountryData
            {
                Name = @"Venezuela",
                Iso = @"VE",
                Id = 862,
                Region = @"Americas",
                RegionId = 862,
                SubRegion = @"Latin America and the Caribbean",
                SubRegionId = 419
            },
            [876] = new CountryData
            {
                Name = @"Wallis and Futuna",
                Iso = @"WF",
                Id = 876,
                Region = @"Oceania",
                RegionId = 876,
                SubRegion = @"Polynesia",
                SubRegionId = 61
            },
            [882] = new CountryData
            {
                Name = @"Western Samoa",
                Iso = @"WS",
                Id = 882,
                Region = @"Oceania",
                RegionId = 882,
                SubRegion = @"Polynesia",
                SubRegionId = 61
            },
            [887] = new CountryData
            {
                Name = @"Yemen",
                Iso = @"YE",
                Id = 887,
                Region = @"Asia",
                RegionId = 887,
                SubRegion = @"Western Asia",
                SubRegionId = 145
            },
            [894] = new CountryData
            {
                Name = @"Zambia",
                Iso = @"ZM",
                Id = 894,
                Region = @"Africa",
                RegionId = 894,
                SubRegion = @"Sub-Saharan Africa",
                SubRegionId = 202
            },
        };
    }
}
