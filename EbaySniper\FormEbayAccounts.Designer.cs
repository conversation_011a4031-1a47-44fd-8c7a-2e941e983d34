﻿using System.ComponentModel;
using DevExpress.Utils;
using DevExpress.XtraBars;
using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;

namespace uBuyFirst
{
    partial class FormEbayAccounts
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormEbayAccounts));
            this.formAssistant1 = new DevExpress.XtraBars.FormAssistant();
            this.ribbonControl1 = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.listboxEbayAccounts = new DevExpress.XtraEditors.ListBoxControl();
            this.toolTipController1 = new DevExpress.Utils.ToolTipController(this.components);
            this.btnRemoveAccount = new DevExpress.XtraEditors.SimpleButton();
            this.btnAddAccount = new DevExpress.XtraEditors.SimpleButton();
            this.btnUp = new DevExpress.XtraEditors.SimpleButton();
            this.btnDown = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.btnEditAccount = new DevExpress.XtraEditors.SimpleButton();
            this.hyperlinkLabelControl1 = new DevExpress.XtraEditors.HyperlinkLabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.listboxEbayAccounts)).BeginInit();
            this.SuspendLayout();
            // 
            // ribbonControl1
            // 
            this.ribbonControl1.ExpandCollapseItem.Id = 0;
            this.ribbonControl1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.ribbonControl1.ExpandCollapseItem});
            this.ribbonControl1.Location = new System.Drawing.Point(0, 0);
            this.ribbonControl1.MaxItemId = 1;
            this.ribbonControl1.Name = "ribbonControl1";
            this.ribbonControl1.ShowApplicationButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowCategoryInCaption = false;
            this.ribbonControl1.ShowDisplayOptionsMenuButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowExpandCollapseButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowPageHeadersMode = DevExpress.XtraBars.Ribbon.ShowPageHeadersMode.Hide;
            this.ribbonControl1.ShowQatLocationSelector = false;
            this.ribbonControl1.ShowToolbarCustomizeItem = false;
            this.ribbonControl1.Size = new System.Drawing.Size(327, 32);
            this.ribbonControl1.Toolbar.ShowCustomizeItem = false;
            // 
            // listboxEbayAccounts
            // 
            this.listboxEbayAccounts.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.listboxEbayAccounts.Cursor = System.Windows.Forms.Cursors.Default;
            this.listboxEbayAccounts.Location = new System.Drawing.Point(12, 74);
            this.listboxEbayAccounts.Name = "listboxEbayAccounts";
            this.listboxEbayAccounts.Size = new System.Drawing.Size(267, 99);
            this.listboxEbayAccounts.TabIndex = 1;
            this.listboxEbayAccounts.ToolTip = resources.GetString("listboxEbayAccounts.ToolTip");
            this.listboxEbayAccounts.ToolTipController = this.toolTipController1;
            // 
            // btnRemoveAccount
            // 
            this.btnRemoveAccount.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnRemoveAccount.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnRemoveAccount.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Remove;
            this.btnRemoveAccount.ImageOptions.SvgImageSize = new System.Drawing.Size(16, 16);
            this.btnRemoveAccount.Location = new System.Drawing.Point(249, 38);
            this.btnRemoveAccount.Name = "btnRemoveAccount";
            this.btnRemoveAccount.Size = new System.Drawing.Size(30, 30);
            this.btnRemoveAccount.TabIndex = 4;
            this.btnRemoveAccount.ToolTip = "Remove eBay account";
            this.btnRemoveAccount.ToolTipController = this.toolTipController1;
            this.btnRemoveAccount.Click += new System.EventHandler(this.btnRemoveAccount_Click);
            // 
            // btnAddAccount
            // 
            this.btnAddAccount.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnAddAccount.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnAddAccount.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Add;
            this.btnAddAccount.ImageOptions.SvgImageSize = new System.Drawing.Size(32, 32);
            this.btnAddAccount.Location = new System.Drawing.Point(177, 38);
            this.btnAddAccount.Name = "btnAddAccount";
            this.btnAddAccount.Size = new System.Drawing.Size(30, 30);
            this.btnAddAccount.TabIndex = 5;
            this.btnAddAccount.ToolTip = "Add eBay account";
            this.btnAddAccount.ToolTipController = this.toolTipController1;
            this.btnAddAccount.Click += new System.EventHandler(this.btnAddAccount_Click);
            // 
            // btnUp
            // 
            this.btnUp.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnUp.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnUp.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Up;
            this.btnUp.ImageOptions.SvgImageSize = new System.Drawing.Size(16, 16);
            this.btnUp.Location = new System.Drawing.Point(285, 74);
            this.btnUp.Name = "btnUp";
            this.btnUp.Size = new System.Drawing.Size(30, 30);
            this.btnUp.TabIndex = 4;
            this.btnUp.ToolTip = "If you have more than one account, move the account that you want to be\r\nyour def" +
    "ault purchasing account to the TOP of the list. ";
            this.btnUp.ToolTipController = this.toolTipController1;
            this.btnUp.Click += new System.EventHandler(this.btnUp_Click);
            // 
            // btnDown
            // 
            this.btnDown.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnDown.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnDown.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Down;
            this.btnDown.ImageOptions.SvgImageSize = new System.Drawing.Size(16, 16);
            this.btnDown.Location = new System.Drawing.Point(285, 110);
            this.btnDown.Name = "btnDown";
            this.btnDown.Size = new System.Drawing.Size(30, 30);
            this.btnDown.TabIndex = 4;
            this.btnDown.ToolTip = "If you have more than one account, move the account that you want to be\r\nyour def" +
    "ault purchasing account to the TOP of the list. ";
            this.btnDown.ToolTipController = this.toolTipController1;
            this.btnDown.Click += new System.EventHandler(this.btnDown_Click);
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(13, 53);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(74, 13);
            this.labelControl1.TabIndex = 9;
            this.labelControl1.Text = "eBay accounts:";
            // 
            // btnEditAccount
            // 
            this.btnEditAccount.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnEditAccount.ImageOptions.Location = DevExpress.XtraEditors.ImageLocation.MiddleCenter;
            this.btnEditAccount.ImageOptions.SvgImage = global::uBuyFirst.Properties.Resources.Edit;
            this.btnEditAccount.ImageOptions.SvgImageSize = new System.Drawing.Size(16, 16);
            this.btnEditAccount.Location = new System.Drawing.Point(213, 38);
            this.btnEditAccount.Name = "btnEditAccount";
            this.btnEditAccount.Size = new System.Drawing.Size(30, 30);
            this.btnEditAccount.TabIndex = 5;
            this.btnEditAccount.ToolTip = "Edit eBay account";
            this.btnEditAccount.Click += new System.EventHandler(this.btnEditAccount_Click);
            // 
            // hyperlinkLabelControl1
            // 
            this.hyperlinkLabelControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.hyperlinkLabelControl1.Cursor = System.Windows.Forms.Cursors.Hand;
            this.hyperlinkLabelControl1.Location = new System.Drawing.Point(13, 179);
            this.hyperlinkLabelControl1.Name = "hyperlinkLabelControl1";
            this.hyperlinkLabelControl1.Size = new System.Drawing.Size(21, 13);
            this.hyperlinkLabelControl1.TabIndex = 11;
            this.hyperlinkLabelControl1.Tag = "https://ubuyfirst.com/using-multiple-ebay-accounts-browsers/";
            this.hyperlinkLabelControl1.Text = "Help";
            this.hyperlinkLabelControl1.ToolTip = "In order to get the most details for items and buy directly through the app, you’" +
    "ll need to authenticate your eBay account.";
            this.hyperlinkLabelControl1.Click += new System.EventHandler(this.hyperlinkLabelControl1_Click);
            // 
            // FormEbayAccounts
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(327, 204);
            this.Controls.Add(this.hyperlinkLabelControl1);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.btnDown);
            this.Controls.Add(this.btnUp);
            this.Controls.Add(this.btnRemoveAccount);
            this.Controls.Add(this.btnEditAccount);
            this.Controls.Add(this.btnAddAccount);
            this.Controls.Add(this.listboxEbayAccounts);
            this.Controls.Add(this.ribbonControl1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "FormEbayAccounts";
            this.Ribbon = this.ribbonControl1;
            this.Text = "eBay Accounts";
            this.Load += new System.EventHandler(this.FormEbayAccounts_Load);
            this.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.FormEbayAccounts_KeyPress);
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.listboxEbayAccounts)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private FormAssistant formAssistant1;
        private RibbonControl ribbonControl1;
        private ListBoxControl listboxEbayAccounts;
        private SimpleButton btnRemoveAccount;
        private SimpleButton btnAddAccount;
        private SimpleButton btnUp;
        private SimpleButton btnDown;
        private ToolTipController toolTipController1;
        private LabelControl labelControl1;
        private SimpleButton btnEditAccount;
        private HyperlinkLabelControl hyperlinkLabelControl1;
    }
}