﻿using System.Windows.Forms;

namespace uBuyFirst.Tools
{
    public static class ControlHelpers
    {
        public static void InvokeIfRequired(this Control control, MethodInvoker action)
        {
            if (control != null && control.IsHandleCreated && !control.IsDisposed)
                if (control.InvokeRequired)
                {
                    if (control.IsHandleCreated && !control.IsDisposed)
                        control.Invoke(action);
                }
                else
                {
                    action();
                }
        }
    }
}