﻿using System;
using System.Text.RegularExpressions;
using Lucene.Net.Analysis;
using Lucene.Net.Analysis.Core;
using Lucene.Net.Index.Memory;
using Lucene.Net.QueryParsers.Classic;
using Lucene.Net.Search;
using Lucene.Net.Util;
using uBuyFirst.Other;

namespace uBuyFirst.Tools
{
    public static class KeywordHelpers
    {
        private static Analyzer s_analyzer = new StandardAnalyzerFix(LuceneVersion.LUCENE_48);

        public static string ValidateLuceneKeyword(string keyword, bool allowLeadingWildcard)
        {
            try
            {
                ParseKeyword2LuceneQuery(keyword, allowLeadingWildcard);
                return "";
            }
            catch (Exception ex)
            {
                return ex.Message;
            }
        }

        public static Query ParseKeyword2LuceneQuery(string keyword, bool allowLeadingWildCard)
        {
            var r1 = $"({keyword.ToLower()})";
            while (r1.Contains("(") && r1.Contains(")"))
            {
                var innerBrackets = Helpers.RegexValue(r1, @"\(([^()]*)\)");
                var phrases = innerBrackets.Split(',');
                for (var i = 0; i < phrases.Length; i++)
                {
                    phrases[i] = $"{{{phrases[i]}}}";
                }

                innerBrackets = string.Join(" OR ", phrases);
                var rgx = new Regex(@"\(([^()]*)\)");
                r1 = rgx.Replace(r1, "[" + innerBrackets + "]", 1);
            }

            r1 = r1.Replace("{", "(").Replace("}", ")");
            r1 = r1.Replace("[", "(").Replace("]", ")");

            r1 = StripOuterParensSafely(r1);

            var parser = new QueryParser(LuceneVersion.LUCENE_48, "x", s_analyzer) { DefaultOperator = Operator.AND, AllowLeadingWildcard = allowLeadingWildCard };
            if (r1.Contains("/"))
                r1 = r1.Replace("/", @"\/");
            return parser.Parse(r1);
        }

        public static void SetAnalyzerType(bool useWhiteSpaceAnalyzer)
        {
            if (useWhiteSpaceAnalyzer)
            {
                s_analyzer = new WhitespaceAnalyzer(LuceneVersion.LUCENE_48);
            }
            else
            {
                s_analyzer = new StandardAnalyzerFix(LuceneVersion.LUCENE_48);
            }
        }

        public static bool IsMatch(string text, Query luceneQuery)
        {
            if (luceneQuery == null)
                return false;

            var index = new MemoryIndex();
            index.AddField("x", text.ToLower(), s_analyzer);
            var score = index.Search(luceneQuery);
            return score > 0;
        }

        public static string StripOuterParensSafely(string s)
        {
            while (s.Length >= 2 && s[0] == '(' && s[s.Length - 1] == ')')         // <-- use s[s.Length - 1] instead of s[^1]
            {
                var candidate = s.Substring(1, s.Length - 2);
                if (IsBalanced(candidate))
                    s = candidate;
                else
                    break;
            }
            return s;
        }

        static bool IsBalanced(string s)
        {
            var depth = 0;
            foreach (var c in s)
            {
                if (c == '(') depth++;
                else if (c == ')')
                {
                    if (--depth < 0) return false;
                }
            }
            return depth == 0;
        }
    }
}
