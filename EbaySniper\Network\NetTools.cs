﻿using System;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;
using System.Web.Services.Protocols;
using uBuyFirst.Prefs;
using uBuyFirst.Tools;

namespace uBuyFirst.Network
{
    public static class NetTools
    {
        public static string FetchUrl(string url, string host = null)
        {
            for (int i = 0; i < 2; i++)
            {
                try
                {
                    ServicePointManager.Expect100Continue = false;
                    var httpWebRequest = (HttpWebRequest)WebRequest.Create(url);
                    if (host != null)
                        httpWebRequest.Host = host;
                    httpWebRequest.Timeout = 30 * 1000;
                    httpWebRequest.ReadWriteTimeout = 30 * 1000;
                    //httpWebRequest.ContentType = "application/x-www-form-urlencoded";
                    httpWebRequest.Method = "GET";
                    httpWebRequest.UserAgent = ProgramState.ChromeUA;
                    httpWebRequest.Accept = "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8";
                    //httpWebRequest.Referer = url;
                    httpWebRequest.Headers.Add("Accept-Language", "en-us,en;q=0.5");
                    httpWebRequest.Headers.Add("Accept-Encoding", "gzip,deflate");
                    httpWebRequest.Headers.Add("Accept-Charset", "utf-8;q=0.7,*;q=0.7");
                    httpWebRequest.AllowAutoRedirect = true;
                    httpWebRequest.AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate;

                    var response = (HttpWebResponse)httpWebRequest.GetResponse();

                    using (Stream receiveStream = response.GetResponseStream())
                    {
                        if (receiveStream != null)
                        {
                            string result;
                            using (var readStream = new StreamReader(receiveStream, Encoding.UTF8, false, 40960))
                            {
                                result = readStream.ReadToEnd();
                                response.Close();
                            }

                            return result;
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (ex.Message == "The remote server returned an error: (403) Forbidden.")
                    {
                    }

                    // ignored
                }
            }

            return "";
        }

        public static async Task<string> FetchUrlUsingCookiesAsync(string url, CookieContainer cookies)
        {
            for (var i = 0; i < 4; i++)
            {
                try
                {
                    ServicePointManager.Expect100Continue = false;
                    ServicePointManager.SecurityProtocol = (SecurityProtocolType)3072;

                    var baseAddress = new Uri(url);
                    var handler = new Http2CustomHandler(Helpers.BuyItNowConfirmation)
                    {
                        CookieContainer = cookies,
                        CookieUsePolicy = CookieUsePolicy.UseSpecifiedCookieContainer,
                        //var result = await httpClient.GetStringAsync(baseAddress);
                        AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate,
                        WindowsProxyUsePolicy = WindowsProxyUsePolicy.UseWinInetProxy
                    };
                    if (Debugger.IsAttached || Program.AffiliateOff)
                    {
                        //handler.Proxy = new WebProxy(new Uri("http://127.0.0.1:8889"));
                        //handler.WindowsProxyUsePolicy = WindowsProxyUsePolicy.UseCustomProxy;
                    }

                    var chromeVersionShort = Helpers.RegexValue(ProgramState.ChromeUA, "Chrome/([0-9]+)");
                    var chromeVersionLong = Helpers.RegexValue(ProgramState.ChromeUA, "Chrome/([0-9\\.]+)");
                    var sec_ch_ua = $"\".Not/A)Brand\";v=\"24\", \"Google Chrome\";v=\"{chromeVersionShort}\", \"Chromium\";v=\"{chromeVersionShort}\"";

                    using (handler)
                        //using (var httpClient = new HttpClient(handler)
                    using (var httpClient = new HttpClient(handler))
                    {
                        httpClient.BaseAddress = baseAddress;
                        httpClient.Timeout = new TimeSpan(0, 0, 10);
                        httpClient.DefaultRequestHeaders.Add("Accept-Language", "en-us,en;q=0.5");
                        httpClient.DefaultRequestHeaders.Add("Accept-Encoding", "gzip,deflate");
                        httpClient.DefaultRequestHeaders.Add("Accept-Charset", "utf-8;q=0.7,*;q=0.7");
                        httpClient.DefaultRequestHeaders.Add("User-Agent", ProgramState.ChromeUA.Replace(chromeVersionLong, chromeVersionShort + ".0.0.0"));

                        httpClient.DefaultRequestHeaders.Add("sec-ch-ua", sec_ch_ua);
                        httpClient.DefaultRequestHeaders.Add("sec-ch-ua-mobile", "?0");
                        httpClient.DefaultRequestHeaders.Add("sec-ch-ua-platform", "\"Windows\"");
                        httpClient.DefaultRequestHeaders.Add("sec-ch-ua-platform-version", "\"10.0.0\"");
                        httpClient.DefaultRequestHeaders.Add("sec-ch-ua-model", "\"\"");
                        httpClient.DefaultRequestHeaders.Add("sec-ch-ua-full-version", $"\"{chromeVersionLong}\"");

                        httpClient.DefaultRequestHeaders.Add("upgrade-insecure-requests", "1");
                        httpClient.DefaultRequestHeaders.Add("sec-fetch-site", "none");
                        httpClient.DefaultRequestHeaders.Add("sec-fetch-mode", "navigate");
                        httpClient.DefaultRequestHeaders.Add("sec-fetch-user", "?1");
                        httpClient.DefaultRequestHeaders.Add("sec-fetch-dest", "document");

                        httpClient.DefaultRequestHeaders.Add("Accept",
                            "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.98");

                        var request = new HttpRequestMessage(HttpMethod.Get, new Uri(url)) { Version = new Version(2, 0), };
                        var httpResponseMessage = await httpClient.SendAsync(request);

                        if (httpResponseMessage?.RequestMessage?.RequestUri?.AbsoluteUri?.Contains("https://www.ebay.com/splashui/captcha") == true)
                        {
                            Process.Start(httpResponseMessage.RequestMessage.RequestUri.AbsoluteUri);

                            return "";
                        }

                        if (httpResponseMessage?.Content != null)
                        {
                            var result = await httpResponseMessage.Content.ReadAsStringAsync();

                            //var result = await httpClient.GetStringAsync(baseAddress);

                            return result;
                        }
                    }

                    // var httpWebRequest = (HttpWebRequest) WebRequest.Create(url);
                    //httpWebRequest.Timeout = 30 * 1000;
                    //httpWebRequest.ReadWriteTimeout = 30 * 1000;
                    //httpWebRequest.Method = "GET";
                    //httpWebRequest.UserAgent = ProgramState.ChromeUA;
                    //httpWebRequest.Accept = "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8";

                    //httpWebRequest.Headers.Add("Accept-Language", "en-us,en;q=0.5");
                    //httpWebRequest.Headers.Add("Accept-Encoding", "gzip,deflate");
                    //httpWebRequest.Headers.Add("Accept-Charset", "utf-8;q=0.7,*;q=0.7");
                    ////httpWebRequest.AllowAutoRedirect = true;
                    /////httpWebRequest.AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate;
                    //httpWebRequest.CookieContainer = cookies;
                    /*var response = (HttpWebResponse) httpWebRequest.GetResponse();
                    var receiveStream = response.GetResponseStream();
                    if (receiveStream != null)
                    {
                        var readStream = new StreamReader(receiveStream);
                        var result = readStream.ReadToEnd();
                        response.Close();
                        readStream.Close();

                        return result;
                    }*/
                }
                catch (Exception ex)
                {
                    //MessageBox.Show(url);
                    ExM.ubuyExceptionHandler("Checkout: ", ex);
                    //XtraMessageBox.Show( ex.Message);
                }
            }

            return "";
        }

        public static void InitHttPprotocol()
        {
            var type = typeof(HttpWebClientProtocol);
            var ua = type.GetField("UserAgentDefault", BindingFlags.Static | BindingFlags.NonPublic);
            ua?.SetValue(null, ProgramState.ChromeUA);
            ServicePointManager.Expect100Continue = false;
            ServicePointManager.DefaultConnectionLimit = 100;
        }

        public static string ShoppingApiCall(string itemIDs, string callName, string detailLevel)
        {
            //"&IncludeSelector=Details"
            try
            {
                var url = $"{ConnectionConfig.ShoppingApiHost}"
                          + $"/shopping?{detailLevel}callname={callName}&appid={ConnectionConfig.ShoppingApiKey}&responseencoding=XML&siteid=0&version=1081&ItemID={itemIDs}";
                var httpWebRequest = (HttpWebRequest)WebRequest.Create(url);
                httpWebRequest.Timeout = 5 * 1000;
                httpWebRequest.ReadWriteTimeout = 5 * 1000;
                httpWebRequest.ContentType = "application/x-www-form-urlencoded";
                httpWebRequest.Method = "GET";
                httpWebRequest.UserAgent = ProgramState.ChromeUA;
                httpWebRequest.Accept = "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8";
                httpWebRequest.Referer = url;
                httpWebRequest.Headers.Add("Accept-Language", "en-us,en;q=0.5");
                httpWebRequest.Headers.Add("Accept-Encoding", "gzip,deflate");
                httpWebRequest.Headers.Add("Accept-Charset", "utf-8;q=0.7,*;q=0.7");
                httpWebRequest.AllowAutoRedirect = false;
                httpWebRequest.AutomaticDecompression = DecompressionMethods.GZip | DecompressionMethods.Deflate;
                var response = (HttpWebResponse)httpWebRequest.GetResponse();
                var receiveStream = response.GetResponseStream();
                if (receiveStream != null)
                {
                    var readStream = new StreamReader(receiveStream, Encoding.UTF8);
                    var result = readStream.ReadToEnd();
                    response.Close();
                    readStream.Close();

                    if (result.Contains("<Ack>Failure</Ack>"))
                        return "";

                    return result;
                }
            }
            catch (Exception)
            {
                // ignored
            }

            return "";
        }
    }
}
