using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using uBuyFirst.Filters;

namespace uBuyFirst.Tests.Filters
{
    [TestClass]
    public class RestockFilterUIConfiguratorTests
    {
        private RestockFilterUIConfigurator _configurator;
        private Mock<XFilterClass> _mockFilter;
        private Mock<IFormDataAccessor> _mockFormAccessor;

        [TestInitialize]
        public void Setup()
        {
            _configurator = new RestockFilterUIConfigurator();
            _mockFilter = new Mock<XFilterClass>();
            _mockFormAccessor = new Mock<IFormDataAccessor>();
        }

        [TestMethod]
        public void ActionTypeIdentifier_ReturnsCorrectValue()
        {
            // Act
            var identifier = _configurator.ActionTypeIdentifier;

            // Assert
            Assert.AreEqual("RESTOCK", identifier);
            Assert.AreEqual(RestockFilterAction.IDENTIFIER, identifier);
        }

        [TestMethod]
        public void GetUIConfiguration_ReturnsCorrectConfiguration()
        {
            // Act
            var config = _configurator.GetUIConfiguration();

            // Assert
            Assert.IsNotNull(config);
            Assert.IsFalse(config.ShowColumnSelection);
            Assert.IsFalse(config.ShowFormatControls);
            Assert.IsNotNull(config.AdditionalControlsToShow);
            Assert.IsNotNull(config.AdditionalControlsToHide);
        }

        [TestMethod]
        public void LoadDataFromFilter_WithNullFilter_DoesNotThrow()
        {
            // Act & Assert
            _configurator.LoadDataFromFilter(null, _mockFormAccessor.Object);
            // Should not throw any exceptions
        }

        [TestMethod]
        public void LoadDataFromFilter_WithValidFilter_DoesNotThrow()
        {
            // Act & Assert
            _configurator.LoadDataFromFilter(_mockFilter.Object, _mockFormAccessor.Object);
            // Should not throw any exceptions
        }

        [TestMethod]
        public void SaveDataToFilter_WithNullFilter_DoesNotThrow()
        {
            // Act & Assert
            _configurator.SaveDataToFilter(null, _mockFormAccessor.Object);
            // Should not throw any exceptions
        }

        [TestMethod]
        public void SaveDataToFilter_WithValidFilter_DoesNotThrow()
        {
            // Act & Assert
            _configurator.SaveDataToFilter(_mockFilter.Object, _mockFormAccessor.Object);
            // Should not throw any exceptions
        }

        [TestMethod]
        public void SaveDataToFilter_WithRestockFilterAction_CallsSaveToFilter()
        {
            // Arrange
            var restockAction = new RestockFilterAction();
            _mockFilter.Setup(f => f.ActionHandler).Returns(restockAction);

            // Act
            _configurator.SaveDataToFilter(_mockFilter.Object, _mockFormAccessor.Object);

            // Assert
            // Verify that the action's SaveToFilter method would be called
            // Since we can't easily mock the RestockFilterAction, we just verify no exceptions
            Assert.IsNotNull(restockAction);
        }

        [TestMethod]
        public void LoadDataFromFilter_WithRestockFilterAction_DoesNotThrow()
        {
            // Arrange
            var restockAction = new RestockFilterAction();
            _mockFilter.Setup(f => f.ActionHandler).Returns(restockAction);

            // Act & Assert
            _configurator.LoadDataFromFilter(_mockFilter.Object, _mockFormAccessor.Object);
            // Should not throw any exceptions
        }
    }
}
