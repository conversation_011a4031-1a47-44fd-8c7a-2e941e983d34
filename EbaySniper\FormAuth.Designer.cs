﻿using System.ComponentModel;
using System.Windows.Forms;
using DevExpress.XtraBars;
using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;

namespace uBuyFirst
{
    partial class FormAuth
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormAuth));
            DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule conditionValidationRule7 = new DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule();
            DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule conditionValidationRule1 = new DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule();
            DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule conditionValidationRule2 = new DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule();
            DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule conditionValidationRule3 = new DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule();
            DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule conditionValidationRule4 = new DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule();
            DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule conditionValidationRule5 = new DevExpress.XtraEditors.DXErrorProvider.ConditionValidationRule();
            this.openFileDialog1 = new System.Windows.Forms.OpenFileDialog();
            this.toolTip1 = new System.Windows.Forms.ToolTip(this.components);
            this.formAssistant1 = new DevExpress.XtraBars.FormAssistant();
            this.ribbonControl1 = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.btnSaveAuthToken = new DevExpress.XtraEditors.SimpleButton();
            this.btnConfirmAuth = new DevExpress.XtraEditors.SimpleButton();
            this.btnxEbayDoAuth = new DevExpress.XtraEditors.SimpleButton();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl2 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl3 = new DevExpress.XtraEditors.LabelControl();
            this.lblValSessionStatus = new DevExpress.XtraEditors.LabelControl();
            this.lblStatusCurrent = new DevExpress.XtraEditors.LabelControl();
            this.boxCustomColumnSite = new DevExpress.XtraEditors.ComboBoxEdit();
            this.hyperlinkLabelControl1 = new DevExpress.XtraEditors.HyperlinkLabelControl();
            this.tabShippingAndBrowser = new DevExpress.XtraTab.XtraTabControl();
            this.tabPageShippingOptions = new DevExpress.XtraTab.XtraTabPage();
            this.layoutControl1 = new DevExpress.XtraLayout.LayoutControl();
            this.txtFullName = new DevExpress.XtraEditors.TextEdit();
            this.txtCountry = new DevExpress.XtraEditors.TextEdit();
            this.textEdit2 = new DevExpress.XtraEditors.TextEdit();
            this.textEdit1 = new DevExpress.XtraEditors.TextEdit();
            this.textEdit8 = new DevExpress.XtraEditors.TextEdit();
            this.textEdit3 = new DevExpress.XtraEditors.TextEdit();
            this.textEdit7 = new DevExpress.XtraEditors.TextEdit();
            this.txtPhone = new DevExpress.XtraEditors.TextEdit();
            this.layoutControlGroup1 = new DevExpress.XtraLayout.LayoutControlGroup();
            this.lciFullName = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciCountry = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciAddressLine2 = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciAddressLine1 = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciCity = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciPhone = new DevExpress.XtraLayout.LayoutControlItem();
            this.emptySpaceItem4 = new DevExpress.XtraLayout.EmptySpaceItem();
            this.lciPostalCode = new DevExpress.XtraLayout.LayoutControlItem();
            this.lciState = new DevExpress.XtraLayout.LayoutControlItem();
            this.tabPageBrowser = new DevExpress.XtraTab.XtraTabPage();
            this.groupControl1 = new DevExpress.XtraEditors.GroupControl();
            this.labelControl7 = new DevExpress.XtraEditors.LabelControl();
            this.labelControl5 = new DevExpress.XtraEditors.LabelControl();
            this.lblBrowserHelp = new DevExpress.XtraEditors.HyperlinkLabelControl();
            this.btnChooseDefBrowser = new DevExpress.XtraEditors.SimpleButton();
            this.lblValBrowser = new DevExpress.XtraEditors.LabelControl();
            this.dxValidationProvider1 = new DevExpress.XtraEditors.DXErrorProvider.DXValidationProvider(this.components);
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.boxCustomColumnSite.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabShippingAndBrowser)).BeginInit();
            this.tabShippingAndBrowser.SuspendLayout();
            this.tabPageShippingOptions.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).BeginInit();
            this.layoutControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtFullName.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCountry.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit8.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit3.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit7.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPhone.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciFullName)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciCountry)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciAddressLine2)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciAddressLine1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciCity)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciPhone)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem4)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciPostalCode)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciState)).BeginInit();
            this.tabPageBrowser.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).BeginInit();
            this.groupControl1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dxValidationProvider1)).BeginInit();
            this.SuspendLayout();
            // 
            // openFileDialog1
            // 
            this.openFileDialog1.DefaultExt = "exe";
            this.openFileDialog1.FileName = "openFileDialog1";
            this.openFileDialog1.Filter = "Browsers|*.exe|All files|*.*";
            // 
            // ribbonControl1
            // 
            this.ribbonControl1.ExpandCollapseItem.Id = 0;
            this.ribbonControl1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.ribbonControl1.ExpandCollapseItem});
            this.ribbonControl1.Location = new System.Drawing.Point(0, 0);
            this.ribbonControl1.MaxItemId = 1;
            this.ribbonControl1.Name = "ribbonControl1";
            this.ribbonControl1.ShowApplicationButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowCategoryInCaption = false;
            this.ribbonControl1.ShowDisplayOptionsMenuButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowExpandCollapseButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowPageHeadersMode = DevExpress.XtraBars.Ribbon.ShowPageHeadersMode.Hide;
            this.ribbonControl1.ShowQatLocationSelector = false;
            this.ribbonControl1.ShowToolbarCustomizeItem = false;
            this.ribbonControl1.Size = new System.Drawing.Size(425, 32);
            this.ribbonControl1.Toolbar.ShowCustomizeItem = false;
            // 
            // btnSaveAuthToken
            // 
            this.btnSaveAuthToken.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Right)));
            this.btnSaveAuthToken.DialogResult = System.Windows.Forms.DialogResult.OK;
            this.btnSaveAuthToken.Enabled = false;
            this.btnSaveAuthToken.Location = new System.Drawing.Point(300, 548);
            this.btnSaveAuthToken.Name = "btnSaveAuthToken";
            this.btnSaveAuthToken.Size = new System.Drawing.Size(113, 23);
            this.btnSaveAuthToken.TabIndex = 10;
            this.btnSaveAuthToken.Text = "Save";
            this.btnSaveAuthToken.Click += new System.EventHandler(this.BtnClose);
            // 
            // btnConfirmAuth
            // 
            this.btnConfirmAuth.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnConfirmAuth.Enabled = false;
            this.btnConfirmAuth.Location = new System.Drawing.Point(300, 220);
            this.btnConfirmAuth.Name = "btnConfirmAuth";
            this.btnConfirmAuth.Size = new System.Drawing.Size(113, 23);
            this.btnConfirmAuth.TabIndex = 10;
            this.btnConfirmAuth.Text = "Confirm";
            this.btnConfirmAuth.Click += new System.EventHandler(this.btnEbayAuthConfirm_Click);
            // 
            // btnxEbayDoAuth
            // 
            this.btnxEbayDoAuth.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnxEbayDoAuth.Enabled = false;
            this.btnxEbayDoAuth.Location = new System.Drawing.Point(300, 175);
            this.btnxEbayDoAuth.Name = "btnxEbayDoAuth";
            this.btnxEbayDoAuth.Size = new System.Drawing.Size(113, 23);
            this.btnxEbayDoAuth.TabIndex = 10;
            this.btnxEbayDoAuth.Text = "Authenticate";
            this.btnxEbayDoAuth.Click += new System.EventHandler(this.btnEbayDoAuth_Click);
            // 
            // labelControl1
            // 
            this.labelControl1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.labelControl1.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical;
            this.labelControl1.Location = new System.Drawing.Point(16, 33);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(397, 104);
            this.labelControl1.TabIndex = 11;
            this.labelControl1.Text = resources.GetString("labelControl1.Text");
            // 
            // labelControl2
            // 
            this.labelControl2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.labelControl2.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical;
            this.labelControl2.Location = new System.Drawing.Point(16, 152);
            this.labelControl2.Name = "labelControl2";
            this.labelControl2.Size = new System.Drawing.Size(260, 91);
            this.labelControl2.TabIndex = 11;
            this.labelControl2.Text = resources.GetString("labelControl2.Text");
            // 
            // labelControl3
            // 
            this.labelControl3.Location = new System.Drawing.Point(16, 249);
            this.labelControl3.Name = "labelControl3";
            this.labelControl3.Size = new System.Drawing.Size(35, 13);
            this.labelControl3.TabIndex = 12;
            this.labelControl3.Text = "Status:";
            // 
            // lblValSessionStatus
            // 
            this.lblValSessionStatus.Location = new System.Drawing.Point(57, 249);
            this.lblValSessionStatus.Name = "lblValSessionStatus";
            this.lblValSessionStatus.Size = new System.Drawing.Size(4, 13);
            this.lblValSessionStatus.TabIndex = 12;
            this.lblValSessionStatus.Text = "|";
            // 
            // lblStatusCurrent
            // 
            this.lblStatusCurrent.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.lblStatusCurrent.AutoSizeMode = DevExpress.XtraEditors.LabelAutoSizeMode.Vertical;
            this.lblStatusCurrent.Location = new System.Drawing.Point(57, 268);
            this.lblStatusCurrent.Name = "lblStatusCurrent";
            this.lblStatusCurrent.Size = new System.Drawing.Size(356, 65);
            this.lblStatusCurrent.TabIndex = 11;
            this.lblStatusCurrent.Text = "\r\n\r\n\r\n\r\n\r\n";
            // 
            // boxCustomColumnSite
            // 
            this.boxCustomColumnSite.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.boxCustomColumnSite.EditValue = "Select";
            this.boxCustomColumnSite.Location = new System.Drawing.Point(300, 149);
            this.boxCustomColumnSite.MenuManager = this.ribbonControl1;
            this.boxCustomColumnSite.Name = "boxCustomColumnSite";
            this.boxCustomColumnSite.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.boxCustomColumnSite.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.boxCustomColumnSite.Size = new System.Drawing.Size(113, 20);
            this.boxCustomColumnSite.TabIndex = 15;
            // 
            // hyperlinkLabelControl1
            // 
            this.hyperlinkLabelControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.hyperlinkLabelControl1.Cursor = System.Windows.Forms.Cursors.Hand;
            this.hyperlinkLabelControl1.Location = new System.Drawing.Point(12, 558);
            this.hyperlinkLabelControl1.Name = "hyperlinkLabelControl1";
            this.hyperlinkLabelControl1.Size = new System.Drawing.Size(21, 13);
            this.hyperlinkLabelControl1.TabIndex = 17;
            this.hyperlinkLabelControl1.Tag = "https://ubuyfirst.com/05-authenticating-ebay-account/";
            this.hyperlinkLabelControl1.Text = "Help";
            this.hyperlinkLabelControl1.ToolTip = resources.GetString("hyperlinkLabelControl1.ToolTip");
            this.hyperlinkLabelControl1.Click += new System.EventHandler(this.hyperlinkLabelControl1_Click);
            // 
            // tabShippingAndBrowser
            // 
            this.tabShippingAndBrowser.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.tabShippingAndBrowser.Location = new System.Drawing.Point(12, 329);
            this.tabShippingAndBrowser.Name = "tabShippingAndBrowser";
            this.tabShippingAndBrowser.SelectedTabPage = this.tabPageShippingOptions;
            this.tabShippingAndBrowser.Size = new System.Drawing.Size(401, 203);
            this.tabShippingAndBrowser.TabIndex = 19;
            this.tabShippingAndBrowser.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
            this.tabPageShippingOptions,
            this.tabPageBrowser});
            // 
            // tabPageShippingOptions
            // 
            this.tabPageShippingOptions.Controls.Add(this.layoutControl1);
            this.tabPageShippingOptions.Name = "tabPageShippingOptions";
            this.tabPageShippingOptions.Size = new System.Drawing.Size(403, 178);
            this.tabPageShippingOptions.Text = "Shipping address";
            // 
            // layoutControl1
            // 
            this.layoutControl1.Controls.Add(this.txtFullName);
            this.layoutControl1.Controls.Add(this.txtCountry);
            this.layoutControl1.Controls.Add(this.textEdit2);
            this.layoutControl1.Controls.Add(this.textEdit1);
            this.layoutControl1.Controls.Add(this.textEdit8);
            this.layoutControl1.Controls.Add(this.textEdit3);
            this.layoutControl1.Controls.Add(this.textEdit7);
            this.layoutControl1.Controls.Add(this.txtPhone);
            this.layoutControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.layoutControl1.Location = new System.Drawing.Point(0, 0);
            this.layoutControl1.Name = "layoutControl1";
            this.layoutControl1.OptionsCustomizationForm.DesignTimeCustomizationFormPositionAndSize = new System.Drawing.Rectangle(548, 162, 450, 400);
            this.layoutControl1.Root = this.layoutControlGroup1;
            this.layoutControl1.Size = new System.Drawing.Size(403, 178);
            this.layoutControl1.TabIndex = 0;
            this.layoutControl1.Text = "layoutControl1";
            // 
            // txtFullName
            // 
            this.txtFullName.Location = new System.Drawing.Point(85, 12);
            this.txtFullName.Name = "txtFullName";
            this.txtFullName.Properties.EditValueChanged += new System.EventHandler(this.ValidateOnTextChange);
            this.txtFullName.Size = new System.Drawing.Size(306, 20);
            this.txtFullName.StyleController = this.layoutControl1;
            this.txtFullName.TabIndex = 28;
            conditionValidationRule7.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank;
            conditionValidationRule7.ErrorText = "This value is not valid";
            conditionValidationRule7.Value1 = "<Null>";
            this.dxValidationProvider1.SetValidationRule(this.txtFullName, conditionValidationRule7);
            // 
            // txtCountry
            // 
            this.txtCountry.EditValue = "US";
            this.txtCountry.Enabled = false;
            this.txtCountry.Location = new System.Drawing.Point(340, 132);
            this.txtCountry.Name = "txtCountry";
            this.txtCountry.Size = new System.Drawing.Size(51, 20);
            this.txtCountry.StyleController = this.layoutControl1;
            this.txtCountry.TabIndex = 23;
            this.txtCountry.EditValueChanged += new System.EventHandler(this.ValidateOnTextChange);
            // 
            // textEdit2
            // 
            this.textEdit2.Location = new System.Drawing.Point(85, 84);
            this.textEdit2.Name = "textEdit2";
            this.textEdit2.Size = new System.Drawing.Size(306, 20);
            this.textEdit2.StyleController = this.layoutControl1;
            this.textEdit2.TabIndex = 21;
            this.textEdit2.EditValueChanged += new System.EventHandler(this.ValidateOnTextChange);
            // 
            // textEdit1
            // 
            this.textEdit1.Location = new System.Drawing.Point(85, 60);
            this.textEdit1.Name = "textEdit1";
            this.textEdit1.Size = new System.Drawing.Size(306, 20);
            this.textEdit1.StyleController = this.layoutControl1;
            this.textEdit1.TabIndex = 20;
            conditionValidationRule1.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank;
            conditionValidationRule1.ErrorText = "This value is not valid";
            conditionValidationRule1.Value1 = "<Null>";
            this.dxValidationProvider1.SetValidationRule(this.textEdit1, conditionValidationRule1);
            this.textEdit1.EditValueChanged += new System.EventHandler(this.ValidateOnTextChange);
            // 
            // textEdit8
            // 
            this.textEdit8.Location = new System.Drawing.Point(212, 132);
            this.textEdit8.Name = "textEdit8";
            this.textEdit8.Size = new System.Drawing.Size(51, 20);
            this.textEdit8.StyleController = this.layoutControl1;
            this.textEdit8.TabIndex = 27;
            conditionValidationRule2.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank;
            conditionValidationRule2.ErrorText = "This value is not valid";
            this.dxValidationProvider1.SetValidationRule(this.textEdit8, conditionValidationRule2);
            this.textEdit8.EditValueChanged += new System.EventHandler(this.ValidateOnTextChange);
            // 
            // textEdit3
            // 
            this.textEdit3.Location = new System.Drawing.Point(85, 108);
            this.textEdit3.Name = "textEdit3";
            this.textEdit3.Size = new System.Drawing.Size(306, 20);
            this.textEdit3.StyleController = this.layoutControl1;
            this.textEdit3.TabIndex = 22;
            conditionValidationRule3.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank;
            conditionValidationRule3.ErrorText = "This value is not valid";
            this.dxValidationProvider1.SetValidationRule(this.textEdit3, conditionValidationRule3);
            this.textEdit3.EditValueChanged += new System.EventHandler(this.ValidateOnTextChange);
            // 
            // textEdit7
            // 
            this.textEdit7.Location = new System.Drawing.Point(85, 132);
            this.textEdit7.Name = "textEdit7";
            this.textEdit7.Properties.Mask.EditMask = "\\d\\d\\d\\d\\d(-\\d\\d\\d\\d)?";
            this.textEdit7.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.RegEx;
            this.textEdit7.Size = new System.Drawing.Size(50, 20);
            this.textEdit7.StyleController = this.layoutControl1;
            this.textEdit7.TabIndex = 26;
            conditionValidationRule4.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank;
            conditionValidationRule4.ErrorText = "This value is not valid";
            this.dxValidationProvider1.SetValidationRule(this.textEdit7, conditionValidationRule4);
            this.textEdit7.EditValueChanged += new System.EventHandler(this.ValidateOnTextChange);
            // 
            // txtPhone
            // 
            this.txtPhone.Location = new System.Drawing.Point(85, 36);
            this.txtPhone.Name = "txtPhone";
            this.txtPhone.Properties.Mask.EditMask = "(\\d?\\d?\\d?) \\d\\d\\d-\\d\\d\\d\\d";
            this.txtPhone.Properties.Mask.MaskType = DevExpress.XtraEditors.Mask.MaskType.Regular;
            this.txtPhone.Size = new System.Drawing.Size(306, 20);
            this.txtPhone.StyleController = this.layoutControl1;
            this.txtPhone.TabIndex = 25;
            conditionValidationRule5.ConditionOperator = DevExpress.XtraEditors.DXErrorProvider.ConditionOperator.IsNotBlank;
            conditionValidationRule5.ErrorText = "This value is not valid";
            conditionValidationRule5.Value1 = "<Null>";
            this.dxValidationProvider1.SetValidationRule(this.txtPhone, conditionValidationRule5);
            this.txtPhone.EditValueChanged += new System.EventHandler(this.ValidateOnTextChange);
            // 
            // layoutControlGroup1
            // 
            this.layoutControlGroup1.EnableIndentsWithoutBorders = DevExpress.Utils.DefaultBoolean.True;
            this.layoutControlGroup1.GroupBordersVisible = false;
            this.layoutControlGroup1.Items.AddRange(new DevExpress.XtraLayout.BaseLayoutItem[] {
            this.lciFullName,
            this.lciCountry,
            this.lciAddressLine2,
            this.lciAddressLine1,
            this.lciCity,
            this.lciPhone,
            this.emptySpaceItem4,
            this.lciPostalCode,
            this.lciState});
            this.layoutControlGroup1.Name = "Root";
            this.layoutControlGroup1.Size = new System.Drawing.Size(403, 178);
            this.layoutControlGroup1.TextVisible = false;
            // 
            // lciFullName
            // 
            this.lciFullName.Control = this.txtFullName;
            this.lciFullName.CustomizationFormText = "Full Name";
            this.lciFullName.Location = new System.Drawing.Point(0, 0);
            this.lciFullName.Name = "lciFullName";
            this.lciFullName.Size = new System.Drawing.Size(383, 24);
            this.lciFullName.Text = "Full Name";
            this.lciFullName.TextSize = new System.Drawing.Size(70, 13);
            // 
            // lciCountry
            // 
            this.lciCountry.Control = this.txtCountry;
            this.lciCountry.CustomizationFormText = "Country";
            this.lciCountry.Location = new System.Drawing.Point(255, 120);
            this.lciCountry.Name = "lciCountry";
            this.lciCountry.Size = new System.Drawing.Size(128, 24);
            this.lciCountry.Text = "         Country";
            this.lciCountry.TextSize = new System.Drawing.Size(70, 13);
            // 
            // lciAddressLine2
            // 
            this.lciAddressLine2.Control = this.textEdit2;
            this.lciAddressLine2.CustomizationFormText = "Address Line 2";
            this.lciAddressLine2.Location = new System.Drawing.Point(0, 72);
            this.lciAddressLine2.Name = "lciAddressLine2";
            this.lciAddressLine2.Size = new System.Drawing.Size(383, 24);
            this.lciAddressLine2.Text = "Address Line 2";
            this.lciAddressLine2.TextSize = new System.Drawing.Size(70, 13);
            // 
            // lciAddressLine1
            // 
            this.lciAddressLine1.Control = this.textEdit1;
            this.lciAddressLine1.CustomizationFormText = "Address Line 1";
            this.lciAddressLine1.Location = new System.Drawing.Point(0, 48);
            this.lciAddressLine1.Name = "lciAddressLine1";
            this.lciAddressLine1.Size = new System.Drawing.Size(383, 24);
            this.lciAddressLine1.Text = "Address Line 1";
            this.lciAddressLine1.TextSize = new System.Drawing.Size(70, 13);
            // 
            // lciCity
            // 
            this.lciCity.Control = this.textEdit3;
            this.lciCity.CustomizationFormText = "City";
            this.lciCity.Location = new System.Drawing.Point(0, 96);
            this.lciCity.Name = "lciCity";
            this.lciCity.Size = new System.Drawing.Size(383, 24);
            this.lciCity.Text = "City";
            this.lciCity.TextSize = new System.Drawing.Size(70, 13);
            // 
            // lciPhone
            // 
            this.lciPhone.Control = this.txtPhone;
            this.lciPhone.CustomizationFormText = "Phone";
            this.lciPhone.Location = new System.Drawing.Point(0, 24);
            this.lciPhone.Name = "lciPhone";
            this.lciPhone.Size = new System.Drawing.Size(383, 24);
            this.lciPhone.Text = "Phone";
            this.lciPhone.TextSize = new System.Drawing.Size(70, 13);
            // 
            // emptySpaceItem4
            // 
            this.emptySpaceItem4.AllowHotTrack = false;
            this.emptySpaceItem4.CustomizationFormText = "emptySpaceItem4";
            this.emptySpaceItem4.Location = new System.Drawing.Point(0, 144);
            this.emptySpaceItem4.Name = "emptySpaceItem4";
            this.emptySpaceItem4.Size = new System.Drawing.Size(383, 14);
            this.emptySpaceItem4.TextSize = new System.Drawing.Size(0, 0);
            // 
            // lciPostalCode
            // 
            this.lciPostalCode.Control = this.textEdit7;
            this.lciPostalCode.CustomizationFormText = "Postal Code";
            this.lciPostalCode.Location = new System.Drawing.Point(0, 120);
            this.lciPostalCode.Name = "lciPostalCode";
            this.lciPostalCode.Size = new System.Drawing.Size(127, 24);
            this.lciPostalCode.Text = "Postal Code";
            this.lciPostalCode.TextSize = new System.Drawing.Size(70, 13);
            // 
            // lciState
            // 
            this.lciState.Control = this.textEdit8;
            this.lciState.CustomizationFormText = "State";
            this.lciState.Location = new System.Drawing.Point(127, 120);
            this.lciState.Name = "lciState";
            this.lciState.Size = new System.Drawing.Size(128, 24);
            this.lciState.Text = "           State";
            this.lciState.TextSize = new System.Drawing.Size(70, 13);
            // 
            // tabPageBrowser
            // 
            this.tabPageBrowser.Controls.Add(this.groupControl1);
            this.tabPageBrowser.Name = "tabPageBrowser";
            this.tabPageBrowser.Size = new System.Drawing.Size(399, 178);
            this.tabPageBrowser.Text = "Browser";
            // 
            // groupControl1
            // 
            this.groupControl1.Controls.Add(this.labelControl7);
            this.groupControl1.Controls.Add(this.labelControl5);
            this.groupControl1.Controls.Add(this.lblBrowserHelp);
            this.groupControl1.Controls.Add(this.btnChooseDefBrowser);
            this.groupControl1.Controls.Add(this.lblValBrowser);
            this.groupControl1.Location = new System.Drawing.Point(3, 10);
            this.groupControl1.Name = "groupControl1";
            this.groupControl1.Size = new System.Drawing.Size(399, 143);
            this.groupControl1.TabIndex = 13;
            this.groupControl1.Text = "Browser settings";
            // 
            // labelControl7
            // 
            this.labelControl7.Appearance.Font = new System.Drawing.Font("Tahoma", 8.25F, System.Drawing.FontStyle.Italic, System.Drawing.GraphicsUnit.Point, ((byte)(204)));
            this.labelControl7.Appearance.Options.UseFont = true;
            this.labelControl7.Location = new System.Drawing.Point(13, 97);
            this.labelControl7.Name = "labelControl7";
            this.labelControl7.Size = new System.Drawing.Size(292, 13);
            this.labelControl7.TabIndex = 13;
            this.labelControl7.Text = "* Useful when purchasing from more than one eBay Account";
            // 
            // labelControl5
            // 
            this.labelControl5.Location = new System.Drawing.Point(13, 37);
            this.labelControl5.Name = "labelControl5";
            this.labelControl5.Size = new System.Drawing.Size(164, 13);
            this.labelControl5.TabIndex = 13;
            this.labelControl5.Text = "Current web browser for account:";
            // 
            // lblBrowserHelp
            // 
            this.lblBrowserHelp.Cursor = System.Windows.Forms.Cursors.Hand;
            this.lblBrowserHelp.Location = new System.Drawing.Point(13, 62);
            this.lblBrowserHelp.Name = "lblBrowserHelp";
            this.lblBrowserHelp.Size = new System.Drawing.Size(137, 13);
            this.lblBrowserHelp.TabIndex = 13;
            this.lblBrowserHelp.Tag = "https://ubuyfirst.com/default-browser-settings-view-ebay-listings/";
            this.lblBrowserHelp.Text = "Choose alternative browser:";
            this.lblBrowserHelp.Click += new System.EventHandler(this.hyperlinkLabelControl1_Click);
            // 
            // btnChooseDefBrowser
            // 
            this.btnChooseDefBrowser.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.btnChooseDefBrowser.Location = new System.Drawing.Point(277, 62);
            this.btnChooseDefBrowser.Name = "btnChooseDefBrowser";
            this.btnChooseDefBrowser.Size = new System.Drawing.Size(113, 23);
            this.btnChooseDefBrowser.TabIndex = 10;
            this.btnChooseDefBrowser.Text = "Choose...";
            this.btnChooseDefBrowser.Click += new System.EventHandler(this.btnChooseBrowser_Click);
            // 
            // lblValBrowser
            // 
            this.lblValBrowser.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.lblValBrowser.Location = new System.Drawing.Point(268, 37);
            this.lblValBrowser.Name = "lblValBrowser";
            this.lblValBrowser.Size = new System.Drawing.Size(93, 13);
            this.lblValBrowser.TabIndex = 13;
            this.lblValBrowser.Text = "<Default browser>";
            // 
            // dxValidationProvider1
            // 
            this.dxValidationProvider1.ValidationFailed += new DevExpress.XtraEditors.DXErrorProvider.ValidationFailedEventHandler(this.dxValidationProvider1_ValidationFailed);
            this.dxValidationProvider1.ValidationSucceeded += new DevExpress.XtraEditors.DXErrorProvider.ValidationSucceededEventHandler(this.dxValidationProvider1_ValidationSucceeded);
            // 
            // FormAuth
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(425, 583);
            this.Controls.Add(this.tabShippingAndBrowser);
            this.Controls.Add(this.hyperlinkLabelControl1);
            this.Controls.Add(this.boxCustomColumnSite);
            this.Controls.Add(this.lblValSessionStatus);
            this.Controls.Add(this.labelControl3);
            this.Controls.Add(this.lblStatusCurrent);
            this.Controls.Add(this.labelControl2);
            this.Controls.Add(this.labelControl1);
            this.Controls.Add(this.btnxEbayDoAuth);
            this.Controls.Add(this.btnConfirmAuth);
            this.Controls.Add(this.btnSaveAuthToken);
            this.Controls.Add(this.ribbonControl1);
            this.Icon = ((System.Drawing.Icon)(resources.GetObject("$this.Icon")));
            this.Name = "FormAuth";
            this.Ribbon = this.ribbonControl1;
            this.Text = "eBay Account Authentication";
            this.Load += new System.EventHandler(this.FormAuth_Load);
            this.Shown += new System.EventHandler(this.FormAuth_Shown);
            this.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.FormAuth_KeyPress);
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.boxCustomColumnSite.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.tabShippingAndBrowser)).EndInit();
            this.tabShippingAndBrowser.ResumeLayout(false);
            this.tabPageShippingOptions.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.layoutControl1)).EndInit();
            this.layoutControl1.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.txtFullName.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCountry.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit8.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit3.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit7.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtPhone.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.layoutControlGroup1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciFullName)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciCountry)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciAddressLine2)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciAddressLine1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciCity)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciPhone)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.emptySpaceItem4)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciPostalCode)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.lciState)).EndInit();
            this.tabPageBrowser.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.groupControl1)).EndInit();
            this.groupControl1.ResumeLayout(false);
            this.groupControl1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.dxValidationProvider1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private OpenFileDialog openFileDialog1;
        private ToolTip toolTip1;
        private FormAssistant formAssistant1;
        private RibbonControl ribbonControl1;
        private SimpleButton btnSaveAuthToken;
        private SimpleButton btnConfirmAuth;
        private SimpleButton btnxEbayDoAuth;
        private LabelControl labelControl1;
        private LabelControl labelControl2;
        private LabelControl labelControl3;
        private LabelControl lblValSessionStatus;
        private LabelControl lblStatusCurrent;
        private ComboBoxEdit boxCustomColumnSite;
        private HyperlinkLabelControl hyperlinkLabelControl1;
        private DevExpress.XtraTab.XtraTabControl tabShippingAndBrowser;
        private DevExpress.XtraTab.XtraTabPage tabPageBrowser;
        private GroupControl groupControl1;
        private LabelControl labelControl7;
        private LabelControl labelControl5;
        private HyperlinkLabelControl lblBrowserHelp;
        private SimpleButton btnChooseDefBrowser;
        private LabelControl lblValBrowser;
        private DevExpress.XtraTab.XtraTabPage tabPageShippingOptions;
        private DevExpress.XtraLayout.LayoutControl layoutControl1;
        private DevExpress.XtraLayout.LayoutControlGroup layoutControlGroup1;
        private TextEdit txtFullName;
        private DevExpress.XtraEditors.DXErrorProvider.DXValidationProvider dxValidationProvider1;
        private TextEdit txtCountry;
        private TextEdit textEdit2;
        private TextEdit textEdit1;
        private TextEdit textEdit8;
        private TextEdit textEdit3;
        private TextEdit textEdit7;
        private TextEdit txtPhone;
        private DevExpress.XtraLayout.LayoutControlItem lciFullName;
        private DevExpress.XtraLayout.LayoutControlItem lciCountry;
        private DevExpress.XtraLayout.LayoutControlItem lciAddressLine2;
        private DevExpress.XtraLayout.LayoutControlItem lciAddressLine1;
        private DevExpress.XtraLayout.LayoutControlItem lciCity;
        private DevExpress.XtraLayout.LayoutControlItem lciPhone;
        private DevExpress.XtraLayout.EmptySpaceItem emptySpaceItem4;
        private DevExpress.XtraLayout.LayoutControlItem lciPostalCode;
        private DevExpress.XtraLayout.LayoutControlItem lciState;
    }
}