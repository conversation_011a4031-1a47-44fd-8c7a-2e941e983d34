﻿using System;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Threading.Tasks;
using Lucene.Net.Analysis.Hunspell;

namespace uBuyFirst.Images
{
    // This holds large images so don't have to be retrieved twice for row you are on and can switch between them
    public sealed class ImageCache
    {
        // Start of Singleton Pattern
        private static readonly Lazy<ImageCache> lazy =
            new Lazy<ImageCache>(() => new ImageCache());

        public static ImageCache Inst { get { return lazy.Value; } }

        private ImageCache()
        {
        }
        // End of Singleton Pattern

        // Dictionary to hold large images by path so can easily be used again
        private System.Collections.Concurrent.ConcurrentDictionary<string, Image> _imageItemsByPath = new System.Collections.Concurrent.ConcurrentDictionary<string, Image>();

        public void ClearCache()
        {
            _imageItemsByPath.Clear();
        }

        public void AddToCache(string imagePath, Image image)
        {
            _imageItemsByPath.TryAdd(imagePath, image);
        }

        public bool GetImage(string imagePath, out Image image)
        {
            return _imageItemsByPath.TryGetValue(imagePath, out image);
        }


    }
}
