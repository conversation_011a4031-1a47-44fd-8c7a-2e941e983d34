﻿using System.ComponentModel;
using DevExpress.Utils;
using DevExpress.XtraBars;
using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;

namespace uBuyFirst
{
    partial class FormShortcuts
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormShortcuts));
            this.formAssistant1 = new DevExpress.XtraBars.FormAssistant();
            this.ribbonControl1 = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.groupShortcuts = new DevExpress.XtraEditors.GroupControl();
            this.textEdit1 = new DevExpress.XtraEditors.TextEdit();
            this.cbMakeOfferKey = new DevExpress.XtraEditors.ComboBoxEdit();
            this.cbMakeOfferModifier = new DevExpress.XtraEditors.ComboBoxEdit();
            this.cbQuickBuyKey = new DevExpress.XtraEditors.ComboBoxEdit();
            this.cbQuickBuyModifier = new DevExpress.XtraEditors.ComboBoxEdit();
            this.cbGoToCheckoutKey = new DevExpress.XtraEditors.ComboBoxEdit();
            this.cbGoToCheckoutModifier = new DevExpress.XtraEditors.ComboBoxEdit();
            this.cbBuyKey = new DevExpress.XtraEditors.ComboBoxEdit();
            this.lblTestYourKeyboard = new DevExpress.XtraEditors.LabelControl();
            this.lblMakeOffer = new DevExpress.XtraEditors.LabelControl();
            this.cbBuyModifier = new DevExpress.XtraEditors.ComboBoxEdit();
            this.lblQuickBuy = new DevExpress.XtraEditors.LabelControl();
            this.labelModifier = new DevExpress.XtraEditors.LabelControl();
            this.lblSkipToCheckout = new DevExpress.XtraEditors.LabelControl();
            this.labelKey = new DevExpress.XtraEditors.LabelControl();
            this.lblBuy = new DevExpress.XtraEditors.LabelControl();
            this.toolTipController1 = new DevExpress.Utils.ToolTipController(this.components);
            this.lblImmediateBuy = new DevExpress.XtraEditors.LabelControl();
            this.cbImmediateBuyModifier = new DevExpress.XtraEditors.ComboBoxEdit();
            this.cbImmediateBuyKey = new DevExpress.XtraEditors.ComboBoxEdit();
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupShortcuts)).BeginInit();
            this.groupShortcuts.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbMakeOfferKey.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbMakeOfferModifier.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbQuickBuyKey.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbQuickBuyModifier.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbGoToCheckoutKey.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbGoToCheckoutModifier.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbBuyKey.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbBuyModifier.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbImmediateBuyModifier.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbImmediateBuyKey.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // ribbonControl1
            // 
            this.ribbonControl1.ExpandCollapseItem.Id = 0;
            this.ribbonControl1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.ribbonControl1.ExpandCollapseItem,
            this.ribbonControl1.SearchEditItem});
            this.ribbonControl1.Location = new System.Drawing.Point(0, 0);
            this.ribbonControl1.MaxItemId = 1;
            this.ribbonControl1.Name = "ribbonControl1";
            this.ribbonControl1.OptionsPageCategories.ShowCaptions = false;
            this.ribbonControl1.ShowApplicationButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowDisplayOptionsMenuButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowExpandCollapseButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowPageHeadersMode = DevExpress.XtraBars.Ribbon.ShowPageHeadersMode.Hide;
            this.ribbonControl1.ShowQatLocationSelector = false;
            this.ribbonControl1.ShowToolbarCustomizeItem = false;
            this.ribbonControl1.Size = new System.Drawing.Size(391, 27);
            this.ribbonControl1.Toolbar.ShowCustomizeItem = false;
            // 
            // groupShortcuts
            // 
            this.groupShortcuts.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom)
            | System.Windows.Forms.AnchorStyles.Left)
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupShortcuts.Controls.Add(this.textEdit1);
            this.groupShortcuts.Controls.Add(this.cbMakeOfferKey);
            this.groupShortcuts.Controls.Add(this.cbMakeOfferModifier);
            this.groupShortcuts.Controls.Add(this.cbImmediateBuyKey);
            this.groupShortcuts.Controls.Add(this.cbQuickBuyKey);
            this.groupShortcuts.Controls.Add(this.cbImmediateBuyModifier);
            this.groupShortcuts.Controls.Add(this.cbQuickBuyModifier);
            this.groupShortcuts.Controls.Add(this.cbGoToCheckoutKey);
            this.groupShortcuts.Controls.Add(this.cbGoToCheckoutModifier);
            this.groupShortcuts.Controls.Add(this.cbBuyKey);
            this.groupShortcuts.Controls.Add(this.lblTestYourKeyboard);
            this.groupShortcuts.Controls.Add(this.lblMakeOffer);
            this.groupShortcuts.Controls.Add(this.lblImmediateBuy);
            this.groupShortcuts.Controls.Add(this.cbBuyModifier);
            this.groupShortcuts.Controls.Add(this.lblQuickBuy);
            this.groupShortcuts.Controls.Add(this.labelModifier);
            this.groupShortcuts.Controls.Add(this.lblSkipToCheckout);
            this.groupShortcuts.Controls.Add(this.labelKey);
            this.groupShortcuts.Controls.Add(this.lblBuy);
            this.groupShortcuts.Location = new System.Drawing.Point(12, 38);
            this.groupShortcuts.Name = "groupShortcuts";
            this.groupShortcuts.Size = new System.Drawing.Size(366, 222);
            this.groupShortcuts.TabIndex = 2;
            this.groupShortcuts.Text = "Shortcuts";
            // 
            // textEdit1
            // 
            this.textEdit1.Location = new System.Drawing.Point(129, 175);
            this.textEdit1.MenuManager = this.ribbonControl1;
            this.textEdit1.Name = "textEdit1";
            this.textEdit1.Size = new System.Drawing.Size(207, 20);
            this.textEdit1.TabIndex = 4;
            this.textEdit1.ToolTip = "Put cursor inside the textbox. Press keys on your keyboard to reveal a key name.";
            this.textEdit1.KeyDown += new System.Windows.Forms.KeyEventHandler(this.textEdit1_KeyDown);
            // 
            // cbMakeOfferKey
            // 
            this.cbMakeOfferKey.Location = new System.Drawing.Point(129, 125);
            this.cbMakeOfferKey.Name = "cbMakeOfferKey";
            this.cbMakeOfferKey.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbMakeOfferKey.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbMakeOfferKey.Size = new System.Drawing.Size(124, 20);
            this.cbMakeOfferKey.TabIndex = 3;
            this.cbMakeOfferKey.ToolTip = "Default: Oemtilde, None";
            // 
            // cbMakeOfferModifier
            // 
            this.cbMakeOfferModifier.Location = new System.Drawing.Point(259, 125);
            this.cbMakeOfferModifier.Name = "cbMakeOfferModifier";
            this.cbMakeOfferModifier.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbMakeOfferModifier.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbMakeOfferModifier.Size = new System.Drawing.Size(77, 20);
            this.cbMakeOfferModifier.TabIndex = 3;
            this.cbMakeOfferModifier.ToolTip = "Default: Oemtilde, None";
            // 
            // cbQuickBuyKey
            // 
            this.cbQuickBuyKey.Location = new System.Drawing.Point(129, 99);
            this.cbQuickBuyKey.Name = "cbQuickBuyKey";
            this.cbQuickBuyKey.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbQuickBuyKey.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbQuickBuyKey.Size = new System.Drawing.Size(124, 20);
            this.cbQuickBuyKey.TabIndex = 3;
            this.cbQuickBuyKey.ToolTip = "Default: Enter, Control";
            // 
            // cbQuickBuyModifier
            // 
            this.cbQuickBuyModifier.Location = new System.Drawing.Point(259, 99);
            this.cbQuickBuyModifier.Name = "cbQuickBuyModifier";
            this.cbQuickBuyModifier.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbQuickBuyModifier.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbQuickBuyModifier.Size = new System.Drawing.Size(77, 20);
            this.cbQuickBuyModifier.TabIndex = 3;
            this.cbQuickBuyModifier.ToolTip = "Default: Enter, Control";
            // 
            // cbGoToCheckoutKey
            // 
            this.cbGoToCheckoutKey.Location = new System.Drawing.Point(129, 73);
            this.cbGoToCheckoutKey.Name = "cbGoToCheckoutKey";
            this.cbGoToCheckoutKey.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbGoToCheckoutKey.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbGoToCheckoutKey.Size = new System.Drawing.Size(124, 20);
            this.cbGoToCheckoutKey.TabIndex = 3;
            this.cbGoToCheckoutKey.ToolTip = "Default: Enter, Shift";
            // 
            // cbGoToCheckoutModifier
            // 
            this.cbGoToCheckoutModifier.Location = new System.Drawing.Point(259, 73);
            this.cbGoToCheckoutModifier.Name = "cbGoToCheckoutModifier";
            this.cbGoToCheckoutModifier.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbGoToCheckoutModifier.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbGoToCheckoutModifier.Size = new System.Drawing.Size(77, 20);
            this.cbGoToCheckoutModifier.TabIndex = 3;
            this.cbGoToCheckoutModifier.ToolTip = "Default: Enter, Shift";
            // 
            // cbBuyKey
            // 
            this.cbBuyKey.Location = new System.Drawing.Point(129, 47);
            this.cbBuyKey.Name = "cbBuyKey";
            this.cbBuyKey.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbBuyKey.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbBuyKey.Size = new System.Drawing.Size(124, 20);
            this.cbBuyKey.TabIndex = 3;
            this.cbBuyKey.ToolTip = "Default: Enter, None";
            // 
            // lblTestYourKeyboard
            // 
            this.lblTestYourKeyboard.Location = new System.Drawing.Point(16, 178);
            this.lblTestYourKeyboard.Name = "lblTestYourKeyboard";
            this.lblTestYourKeyboard.Size = new System.Drawing.Size(69, 13);
            this.lblTestYourKeyboard.TabIndex = 0;
            this.lblTestYourKeyboard.Text = "Test your keys";
            this.lblTestYourKeyboard.ToolTip = "Put cursor inside the textbox. Press keys on your keyboard to reveal a key name.";
            // 
            // lblMakeOffer
            // 
            this.lblMakeOffer.Location = new System.Drawing.Point(16, 128);
            this.lblMakeOffer.Name = "lblMakeOffer";
            this.lblMakeOffer.Size = new System.Drawing.Size(51, 13);
            this.lblMakeOffer.TabIndex = 0;
            this.lblMakeOffer.Text = "Make offer";
            this.lblMakeOffer.ToolTip = "Default: Oemtilde, None";
            // 
            // cbBuyModifier
            // 
            this.cbBuyModifier.Location = new System.Drawing.Point(259, 47);
            this.cbBuyModifier.MenuManager = this.ribbonControl1;
            this.cbBuyModifier.Name = "cbBuyModifier";
            this.cbBuyModifier.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbBuyModifier.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbBuyModifier.Size = new System.Drawing.Size(77, 20);
            this.cbBuyModifier.TabIndex = 3;
            this.cbBuyModifier.ToolTip = "Default: Enter, None";
            // 
            // lblQuickBuy
            // 
            this.lblQuickBuy.Location = new System.Drawing.Point(16, 102);
            this.lblQuickBuy.Name = "lblQuickBuy";
            this.lblQuickBuy.Size = new System.Drawing.Size(49, 13);
            this.lblQuickBuy.TabIndex = 0;
            this.lblQuickBuy.Text = "Quick Buy";
            this.lblQuickBuy.ToolTip = "Default: Enter, Control";
            this.lblQuickBuy.DoubleClick += new System.EventHandler(this.lblQuickBuy_Click);
            // 
            // labelModifier
            // 
            this.labelModifier.Location = new System.Drawing.Point(276, 28);
            this.labelModifier.Name = "labelModifier";
            this.labelModifier.Size = new System.Drawing.Size(37, 13);
            this.labelModifier.TabIndex = 0;
            this.labelModifier.Text = "Modifier";
            // 
            // lblSkipToCheckout
            // 
            this.lblSkipToCheckout.Location = new System.Drawing.Point(16, 76);
            this.lblSkipToCheckout.Name = "lblSkipToCheckout";
            this.lblSkipToCheckout.Size = new System.Drawing.Size(105, 13);
            this.lblSkipToCheckout.TabIndex = 0;
            this.lblSkipToCheckout.Text = "Go To Web Checkout";
            this.lblSkipToCheckout.ToolTip = "Default: Enter, Shift";
            // 
            // labelKey
            // 
            this.labelKey.Location = new System.Drawing.Point(182, 28);
            this.labelKey.Name = "labelKey";
            this.labelKey.Size = new System.Drawing.Size(18, 13);
            this.labelKey.TabIndex = 0;
            this.labelKey.Text = "Key";
            // 
            // lblBuy
            // 
            this.lblBuy.Location = new System.Drawing.Point(16, 50);
            this.lblBuy.Name = "lblBuy";
            this.lblBuy.Size = new System.Drawing.Size(18, 13);
            this.lblBuy.TabIndex = 0;
            this.lblBuy.Text = "Buy";
            this.lblBuy.ToolTip = "Default: Enter, None";
            // 
            // lblImmediateBuy
            // 
            this.lblImmediateBuy.Location = new System.Drawing.Point(16, 154);
            this.lblImmediateBuy.Name = "lblImmediateBuy";
            this.lblImmediateBuy.Size = new System.Drawing.Size(69, 13);
            this.lblImmediateBuy.TabIndex = 0;
            this.lblImmediateBuy.Text = "Immediate Buy";
            this.lblImmediateBuy.ToolTip = "Default: Enter, Control";
            this.lblImmediateBuy.Visible = false;
            // 
            // cbImmediateBuyModifier
            // 
            this.cbImmediateBuyModifier.Location = new System.Drawing.Point(259, 151);
            this.cbImmediateBuyModifier.Name = "cbImmediateBuyModifier";
            this.cbImmediateBuyModifier.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbImmediateBuyModifier.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbImmediateBuyModifier.Size = new System.Drawing.Size(77, 20);
            this.cbImmediateBuyModifier.TabIndex = 3;
            this.cbImmediateBuyModifier.ToolTip = "Default: Enter, Control";
            this.cbImmediateBuyModifier.Visible = false;
            // 
            // cbImmediateBuyKey
            // 
            this.cbImmediateBuyKey.Location = new System.Drawing.Point(129, 151);
            this.cbImmediateBuyKey.Name = "cbImmediateBuyKey";
            this.cbImmediateBuyKey.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbImmediateBuyKey.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.DisableTextEditor;
            this.cbImmediateBuyKey.Size = new System.Drawing.Size(124, 20);
            this.cbImmediateBuyKey.TabIndex = 3;
            this.cbImmediateBuyKey.ToolTip = "Default: Enter, Control";
            this.cbImmediateBuyKey.Visible = false;
            // 
            // FormShortcuts
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(391, 286);
            this.Controls.Add(this.groupShortcuts);
            this.Controls.Add(this.ribbonControl1);
            this.IconOptions.Icon = ((System.Drawing.Icon)(resources.GetObject("FormShortcuts.IconOptions.Icon")));
            this.Name = "FormShortcuts";
            this.Ribbon = this.ribbonControl1;
            this.Text = "Shortcut Editor";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FormHighlightWords_FormClosing);
            this.Load += new System.EventHandler(this.FormHighlightWords_Load);
            this.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.FormHighlightWords_KeyPress);
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupShortcuts)).EndInit();
            this.groupShortcuts.ResumeLayout(false);
            this.groupShortcuts.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.textEdit1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbMakeOfferKey.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbMakeOfferModifier.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbQuickBuyKey.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbQuickBuyModifier.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbGoToCheckoutKey.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbGoToCheckoutModifier.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbBuyKey.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbBuyModifier.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbImmediateBuyModifier.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbImmediateBuyKey.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private FormAssistant formAssistant1;
        private RibbonControl ribbonControl1;
        private GroupControl groupShortcuts;
        private ToolTipController toolTipController1;
        private LabelControl lblBuy;
        private ComboBoxEdit cbBuyKey;
        private ComboBoxEdit cbBuyModifier;
        private LabelControl labelKey;
        private LabelControl labelModifier;
        private ComboBoxEdit cbMakeOfferKey;
        private ComboBoxEdit cbMakeOfferModifier;
        private ComboBoxEdit cbQuickBuyKey;
        private ComboBoxEdit cbQuickBuyModifier;
        private ComboBoxEdit cbGoToCheckoutKey;
        private ComboBoxEdit cbGoToCheckoutModifier;
        private LabelControl lblMakeOffer;
        private LabelControl lblQuickBuy;
        private LabelControl lblSkipToCheckout;
        private TextEdit textEdit1;
        private LabelControl lblTestYourKeyboard;
        private ComboBoxEdit cbImmediateBuyKey;
        private ComboBoxEdit cbImmediateBuyModifier;
        private LabelControl lblImmediateBuy;
    }
}