﻿using DevExpress.XtraBars;
using PushbulletSharp;
using PushbulletSharp.Filters;
using PushbulletSharp.Models.Requests;
using PushbulletSharp.Models.Responses;
using System;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using uBuyFirst.Network;
using uBuyFirst.Tools;

namespace uBuyFirst
{
    public partial class Form1
    {
        private WebSocketClient? PushBulletWebSocketClient { get; set; }

        private void barCheckItemPushBullet_CheckedChanged(object sender, ItemClickEventArgs e)
        {
            _pushbullet ??= new PushbulletSender();
            _pushbullet.Enabled = barCheckItemPushBullet.Checked;
        }

        private void barButtonItemPushBullet_ItemClick(object sender, ItemClickEventArgs e)
        {
            _pushbullet ??= new PushbulletSender();

            var formPushbullet = new FormPushbullet();
            var tmp = new PushbulletSender();

            tmp.ActiveIden = _pushbullet.ActiveIden;
            tmp.ActiveNickname = _pushbullet.ActiveNickname;
            tmp.uBuyFirstIden = _pushbullet.uBuyFirstIden;
            tmp.CombinePushes = _pushbullet.CombinePushes;
            tmp.BodyColumns = _pushbullet.BodyColumns;
            tmp.BodyTemplate = _pushbullet.BodyTemplate;

            tmp.Enabled = _pushbullet.Enabled;
            tmp.SendCheckoutUrl = _pushbullet.SendCheckoutUrl;
            tmp.TitleColumns = _pushbullet.TitleColumns;
            tmp.TitleTemplate = _pushbullet.TitleTemplate;
            tmp.Token = _pushbullet.Token;
            if (!string.IsNullOrEmpty(tmp.Token))
            {
                tmp.PbClient = new PushbulletClient(tmp.Token);
            }

            formPushbullet.PushbulletTmp = tmp;
            formPushbullet.ebaySearches = _ebaySearches.ChildrenCore;
            var result = formPushbullet.ShowDialog();

            if (result != DialogResult.OK)
            {
                return;
            }

            if (_pushbullet.ActiveIden != formPushbullet.PushbulletTmp.ActiveIden)
            {
                try
                {
                    var push = new PushNoteRequest();
                    push.Title = "uBuyFirst";
                    push.Body = "You can send commands to uBuyFirst from your other PushBullet devices.\n"
                                + "1) Select 'uBuyFirst' device on your other device.\n"
                                + "2) Send message 'Help' to see a list of available commands.";
                    push.DeviceIden = formPushbullet.PushbulletTmp.ActiveIden;
                    push.SourceDeviceIden = formPushbullet.PushbulletTmp.uBuyFirstIden;
                    try
                    {
                        formPushbullet.PushbulletTmp.PbClient.PushNote(push);
                    }
                    catch (Exception ex)
                    {
                        if (ex.Message.Contains("No valid access token provided"))
                        {
                            formPushbullet.PushbulletTmp.PbClient.AccessToken = "";
                        }
                    }
                }
                catch (Exception)
                {
                    //ignored
                }
            }

            _pushbullet = formPushbullet.PushbulletTmp;
            SaveSettings();
            PushBulletListen();
        }

        private async Task PushBulletListen()
        {
            if (PushBulletWebSocketClient != null)
                await PushBulletWebSocketClient?.Disconnect();
            if (string.IsNullOrEmpty(_pushbullet.Token))
                return;

            PushBulletWebSocketClient = new WebSocketClient("wss://stream.pushbullet.com/websocket/" + _pushbullet.Token);
            var pushResponseFilter = new PushResponseFilter { Active = true, IncludeTypes = new[] { PushResponseType.Note }, ModifiedDate = DateTime.Now.AddMinutes(-1) };

            var pbClient = _pushbullet.PbClient;
            try
            {
                var initialPushes = pbClient.GetPushes(pushResponseFilter);
                _pushbullet.SeenPushes = initialPushes.Pushes.Select(push => push.Iden).ToList();
                //handle too many requests (429) Too Many Requests

                await PushBulletWebSocketClient.Connect();
                PushBulletWebSocketClient.MessageReceived += OnWebsocketMessageReceived;
            }
            catch (Exception ex)
            {
                if (ex.Message.Contains("401 "))
                {
                    _pushbullet.Token = "";
                    Task.Run(() =>
                    {
                        // Show the message box in a background thread to avoid blocking the UI.
                        XtraMessageBox.Show("Pushbullet token is not valid and was removed.");
                    });

                    return;
                }

                if (ex.Message.Contains("The underlying connection was closed"))
                {
                    XtraMessageBox.Show("Can't connect to Pushbullet server.");

                    return;
                }

                ExM.ubuyExceptionHandler("PushBulletListen: ", ex);
            }
        }

        private void OnWebsocketMessageReceived(object sender, string e)
        {
            var pbClient = _pushbullet.PbClient;

            if (!e.Contains("\"type\":\"tickle\""))
            {
                return;
            }

            var getPushesResponse = pbClient.GetPushes(new PushResponseFilter { Active = true, IncludeTypes = new[] { PushResponseType.Note }, ModifiedDate = DateTime.Now.AddMinutes(-1) });

            foreach (var push in getPushesResponse.Pushes)
            {
                if (_pushbullet.SeenPushes == null || (_pushbullet.SeenPushes.Contains(push.Iden) || push.TargetDeviceIden != _pushbullet.uBuyFirstIden))
                {
                    continue;
                }

                _pushbullet.SeenPushes.Add(push.Iden);
                var pushNoteRequest = new PushNoteRequest();
                pushNoteRequest.Title = "uBuyFirst";
                pushNoteRequest.DeviceIden = push.SourceDeviceIden;
                if (push.SourceDeviceIden == null)
                {
                    pushNoteRequest.Email = push.SenderEmail;
                }

                pushNoteRequest.SourceDeviceIden = _pushbullet.uBuyFirstIden;
                var command = push.Body.Trim().ToLower();
                switch (command)
                {
                    case "start":
                        Debug.WriteLine("Started");

                        if (_searchService != null && _searchService.Running)
                        {
                            pushNoteRequest.Body = "Search is already running";
                        }
                        else
                        {
                            Instance.InvokeIfRequired(() => Instance.StartWorking());
                            pushNoteRequest.Body = "Search started";
                        }

                        break;

                    case "stop":
                        Debug.WriteLine("Stopped");
                        if (_searchService is { Running: true })
                        {
                            Instance.InvokeIfRequired(() => Instance.StopWorking().ConfigureAwait(false));
                            pushNoteRequest.Body = "Search stopped";
                        }
                        else
                            pushNoteRequest.Body = "Search is already stopped";

                        break;

                    case "help":
                        pushNoteRequest.Body = "Available commands - Help, Start, Stop, Enable [device], Disable, Status ";
                        Debug.WriteLine("Help");

                        break;

                    case "status":
                        pushNoteRequest.Body = $"Status:\nStarted - {_searchService?.Running}\n" + $"Push enabled - {_pushbullet.Enabled}\nDevice - {_pushbullet.ActiveNickname}";

                        break;

                    default:

                        if (command.StartsWith("enable"))
                        {
                            var deviceName = command.Replace("enable ", "").Trim();

                            if (deviceName is "" or "enable")
                            {
                                _pushbullet.Enabled = true;
                                pushNoteRequest.Body = "Push notifications enabled";
                            }
                            else
                            {
                                var deviceList = _pushbullet.PbClient.CurrentUsersDevices(true);
                                var device = deviceList.Devices.FirstOrDefault(d => d.Nickname.ToLower() == deviceName);
                                if (device == null)
                                {
                                    pushNoteRequest.Body = $"{deviceName} - no such device found\n";
                                    pushNoteRequest.Body += $"Available devices: {string.Join(", ", deviceList.Devices.Select(d => d.Nickname))}";
                                }
                                else
                                {
                                    _pushbullet.ActiveIden = device.Iden;
                                    _pushbullet.ActiveNickname = device.Nickname;
                                    _pushbullet.Enabled = true;
                                    pushNoteRequest.Body = $"{device.Nickname} - push notifications enabled";
                                }
                            }
                        }
                        else
                        {
                            if (command.StartsWith("disable"))
                            {
                                _pushbullet.Enabled = false;
                                pushNoteRequest.Body = "Push notifications disabled";
                            }
                            else
                            {
                                pushNoteRequest.Body = "Available commands - Help, Start, Stop, Enable [device], Disable, Status";
                            }
                        }

                        break;
                }

                _synchronizationContext.Send(_ => SaveSettings(), null);
                if (pushNoteRequest.Body != null)
                    pbClient.PushNote(pushNoteRequest);
            }
        }
    }
}
