﻿using System;
using System.Linq;
using eBay.Service.Call;
using eBay.Service.Core.Sdk;
using eBay.Service.Core.Soap;
using uBuyFirst.Stats;

namespace uBuyFirst.API.TradingAPI
{
    public class TradingAPIService
    {
        internal ItemType GetItem(ApiContext apiContext, string itemID)
        {
            try
            {
                var getItemCall = new GetItemCall(apiContext);
                getItemCall.Site = apiContext.Site;
                getItemCall.IncludeItemSpecifics = true;
                getItemCall.DetailLevelList = new DetailLevelCodeTypeCollection { DetailLevelCodeType.ReturnAll };
                getItemCall.ApiRequest.EndUserIP = "201.201.201." + new Random().Next(1, 200);

                var tmpItem = getItemCall.GetItem(itemID);

                return tmpItem;
            }
            catch (Exception)
            {
                return null;
            }
        }

        public static string ShowApiErrors(ErrorTypeCollection errors)
        {
            var errorText = "\n";
            foreach (ErrorType error in errors)
            {
                errorText += error.LongMessage + "\n";
                if (error.ErrorClassificationSpecified)
                    errorText += error.ErrorClassification + "# ";

                errorText += error.ErrorCode + "\n";
                if (error.UserDisplayHintSpecified)
                    errorText += error.UserDisplayHint + "\n";

                if (error.ErrorParameters?.Count > 0)
                    errorText += string.Join("\n", error.ErrorParameters?.ToArray().ToList().Select(a => a.ParamID + ": " + a.Value));

                Analytics.CreateEvent(error.LongMessage, "Purchasing", 1);
            }

            return errorText;
        }
    }
}
