using System;
using System.Diagnostics;

namespace uBuyFirst.Restocker.Services
{
    /// <summary>
    /// Manages captcha detection and cooldown periods for restock operations.
    /// When a captcha is detected, all restock purchasing is paused for 5 minutes.
    /// </summary>
    public static class CaptchaCooldownManager
    {
        private static DateTime? _cooldownStartTime;
        private static readonly TimeSpan CooldownDuration = TimeSpan.FromMinutes(5);
        private static readonly object _lock = new object();

        /// <summary>
        /// Gets whether restock operations are currently in cooldown due to captcha detection
        /// </summary>
        public static bool IsInCooldown
        {
            get
            {
                lock (_lock)
                {
                    if (_cooldownStartTime == null)
                        return false;

                    var elapsed = DateTime.UtcNow - _cooldownStartTime.Value;
                    if (elapsed >= CooldownDuration)
                    {
                        // Cooldown period has expired, clear it
                        _cooldownStartTime = null;
                        Debug.WriteLine("Restock captcha cooldown period has expired. Resuming auto purchasing.");
                        return false;
                    }

                    return true;
                }
            }
        }

        /// <summary>
        /// Gets the remaining cooldown time, or null if not in cooldown
        /// </summary>
        public static TimeSpan? RemainingCooldownTime
        {
            get
            {
                lock (_lock)
                {
                    if (_cooldownStartTime == null)
                        return null;

                    var elapsed = DateTime.UtcNow - _cooldownStartTime.Value;
                    var remaining = CooldownDuration - elapsed;

                    return remaining > TimeSpan.Zero ? remaining : null;
                }
            }
        }

        /// <summary>
        /// Starts a 5-minute cooldown period for restock operations
        /// </summary>
        public static void StartCooldown()
        {
            lock (_lock)
            {
                _cooldownStartTime = DateTime.UtcNow;
                Debug.WriteLine($"Restock captcha cooldown started at {_cooldownStartTime.Value:HH:mm:ss UTC}. Auto purchasing paused for 5 minutes.");
            }
        }

        /// <summary>
        /// Manually clears the cooldown period (for testing or manual override)
        /// </summary>
        public static void ClearCooldown()
        {
            lock (_lock)
            {
                _cooldownStartTime = null;
                Debug.WriteLine("Restock captcha cooldown manually cleared. Auto purchasing resumed.");
            }
        }

        /// <summary>
        /// Checks if the given error message indicates a captcha scenario
        /// </summary>
        /// <param name="errorMessage">The error message to check</param>
        /// <returns>True if the error indicates a captcha scenario</returns>
        public static bool IsCaptchaError(string errorMessage)
        {
            if (string.IsNullOrEmpty(errorMessage))
                return false;

            // Check for the specific error message that indicates captcha
            return errorMessage.IndexOf("Session ID not found in session page HTML", StringComparison.OrdinalIgnoreCase) >= 0;
        }

        /// <summary>
        /// Gets a user-friendly status message about the current cooldown state
        /// </summary>
        public static string GetStatusMessage()
        {
            if (!IsInCooldown)
                return "Restock auto purchasing is active";

            var remaining = RemainingCooldownTime;
            if (remaining.HasValue)
            {
                var minutes = (int)remaining.Value.TotalMinutes;
                var seconds = remaining.Value.Seconds;
                return $"Restock auto purchasing paused due to captcha. Resuming in {minutes}m {seconds}s";
            }

            return "Restock auto purchasing is active";
        }
    }
}
