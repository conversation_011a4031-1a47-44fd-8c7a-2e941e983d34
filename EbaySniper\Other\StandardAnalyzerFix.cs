﻿using System.IO;
using Lucene.Net.Analysis;
using Lucene.Net.Analysis.Core;
using Lucene.Net.Analysis.Standard;
using Lucene.Net.Analysis.Util;
using Lucene.Net.Util;

namespace uBuyFirst.Other
{
    public sealed class StandardAnalyzerFix : StopwordAnalyzerBase
    {
        /// <summary>
        /// Default maximum allowed token length </summary>
        private const int DefaultMaxTokenLength = 255;

        private int _maxTokenLength = DefaultMaxTokenLength;

        /// <summary>
        /// An unmodifiable set containing some common English words that are usually not
        /// useful for searching.
        /// </summary>
        public static readonly CharArraySet STOP_WORDS_SET = new CharArraySet(LuceneVersion.LUCENE_48, 1, true);

        /// <summary>
        /// Builds an analyzer with the given stop words. </summary>
        /// <param name="matchVersion"> Lucene compatibility version - See <see cref="StandardAnalyzerFix"/> </param>
        /// <param name="stopWords"> stop words  </param>
        public StandardAnalyzerFix(LuceneVersion matchVersion, CharArraySet stopWords)
            : base(matchVersion, stopWords)
        {
        }

        /// <summary>
        /// Builds an analyzer with the default stop words (<see cref="STOP_WORDS_SET"/>). </summary>
        /// <param name="matchVersion"> Lucene compatibility version - See <see cref="StandardAnalyzerFix"/> </param>
        public StandardAnalyzerFix(LuceneVersion matchVersion)
            : this(matchVersion, STOP_WORDS_SET)
        {
        }

        /// <summary>
        /// Builds an analyzer with the stop words from the given reader. </summary>
        /// <seealso cref="WordlistLoader.GetWordSet(TextReader, LuceneVersion)"/>
        /// <param name="matchVersion"> Lucene compatibility version - See <see cref="StandardAnalyzerFix"/> </param>
        /// <param name="stopwords"> <see cref="TextReader"/> to read stop words from  </param>
        public StandardAnalyzerFix(LuceneVersion matchVersion, TextReader stopwords)
            : this(matchVersion, LoadStopwordSet(stopwords, matchVersion))
        {
        }

        /// <summary>
        /// Set maximum allowed token length.  If a token is seen
        /// that exceeds this length then it is discarded.  This
        /// setting only takes effect the next time tokenStream or
        /// tokenStream is called.
        /// </summary>
        public int MaxTokenLength
        {
            set => _maxTokenLength = value;
            get => _maxTokenLength;
        }

        protected override TokenStreamComponents CreateComponents(string fieldName, TextReader reader)
        {
            //var src = new StandardTokenizer(m_matchVersion, reader);
            var src = new ClassicTokenizer(m_matchVersion, reader);

            src.MaxTokenLength = _maxTokenLength;
            TokenStream tok = new StandardFilter(m_matchVersion, src);
            //TokenStream tok = new LowerCaseFilter(m_matchVersion, src);
            tok = new LowerCaseFilter(m_matchVersion, tok);
            //tok = new StopFilter(m_matchVersion, tok, m_stopwords);
            var tokenStreamComponents = new TokenStreamComponents(src, tok);
            return tokenStreamComponents;
            //return (TokenStreamComponents)new ClassicAnalyzer.TokenStreamComponentsAnonymousInnerClassHelper(this, src, tok, reader);
            //return new TokenStreamComponentsAnonymousInnerClassHelper(this, src, tok, reader);
        }
    }
}
