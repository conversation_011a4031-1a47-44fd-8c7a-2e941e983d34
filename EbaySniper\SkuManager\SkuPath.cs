﻿namespace uBuyFirst.SkuManager
{
    /// <summary>
    /// Constants for SKU Manager file and folder paths
    /// </summary>
    public static class SkuPath
    {
        /// <summary>
        /// Core script files used by the SKU Manager
        /// </summary>
        public static class Scripts
        {
            public const string InternalSkuScript = "internalSkuScript.py";
            public const string InternalSkuScriptTemplate = "internalSkuScriptTemplate.py";
            public const string InternalSkuScriptInternal = "internalSkuScriptInternal.py";
            public const string InternalSkuScriptAITemplate = "internalSkuScript_AI_Template.py";
            public const string SetupScriptItems = "SetupScriptItems.bat";
            public const string PythonInstaller = "python-3.12.8-amd64.exe";
        }

        /// <summary>
        /// Configuration files for field mapping and settings
        /// </summary>
        public static class Configuration
        {
            public const string SenderFields = "SenderFields.txt";
            public const string SenderCategoryFields = "SenderCategoryFields.txt";
            public const string UniqueFields = "UniqueFields.txt";
            public const string CsvScriptPath = "CsvScriptPath.txt";
            public const string CsvScriptPart = "CsvScriptPart.txt";
            public const string RequestCallFields = "RequestCallFields.txt";
            public const string Environment = ".env";
        }

        /// <summary>
        /// Script component files for matching and display templates
        /// </summary>
        public static class Components
        {
            public const string MatchScript = "MatchScript.txt";
            public const string DisplayTemplateScript = "DisplayTemplateScript.txt";
        }

        /// <summary>
        /// AI-related files and templates
        /// </summary>
        public static class AI
        {
            public const string SystemMessage = "system_message.txt";
            public const string AnalysisResult = "analysis_result.html";
        }

        /// <summary>
        /// Subfolder names within SkuManagerScripts
        /// </summary>
        public static class Subfolders
        {
            public const string Prompts = "_prompts";
            public const string Templates = "_templates";
        }

        /// <summary>
        /// CefSharp-related subfolder names
        /// </summary>
        public static class CefSharp
        {
            public const string Profiles = "Profiles";
        }
    }
}
