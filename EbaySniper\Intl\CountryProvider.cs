﻿using System;
using System.Collections.Generic;
using System.Linq;
using eBay.Service.Core.Soap;

namespace uBuyFirst.Intl
{
    public static class CountryProvider
    {
        public static readonly Dictionary<string, string> TwoLetterToRss2Code = new Dictionary<string, string>();
        private static List<EBaySite> _eBaySites = GetEbaySites();

        public static List<string> GenerateCountryCodeList()
        {
            var countryCodes = Enum.GetNames(typeof(CountryCodeType)).ToList();
            countryCodes.Sort();
            countryCodes.RemoveAll(code => code == "US" || code == "CA" || code == "GB" || code == "CustomCode");

            countryCodes.InsertRange(0, new[] { "US", "CA", "GB" });

            return countryCodes;
        }

        public static List<string> GenerateCountryCodeListWithAny()
        {
            var countryCodes = GenerateCountryCodeList();
            countryCodes.Insert(0, "Any");

            return countryCodes;
        }

        private static List<EBaySite> GetEbaySites()
        {
            var ebaySitesRaw = new[]
            {
                "	EBAY-AT 	EBAY_AT	de-AT 	AT 	eBay Austria 	16	Austria	ebay.at	picsuffix	3	https://rover.ebay.com/rover/1/5221-53469-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229473			",
                "	EBAY-AU 	EBAY_AU	en-AU 	AU 	eBay Australia 	15	Australia	ebay.com.au	picsuffix	4	https://rover.ebay.com/rover/1/705-53470-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229515			",
                "	EBAY-CH 	EBAY_CH	de-CH 	CH 	eBay Switzerland 	193	Switzerland	ebay.ch	picsuffix	14	https://rover.ebay.com/rover/1/5222-53480-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229536			",
                "	EBAY-DE 	EBAY_DE	en-DE 	DE 	eBay Germany 	77	Germany	ebay.de	picsuffix	11	https://rover.ebay.com/rover/1/707-53477-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229487			",
                "	EBAY-ENCA 	EBAY_CA	en-CA 	CA 	eBay Canada (English) 	2	Canada	ebay.ca	picsuffix	7	https://rover.ebay.com/rover/1/706-53473-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229529			",
                "	EBAY-FRCA 	EBAY_CA	fr-CA 	CA 	eBay Canada (French) 	210	Canada	ebay.ca	picsuffix	7	https://rover.ebay.com/rover/1/706-53473-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229529			",
                "	EBAY-ES 	EBAY_ES	es-ES 	ES 	eBay Spain 	186	Spain	ebay.es	picsuffix	13	https://rover.ebay.com/rover/1/1185-53479-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229501			",
                "	EBAY-FR 	EBAY_FR	fr-FR 	FR 	eBay France 	71	France	ebay.fr	picsuffix	10	https://rover.ebay.com/rover/1/709-53476-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229480			",
                "	EBAY-FRBE 	EBAY_BE	fr-BE 	BE 	eBay Belgium (French) 	23	Belgium_French	befr.ebay.be	picsuffix	5	https://rover.ebay.com/rover/1/1553-53471-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229522			",
                "	EBAY-GB 	EBAY_GB	en-GB 	GB 	eBay UK 	3	UK	ebay.co.uk	picsuffix	15	https://rover.ebay.com/rover/1/710-53481-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229508			",
                "	EBAY-HK 	EBAY_HK	zh-Hant 	HK 	eBay Hong Kong 	201	HongKong	ebay.com.hk	picsuffix		https://rover.ebay.com/rover/1/711-53200-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229466			",
                "	EBAY-IE 	EBAY_IE	en-IE 	IE 	eBay Ireland 	205	Ireland	ebay.ie	picsuffix	2	https://rover.ebay.com/rover/1/5282-53468-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229543			",
                "	EBAY-IN 	EBAY_IN	en-IN 	IN 	eBay India 	203	India	ebay.in	picsuffix		https://rover.ebay.com/rover/1/4686-53472-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229550			",
                "	EBAY-IT 	EBAY_IT	it-IT 	IT 	eBay Italy 	101	Italy	ebay.it	picsuffix	12	https://rover.ebay.com/rover/1/724-53478-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229494			",
                "	EBAY-MOTORS 	EBAY_MOTORS_US	en-US 	US 	eBay Motors 	100	eBayMotors	ebay.com	picsuffix		https://rover.ebay.com/rover/1/711-53200-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229466			",
                "	EBAY-MY 	EBAY_MY	en-MY 	MY 	eBay Malaysia 	207	Malaysia	ebay.com.my	picsuffix		https://rover.ebay.com/rover/1/711-53200-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229466			",
                "	EBAY-NL 	EBAY_NL	nl-NL 	NL 	eBay Netherlands 	146	Netherlands	ebay.nl	picsuffix	16	https://rover.ebay.com/rover/1/1346-53482-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229557			",
                "	EBAY-NLBE 	EBAY_BE	nl-BE 	BE 	eBay Belgium (Dutch) 	123	Belgium_Dutch	benl.ebay.be	picsuffix	5	https://rover.ebay.com/rover/1/1553-53471-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229522			",
                "	EBAY-PH 	EBAY_PH	en-PH 	PH 	eBay Philippines 	211	Philippines	ebay.ph	picsuffix		https://rover.ebay.com/rover/1/711-53200-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229466			",
                "	EBAY-PL 	EBAY_PL	pl-PL 	PL 	eBay Poland 	212	Poland	ebay.pl	picsuffix		https://rover.ebay.com/rover/1/4908-226936-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229466			",
                "	EBAY-SG 	EBAY_SG	en-SG 	SG 	eBay Singapore 	216	Singapore	ebay.com.sg	picsuffix		https://rover.ebay.com/rover/1/711-53200-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229466			",
                "	EBAY-US 	EBAY_US	en-US 	US 	eBay US 	0	US	ebay.com	picsuffix	1	https://rover.ebay.com/rover/1/711-53200-19255-0/1?ff3=2&lgeo=1&campid=**********&vectorid=229466			"
            };

            return ebaySitesRaw.Select(rawstring => new EBaySite(rawstring)).ToList();
        }

        public static List<string> GetEbaySiteNameList()
        {
            return _eBaySites.Select(ebaySite => ebaySite.SiteName).ToList();
        }

        public static EBaySite GetEbaySite(string siteName)
        {
            return _eBaySites.FirstOrDefault(site => site.SiteName == siteName);
        }
        public static EBaySite GetEbaySiteByGlobalID(string globalID)
        {
            return _eBaySites.FirstOrDefault(site => site.GlobalID == globalID);
        }
        public static EBaySite GetEbaySiteByID(string siteID)
        {
            return _eBaySites.FirstOrDefault(site => site.SiteID == siteID);
        }

        #region TwoLetter2Rss2Code

        public static void SetTwoLetterToRss2Code()
        {
            TwoLetterToRss2Code.Clear();
            TwoLetterToRss2Code.Add("AF", "4");
            TwoLetterToRss2Code.Add("AL", "5");
            TwoLetterToRss2Code.Add("DZ", "6");
            TwoLetterToRss2Code.Add("AS", "7");
            TwoLetterToRss2Code.Add("AD", "8");
            TwoLetterToRss2Code.Add("AO", "9");
            TwoLetterToRss2Code.Add("AI", "10");
            TwoLetterToRss2Code.Add("AG", "11");
            TwoLetterToRss2Code.Add("AR", "12");
            TwoLetterToRss2Code.Add("AM", "13");
            TwoLetterToRss2Code.Add("AW", "14");
            TwoLetterToRss2Code.Add("AU", "15");
            TwoLetterToRss2Code.Add("AT", "16");
            TwoLetterToRss2Code.Add("AZ", "17");
            TwoLetterToRss2Code.Add("BS", "18");
            TwoLetterToRss2Code.Add("BH", "19");
            TwoLetterToRss2Code.Add("BD", "20");
            TwoLetterToRss2Code.Add("BB", "21");
            TwoLetterToRss2Code.Add("BY", "22");
            TwoLetterToRss2Code.Add("BE", "23");
            TwoLetterToRss2Code.Add("BZ", "24");
            TwoLetterToRss2Code.Add("BJ", "25");
            TwoLetterToRss2Code.Add("BM", "26");
            TwoLetterToRss2Code.Add("BT", "27");
            TwoLetterToRss2Code.Add("BO", "28");
            TwoLetterToRss2Code.Add("BA", "29");
            TwoLetterToRss2Code.Add("BW", "30");
            TwoLetterToRss2Code.Add("BR", "31");
            TwoLetterToRss2Code.Add("VG", "32");
            TwoLetterToRss2Code.Add("BN", "33");
            TwoLetterToRss2Code.Add("BG", "34");
            TwoLetterToRss2Code.Add("BF", "35");
            TwoLetterToRss2Code.Add("MM", "36");
            TwoLetterToRss2Code.Add("BI", "37");
            TwoLetterToRss2Code.Add("KH", "38");
            TwoLetterToRss2Code.Add("CM", "39");
            TwoLetterToRss2Code.Add("CA", "2");
            TwoLetterToRss2Code.Add("CV", "40");
            TwoLetterToRss2Code.Add("KY", "41");
            TwoLetterToRss2Code.Add("CF", "42");
            TwoLetterToRss2Code.Add("TD", "43");
            TwoLetterToRss2Code.Add("CL", "44");
            TwoLetterToRss2Code.Add("CN", "45");
            TwoLetterToRss2Code.Add("CO", "46");
            TwoLetterToRss2Code.Add("KM", "47");
            TwoLetterToRss2Code.Add("CD", "48");
            TwoLetterToRss2Code.Add("CG", "49");
            TwoLetterToRss2Code.Add("CK", "50");
            TwoLetterToRss2Code.Add("CR", "51");
            TwoLetterToRss2Code.Add("CI", "52");
            TwoLetterToRss2Code.Add("HR", "53");
            TwoLetterToRss2Code.Add("CY", "55");
            TwoLetterToRss2Code.Add("CZ", "56");
            TwoLetterToRss2Code.Add("DK", "57");
            TwoLetterToRss2Code.Add("DJ", "58");
            TwoLetterToRss2Code.Add("DM", "59");
            TwoLetterToRss2Code.Add("DO", "60");
            TwoLetterToRss2Code.Add("EC", "61");
            TwoLetterToRss2Code.Add("EG", "62");
            TwoLetterToRss2Code.Add("SV", "63");
            TwoLetterToRss2Code.Add("GQ", "64");
            TwoLetterToRss2Code.Add("ER", "65");
            TwoLetterToRss2Code.Add("EE", "66");
            TwoLetterToRss2Code.Add("ET", "67");
            TwoLetterToRss2Code.Add("FK", "68");
            TwoLetterToRss2Code.Add("FJ", "69");
            TwoLetterToRss2Code.Add("FI", "70");
            TwoLetterToRss2Code.Add("FR", "71");
            TwoLetterToRss2Code.Add("GF", "72");
            TwoLetterToRss2Code.Add("PF", "73");
            TwoLetterToRss2Code.Add("GA", "74");
            TwoLetterToRss2Code.Add("GM", "75");
            TwoLetterToRss2Code.Add("GE", "76");
            TwoLetterToRss2Code.Add("DE", "77");
            TwoLetterToRss2Code.Add("GH", "78");
            TwoLetterToRss2Code.Add("GI", "79");
            TwoLetterToRss2Code.Add("GR", "80");
            TwoLetterToRss2Code.Add("GL", "81");
            TwoLetterToRss2Code.Add("GD", "82");
            TwoLetterToRss2Code.Add("GP", "83");
            TwoLetterToRss2Code.Add("GU", "84");
            TwoLetterToRss2Code.Add("GT", "85");
            TwoLetterToRss2Code.Add("GG", "86");
            TwoLetterToRss2Code.Add("GN", "87");
            TwoLetterToRss2Code.Add("GW", "88");
            TwoLetterToRss2Code.Add("GY", "89");
            TwoLetterToRss2Code.Add("HT", "90");
            TwoLetterToRss2Code.Add("HN", "91");
            TwoLetterToRss2Code.Add("HK", "92");
            TwoLetterToRss2Code.Add("HU", "93");
            TwoLetterToRss2Code.Add("IS", "94");
            TwoLetterToRss2Code.Add("IN", "95");
            TwoLetterToRss2Code.Add("ID", "96");
            TwoLetterToRss2Code.Add("IE", "99");
            TwoLetterToRss2Code.Add("IL", "100");
            TwoLetterToRss2Code.Add("IT", "101");
            TwoLetterToRss2Code.Add("JM", "102");
            TwoLetterToRss2Code.Add("JP", "104");
            TwoLetterToRss2Code.Add("JE", "105");
            TwoLetterToRss2Code.Add("JO", "106");
            TwoLetterToRss2Code.Add("KZ", "107");
            TwoLetterToRss2Code.Add("KE", "108");
            TwoLetterToRss2Code.Add("KI", "109");
            TwoLetterToRss2Code.Add("KR", "111");
            TwoLetterToRss2Code.Add("KW", "112");
            TwoLetterToRss2Code.Add("KG", "113");
            TwoLetterToRss2Code.Add("LA", "114");
            TwoLetterToRss2Code.Add("LV", "115");
            TwoLetterToRss2Code.Add("LB", "116");
            TwoLetterToRss2Code.Add("LI", "120");
            TwoLetterToRss2Code.Add("LT", "121");
            TwoLetterToRss2Code.Add("LU", "122");
            TwoLetterToRss2Code.Add("MO", "123");
            TwoLetterToRss2Code.Add("MK", "124");
            TwoLetterToRss2Code.Add("MG", "125");
            TwoLetterToRss2Code.Add("MW", "126");
            TwoLetterToRss2Code.Add("MY", "127");
            TwoLetterToRss2Code.Add("MV", "128");
            TwoLetterToRss2Code.Add("ML", "129");
            TwoLetterToRss2Code.Add("MT", "130");
            TwoLetterToRss2Code.Add("MH", "131");
            TwoLetterToRss2Code.Add("MQ", "132");
            TwoLetterToRss2Code.Add("MR", "133");
            TwoLetterToRss2Code.Add("MU", "134");
            TwoLetterToRss2Code.Add("YT", "135");
            TwoLetterToRss2Code.Add("MX", "136");
            TwoLetterToRss2Code.Add("FM", "226");
            TwoLetterToRss2Code.Add("MD", "137");
            TwoLetterToRss2Code.Add("MC", "138");
            TwoLetterToRss2Code.Add("MN", "139");
            TwoLetterToRss2Code.Add("ME", "228");
            TwoLetterToRss2Code.Add("MS", "140");
            TwoLetterToRss2Code.Add("MA", "141");
            TwoLetterToRss2Code.Add("MZ", "142");
            TwoLetterToRss2Code.Add("NA", "143");
            TwoLetterToRss2Code.Add("NR", "144");
            TwoLetterToRss2Code.Add("NP", "145");
            TwoLetterToRss2Code.Add("NL", "146");
            TwoLetterToRss2Code.Add("AN", "147");
            TwoLetterToRss2Code.Add("NC", "148");
            TwoLetterToRss2Code.Add("NZ", "149");
            TwoLetterToRss2Code.Add("NI", "150");
            TwoLetterToRss2Code.Add("NE", "151");
            TwoLetterToRss2Code.Add("NG", "152");
            TwoLetterToRss2Code.Add("NU", "153");
            TwoLetterToRss2Code.Add("NO", "154");
            TwoLetterToRss2Code.Add("OM", "155");
            TwoLetterToRss2Code.Add("PK", "156");
            TwoLetterToRss2Code.Add("PW", "157");
            TwoLetterToRss2Code.Add("PA", "158");
            TwoLetterToRss2Code.Add("PG", "159");
            TwoLetterToRss2Code.Add("PY", "160");
            TwoLetterToRss2Code.Add("PE", "161");
            TwoLetterToRss2Code.Add("PH", "162");
            TwoLetterToRss2Code.Add("PL", "163");
            TwoLetterToRss2Code.Add("PT", "164");
            TwoLetterToRss2Code.Add("PR", "165");
            TwoLetterToRss2Code.Add("QA", "166");
            TwoLetterToRss2Code.Add("RE", "227");
            TwoLetterToRss2Code.Add("RO", "167");
            TwoLetterToRss2Code.Add("RU", "168");
            TwoLetterToRss2Code.Add("RW", "169");
            TwoLetterToRss2Code.Add("SH", "170");
            TwoLetterToRss2Code.Add("KN", "171");
            TwoLetterToRss2Code.Add("LC", "172");
            TwoLetterToRss2Code.Add("PM", "173");
            TwoLetterToRss2Code.Add("VC", "174");
            TwoLetterToRss2Code.Add("SM", "175");
            TwoLetterToRss2Code.Add("SA", "176");
            TwoLetterToRss2Code.Add("SN", "177");
            TwoLetterToRss2Code.Add("RS", "229");
            TwoLetterToRss2Code.Add("SC", "178");
            TwoLetterToRss2Code.Add("SL", "179");
            TwoLetterToRss2Code.Add("SG", "180");
            TwoLetterToRss2Code.Add("SK", "181");
            TwoLetterToRss2Code.Add("SI", "182");
            TwoLetterToRss2Code.Add("SB", "183");
            TwoLetterToRss2Code.Add("SO", "184");
            TwoLetterToRss2Code.Add("ZA", "185");
            TwoLetterToRss2Code.Add("ES", "186");
            TwoLetterToRss2Code.Add("LK", "187");
            TwoLetterToRss2Code.Add("SR", "189");
            TwoLetterToRss2Code.Add("SZ", "191");
            TwoLetterToRss2Code.Add("SE", "192");
            TwoLetterToRss2Code.Add("CH", "193");
            TwoLetterToRss2Code.Add("TW", "196");
            TwoLetterToRss2Code.Add("TJ", "197");
            TwoLetterToRss2Code.Add("TZ", "198");
            TwoLetterToRss2Code.Add("TH", "199");
            TwoLetterToRss2Code.Add("TG", "200");
            TwoLetterToRss2Code.Add("TO", "201");
            TwoLetterToRss2Code.Add("TT", "202");
            TwoLetterToRss2Code.Add("TN", "203");
            TwoLetterToRss2Code.Add("TR", "204");
            TwoLetterToRss2Code.Add("TM", "205");
            TwoLetterToRss2Code.Add("TC", "206");
            TwoLetterToRss2Code.Add("TV", "207");
            TwoLetterToRss2Code.Add("UG", "208");
            TwoLetterToRss2Code.Add("UA", "209");
            TwoLetterToRss2Code.Add("AE", "210");
            TwoLetterToRss2Code.Add("GB", "3");
            TwoLetterToRss2Code.Add("US", "1");
            TwoLetterToRss2Code.Add("UY", "211");
            TwoLetterToRss2Code.Add("UZ", "212");
            TwoLetterToRss2Code.Add("VU", "213");
            TwoLetterToRss2Code.Add("VA", "214");
            TwoLetterToRss2Code.Add("VE", "215");
            TwoLetterToRss2Code.Add("VN", "216");
            TwoLetterToRss2Code.Add("VI", "217");
            TwoLetterToRss2Code.Add("WF", "218");
            TwoLetterToRss2Code.Add("EH", "219");
            TwoLetterToRss2Code.Add("WS", "220");
            TwoLetterToRss2Code.Add("YE", "221");
            TwoLetterToRss2Code.Add("ZM", "223");
            TwoLetterToRss2Code.Add("ZW", "224");
        }

        #endregion TwoLetter2Rss2Code

        public static string[] DigitCodeAndFullCountryName =
        {
            "4", "Afghanistan", "5", "Albania", "6", "Algeria", "7", "American Samoa", "8", "Andorra", "9", "Angola", "10", "Anguilla", "11", "Antigua and Barbuda", "12", "Argentina", "13",
            "Armenia", "14", "Aruba", "15", "Australia", "16", "Austria", "17", "Azerbaijan Republic", "18", "Bahamas", "19", "Bahrain", "20", "Bangladesh", "21", "Barbados", "22", "Belarus",
            "23", "Belgium", "24", "Belize", "25", "Benin", "26", "Bermuda", "27", "Bhutan", "28", "Bolivia", "29", "Bosnia and Herzegovina", "30", "Botswana", "31", "Brazil", "32",
            "British Virgin Islands", "33", "Brunei Darussalam", "34", "Bulgaria", "35", "Burkina Faso", "37", "Burundi", "38", "Cambodia", "39", "Cameroon", "2", "Canada", "40",
            "Cape Verde Islands", "41", "Cayman Islands", "42", "Central African Republic", "43", "Chad", "44", "Chile", "45", "China", "46", "Colombia", "47", "Comoros", "48",
            "Congo, Democratic Republic of the", "49", "Congo, Republic of the", "50", "Cook Islands", "51", "Costa Rica", "52", "Cote d Ivoire (Ivory Coast)", "53", "Croatia, Republic of", "55",
            "Cyprus", "56", "Czech Republic", "57", "Denmark", "58", "Djibouti", "59", "Dominica", "60", "Dominican Republic", "61", "Ecuador", "62", "Egypt", "63", "El Salvador", "64",
            "Equatorial Guinea", "65", "Eritrea", "66", "Estonia", "67", "Ethiopia", "68", "Falkland Islands (Islas Malvinas)", "69", "Fiji", "70", "Finland", "71", "France", "72",
            "French Guiana", "73", "French Polynesia", "74", "Gabon Republic", "75", "Gambia", "76", "Georgia", "77", "Germany", "78", "Ghana", "79", "Gibraltar", "80", "Greece", "81",
            "Greenland", "82", "Grenada", "83", "Guadeloupe", "84", "Guam", "85", "Guatemala", "86", "Guernsey", "87", "Guinea", "88", "Guinea-Bissau", "89", "Guyana", "90", "Haiti", "91",
            "Honduras", "92", "Hong Kong", "93", "Hungary", "94", "Iceland", "95", "India", "96", "Indonesia", "98", "Iraq", "99", "Ireland", "100", "Israel", "101", "Italy", "102", "Jamaica",
            "104", "Japan", "105", "Jersey", "106", "Jordan", "107", "Kazakhstan", "108", "Kenya", "109", "Kiribati", "111", "Korea, South", "112", "Kuwait", "113", "Kyrgyzstan", "114", "Laos",
            "115", "Latvia", "116", "Lebanon", "117", "Lesotho", "118", "Liberia", "119", "Libya", "120", "Liechtenstein", "121", "Lithuania", "122", "Luxembourg", "123", "Macau", "124",
            "Macedonia", "125", "Madagascar", "126", "Malawi", "127", "Malaysia", "128", "Maldives", "129", "Mali", "130", "Malta", "131", "Marshall Islands", "132", "Martinique", "133",
            "Mauritania", "134", "Mauritius", "135", "Mayotte", "136", "Mexico", "226", "Micronesia", "137", "Moldova", "138", "Monaco", "139", "Mongolia", "228", "Montenegro", "140",
            "Montserrat", "141", "Morocco", "142", "Mozambique", "143", "Namibia", "144", "Nauru", "145", "Nepal", "146", "Netherlands", "147", "Netherlands Antilles", "148", "New Caledonia",
            "149", "New Zealand", "150", "Nicaragua", "151", "Niger", "152", "Nigeria", "153", "Niue", "154", "Norway", "155", "Oman", "156", "Pakistan", "157", "Palau", "158", "Panama", "159",
            "Papua New Guinea", "160", "Paraguay", "161", "Peru", "162", "Philippines", "163", "Poland", "164", "Portugal", "165", "Puerto Rico", "166", "Qatar", "227", "Reunion", "167",
            "Romania", "168", "Russian Federation", "169", "Rwanda", "170", "Saint Helena", "171", "Saint Kitts-Nevis", "172", "Saint Lucia", "173", "Saint Pierre and Miquelon", "174",
            "Saint Vincent and the Grenadines", "175", "San Marino", "176", "Saudi Arabia", "177", "Senegal", "229", "Serbia", "178", "Seychelles", "179", "Sierra Leone", "180", "Singapore",
            "181", "Slovakia", "182", "Slovenia", "183", "Solomon Islands", "184", "Somalia", "185", "South Africa", "186", "Spain", "187", "Sri Lanka", "189", "Suriname", "191", "Swaziland",
            "192", "Sweden", "193", "Switzerland", "196", "Taiwan", "197", "Tajikistan", "198", "Tanzania", "199", "Thailand", "200", "Togo", "201", "Tonga", "202", "Trinidad and Tobago", "203",
            "Tunisia", "204", "Turkey", "205", "Turkmenistan", "206", "Turks and Caicos Islands", "207", "Tuvalu", "208", "Uganda", "209", "Ukraine", "210", "United Arab Emirates", "3",
            "United Kingdom", "1", "United States", "211", "Uruguay", "212", "Uzbekistan", "213", "Vanuatu", "214", "Vatican City State", "215", "Venezuela", "216", "Vietnam", "217",
            "Virgin Islands (U.S.)", "218", "Wallis and Futuna", "219", "Western Sahara", "220", "Western Samoa", "221", "Yemen", "223", "Zambia", "224", "Zimbabwe",
        };
    }
}
