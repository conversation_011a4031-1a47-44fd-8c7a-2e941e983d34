//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace TaxonomyAPI
{
	using System;
	using System.Linq;
	using System.Collections.Generic;
	using System.Threading.Tasks;
	using System.Net.Http;
	using Newtonsoft.Json;
	using Fonlow.Net.Http;
	
	
	/// <summary>
	/// This type contains information about one of the ancestors of a suggested category. An ordered list of these references describes the path from the suggested category to the root of the category tree it belongs to.
	/// </summary>
	[System.Runtime.Serialization.DataContract(Namespace="http://taxonomyapi.ebay/2020/03")]
	public class AncestorReference
	{
		
		/// <summary>
		/// The unique identifier of the eBay ancestor category. Note: The root node of a full default category tree includes the categoryId field, but its value should not be relied upon. It provides no useful information for application development.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="categoryId")]
		public string CategoryId { get; set; }
		
		/// <summary>
		/// The name of the ancestor category identified by categoryId.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="categoryName")]
		public string CategoryName { get; set; }
		
		/// <summary>
		/// The href portion of the getCategorySubtree call that retrieves the subtree below the ancestor category node.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="categorySubtreeNodeHref")]
		public string CategorySubtreeNodeHref { get; set; }
		
		/// <summary>
		/// The absolute level of the ancestor category node in the hierarchy of its category tree. Note: The root node of any full category tree is always at level 0.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="categoryTreeNodeLevel")]
		public System.Nullable<System.Int32> CategoryTreeNodeLevel { get; set; }
	}
	
	/// <summary>
	/// This type contains information about an item attribute (for example, color) that is appropriate or necessary for accurately describing items in a particular leaf category. Sellers are required or encouraged to provide one or more values of this aspect when offering an item in that category on eBay.
	/// </summary>
	[System.Runtime.Serialization.DataContract(Namespace="http://taxonomyapi.ebay/2020/03")]
	public class Aspect
	{
		
		/// <summary>
		/// This type contains information about the formatting, occurrence, and support of an aspect.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="aspectConstraint")]
		public AspectConstraint AspectConstraint { get; set; }
		
		/// <summary>
		/// A list of valid values for this aspect (for example: Red, Green, and Blue), along with any constraints on those values.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="aspectValues")]
		public AspectValue[] AspectValues { get; set; }
		
		/// <summary>
		/// The localized name of this aspect (for example: Colour on the eBay UK site). Note: This name is always localized for the specified marketplace.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="localizedAspectName")]
		public string LocalizedAspectName { get; set; }
		
		/// <summary>
		/// The relevance of this aspect. This field is returned if eBay has data on how many searches have been performed for listings in the category using this item aspect. Note: This container is restricted to applications that have been granted permission to access this feature. You must submit an App Check ticket to request this access. In the App Check form, add a note to the Application Title/Summary and/or Application Details fields that you want access to 'Buyer Demand Data' in the Taxonomy API.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="relevanceIndicator")]
		public RelevanceIndicator RelevanceIndicator { get; set; }
	}
	
	/// <summary>
	/// This type contains information about the formatting, occurrence, and support of an aspect.
	/// </summary>
	[System.Runtime.Serialization.DataContract(Namespace="http://taxonomyapi.ebay/2020/03")]
	public class AspectConstraint
	{
		
		/// <summary>
		/// This value indicate if the aspect identified by the aspects.localizedAspectName field is a product aspect (relevant to catalog products in the category) or an item/instance aspect, which is an aspect whose value will vary based on a particular instance of the product.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="aspectApplicableTo")]
		public string[] AspectApplicableTo { get; set; }
		
		/// <summary>
		/// The data type of this aspect. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/commerce/taxonomy/types/txn:AspectDataTypeEnum'>eBay API documentation</a>
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="aspectDataType")]
		public string AspectDataType { get; set; }
		
		/// <summary>
		/// A value of true indicates that this aspect can be used to help identify item variations.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="aspectEnabledForVariations")]
		public System.Nullable<System.Boolean> AspectEnabledForVariations { get; set; }
		
		/// <summary>
		/// Returned only if the value of aspectDataType identifies a data type that requires specific formatting. Currently, this field provides formatting hints as follows: DATE: YYYY, YYYYMM, YYYYMMDD NUMBER: int32, double
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="aspectFormat")]
		public string AspectFormat { get; set; }
		
		/// <summary>
		/// The maximum length of the item/instance aspect's value. The seller must make sure not to exceed this length when specifying the instance aspect's value for a product. This field is only returned for instance aspects.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="aspectMaxLength")]
		public System.Nullable<System.Int32> AspectMaxLength { get; set; }
		
		/// <summary>
		/// The manner in which values of this aspect must be specified by the seller (as free text or by selecting from available options). For implementation help, refer to <a href='https://developer.ebay.com/api-docs/commerce/taxonomy/types/txn:AspectModeEnum'>eBay API documentation</a>
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="aspectMode")]
		public string AspectMode { get; set; }
		
		/// <summary>
		/// A value of true indicates that this aspect is required when offering items in the specified category.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="aspectRequired")]
		public System.Nullable<System.Boolean> AspectRequired { get; set; }
		
		/// <summary>
		/// The enumeration value returned in this field will indicate if the corresponding aspect is recommended or optional. Note: This field is always returned, even for hard-mandated/required aspects (where aspectRequired: true). The value returned for required aspects will be RECOMMENDED, but they are actually required and a seller will be blocked from listing or revising an item without these aspects. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/commerce/taxonomy/types/txn:AspectUsageEnum'>eBay API documentation</a>
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="aspectUsage")]
		public string AspectUsage { get; set; }
		
		/// <summary>
		/// The expected date after which the aspect will be required. Note: The value returned in this field specifies only an approximate date, which may not reflect the actual date after which the aspect is required.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="expectedRequiredByDate")]
		public string ExpectedRequiredByDate { get; set; }
		
		/// <summary>
		/// Indicates whether this aspect can accept single or multiple values for items in the specified category. For implementation help, refer to <a href='https://developer.ebay.com/api-docs/commerce/taxonomy/types/txn:ItemToAspectCardinalityEnum'>eBay API documentation</a>
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="itemToAspectCardinality")]
		public string ItemToAspectCardinality { get; set; }
	}
	
	/// <summary>
	/// This type contains a valid value for an aspect, along with any constraints on the occurrence of that value.
	/// </summary>
	[System.Runtime.Serialization.DataContract(Namespace="http://taxonomyapi.ebay/2020/03")]
	public class AspectValue
	{
		
		/// <summary>
		/// The localized value of this aspect. Note: This value is always localized for the specified marketplace.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="localizedValue")]
		public string LocalizedValue { get; set; }
		
		/// <summary>
		/// Not returned if the value of the localizedValue field can always be selected for this aspect of the specified category. Contains a list of the dependencies that identify when the value of the localizedValue field is available for the current aspect. Each dependency specifies the values of another aspect of the same category (a control aspect), for which the current value of the current aspect can also be selected by the seller. Example: A shirt is available in three sizes and three colors, but only the Small and Medium sizes come in Green. Thus for the Color aspect, the value Green is constrained by its dependency on Size (the control aspect). Only when the Size aspect value is Small or Medium, can the Color aspect value of Green be selected by the seller.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="valueConstraints")]
		public ValueConstraint[] ValueConstraints { get; set; }
	}
	
	/// <summary>
	/// This type contains a list of the dependencies that identify when a particular value is available for a given aspect of a given category. Each dependency specifies the values of another aspect of the same category (the control aspect), for which the given value of the given aspect can also be selected by the seller. This container consists of constraint information for the corresponding product aspect value.
	/// </summary>
	[System.Runtime.Serialization.DataContract(Namespace="http://taxonomyapi.ebay/2020/03")]
	public class ValueConstraint
	{
		
		/// <summary>
		/// The name of the control aspect on which the current aspect value depends.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="applicableForLocalizedAspectName")]
		public string ApplicableForLocalizedAspectName { get; set; }
		
		/// <summary>
		/// Contains a list of the values of the control aspect on which this aspect's value depends. When the control aspect has any of the specified values, the current value of the current aspect will also be available.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="applicableForLocalizedAspectValues")]
		public string[] ApplicableForLocalizedAspectValues { get; set; }
	}
	
	/// <summary>
	/// The relevance of this aspect. This field is returned if eBay has data on how many searches have been performed for listings in the category using this item aspect. Note: This container is restricted to applications that have been granted permission to access this feature. You must submit an App Check ticket to request this access. In the App Check form, add a note to the Application Title/Summary and/or Application Details fields that you want access to 'Buyer Demand Data' in the Taxonomy API.
	/// </summary>
	[System.Runtime.Serialization.DataContract(Namespace="http://taxonomyapi.ebay/2020/03")]
	public class RelevanceIndicator
	{
		
		/// <summary>
		/// The number of recent searches (based on 30 days of data) for the aspect.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="searchCount")]
		public System.Nullable<System.Int32> SearchCount { get; set; }
	}
	
	/// <summary>
	/// This type is the container type for the response payload of the getItemAspectsForCategory call.
	/// </summary>
	[System.Runtime.Serialization.DataContract(Namespace="http://taxonomyapi.ebay/2020/03")]
	public class AspectMetadata
	{
		
		/// <summary>
		/// A list of item aspects (for example, color) that are appropriate or necessary for accurately describing items in a particular leaf category. Each category has a different set of aspects and different requirements for aspect values. Sellers are required or encouraged to provide one or more acceptable values for each aspect when offering an item in that category on eBay.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="aspects")]
		public Aspect[] Aspects { get; set; }
	}
	
	/// <summary>
	/// This type contains identifying information for the category tree associated with a particular eBay marketplace.
	/// </summary>
	[System.Runtime.Serialization.DataContract(Namespace="http://taxonomyapi.ebay/2020/03")]
	public class BaseCategoryTree
	{
		
		/// <summary>
		/// The unique identifier of the eBay category tree for the specified marketplace.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="categoryTreeId")]
		public string CategoryTreeId { get; set; }
		
		/// <summary>
		/// The version of the category tree identified by categoryTreeId. It's a good idea to cache this value for comparison so you can determine if this category tree has been modified in subsequent calls.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="categoryTreeVersion")]
		public string CategoryTreeVersion { get; set; }
	}
	
	/// <summary>
	/// This type contains information about a particular eBay category.
	/// </summary>
	[System.Runtime.Serialization.DataContract(Namespace="http://taxonomyapi.ebay/2020/03")]
	public class Category
	{
		
		/// <summary>
		/// The unique identifier of the eBay category within its category tree. Note: The root node of a full default category tree includes the categoryId field, but its value should not be relied upon. It provides no useful information for application development.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="categoryId")]
		public string CategoryId { get; set; }
		
		/// <summary>
		/// The name of the category identified by categoryId.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="categoryName")]
		public string CategoryName { get; set; }
	}
	
	[System.Runtime.Serialization.DataContract(Namespace="http://taxonomyapi.ebay/2020/03")]
	public class CategoryAspect
	{
		
		/// <summary>
		/// This type contains information about a particular eBay category.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="category")]
		public Category Category { get; set; }
		
		/// <summary>
		/// A list of aspect metadata that is used to describe the items in a particular leaf category.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="aspects")]
		public Aspect[] Aspects { get; set; }
	}
	
	/// <summary>
	/// This type contains information about a particular subtree of a specified eBay category tree. A category subtree consists of a non-root node of the category tree, and all of its descendants down to the leaf nodes.
	/// </summary>
	[System.Runtime.Serialization.DataContract(Namespace="http://taxonomyapi.ebay/2020/03")]
	public class CategorySubtree
	{
		
		/// <summary>
		/// This type contains information about all nodes of a category tree or subtree hierarchy, including and below the specified Category, down to the leaf nodes. It is a recursive structure.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="categorySubtreeNode")]
		public CategoryTreeNode CategorySubtreeNode { get; set; }
		
		/// <summary>
		/// The unique identifier of the eBay category tree to which this subtree belongs.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="categoryTreeId")]
		public string CategoryTreeId { get; set; }
		
		/// <summary>
		/// The version of the category tree identified by categoryTreeId. It's a good idea to cache this value for comparison so you can determine if this category tree has been modified in subsequent calls.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="categoryTreeVersion")]
		public string CategoryTreeVersion { get; set; }
	}
	
	/// <summary>
	/// This type contains information about all nodes of a category tree or subtree hierarchy, including and below the specified Category, down to the leaf nodes. It is a recursive structure.
	/// </summary>
	[System.Runtime.Serialization.DataContract(Namespace="http://taxonomyapi.ebay/2020/03")]
	public class CategoryTreeNode
	{
		
		/// <summary>
		/// This type contains information about a particular eBay category.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="category")]
		public Category Category { get; set; }
		
		/// <summary>
		/// The absolute level of the current category tree node in the hierarchy of its category tree. Note: The root node of any full category tree is always at level 0.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="categoryTreeNodeLevel")]
		public System.Nullable<System.Int32> CategoryTreeNodeLevel { get; set; }
		
		/// <summary>
		/// An array of one or more category tree nodes that are the immediate children of the current category tree node, as well as their children, recursively down to the leaf nodes. Returned only if the current category tree node is not a leaf node (the value of leafCategoryTreeNode is false).
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="childCategoryTreeNodes")]
		public CategoryTreeNode[] ChildCategoryTreeNodes { get; set; }
		
		/// <summary>
		/// A value of true indicates that the current category tree node is a leaf node (it has no child nodes). A value of false indicates that the current node has one or more child nodes, which are identified by the childCategoryTreeNodes array. Returned only if the value of this field is true.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="leafCategoryTreeNode")]
		public System.Nullable<System.Boolean> LeafCategoryTreeNode { get; set; }
		
		/// <summary>
		/// The href portion of the getCategorySubtree call that retrieves the subtree below the parent of this category tree node. Not returned if the current category tree node is the root node of its tree.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="parentCategoryTreeNodeHref")]
		public string ParentCategoryTreeNodeHref { get; set; }
	}
	
	/// <summary>
	/// This type contains information about a suggested category tree leaf node that corresponds to keywords provided in the request. It includes details about each of the category's ancestor nodes extending up to the root of the category tree.
	/// </summary>
	[System.Runtime.Serialization.DataContract(Namespace="http://taxonomyapi.ebay/2020/03")]
	public class CategorySuggestion
	{
		
		/// <summary>
		/// This type contains information about a particular eBay category.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="category")]
		public Category Category { get; set; }
		
		/// <summary>
		/// An ordered list of category references that describes the location of the suggested category in the specified category tree. The list identifies the category's ancestry as a sequence of parent nodes, from the current node's immediate parent to the root node of the category tree. Note: The root node of a full default category tree includes categoryId and categoryName fields, but their values should not be relied upon. They provide no useful information for application development.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="categoryTreeNodeAncestors")]
		public AncestorReference[] CategoryTreeNodeAncestors { get; set; }
		
		/// <summary>
		/// The absolute level of the category tree node in the hierarchy of its category tree. Note: The root node of any full category tree is always at level 0.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="categoryTreeNodeLevel")]
		public System.Nullable<System.Int32> CategoryTreeNodeLevel { get; set; }
		
		/// <summary>
		/// This field is reserved for internal or future use.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="relevancy")]
		public string Relevancy { get; set; }
	}
	
	/// <summary>
	/// This type contains an array of suggested category tree nodes that are considered by eBay to most closely correspond to the keywords provided in a query string, from a specified category tree.
	/// </summary>
	[System.Runtime.Serialization.DataContract(Namespace="http://taxonomyapi.ebay/2020/03")]
	public class CategorySuggestionResponse
	{
		
		/// <summary>
		/// Contains details about one or more suggested categories that correspond to the provided keywords. The array of suggested categories is sorted in order of eBay's confidence of the relevance of each category (the first category is the most relevant). Important: This call is not supported in the Sandbox environment. It will return a response payload in which the categoryName fields contain random or boilerplate text regardless of the query submitted.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="categorySuggestions")]
		public CategorySuggestion[] CategorySuggestions { get; set; }
		
		/// <summary>
		/// The unique identifier of the eBay category tree from which suggestions are returned.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="categoryTreeId")]
		public string CategoryTreeId { get; set; }
		
		/// <summary>
		/// The version of the category tree identified by categoryTreeId. It's a good idea to cache this value for comparison so you can determine if this category tree has been modified in subsequent calls.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="categoryTreeVersion")]
		public string CategoryTreeVersion { get; set; }
	}
	
	/// <summary>
	/// This type contains information about all nodes of a specified eBay category tree.
	/// </summary>
	[System.Runtime.Serialization.DataContract(Namespace="http://taxonomyapi.ebay/2020/03")]
	public class CategoryTree
	{
		
		/// <summary>
		/// A list of one or more identifiers of the eBay marketplaces that use this category tree.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="applicableMarketplaceIds")]
		public string[] ApplicableMarketplaceIds { get; set; }
		
		/// <summary>
		/// The unique identifier of this eBay category tree.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="categoryTreeId")]
		public string CategoryTreeId { get; set; }
		
		/// <summary>
		/// The version of this category tree. It's a good idea to cache this value for comparison so you can determine if this category tree has been modified in subsequent calls.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="categoryTreeVersion")]
		public string CategoryTreeVersion { get; set; }
		
		/// <summary>
		/// This type contains information about all nodes of a category tree or subtree hierarchy, including and below the specified Category, down to the leaf nodes. It is a recursive structure.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="rootCategoryNode")]
		public CategoryTreeNode RootCategoryNode { get; set; }
	}
	
	/// <summary>
	/// This type is used by the compatibilityProperties array that is returned in the getCompatibilityProperties call. The compatibilityProperties container consists of an array of all compatible vehicle properties applicable to the specified eBay marketplace and eBay category ID.
	/// </summary>
	[System.Runtime.Serialization.DataContract(Namespace="http://taxonomyapi.ebay/2020/03")]
	public class CompatibilityProperty
	{
		
		/// <summary>
		/// This is the actual name of the compatible vehicle property as it is known on the specified eBay marketplace and in the eBay category. This is the string value that should be used in the compatibility_property and filter query parameters of a getCompatibilityPropertyValues request URI. Typical vehicle properties are 'Make', 'Model', 'Year', 'Engine', and 'Trim', but will vary based on the eBay marketplace and the eBay category.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="name")]
		public string Name { get; set; }
		
		/// <summary>
		/// This is the localized name of the compatible vehicle property. The language that is used will depend on the user making the call, or based on the language specified if the Content-Language HTTP header is used. In some instances, the string value in this field may be the same as the string in the corresponding name field.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="localizedName")]
		public string LocalizedName { get; set; }
	}
	
	/// <summary>
	/// This type is used by the compatibilityPropertyValues array that is returned in the getCompatibilityPropertyValues response. The compatibilityPropertyValues array contains all compatible vehicle property values that match the specified eBay marketplace, specified eBay category, and filters in the request. If the compatibility_property parameter value in the request is 'Trim', each value returned in each value field will be a different vehicle trim, applicable to any filters that are set in the filter query parameter of the request, and also based on the eBay marketplace and category specified in the call request.
	/// </summary>
	[System.Runtime.Serialization.DataContract(Namespace="http://taxonomyapi.ebay/2020/03")]
	public class CompatibilityPropertyValue
	{
		
		/// <summary>
		/// Each value field shows one applicable compatible vehicle property value. The values that are returned will depend on the specified eBay marketplace, specified eBay category, and filters in the request.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="value")]
		public string Value { get; set; }
	}
	
	/// <summary>
	/// This type defines the fields that can be returned in an error.
	/// </summary>
	[System.Runtime.Serialization.DataContract(Namespace="http://taxonomyapi.ebay/2020/03")]
	public class Error
	{
		
		/// <summary>
		/// Identifies the type of erro.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="category")]
		public string Category { get; set; }
		
		/// <summary>
		/// Name for the primary system where the error occurred. This is relevant for application errors.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="domain")]
		public string Domain { get; set; }
		
		/// <summary>
		/// A unique number to identify the error.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="errorId")]
		public System.Nullable<System.Int32> ErrorId { get; set; }
		
		/// <summary>
		/// An array of request elements most closely associated to the error.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="inputRefIds")]
		public string[] InputRefIds { get; set; }
		
		/// <summary>
		/// A more detailed explanation of the error.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="longMessage")]
		public string LongMessage { get; set; }
		
		/// <summary>
		/// Information on how to correct the problem, in the end user's terms and language where applicable.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="message")]
		public string Message { get; set; }
		
		/// <summary>
		/// An array of request elements most closely associated to the error.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="outputRefIds")]
		public string[] OutputRefIds { get; set; }
		
		/// <summary>
		/// An array of name/value pairs that describe details the error condition. These are useful when multiple errors are returned.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="parameters")]
		public ErrorParameter[] Parameters { get; set; }
		
		/// <summary>
		/// Further helps indicate which subsystem the error is coming from. System subcategories include: Initialization, Serialization, Security, Monitoring, Rate Limiting, etc.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="subdomain")]
		public string Subdomain { get; set; }
	}
	
	[System.Runtime.Serialization.DataContract(Namespace="http://taxonomyapi.ebay/2020/03")]
	public class ErrorParameter
	{
		
		/// <summary>
		/// The object of the error.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="name")]
		public string Name { get; set; }
		
		/// <summary>
		/// The value of the object.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="value")]
		public string Value { get; set; }
	}
	
	[System.Runtime.Serialization.DataContract(Namespace="http://taxonomyapi.ebay/2020/03")]
	public class GetCategoriesAspectResponse
	{
		
		/// <summary>
		/// The unique identifier of the eBay category tree being requested.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="categoryTreeId")]
		public string CategoryTreeId { get; set; }
		
		/// <summary>
		/// The version of the category tree that is returned in the categoryTreeId field.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="categoryTreeVersion")]
		public string CategoryTreeVersion { get; set; }
		
		/// <summary>
		/// An array of aspects that are appropriate or necessary for accurately describing items in a particular leaf category.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="categoryAspects")]
		public CategoryAspect[] CategoryAspects { get; set; }
	}
	
	/// <summary>
	/// This type is used by the base response of the getCompatibilityProperties method.
	/// </summary>
	[System.Runtime.Serialization.DataContract(Namespace="http://taxonomyapi.ebay/2020/03")]
	public class GetCompatibilityMetadataResponse
	{
		
		/// <summary>
		/// This container consists of an array of all compatible vehicle properties applicable to the specified eBay marketplace and eBay category ID.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="compatibilityProperties")]
		public CompatibilityProperty[] CompatibilityProperties { get; set; }
	}
	
	/// <summary>
	/// The base response type of the getCompatibilityPropertyValues method.
	/// </summary>
	[System.Runtime.Serialization.DataContract(Namespace="http://taxonomyapi.ebay/2020/03")]
	public class GetCompatibilityPropertyValuesResponse
	{
		
		/// <summary>
		/// This array contains all compatible vehicle property values that match the specified eBay marketplace, specified eBay category, and filters in the request. If the compatibility_property parameter value in the request is 'Trim', each value returned in each value field will be a different vehicle trim, applicable to any filters that are set in the filter query parameter of the request, and also based on the eBay marketplace and category specified in the call request.
		/// </summary>
		[System.Runtime.Serialization.DataMember(Name="compatibilityPropertyValues")]
		public CompatibilityPropertyValue[] CompatibilityPropertyValues { get; set; }
	}
	
	public partial class TaxonomyAPIClass
	{
		
		private System.Net.Http.HttpClient client;
		
		private JsonSerializerSettings jsonSerializerSettings;
		
		public TaxonomyAPIClass(System.Net.Http.HttpClient client, JsonSerializerSettings jsonSerializerSettings=null)
		{
			if (client == null)
				throw new ArgumentNullException("Null HttpClient.", "client");

			if (client.BaseAddress == null)
				throw new ArgumentNullException("HttpClient has no BaseAddress", "client");

			this.client = client;
			this.jsonSerializerSettings = jsonSerializerSettings;
		}
		
		/// <summary>
		/// Get Aspects for All Leaf Categories in a Marketplace
		/// This call returns a complete list of aspects for all of the leaf categories that belong to an eBay marketplace. The eBay marketplace is specified through the category_tree_id URI parameter. Note: This call can return a large payload, so the call returns the response as a gzipped JSON file. The open source Taxonomy SDK can be used to compare the aspect metadata that is returned in this response. The bulk download capability that this method provides, when combined with the Taxonomy SDK, brings transparency to the evolution of the metadata.
		/// FetchItemAspects category_tree/{category_tree_id}/fetch_item_aspects
		/// </summary>
		/// <param name="category_tree_id">The unique identifier of the eBay category tree being requested.</param>
		/// <returns>Success</returns>
		public async Task<GetCategoriesAspectResponse> FetchItemAspectsAsync(string category_tree_id, Action<System.Net.Http.Headers.HttpRequestHeaders> handleHeaders = null)
		{
			var requestUri = "category_tree/"+ (category_tree_id==null? "" : Uri.EscapeDataString(category_tree_id))+"/fetch_item_aspects";
			using (var httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, requestUri))
			{
			if (handleHeaders != null)
			{
				handleHeaders(httpRequestMessage.Headers);
			}

			var responseMessage = await client.SendAsync(httpRequestMessage);
			try
			{
				responseMessage.EnsureSuccessStatusCodeEx();
				var stream = await responseMessage.Content.ReadAsStreamAsync();
				using (JsonReader jsonReader = new JsonTextReader(new System.IO.StreamReader(stream)))
				{
				var serializer = new JsonSerializer();
				return serializer.Deserialize<GetCategoriesAspectResponse>(jsonReader);
				}
			}
			finally
			{
				responseMessage.Dispose();
			}
			}
		}
		
		/// <summary>
		/// Get a Default Category Tree ID
		/// A given eBay marketplace might use multiple category trees, but one of those trees is considered to be the default for that marketplace. This call retrieves a reference to the default category tree associated with the specified eBay marketplace ID. The response includes only the tree's unique identifier and version, which you can use to retrieve more details about the tree, its structure, and its individual category nodes.
		/// GetDefaultCategoryTreeId get_default_category_tree_id
		/// </summary>
		/// <param name="marketplace_id">The ID of the eBay marketplace for which the category tree ID is being requested. For a list of supported marketplace IDs, see Marketplaces with Default Category Trees.</param>
		/// <returns>Success</returns>
		public async Task<BaseCategoryTree> GetDefaultCategoryTreeIdAsync(string marketplace_id, Action<System.Net.Http.Headers.HttpRequestHeaders> handleHeaders = null)
		{
			var requestUri = "get_default_category_tree_id?marketplace_id=" + (marketplace_id==null? "" : Uri.EscapeDataString(marketplace_id));
			using (var httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, requestUri))
			{
			if (handleHeaders != null)
			{
				handleHeaders(httpRequestMessage.Headers);
			}

			var responseMessage = await client.SendAsync(httpRequestMessage);
			try
			{
				responseMessage.EnsureSuccessStatusCodeEx();
				var stream = await responseMessage.Content.ReadAsStreamAsync();
				using (JsonReader jsonReader = new JsonTextReader(new System.IO.StreamReader(stream)))
				{
				var serializer = new JsonSerializer();
				return serializer.Deserialize<BaseCategoryTree>(jsonReader);
				}
			}
			finally
			{
				responseMessage.Dispose();
			}
			}
		}
		
		/// <summary>
		/// Get a Category Tree
		/// This call retrieves the complete category tree that is identified by the category_tree_id parameter. The value of category_tree_id was returned by the getDefaultCategoryTreeId call in the categoryTreeId field. The response contains details of all nodes of the specified eBay category tree, as well as the eBay marketplaces that use this category tree. Note: This call can return a very large payload, so you are strongly advised to submit the request with the following HTTP header: &nbsp;&nbsp;Accept-Encoding: application/gzip With this header (in addition to the required headers described under HTTP Request Headers), the call returns the response with gzip compression.
		/// GetCategoryTree category_tree/{category_tree_id}
		/// </summary>
		/// <param name="category_tree_id">The unique identifier of the eBay category tree being requested.</param>
		/// <returns>Success</returns>
		public async Task<CategoryTree> GetCategoryTreeAsync(string category_tree_id, Action<System.Net.Http.Headers.HttpRequestHeaders> handleHeaders = null)
		{
			var requestUri = "category_tree/"+ (category_tree_id==null? "" : Uri.EscapeDataString(category_tree_id));
			using (var httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, requestUri))
			{
			if (handleHeaders != null)
			{
				handleHeaders(httpRequestMessage.Headers);
			}

			var responseMessage = await client.SendAsync(httpRequestMessage);
			try
			{
				responseMessage.EnsureSuccessStatusCodeEx();
				var stream = await responseMessage.Content.ReadAsStreamAsync();
				using (JsonReader jsonReader = new JsonTextReader(new System.IO.StreamReader(stream)))
				{
				var serializer = new JsonSerializer();
				return serializer.Deserialize<CategoryTree>(jsonReader);
				}
			}
			finally
			{
				responseMessage.Dispose();
			}
			}
		}
		
		/// <summary>
		/// Get a Category Subtree
		/// This call retrieves the details of all nodes of the category tree hierarchy (the subtree) below a specified category of a category tree. You identify the tree using the category_tree_id parameter, which was returned by the getDefaultCategoryTreeId call in the categoryTreeId field. Note: This call can return a very large payload, so you are strongly advised to submit the request with the following HTTP header: &nbsp;&nbsp;Accept-Encoding: application/gzip With this header (in addition to the required headers described under HTTP Request Headers), the call returns the response with gzip compression.
		/// GetCategorySubtree category_tree/{category_tree_id}/get_category_subtree
		/// </summary>
		/// <param name="category_id">The unique identifier of the category at the top of the subtree being requested. Note: If the category_id submitted identifies the root node of the tree, this call returns an error. To retrieve the complete tree, use this value with the getCategoryTree call. If the category_id submitted identifies a leaf node of the tree, the call response will contain information about only that leaf node, which is a valid subtree.</param>
		/// <param name="category_tree_id">The unique identifier of the eBay category tree from which a category subtree is being requested.</param>
		/// <returns>Success</returns>
		public async Task<CategorySubtree> GetCategorySubtreeAsync(string category_id, string category_tree_id, Action<System.Net.Http.Headers.HttpRequestHeaders> handleHeaders = null)
		{
			var requestUri = "category_tree/"+ (category_tree_id==null? "" : Uri.EscapeDataString(category_tree_id))+"/get_category_subtree?category_id=" + (category_id==null? "" : Uri.EscapeDataString(category_id));
			using (var httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, requestUri))
			{
			if (handleHeaders != null)
			{
				handleHeaders(httpRequestMessage.Headers);
			}

			var responseMessage = await client.SendAsync(httpRequestMessage);
			try
			{
				responseMessage.EnsureSuccessStatusCodeEx();
				var stream = await responseMessage.Content.ReadAsStreamAsync();
				using (JsonReader jsonReader = new JsonTextReader(new System.IO.StreamReader(stream)))
				{
				var serializer = new JsonSerializer();
				return serializer.Deserialize<CategorySubtree>(jsonReader);
				}
			}
			finally
			{
				responseMessage.Dispose();
			}
			}
		}
		
		/// <summary>
		/// Get Suggested Categories
		/// This call returns an array of category tree leaf nodes in the specified category tree that are considered by eBay to most closely correspond to the query string q. Returned with each suggested node is a localized name for that category (based on the Accept-Language header specified for the call), and details about each of the category's ancestor nodes, extending from its immediate parent up to the root of the category tree. Note: This call can return a large payload, so you are advised to submit the request with the following HTTP header: &nbsp;&nbsp;Accept-Encoding: application/gzip With this header (in addition to the required headers described under HTTP Request Headers), the call returns the response with gzip compression. You identify the tree using the category_tree_id parameter, which was returned by the getDefaultCategoryTreeId call in the categoryTreeId field. Important: This call is not supported in the Sandbox environment. It will return a response payload in which the categoryName fields contain random or boilerplate text regardless of the query submitted.
		/// GetCategorySuggestions category_tree/{category_tree_id}/get_category_suggestions
		/// </summary>
		/// <param name="category_tree_id">The unique identifier of the eBay category tree for which suggested nodes are being requested.</param>
		/// <param name="q">A quoted string that describes or characterizes the item being offered for sale. The string format is free form, and can contain any combination of phrases or keywords. eBay will parse the string and return suggested categories for the item.</param>
		/// <returns>Success</returns>
		public async Task<CategorySuggestionResponse> GetCategorySuggestionsAsync(string category_tree_id, string q, Action<System.Net.Http.Headers.HttpRequestHeaders> handleHeaders = null)
		{
			var requestUri = "category_tree/"+ (category_tree_id==null? "" : Uri.EscapeDataString(category_tree_id))+"/get_category_suggestions&q=" + (q==null? "" : Uri.EscapeDataString(q));
			using (var httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, requestUri))
			{
			if (handleHeaders != null)
			{
				handleHeaders(httpRequestMessage.Headers);
			}

			var responseMessage = await client.SendAsync(httpRequestMessage);
			try
			{
				responseMessage.EnsureSuccessStatusCodeEx();
				var stream = await responseMessage.Content.ReadAsStreamAsync();
				using (JsonReader jsonReader = new JsonTextReader(new System.IO.StreamReader(stream)))
				{
				var serializer = new JsonSerializer();
				return serializer.Deserialize<CategorySuggestionResponse>(jsonReader);
				}
			}
			finally
			{
				responseMessage.Dispose();
			}
			}
		}
		
		/// <summary>
		/// This call returns a list of aspects that are appropriate or necessary for accurately describing items in the specified leaf category. Each aspect identifies an item attribute (for example, color) for which the seller will be required or encouraged to provide a value (or variation values) when offering an item in that category on eBay. For each aspect, getItemAspectsForCategory provides complete metadata, including: The aspect's data type, format, and entry mode Whether the aspect is required in listings Whether the aspect can be used for item variations Whether the aspect accepts multiple values for an item Allowed values for the aspect Use this information to construct an interface through which sellers can enter or select the appropriate values for their items or item variations. Once you collect those values, include them as product aspects when creating inventory items using the Inventory API.
		/// GetItemAspectsForCategory category_tree/{category_tree_id}/get_item_aspects_for_category
		/// </summary>
		/// <param name="category_id">The unique identifier of the leaf category for which aspects are being requested. Note: If the category_id submitted does not identify a leaf node of the tree, this call returns an error.</param>
		/// <param name="category_tree_id">The unique identifier of the eBay category tree from which the specified category's aspects are being requested.</param>
		/// <returns>Success</returns>
		public async Task<AspectMetadata> GetItemAspectsForCategoryAsync(string category_id, string category_tree_id, Action<System.Net.Http.Headers.HttpRequestHeaders> handleHeaders = null)
		{
			var requestUri = "category_tree/"+ (category_tree_id==null? "" : Uri.EscapeDataString(category_tree_id))+"/get_item_aspects_for_category?category_id=" + (category_id==null? "" : Uri.EscapeDataString(category_id));
			using (var httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, requestUri))
			{
			if (handleHeaders != null)
			{
				handleHeaders(httpRequestMessage.Headers);
			}

			var responseMessage = await client.SendAsync(httpRequestMessage);
			try
			{
				responseMessage.EnsureSuccessStatusCodeEx();
				var stream = await responseMessage.Content.ReadAsStreamAsync();
				using (JsonReader jsonReader = new JsonTextReader(new System.IO.StreamReader(stream)))
				{
				var serializer = new JsonSerializer();
				return serializer.Deserialize<AspectMetadata>(jsonReader);
				}
			}
			finally
			{
				responseMessage.Dispose();
			}
			}
		}
		
		/// <summary>
		/// Get Compatibility Properties
		/// This call retrieves the compatible vehicle aspects that are used to define a motor vehicle that is compatible with a motor vehicle part or accessory. The values that are retrieved here might include motor vehicle aspects such as 'Make', 'Model', 'Year', 'Engine', and 'Trim', and each of these aspects are localized for the eBay marketplace. The category_tree_id value is passed in as a path parameter, and this value identifies the eBay category tree. The category_id value is passed in as a query parameter, as this parameter is also required. The specified category must be a category that supports parts compatibility. At this time, this operation only supports parts and accessories listings for cars, trucks, and motorcycles (not boats, power sports, or any other vehicle types). Only the following eBay marketplaces support parts compatibility: eBay US (Motors and non-Motors categories) eBay Canada (Motors and non-Motors categories) eBay UK eBay Germany eBay Australia eBay France eBay Italy eBay Spain
		/// GetCompatibilityProperties category_tree/{category_tree_id}/get_compatibility_properties
		/// </summary>
		/// <param name="category_tree_id">This is the unique identifier of category tree. The following is the list of category_tree_id values and the eBay marketplaces that they represent. One of these ID values must be passed in as a path parameter, and the category_id value, that is passed in as query parameter, must be a valid eBay category on that eBay marketplace that supports parts compatibility for cars, trucks, or motorcyles. eBay US: 0 eBay Motors US: 100 eBay Canada: 2 eBay UK: 3 eBay Germany: 77 eBay Australia: 15 eBay France: 71 eBay Italy: 101 eBay Spain: 186</param>
		/// <param name="category_id">The unique identifier of an eBay category. This eBay category must be a valid eBay category on the specified eBay marketplace, and the category must support parts compatibility for cars, trucks, or motorcyles. The getAutomotivePartsCompatibilityPolicies method of the Selling Metadata API can be used to retrieve all eBay categories for an eBay marketplace that supports parts compatibility cars, trucks, or motorcyles. The getAutomotivePartsCompatibilityPolicies method can also be used to see if one or more specific eBay categories support parts compatibility.</param>
		/// <returns>Success</returns>
		public async Task<GetCompatibilityMetadataResponse> GetCompatibilityPropertiesAsync(string category_tree_id, string category_id, Action<System.Net.Http.Headers.HttpRequestHeaders> handleHeaders = null)
		{
			var requestUri = "category_tree/"+ (category_tree_id==null? "" : Uri.EscapeDataString(category_tree_id))+"/get_compatibility_properties&category_id=" + (category_id==null? "" : Uri.EscapeDataString(category_id));
			using (var httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, requestUri))
			{
			if (handleHeaders != null)
			{
				handleHeaders(httpRequestMessage.Headers);
			}

			var responseMessage = await client.SendAsync(httpRequestMessage);
			try
			{
				responseMessage.EnsureSuccessStatusCodeEx();
				var stream = await responseMessage.Content.ReadAsStreamAsync();
				using (JsonReader jsonReader = new JsonTextReader(new System.IO.StreamReader(stream)))
				{
				var serializer = new JsonSerializer();
				return serializer.Deserialize<GetCompatibilityMetadataResponse>(jsonReader);
				}
			}
			finally
			{
				responseMessage.Dispose();
			}
			}
		}
		
		/// <summary>
		/// Get Compatibility Property Values
		/// This call retrieves applicable compatible vehicle property values based on the specified eBay marketplace, specified eBay category, and filters used in the request. Compatible vehicle properties are returned in the compatibilityProperties.name field of a getCompatibilityProperties response. One compatible vehicle property applicable to the specified eBay marketplace and eBay category is specified through the required compatibility_property filter. Then, the user has the option of further restricting the compatible vehicle property values that are returned in the response by specifying one or more compatible vehicle property name/value pairs through the filter query parameter. See the documentation in URI parameters section for more information on using the compatibility_property and filter query parameters together to customize the data that is retrieved.
		/// GetCompatibilityPropertyValues category_tree/{category_tree_id}/get_compatibility_property_values
		/// </summary>
		/// <param name="category_tree_id">This is the unique identifier of the category tree. The following is the list of category_tree_id values and the eBay marketplaces that they represent. One of these ID values must be passed in as a path parameter, and the category_id value, that is passed in as query parameter, must be a valid eBay category on that eBay marketplace that supports parts compatibility for cars, trucks, or motorcyles. eBay US: 0 eBay Motors US: 100 eBay Canada: 2 eBay UK: 3 eBay Germany: 77 eBay Australia: 15 eBay France: 71 eBay Italy: 101 eBay Spain: 186</param>
		/// <param name="compatibility_property">One compatible vehicle property applicable to the specified eBay marketplace and eBay category is specified in this required filter. Compatible vehicle properties are returned in the compatibilityProperties.name field of a getCompatibilityProperties response. For example, if you wanted to retrieve all vehicle trims for a 2018 Toyota Camry, you would set this filter as follows: compatibility_property=Trim; and then include the following three name/value filters through one filter parameter: filter=Year:2018,Make:Toyota,Model:Camry. So, putting this all together, your URI would look something like this: GET https://api.ebay.com/commerce/ taxonomy/v1/category_tree/100/ get_compatibility_property_values? category_id=6016&amp;compatibility_property=Trim &amp;filter=filter=Year:2018,Make:Toyota,Model:Camry</param>
		/// <param name="category_id">The unique identifier of an eBay category. This eBay category must be a valid eBay category on the specified eBay marketplace, and the category must support parts compatibility for cars, trucks, or motorcyles. The getAutomotivePartsCompatibilityPolicies method of the Selling Metadata API can be used to retrieve all eBay categories for an eBay marketplace that supports parts compatibility cars, trucks, or motorcyles. The getAutomotivePartsCompatibilityPolicies method can also be used to see if one or more specific eBay categories support parts compatibility.</param>
		/// <param name="filter">One or more compatible vehicle property name/value pairs are passed in through this query parameter. The compatible vehicle property name and corresponding value are delimited with a colon (:), such as filter=Year:2018, and multiple compatible vehicle property name/value pairs are delimited with a comma (,). For example, if you wanted to retrieve all vehicle trims for a 2018 Toyota Camry, you would set the compatibility_property filter as follows: compatibility_property=Trim; and then include the following three name/value filters through one filter parameter: filter=Year:2018,Make:Toyota,Model:Camry. So, putting this all together, your URI would look something like this: GET https://api.ebay.com/commerce/ taxonomy/v1/category_tree/100/ get_compatibility_property_values? category_id=6016&amp;compatibility_property=Trim &amp;filter=filter=Year:2018,Make:Toyota,Model:Camry For implementation help, refer to eBay API documentation at https://developer.ebay.com/api-docs/commerce/taxonomy/types/txn:ConstraintFilter</param>
		/// <returns>Success</returns>
		public async Task<GetCompatibilityPropertyValuesResponse> GetCompatibilityPropertyValuesAsync(string category_tree_id, string compatibility_property, string category_id, string filter, Action<System.Net.Http.Headers.HttpRequestHeaders> handleHeaders = null)
		{
			var requestUri = "category_tree/"+ (category_tree_id==null? "" : Uri.EscapeDataString(category_tree_id))+"/get_compatibility_property_values&compatibility_property=" + (compatibility_property==null? "" : Uri.EscapeDataString(compatibility_property))+"&category_id=" + (category_id==null? "" : Uri.EscapeDataString(category_id))+"&filter=" + (filter==null? "" : Uri.EscapeDataString(filter));
			using (var httpRequestMessage = new HttpRequestMessage(HttpMethod.Get, requestUri))
			{
			if (handleHeaders != null)
			{
				handleHeaders(httpRequestMessage.Headers);
			}

			var responseMessage = await client.SendAsync(httpRequestMessage);
			try
			{
				responseMessage.EnsureSuccessStatusCodeEx();
				var stream = await responseMessage.Content.ReadAsStreamAsync();
				using (JsonReader jsonReader = new JsonTextReader(new System.IO.StreamReader(stream)))
				{
				var serializer = new JsonSerializer();
				return serializer.Deserialize<GetCompatibilityPropertyValuesResponse>(jsonReader);
				}
			}
			finally
			{
				responseMessage.Dispose();
			}
			}
		}
	}
}

