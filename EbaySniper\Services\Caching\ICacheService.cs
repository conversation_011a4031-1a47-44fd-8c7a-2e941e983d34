using System;

namespace uBuyFirst.Services.Caching // Corrected namespace
{
    /// <summary>
    /// Defines a generic interface for a cache service.
    /// </summary>
    /// <typeparam name="TK<PERSON>">The type of the cache key.</typeparam>
    /// <typeparam name="TValue">The type of the cached value.</typeparam>
    public interface ICacheService<TKey, TValue>
    {
        /// <summary>
        /// Tries to get the value associated with the specified key from the cache.
        /// </summary>
        /// <param name="key">The key of the value to get.</param>
        /// <param name="value">When this method returns, contains the value associated with the specified key, if the key is found; otherwise, the default value for the type of the value parameter. This parameter is passed uninitialized.</param>
        /// <returns>true if the key was found in the cache; otherwise, false.</returns>
        bool TryGetValue(TKey key, out TValue value);

        /// <summary>
        /// Adds or updates the specified key/value pair in the cache.
        /// </summary>
        /// <param name="key">The key of the element to add or update.</param>
        /// <param name="value">The value to be added or updated.</param>
        void Set(TKey key, TValue value);

        /// <summary>
        /// Removes all keys and values from the cache.
        /// </summary>
        void Clear();

        /// <summary>
        /// Gets all key/value pairs currently stored in the cache.
        /// </summary>
        /// <returns>An enumerable collection of key/value pairs.</returns>
        System.Collections.Generic.IEnumerable<System.Collections.Generic.KeyValuePair<TKey, TValue>> GetAllEntries();

        // Consider adding other methods like GetOrAdd, Remove if needed for broader use cases.
        // TValue GetOrAdd(TKey key, Func<TKey, TValue> valueFactory);
        // bool TryRemove(TKey key, out TValue value);
    }
}