﻿using System.Collections.Generic;
using uBuyFirst.Models;
using uBuyFirst.Seller; // Assuming CountryData is here

namespace uBuyFirst.Services
{
    public interface ICountryService
    {
        /// <summary>
        /// Gets country data by its unique ID.
        /// </summary>
        /// <param name="id">The country ID.</param>
        /// <returns>The CountryData object if found; otherwise, null.</returns>
        CountryData? GetCountryById(int id);

        /// <summary>
        /// Gets all loaded country data.
        /// </summary>
        /// <returns>An enumerable collection of all CountryData objects.</returns>
        IEnumerable<CountryData> GetAllCountries();

        string GetSellerCountry(SellerUser sellerUser);
    }
}
