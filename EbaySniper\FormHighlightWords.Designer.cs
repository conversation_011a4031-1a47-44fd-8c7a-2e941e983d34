﻿using System.ComponentModel;
using DevExpress.Utils;
using DevExpress.XtraBars;
using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;

namespace uBuyFirst
{
    partial class FormHighlightWords
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(FormHighlightWords));
            this.formAssistant1 = new DevExpress.XtraBars.FormAssistant();
            this.ribbonControl1 = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.groupWordsHighlight = new DevExpress.XtraEditors.GroupControl();
            this.colorPickEdit3 = new DevExpress.XtraEditors.ColorPickEdit();
            this.toolTipController1 = new DevExpress.Utils.ToolTipController(this.components);
            this.colorPickEdit2 = new DevExpress.XtraEditors.ColorPickEdit();
            this.colorPickEdit1 = new DevExpress.XtraEditors.ColorPickEdit();
            this.WordsHighlight3 = new DevExpress.XtraEditors.MemoEdit();
            this.WordsHighlight2 = new DevExpress.XtraEditors.MemoEdit();
            this.WordsHighlight1 = new DevExpress.XtraEditors.MemoEdit();
            this.hyperlinkLabelControl1 = new DevExpress.XtraEditors.HyperlinkLabelControl();
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupWordsHighlight)).BeginInit();
            this.groupWordsHighlight.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.colorPickEdit3.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorPickEdit2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorPickEdit1.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.WordsHighlight3.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.WordsHighlight2.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.WordsHighlight1.Properties)).BeginInit();
            this.SuspendLayout();
            // 
            // ribbonControl1
            // 
            this.ribbonControl1.ExpandCollapseItem.Id = 0;
            this.ribbonControl1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.ribbonControl1.ExpandCollapseItem});
            this.ribbonControl1.Location = new System.Drawing.Point(0, 0);
            this.ribbonControl1.Margin = new System.Windows.Forms.Padding(7);
            this.ribbonControl1.MaxItemId = 1;
            this.ribbonControl1.Name = "ribbonControl1";
            this.ribbonControl1.OptionsPageCategories.ShowCaptions = false;
            this.ribbonControl1.ShowApplicationButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowDisplayOptionsMenuButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowExpandCollapseButton = DevExpress.Utils.DefaultBoolean.False;
            this.ribbonControl1.ShowPageHeadersMode = DevExpress.XtraBars.Ribbon.ShowPageHeadersMode.Hide;
            this.ribbonControl1.ShowQatLocationSelector = false;
            this.ribbonControl1.ShowToolbarCustomizeItem = false;
            this.ribbonControl1.Size = new System.Drawing.Size(411, 32);
            this.ribbonControl1.Toolbar.ShowCustomizeItem = false;
            // 
            // groupWordsHighlight
            // 
            this.groupWordsHighlight.Anchor = ((System.Windows.Forms.AnchorStyles)((((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Bottom) 
            | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.groupWordsHighlight.Controls.Add(this.colorPickEdit3);
            this.groupWordsHighlight.Controls.Add(this.colorPickEdit2);
            this.groupWordsHighlight.Controls.Add(this.colorPickEdit1);
            this.groupWordsHighlight.Controls.Add(this.WordsHighlight3);
            this.groupWordsHighlight.Controls.Add(this.WordsHighlight2);
            this.groupWordsHighlight.Controls.Add(this.WordsHighlight1);
            this.groupWordsHighlight.Location = new System.Drawing.Point(16, 41);
            this.groupWordsHighlight.Margin = new System.Windows.Forms.Padding(7);
            this.groupWordsHighlight.Name = "groupWordsHighlight";
            this.groupWordsHighlight.Size = new System.Drawing.Size(379, 415);
            this.groupWordsHighlight.TabIndex = 2;
            this.groupWordsHighlight.Text = "Highlight words or phrases in description";
            // 
            // colorPickEdit3
            // 
            this.colorPickEdit3.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.colorPickEdit3.EditValue = System.Drawing.Color.LightSalmon;
            this.colorPickEdit3.Location = new System.Drawing.Point(253, 282);
            this.colorPickEdit3.Margin = new System.Windows.Forms.Padding(7);
            this.colorPickEdit3.Name = "colorPickEdit3";
            this.colorPickEdit3.Properties.AutomaticColor = System.Drawing.Color.Black;
            this.colorPickEdit3.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorPickEdit3.Properties.NullColor = System.Drawing.Color.Empty;
            this.colorPickEdit3.Size = new System.Drawing.Size(106, 20);
            this.colorPickEdit3.TabIndex = 1;
            this.colorPickEdit3.ToolTip = resources.GetString("colorPickEdit3.ToolTip");
            this.colorPickEdit3.ToolTipController = this.toolTipController1;
            // 
            // colorPickEdit2
            // 
            this.colorPickEdit2.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.colorPickEdit2.EditValue = System.Drawing.Color.Yellow;
            this.colorPickEdit2.Location = new System.Drawing.Point(253, 157);
            this.colorPickEdit2.Margin = new System.Windows.Forms.Padding(7);
            this.colorPickEdit2.Name = "colorPickEdit2";
            this.colorPickEdit2.Properties.AutomaticColor = System.Drawing.Color.Black;
            this.colorPickEdit2.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorPickEdit2.Properties.NullColor = System.Drawing.Color.Empty;
            this.colorPickEdit2.Size = new System.Drawing.Size(106, 20);
            this.colorPickEdit2.TabIndex = 1;
            this.colorPickEdit2.ToolTip = resources.GetString("colorPickEdit2.ToolTip");
            this.colorPickEdit2.ToolTipController = this.toolTipController1;
            // 
            // colorPickEdit1
            // 
            this.colorPickEdit1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Right)));
            this.colorPickEdit1.EditValue = System.Drawing.Color.PaleGreen;
            this.colorPickEdit1.Location = new System.Drawing.Point(252, 32);
            this.colorPickEdit1.Margin = new System.Windows.Forms.Padding(7);
            this.colorPickEdit1.MenuManager = this.ribbonControl1;
            this.colorPickEdit1.Name = "colorPickEdit1";
            this.colorPickEdit1.Properties.AutomaticColor = System.Drawing.Color.Black;
            this.colorPickEdit1.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.colorPickEdit1.Properties.NullColor = System.Drawing.Color.Empty;
            this.colorPickEdit1.Size = new System.Drawing.Size(106, 20);
            this.colorPickEdit1.TabIndex = 1;
            this.colorPickEdit1.ToolTip = resources.GetString("colorPickEdit1.ToolTip");
            this.colorPickEdit1.ToolTipController = this.toolTipController1;
            // 
            // WordsHighlight3
            // 
            this.WordsHighlight3.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.WordsHighlight3.Location = new System.Drawing.Point(10, 280);
            this.WordsHighlight3.Margin = new System.Windows.Forms.Padding(7);
            this.WordsHighlight3.Name = "WordsHighlight3";
            this.WordsHighlight3.Properties.NullText = "bad\r\n\"not\"\r\n\"no\"\r\nbroken";
            this.WordsHighlight3.Properties.NullValuePrompt = "bad\r\n\"not\"\r\n\"no\"\r\nbroken";
            this.WordsHighlight3.Properties.WordWrap = false;
            this.WordsHighlight3.Size = new System.Drawing.Size(230, 111);
            this.WordsHighlight3.TabIndex = 0;
            this.WordsHighlight3.ToolTip = resources.GetString("WordsHighlight3.ToolTip");
            this.WordsHighlight3.ToolTipController = this.toolTipController1;
            this.WordsHighlight3.MouseLeave += new System.EventHandler(this.WordsHighlight3_MouseLeave);
            // 
            // WordsHighlight2
            // 
            this.WordsHighlight2.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.WordsHighlight2.Location = new System.Drawing.Point(10, 155);
            this.WordsHighlight2.Margin = new System.Windows.Forms.Padding(7);
            this.WordsHighlight2.Name = "WordsHighlight2";
            this.WordsHighlight2.Properties.NullText = "scratch\r\n\"dent\"\r\n\"dents\"";
            this.WordsHighlight2.Properties.NullValuePrompt = "scratch\r\n\"dent\"\r\n\"dents\"";
            this.WordsHighlight2.Properties.WordWrap = false;
            this.WordsHighlight2.Size = new System.Drawing.Size(230, 111);
            this.WordsHighlight2.TabIndex = 0;
            this.WordsHighlight2.ToolTip = resources.GetString("WordsHighlight2.ToolTip");
            this.WordsHighlight2.ToolTipController = this.toolTipController1;
            this.WordsHighlight2.MouseLeave += new System.EventHandler(this.WordsHighlight3_MouseLeave);
            // 
            // WordsHighlight1
            // 
            this.WordsHighlight1.Anchor = ((System.Windows.Forms.AnchorStyles)(((System.Windows.Forms.AnchorStyles.Top | System.Windows.Forms.AnchorStyles.Left) 
            | System.Windows.Forms.AnchorStyles.Right)));
            this.WordsHighlight1.Location = new System.Drawing.Point(9, 30);
            this.WordsHighlight1.Margin = new System.Windows.Forms.Padding(7);
            this.WordsHighlight1.MenuManager = this.ribbonControl1;
            this.WordsHighlight1.Name = "WordsHighlight1";
            this.WordsHighlight1.Properties.NullText = "good\r\nexcellent";
            this.WordsHighlight1.Properties.NullValuePrompt = "good\r\nexcellent";
            this.WordsHighlight1.Properties.WordWrap = false;
            this.WordsHighlight1.Size = new System.Drawing.Size(230, 111);
            this.WordsHighlight1.TabIndex = 0;
            this.WordsHighlight1.ToolTip = resources.GetString("WordsHighlight1.ToolTip");
            this.WordsHighlight1.ToolTipController = this.toolTipController1;
            this.WordsHighlight1.MouseLeave += new System.EventHandler(this.WordsHighlight3_MouseLeave);
            // 
            // hyperlinkLabelControl1
            // 
            this.hyperlinkLabelControl1.Anchor = ((System.Windows.Forms.AnchorStyles)((System.Windows.Forms.AnchorStyles.Bottom | System.Windows.Forms.AnchorStyles.Left)));
            this.hyperlinkLabelControl1.Cursor = System.Windows.Forms.Cursors.Hand;
            this.hyperlinkLabelControl1.Location = new System.Drawing.Point(16, 473);
            this.hyperlinkLabelControl1.Margin = new System.Windows.Forms.Padding(7);
            this.hyperlinkLabelControl1.Name = "hyperlinkLabelControl1";
            this.hyperlinkLabelControl1.Size = new System.Drawing.Size(21, 13);
            this.hyperlinkLabelControl1.TabIndex = 18;
            this.hyperlinkLabelControl1.Tag = "https://ubuyfirst.com/06-description-highlighting/";
            this.hyperlinkLabelControl1.Text = "Help";
            this.hyperlinkLabelControl1.Click += new System.EventHandler(this.hyperlinkLabelControl1_Click);
            // 
            // FormHighlightWords
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 13F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(411, 502);
            this.Controls.Add(this.hyperlinkLabelControl1);
            this.Controls.Add(this.groupWordsHighlight);
            this.Controls.Add(this.ribbonControl1);
            this.IconOptions.Icon = ((System.Drawing.Icon)(resources.GetObject("FormHighlightWords.IconOptions.Icon")));
            this.Margin = new System.Windows.Forms.Padding(7);
            this.Name = "FormHighlightWords";
            this.Ribbon = this.ribbonControl1;
            this.Text = "Highlight Words";
            this.FormClosing += new System.Windows.Forms.FormClosingEventHandler(this.FormHighlightWords_FormClosing);
            this.Load += new System.EventHandler(this.FormHighlightWords_Load);
            this.KeyPress += new System.Windows.Forms.KeyPressEventHandler(this.FormHighlightWords_KeyPress);
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.groupWordsHighlight)).EndInit();
            this.groupWordsHighlight.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.colorPickEdit3.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorPickEdit2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.colorPickEdit1.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.WordsHighlight3.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.WordsHighlight2.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.WordsHighlight1.Properties)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private FormAssistant formAssistant1;
        private RibbonControl ribbonControl1;
        private GroupControl groupWordsHighlight;
        private ColorPickEdit colorPickEdit3;
        private ColorPickEdit colorPickEdit2;
        private ColorPickEdit colorPickEdit1;
        private MemoEdit WordsHighlight3;
        private MemoEdit WordsHighlight2;
        private MemoEdit WordsHighlight1;
        private ToolTipController toolTipController1;
        private HyperlinkLabelControl hyperlinkLabelControl1;
    }
}