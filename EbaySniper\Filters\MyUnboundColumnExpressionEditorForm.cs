﻿using System.ComponentModel.Design;
using DevExpress.XtraEditors.Design;
using DevExpress.XtraGrid.Columns;

namespace uBuyFirst.Filters
{
    public class MyUnboundColumnExpressionEditorForm : UnboundColumnExpressionEditorForm
    {
        public MyUnboundColumnExpressionEditorForm(object contextInstance, IDesignerHost designerHost) : base(contextInstance, designerHost)
        {
        }

        private string GetExpressionMemoEditText()
        {
            return !(ContextInstance is GridColumn column) ? string.Empty : column.UnboundExpression;
        }

        public void ResetMemoText()
        {
            ExpressionMemoEdit.Text = GetExpressionMemoEditText();
        }
    }
}