using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using uBuyFirst.Filters;

namespace uBuyFirst.Tests.Filters
{
    /// <summary>
    /// Test class to verify the improved filter actions system works correctly
    /// </summary>
    public static class FilterActionsTest
    {
        /// <summary>
        /// Test the factory and action creation
        /// </summary>
        public static void TestActionFactory()
        {
            Console.WriteLine("Testing Improved Filter Actions System...");

            try
            {
                // Initialize the factory (this might be needed)
                FilterActionFactory.Initialize(null); // Pass null for telegram sender in tests
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Factory initialization failed: {ex.Message}");
                // Continue with test anyway
            }

            // Test creating actions (pure business logic)
            var formatCellsAction = FilterActionFactory.CreateAction("FORMAT_CELLS");
            var formatRowsAction = FilterActionFactory.CreateAction("FORMAT_ROWS");
            var removeRowsAction = FilterActionFactory.CreateAction("REMOVE_ROWS");
            var telegramAction = FilterActionFactory.CreateAction("SEND_TO_TELEGRAM");
            var buyWithAction = FilterActionFactory.CreateAction("BUY_WITH_ACCOUNT");
            var webhookAction = FilterActionFactory.CreateAction("SEND_TO_WEBHOOK");
            var restockAction = FilterActionFactory.CreateAction("RESTOCK");
            var openItemPageAction = FilterActionFactory.CreateAction("OPEN_ITEM_PAGE");
            var openCheckoutPageAction = FilterActionFactory.CreateAction("OPEN_CHECKOUT_PAGE");

            Console.WriteLine($"✓ FormatCellsAction: {formatCellsAction?.DisplayName ?? "FAILED"}");
            Console.WriteLine($"✓ FormatRowsAction: {formatRowsAction?.DisplayName ?? "FAILED"}");
            Console.WriteLine($"✓ RemoveRowsAction: {removeRowsAction?.DisplayName ?? "FAILED"}");
            Console.WriteLine($"✓ SendToTelegramAction: {telegramAction?.DisplayName ?? "FAILED"}");
            Console.WriteLine($"✓ BuyWithAccountAction: {buyWithAction?.DisplayName ?? "FAILED"}");
            Console.WriteLine($"✓ SendToWebhookAction: {webhookAction?.DisplayName ?? "FAILED"}");
            Console.WriteLine($"✓ RestockFilterAction: {restockAction?.DisplayName ?? "FAILED"}");
            Console.WriteLine($"✓ OpenItemPageAction: {openItemPageAction?.DisplayName ?? "FAILED"}");
            Console.WriteLine($"✓ OpenCheckoutPageAction: {openCheckoutPageAction?.DisplayName ?? "FAILED"}");

            // Test UI configurators (separate from business logic)
            var formatCellsUI = FilterActionUIRegistry.GetUIConfigurator("FORMAT_CELLS");
            var formatRowsUI = FilterActionUIRegistry.GetUIConfigurator("FORMAT_ROWS");
            var removeRowsUI = FilterActionUIRegistry.GetUIConfigurator("REMOVE_ROWS");
            var telegramUI = FilterActionUIRegistry.GetUIConfigurator("SEND_TO_TELEGRAM");
            var buyWithUI = FilterActionUIRegistry.GetUIConfigurator("BUY_WITH_ACCOUNT");
            var webhookUI = FilterActionUIRegistry.GetUIConfigurator("SEND_TO_WEBHOOK");
            var restockUI = FilterActionUIRegistry.GetUIConfigurator("RESTOCK");
            var openItemPageUI = FilterActionUIRegistry.GetUIConfigurator("OPEN_ITEM_PAGE");
            var openCheckoutPageUI = FilterActionUIRegistry.GetUIConfigurator("OPEN_CHECKOUT_PAGE");

            Console.WriteLine($"✓ FormatCellsUI: {(formatCellsUI != null ? "FOUND" : "FAILED")}");
            Console.WriteLine($"✓ FormatRowsUI: {(formatRowsUI != null ? "FOUND" : "FAILED")}");
            Console.WriteLine($"✓ RemoveRowsUI: {(removeRowsUI != null ? "FOUND" : "FAILED")}");
            Console.WriteLine($"✓ TelegramUI: {(telegramUI != null ? "FOUND" : "FAILED")}");
            Console.WriteLine($"✓ BuyWithUI: {(buyWithUI != null ? "FOUND" : "FAILED")}");
            Console.WriteLine($"✓ WebhookUI: {(webhookUI != null ? "FOUND" : "FAILED")}");
            Console.WriteLine($"✓ RestockUI: {(restockUI != null ? "FOUND" : "FAILED")}");
            Console.WriteLine($"✓ OpenItemPageUI: {(openItemPageUI != null ? "FOUND" : "FAILED")}");
            Console.WriteLine($"✓ OpenCheckoutPageUI: {(openCheckoutPageUI != null ? "FOUND" : "FAILED")}");

            // Test legacy migration
            var legacyFormatCells = FilterActionFactory.CreateFromLegacyAction("Format cells");
            var legacyFormatRows = FilterActionFactory.CreateFromLegacyAction("Format rows");
            var legacyRemoveRows = FilterActionFactory.CreateFromLegacyAction("Remove rows");
            var legacyTelegram = FilterActionFactory.CreateFromLegacyAction("Send to Telegram");
            var legacyBuyWith = FilterActionFactory.CreateFromLegacyAction("Buy with TestUser");
            var legacyOpenItemPage = FilterActionFactory.CreateFromLegacyAction("Open item page");
            var legacyOpenCheckoutPage = FilterActionFactory.CreateFromLegacyAction("Open checkout page");

            Console.WriteLine($"✓ Legacy 'Format cells': {legacyFormatCells?.DisplayName ?? "FAILED"}");
            Console.WriteLine($"✓ Legacy 'Format rows': {legacyFormatRows?.DisplayName ?? "FAILED"}");
            Console.WriteLine($"✓ Legacy 'Remove rows': {legacyRemoveRows?.DisplayName ?? "FAILED"}");
            Console.WriteLine($"✓ Legacy 'Send to Telegram': {legacyTelegram?.DisplayName ?? "FAILED"}");
            Console.WriteLine($"✓ Legacy 'Buy with TestUser': {legacyBuyWith?.DisplayName ?? "FAILED"}");
            Console.WriteLine($"✓ Legacy 'Open item page': {legacyOpenItemPage?.DisplayName ?? "FAILED"}");
            Console.WriteLine($"✓ Legacy 'Open checkout page': {legacyOpenCheckoutPage?.DisplayName ?? "FAILED"}");

            // Test getting all available actions
            var allActions = FilterActionFactory.GetAllAvailableActions();
            Console.WriteLine($"✓ Total available actions: {allActions.Count()}");

            Console.WriteLine("Improved Filter Actions System test completed successfully!");
        }

        /// <summary>
        /// Test action execution with mock data
        /// </summary>
        public static async Task TestActionExecutionAsync()
        {
            Console.WriteLine("\nTesting Action Execution...");

            // Create a mock filter
            var filter = new XFilterClass
            {
                Alias = "Test Filter",
                Enabled = true,
                ActionHandler = FilterActionFactory.CreateAction("SEND_TO_TELEGRAM")
            };

            // Create mock context
            var context = new FilterActionContext
            {
                FilterRule = filter,
                CurrentRow = CreateMockDataRow()
            };

            // Test execution
            if (filter.ActionHandler != null)
            {
                var result = await filter.ActionHandler.ExecuteAsync(context);
                Console.WriteLine($"✓ Action execution result: {result.Success} - {result.Message}");
            }

            Console.WriteLine("Action execution test completed!");
        }

        /// <summary>
        /// Test UI configuration separation
        /// </summary>
        public static void TestUIConfigurationSeparation()
        {
            Console.WriteLine("\nTesting UI Configuration Separation...");

            // Test that actions don't have UI logic
            var telegramAction = FilterActionFactory.CreateAction("SEND_TO_TELEGRAM");
            Console.WriteLine($"✓ Telegram action created without UI dependencies: {telegramAction != null}");

            // Test that UI configurators provide declarative configuration
            var telegramUIConfig = FilterActionUIRegistry.GetUIConfigurator("SEND_TO_TELEGRAM");
            if (telegramUIConfig != null)
            {
                var config = telegramUIConfig.GetUIConfiguration();
                Console.WriteLine($"✓ Telegram UI config - ShowColumnSelection: {config.ShowColumnSelection}");
                Console.WriteLine($"✓ Telegram UI config - ShowFormatControls: {config.ShowFormatControls}");
            }

            var formatCellsUIConfig = FilterActionUIRegistry.GetUIConfigurator("FORMAT_CELLS");
            if (formatCellsUIConfig != null)
            {
                var config = formatCellsUIConfig.GetUIConfiguration();
                Console.WriteLine($"✓ FormatCells UI config - ShowColumnSelection: {config.ShowColumnSelection}");
                Console.WriteLine($"✓ FormatCells UI config - ShowFormatControls: {config.ShowFormatControls}");
            }

            Console.WriteLine("UI Configuration Separation test completed!");
        }

        /// <summary>
        /// Test serialization and deserialization
        /// </summary>
        public static void TestSerialization()
        {
            Console.WriteLine("\nTesting Serialization...");

            // Create an action with data
            var webhookAction = new SendToWebhookAction
            {
                WebhookUrl = "https://example.com/webhook"
            };

            // Test serialization
            var serializedData = webhookAction.SerializeActionData();
            Console.WriteLine($"✓ Serialized data: {serializedData.Count} items");

            // Test deserialization
            var newWebhookAction = new SendToWebhookAction();
            newWebhookAction.DeserializeActionData(serializedData);
            Console.WriteLine($"✓ Deserialized URL: {newWebhookAction.WebhookUrl}");

            Console.WriteLine("Serialization test completed!");
        }

        private static DataRow CreateMockDataRow()
        {
            var table = new DataTable();
            table.Columns.Add("Title", typeof(string));
            table.Columns.Add("Price", typeof(decimal));
            table.Columns.Add("Blob", typeof(object));

            var row = table.NewRow();
            row["Title"] = "Test Item";
            row["Price"] = 99.99m;
            row["Blob"] = new object(); // Mock object for testing

            return row;
        }

        /// <summary>
        /// Test the new browser actions specifically
        /// </summary>
        public static async Task TestBrowserActionsAsync()
        {
            Console.WriteLine("\nTesting Browser Actions...");

            try
            {
                // Test OpenItemPageAction
                var openItemPageAction = FilterActionFactory.CreateAction("OPEN_ITEM_PAGE");
                if (openItemPageAction != null)
                {
                    Console.WriteLine($"✓ OpenItemPageAction created: {openItemPageAction.DisplayName}");

                    // Test validation
                    var filter = new XFilterClass { Alias = "Test Filter" };
                    var isValid = openItemPageAction.ValidateConfiguration(filter, out string errorMessage);
                    Console.WriteLine($"✓ OpenItemPageAction validation: {isValid} - {errorMessage ?? "No errors"}");

                    // Test serialization
                    var serializedData = openItemPageAction.SerializeActionData();
                    Console.WriteLine($"✓ OpenItemPageAction serialization: {serializedData.Count} items");
                }

                // Test OpenCheckoutPageAction
                var openCheckoutPageAction = FilterActionFactory.CreateAction("OPEN_CHECKOUT_PAGE");
                if (openCheckoutPageAction != null)
                {
                    Console.WriteLine($"✓ OpenCheckoutPageAction created: {openCheckoutPageAction.DisplayName}");

                    // Test validation
                    var filter = new XFilterClass { Alias = "Test Filter" };
                    var isValid = openCheckoutPageAction.ValidateConfiguration(filter, out string errorMessage);
                    Console.WriteLine($"✓ OpenCheckoutPageAction validation: {isValid} - {errorMessage ?? "No errors"}");

                    // Test serialization
                    var serializedData = openCheckoutPageAction.SerializeActionData();
                    Console.WriteLine($"✓ OpenCheckoutPageAction serialization: {serializedData.Count} items");
                }

                // Test UI configurators
                var openItemPageUI = FilterActionUIRegistry.GetUIConfigurator("OPEN_ITEM_PAGE");
                if (openItemPageUI != null)
                {
                    var config = openItemPageUI.GetUIConfiguration();
                    Console.WriteLine($"✓ OpenItemPageUI config - ShowColumnSelection: {config.ShowColumnSelection}, ShowFormatControls: {config.ShowFormatControls}");
                }

                var openCheckoutPageUI = FilterActionUIRegistry.GetUIConfigurator("OPEN_CHECKOUT_PAGE");
                if (openCheckoutPageUI != null)
                {
                    var config = openCheckoutPageUI.GetUIConfiguration();
                    Console.WriteLine($"✓ OpenCheckoutPageUI config - ShowColumnSelection: {config.ShowColumnSelection}, ShowFormatControls: {config.ShowFormatControls}");
                }

                Console.WriteLine("Browser Actions test completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Browser Actions test failed: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Run all tests
        /// </summary>
        public static void RunAllTests()
        {
            try
            {
                TestActionFactory();
                // Use Task.Run to avoid potential deadlocks with GetAwaiter().GetResult()
                Task.Run(async () => await TestActionExecutionAsync()).Wait();
                Task.Run(async () => await TestBrowserActionsAsync()).Wait();
                TestUIConfigurationSeparation();
                TestSerialization();
                Console.WriteLine("\n🎉 All improved architecture tests passed successfully!");
                Console.WriteLine("\n📋 Key Improvements Demonstrated:");
                Console.WriteLine("   ✓ Actions contain only business logic");
                Console.WriteLine("   ✓ UI configuration is declarative and separate");
                Console.WriteLine("   ✓ No reflection overhead in normal operation");
                Console.WriteLine("   ✓ Easy to unit test actions independently");
                Console.WriteLine("   ✓ Clean separation of concerns");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n❌ Test failed: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
            }
        }
    }
}
