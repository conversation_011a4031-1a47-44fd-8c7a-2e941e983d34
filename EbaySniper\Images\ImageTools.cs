﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using DevExpress.XtraBars.Alerter;
using uBuyFirst.Network;
using uBuyFirst.Other;
using uBuyFirst.Prefs;
using uBuyFirst.Search;
using uBuyFirst.Tools;

namespace uBuyFirst.Images
{
    public static class ImageTools
    {
        public static FixedSizedQueue<Task<ArrayList>> ImageQueueAvatars = new(100);
        public static FixedSizedQueue<Task<ArrayList>> ImageQueueGallery = new(200);
        public static SemaphoreSlim SemaphoreImage1 = new(2);

        private static readonly HttpClient s_client = new(new Http2CustomHandler(Helpers.BuyItNowConfirmation) { WindowsProxyUsePolicy = WindowsProxyUsePolicy.UseWinInetProxy }) { Timeout = TimeSpan.FromSeconds(120) };

        internal static string UrlToFilePath(string url)
        {
            if (string.IsNullOrEmpty(url))
                return "";

            var diskFileName = Regex.Replace(url.Replace("http://", ""), "[^0-9A-Za-z]+", "");

            if (diskFileName == "")
                return "";
            var trimFileLength = 255 - Folders.Tmp.Length;
            var trimmeddiskFileName = diskFileName.Length > trimFileLength ? diskFileName.Substring(diskFileName.Length - trimFileLength) : diskFileName;
            var filePath = Path.Combine(Folders.Tmp, trimmeddiskFileName);

            return filePath;
        }

        public static async Task<Image> ReadFileToBitmap(string imagePath)
        {
            if (!File.Exists(imagePath))
                return null;

            try
            {
                // Load image in background thread to prevent UI blocking
                return await Task.Run(() =>
                {
                    using var stream = new FileStream(imagePath, FileMode.Open, FileAccess.Read, FileShare.Read);
                    return Image.FromStream(stream);
                }).ConfigureAwait(false);
            }
            catch
            {
                return null;
            }
        }

        public static async Task DownloadImageToDiskAsync(string url, string imagePath)
        {
            try
            {
                if (imagePath.Length > 260)
                    return;

                // Use ConfigureAwait(false) to prevent deadlocks and improve UI responsiveness
                using var response = await s_client.GetAsync(url).ConfigureAwait(false);
                using var stream = await response.Content.ReadAsStreamAsync().ConfigureAwait(false);
                if (stream != null && response.IsSuccessStatusCode)
                {
                    await Task.Run(() => SaveStreamToDisk(stream, imagePath));
                }
            }
            catch (Exception ex)
            {
                // ignored
            }
        }

        public static void QueueImageToDisk(string url)
        {
            var imagePath = UrlToFilePath(url);

            if (ImageQueueAvatars.Count > 50 || imagePath == "")
                return;

            var qTask = new QTask
            {
                Task = new Task<ArrayList>(() =>
                {
                    var result = CreateDownloadImageTask(url, imagePath, null);
                    return result.Result;
                }),
                Name = "Other images"
            };
            ImageQueueGallery.Enqueue(qTask.Task);
        }

        public static string QueueImageToRow(string url, DataRow row)
        {
            var imagePath = UrlToFilePath(url);

            if (imagePath == "")
                return "";

            var qTask = CreateImageDownloadTask(url, row);
            ImageQueueAvatars.Enqueue(qTask.Task);

            return imagePath;
        }

        private static QTask CreateImageDownloadTask(string url, DataRow row)
        {
            var imagePath = UrlToFilePath(url);

            var qTask = new QTask
            {
                Task = new Task<ArrayList>(() =>
                {
                    var result = CreateDownloadImageTask(url, imagePath, row);
                    return result.Result;
                }),
                Name = "Avatar"
            };

            return qTask;
        }

        private static async Task<ArrayList> CreateDownloadImageTask(string url, string downloadPath, DataRow row)
        {
            if (File.Exists(downloadPath))
                return new ArrayList
                {
                    downloadPath,
                    row
                };

            await DownloadImageToDiskAsync(url, downloadPath);

            return new ArrayList
            {
                downloadPath,
                row
            };
        }

        private static void SaveStreamToDisk(Stream stream, string imagePath)
        {
            try
            {
                var directoryPath = Path.GetDirectoryName(imagePath);

                if (directoryPath != null && !Directory.Exists(directoryPath))
                {
                    Directory.CreateDirectory(directoryPath);
                }

                using var fileStream = new FileStream(imagePath, FileMode.Create, FileAccess.Write, FileShare.None);
                stream.CopyTo(fileStream);
            }
            catch
            {
                // ignored
            }
        }

        public static async Task<string> GetImageFromDiskOrInternet(string imageUrl)
        {
            var imagePath = UrlToFilePath(imageUrl);

            if (File.Exists(imagePath))
                return imagePath;

            await DownloadImageToDiskAsync(imageUrl, imagePath);

            return imagePath;
        }

        public static async void ImageDownloadLoop()
        {
            ImageTools.SemaphoreImage1 = new SemaphoreSlim(2);
            ImageTools.ImageQueueAvatars = new FixedSizedQueue<Task<ArrayList>>(100);
            ImageTools.ImageQueueGallery = new FixedSizedQueue<Task<ArrayList>>(200);
            var currentContext = TaskScheduler.FromCurrentSynchronizationContext();
            await Task.Run(async () =>
            {
                while (true)
                {
                    try
                    {
                        //Debug.WriteLine($"--Thumbnail loop {DateTime.Now} ||| {ImageTools.SemaphoreImage1.CurrentCount}");
                        if (ImageQueueAvatars.TryDequeue(out var t))
                        {
                            //Debug.WriteLine($"--Thumbnail Deque {DateTime.Now}");

                            await SemaphoreImage1.WaitAsync();
                            t.Start();

                            SemaphoreImage1.Release();
                            await t.ContinueWith(async _ =>
                            {
                                if (Form1.Instance.Disposing || Form1.Instance.IsDisposed)
                                    return;
                                await AssignDownloadedThumbnail(t.Result);
                            }, currentContext);
                            Interlocked.Increment(ref Stat.AvatarReqCount);
                        }
                        else
                        {
                            if (ImageQueueGallery.TryDequeue(out t))
                            {
                                await SemaphoreImage1.WaitAsync();
                                t.Start();
                                SemaphoreImage1.Release();

                                Interlocked.Increment(ref Stat.OtherImagesReqCount);
                                if (ProgramState.Isdebug)
                                {
                                    //ReporterImages.Report(null);
                                }
                            }
                            else
                            {
                                await Task.Delay(500);
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        Debug.WriteLine(e);
                    }
                }
            }).ConfigureAwait(false);
        }

        private static async Task AssignDownloadedThumbnail(ArrayList t)
        {
            try
            {
                var filePath = t[0].ToString();
                var row = (DataRow)t[1];

                if (string.IsNullOrEmpty(filePath) || row == null)
                    return;

                var bitmap = await ReadFileToBitmap(filePath);
                if (bitmap == null)
                    return;

                if (bitmap.Size.Height > 500 || bitmap.Size.Width > 500)
                    bitmap = ImageProcessor.ScaleImage(bitmap, 500, 500);
                row["Thumbnail"] = bitmap;

                await AssignThumbnailsToAlertWindows(row["ItemID"].ToString(), filePath);
            }
            catch (Exception)
            {
                // ignored
            }
        }

        private static async Task AssignThumbnailsToAlertWindows(string itemID, string filePath)
        {
            if (!AlertOptionsClass.AlertOptions.ShowImage)
                return;

            var alertForm = AlertByItemID(itemID, AlertOptionsClass.AlertControl1.PostponedFormList);
            if (alertForm == null)
            {
                alertForm = AlertByItemID(itemID, AlertOptionsClass.AlertControl1.AlertFormList);
            }

            if (alertForm != null)
            {
                var imageBitmap = await ReadFileToBitmap(filePath);
                if (imageBitmap != null)
                {
                    var maxWidth = AlertOptionsClass.AlertOptions.Width / 3;
                    if (imageBitmap.Size.Width > maxWidth)
                    {
                        imageBitmap = ImageProcessor.ResizeImage(imageBitmap, (int)maxWidth, imageBitmap.Size.Height, true, false);
                    }

                    alertForm.AlertInfo.Image = imageBitmap;
                }
            }
        }

        private static AlertForm AlertByItemID(string itemID, List<AlertForm> alertForm)
        {
            var i = 0;
            while (alertForm.Count > i)
            {
                var rowAlert = (DataRow)alertForm[i].AlertInfo.Tag;
                if (rowAlert?.RowState == DataRowState.Added && rowAlert["ItemID"] != DBNull.Value && itemID == rowAlert["ItemID"].ToString())
                {
                    return alertForm[i];
                }

                i++;
            }

            return null;
        }
    }
}
