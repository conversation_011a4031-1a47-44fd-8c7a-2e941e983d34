﻿using System;
using System.Collections.Generic;

namespace uBuyFirst.Search
{
    public class FoundItem
    {
        public API.ShoppingAPI.ShoppingAPIJson.SimpleItemType? SimpleItem;
        public Keyword2Find? Keyword2Find;
        public string ItemID;
        public SearchSource ItemSource;
        public bool IsMultivariation;
        public bool IsInitialSearch;
        public DateTime StartTime;
        public string Seller;
        public global::BrowseAPI.Item? BrowseItem;
        public string SellerInfoJson;
        public string[]? CategoryPath;

        public FoundItem(API.ShoppingAPI.ShoppingAPIJson.SimpleItemType simpleItem, Keyword2Find keyword2Find, string itemID, DateTime startTime, SearchSource itemSource, bool isMultivariation, bool isInitialSearch, string seller, global::BrowseAPI.Item browseItem, string sellerInfoJson,string[] categoryPath)
        {
            CategoryPath = categoryPath;
            SimpleItem = simpleItem;
            Keyword2Find = keyword2Find;
            ItemID = itemID;
            ItemSource = itemSource;
            IsMultivariation = isMultivariation;
            IsInitialSearch = isInitialSearch;
            StartTime = startTime;
            Seller = seller;
            BrowseItem = browseItem;
            SellerInfoJson = sellerInfoJson;            
        }
    }
}
