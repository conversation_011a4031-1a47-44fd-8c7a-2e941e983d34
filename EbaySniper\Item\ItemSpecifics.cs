﻿using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using BrowseAPI;
using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Repository;
using DevExpress.XtraGrid.Views.BandedGrid;
using DevExpress.XtraLayout;
using eBay.Service.Core.Soap;
using uBuyFirst.CustomClasses;
using uBuyFirst.Grid;

namespace uBuyFirst.Item
{
    static class ItemSpecifics
    {
        public static CustomBindingList<CategorySpecific> CategorySpecificsList;

        public static void AddSpecificToLayoutControl(string columnName, LayoutControl layoutControl)
        {
            if (layoutControl.Controls.ContainsKey("layout" + columnName.Replace(" ", "")))
                return;
            if (layoutControl.Controls.ContainsKey("control" + columnName.Replace(" ", "")))
                return;

            var labelControl = new LabelControl
            {
                Name = "control" + columnName.Replace(" ", ""),
                StyleController = layoutControl
            };
            layoutControl.Controls.Add(labelControl);
            var layoutControlItem = new LayoutControlItem
            {
                Name = "layout" + columnName.Replace(" ", ""),
                CustomizationFormText = columnName,
                Text = columnName,
                TextSize = new Size(101, 13),
                Control = labelControl
            };
            layoutControl.HiddenItems.AddRange(new BaseLayoutItem[] { layoutControlItem });
        }

        public static void SetSpecificForLayoutControlValue(LayoutControl layoutControl, DataRow row)
        {
            foreach (var specific in CategorySpecificsList)
            {
                var control = layoutControl.GetControlByName("control" + specific.ToString().Replace(" ", ""));
                if (control != null)
                    control.Text = row[specific.ToString()].ToString();
            }
        }

        private static void RemoveSpecificFromLayoutControl(string categoryName, LayoutControl layoutControl)
        {
            var itemDetail = layoutControl.Items.FindByName("layout" + categoryName);
            if (itemDetail != null)
            {
                var index = layoutControl.Items.IndexOf(itemDetail);
                if (index > -1)
                {
                    layoutControl.Items.RemoveAt(index);
                }

                index = layoutControl.HiddenItems.IndexOf(itemDetail);
                if (index > -1)
                {
                    layoutControl.HiddenItems.RemoveAt(index);
                }

                var layoutControlItem = (LayoutControlItem)itemDetail;
                if (layoutControlItem is { Control: not null })
                {
                    layoutControl.Controls.Remove(layoutControlItem.Control);
                    layoutControlItem.Control.Dispose();
                }

                itemDetail.Dispose();
            }
        }

        public static void UpdateItemDetailsPanelSpecifics(AdvBandedGridView grView, DataRow row, LayoutControl layoutControl1)
        {
            foreach (var specific in CategorySpecificsList)
            {
                var dataSource = (DataTable)grView.GridControl.DataSource;
                if (dataSource.Columns.Contains(specific.ToString()))
                {
                    var control = layoutControl1.GetControlByName("control" + specific.ToString().Replace(" ", ""));
                    if (control != null)
                        control.Text = row[specific.ToString()].ToString();
                }
            }
        }

        public static void CategorySpecificsList_ItemDeleting(object sender, CategorySpecific e)
        {
            var uniqGrids = GridBuilder.GetUniqGrids(Form1.Instance._ebaySearches.ChildrenCore);
            foreach (var grView in uniqGrids)
            {
                var dataTable = (DataTable)grView.GridControl.DataSource;
                if (dataTable.Columns.Contains(e.CategoryName))
                    dataTable.Columns.Remove(e.CategoryName);

                if (grView.Columns.Contains(grView.Columns.ColumnByFieldName(e.CategoryName)))
                    grView.Columns.Remove(grView.Columns.ColumnByFieldName(e.CategoryName));
            }

            RemoveSpecificFromLayoutControl(e.CategoryName.Replace(" ", ""), Form1.Instance.layoutControl1);
        }

        public static void CategorySpecificsList_ListChanged(object sender, ListChangedEventArgs e)
        {
            if (e.ListChangedType != ListChangedType.ItemAdded)
                return;

            var uniqGrids = GridBuilder.GetUniqGrids(Form1.Instance._ebaySearches.ChildrenCore);
            foreach (var grView in uniqGrids)
            {
                var dataTable = (DataTable)grView.GridControl.DataSource;

                var columnName = CategorySpecificsList[e.NewIndex].CategoryName;
                if (dataTable.Columns.Contains(columnName))
                    continue;
                dataTable.Columns.Add(columnName);

                AddCustomColumnToGridView(grView, columnName);

                AddSpecificToLayoutControl(columnName, Form1.Instance.layoutControl1);
            }
        }

        public static void AddCustomColumnToGridView(AdvBandedGridView grView, string columnName)
        {
            grView.BeginUpdate();
            var column = (BandedGridColumn)grView.Columns.AddVisible(columnName);
            column.Caption = columnName;
            column.AutoFillDown = true;
            column.OptionsColumn.AllowEdit = false;
            if (column.Name.Contains("Reasoning"))
            {
                column.AppearanceCell.Options.UseTextOptions = true;
                column.AppearanceCell.TextOptions.WordWrap = WordWrap.Wrap;
                column.AppearanceCell.TextOptions.VAlignment = VertAlignment.Center;
                column.ColumnEdit = new RepositoryItemMemoEdit();
            }
            grView.Bands.LastVisibleBand.Columns.Add(column);
            grView.EndUpdate();
        }

        public static void AssignItemSpecificsToRow(DataRow row, NameValueListTypeCollection specificsList)
        {
            foreach (var specificName in CategorySpecificsList)
            {
                var nameValueListTypes = specificsList.Cast<NameValueListType>();
                var val = nameValueListTypes.FirstOrDefault(itemSpecific => itemSpecific.Name == specificName.CategoryName);
                if (row.Table.Columns.Contains(specificName.ToString()))
                {
                    if (val != null)
                        row[specificName.ToString()] = string.Join(",", val.Value.ToArray());
                }
            }
        }

        public static void AssignAspectsToRow(DataRow row, Dictionary<string, string> aspectDict)
        {
            foreach (var specificName in CategorySpecificsList)
            {
                if (aspectDict.ContainsKey(specificName.CategoryName))
                {
                    if (row.Table.Columns.Contains(specificName.ToString()))
                    {
                        row[specificName.ToString()] = string.Join(",", aspectDict[specificName.CategoryName]);
                    }
                }
            }
        }

        public static NameValueListTypeCollection ConvertAspectsToSpecifics(Dictionary<string, string> aspectDict)
        {
            var itemSpecifics = new NameValueListTypeCollection();
            foreach (var keyValue in aspectDict)
            {
                var values = keyValue.Value.Split(',');
                var stringCollection = new StringCollection();
                stringCollection.AddRange(values);
                itemSpecifics.Add(new NameValueListType()
                {
                    Name = keyValue.Key,
                    Value = stringCollection
                });
            }

            return itemSpecifics;
        }

        public static Dictionary<string, string> BuildAspectDict(AspectGroup[] aspectGroups)
        {
            var aspectsLists = aspectGroups.Select(group => group.Aspects);
            var aspectDict = new Dictionary<string, string>();
            foreach (var aspectList in aspectsLists)
            {
                foreach (var aspect in aspectList)
                {
                    if (aspectDict.ContainsKey(aspect.LocalizedName))
                        aspectDict[aspect.LocalizedName] = aspectDict[aspect.LocalizedName] + ", " + string.Join(", ", aspect.LocalizedValues);
                    else
                        aspectDict.Add(aspect.LocalizedName, string.Join(", ", aspect.LocalizedValues));
                }
            }

            return aspectDict;
        }

        public static NameValueListTypeCollection ParseItemAspectsBrowseAPI(AspectGroup[]? productAspectGroups, TypedNameValue[]? itemLocalizedAspects)
        {
            var aspectsDict = new Dictionary<string, string>();
            if (itemLocalizedAspects != null)
            {
                aspectsDict = itemLocalizedAspects.ToDictionary(aspect => aspect.Name, aspect => aspect.Value);
            }

            if (productAspectGroups != null)
            {
                var productAspectsDict = BuildAspectDict(productAspectGroups);
                foreach (var aspect in productAspectsDict)
                {
                    if (!aspectsDict.ContainsKey(aspect.Key))
                        aspectsDict.Add(aspect.Key, aspect.Value);
                }
            }

            var itemSpecifics = ConvertAspectsToSpecifics(aspectsDict);
            return itemSpecifics;
        }
    }
}
