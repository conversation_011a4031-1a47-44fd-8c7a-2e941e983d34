using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;

namespace uBuyFirst.Images
{
    internal static class ImageCleaner
    {
        private static System.Timers.Timer _hourlyTimer;

        public static void StartCleanUpTimer()
        {
            _hourlyTimer = new System.Timers.Timer(60 * 60 * 1000);
            _hourlyTimer.Elapsed += (sender, args) => CleanUpExpiredImages();
            _hourlyTimer.Start();
        }

        public static void CleanUpExpiredImages()
        {
            Task.Run(() =>
            {
                try
                {
                    var tmpfolder = Path.Combine(Path.GetTempPath(), "uBuyFirst");
                    if (!Directory.Exists(tmpfolder))
                        return;
                    var di = new DirectoryInfo(tmpfolder);
                    var files = di.GetFileSystemInfos();
                    var i = 0;
                    while (files.Length > i)
                    {
                        if ((DateTime.UtcNow - files[i].CreationTimeUtc).TotalHours > 5)
                            File.Delete(files[i].FullName);
                        i++;
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine(ex);
                }
            });
        }
    }
}