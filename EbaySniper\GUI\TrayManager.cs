﻿using System.Windows.Forms;
using uBuyFirst.Properties;

namespace uBuyFirst.GUI
{
    internal class TrayManager
    {
        private readonly Form1 _form1;

        public TrayManager(Form1 form1)
        {
            _form1 = form1;
        }

        public void PopupFromTray(object? sender)
        {
            if (sender != null)
            {
                _form1.ToolStripMenuItemShow.Text = En_US.Form1_PopupFromTray_Hide;
                _form1.Show();
                _form1.WindowState = FormWindowState.Maximized;
                _form1.Activate();
            }
            else
            {
                if (_form1.WindowState == FormWindowState.Minimized && _form1.barCheckItemMaximizewindow.Checked)
                {
                    _form1.ToolStripMenuItemShow.Text = En_US.Form1_PopupFromTray_Hide;
                    _form1.Show();
                    _form1.WindowState = FormWindowState.Maximized;
                    _form1.Activate();
                }
            }
        }

        public void SwitchMinimizedState()
        {
            if (_form1.WindowState == FormWindowState.Minimized)
            {
                _form1.Show();
                _form1.WindowState = FormWindowState.Maximized;
                _form1.Activate();
            }
            else
            {
                _form1.WindowState = FormWindowState.Minimized;
            }
        }

        public void SetTrayLabel()
        {
            if (_form1.WindowState == FormWindowState.Minimized)
            {
                _form1.ToolStripMenuItemShow.Text = En_US.Form1_TrayIcon_Click_Show;
            }
            else
            {
                _form1.ToolStripMenuItemShow.Text = En_US.Form1_PopupFromTray_Hide;
            }
        }
    }
}
