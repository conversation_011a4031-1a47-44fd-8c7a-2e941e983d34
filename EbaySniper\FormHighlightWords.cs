﻿using System;
using System.Diagnostics;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;
using uBuyFirst.GUI;
using uBuyFirst.Prefs;

namespace uBuyFirst
{
    public partial class FormHighlightWords : RibbonForm
    {
        public FormHighlightWords()
        {
            InitializeComponent();
            LoadWordsHighlight();
            this.StartPosition = FormStartPosition.CenterParent;
        }

        private void LoadWordsHighlight()
        {
            WordsHighlight1.Lines = UserSettings.Highlightsvalues.Words1;
            WordsHighlight2.Lines = UserSettings.Highlightsvalues.Words2;
            WordsHighlight3.Lines = UserSettings.Highlightsvalues.Words3;
            colorPickEdit1.Color = UserSettings.Highlightsvalues.Color1;
            colorPickEdit2.Color = UserSettings.Highlightsvalues.Color2;
            colorPickEdit3.Color = UserSettings.Highlightsvalues.Color3;
        }

        private void ApplyWordsHighlight()
        {
            UserSettings.HighlightsvaluesVersion++;
            WordsHighlight1.Text = WordsHighlight1.Text.Trim();
            WordsHighlight2.Text = WordsHighlight2.Text.Trim();
            WordsHighlight3.Text = WordsHighlight3.Text.Trim();
            UserSettings.Highlightsvalues.Color1 = colorPickEdit1.Color;
            UserSettings.Highlightsvalues.Color2 = colorPickEdit2.Color;
            UserSettings.Highlightsvalues.Color3 = colorPickEdit3.Color;
            UserSettings.Highlightsvalues.HexColor1 = WebView.HexConverter(colorPickEdit1.Color);
            UserSettings.Highlightsvalues.HexColor2 = WebView.HexConverter(colorPickEdit2.Color);
            UserSettings.Highlightsvalues.HexColor3 = WebView.HexConverter(colorPickEdit3.Color);
            UserSettings.Highlightsvalues.Words1 = WordsHighlight1.Lines.ToList().Where(word => !string.IsNullOrWhiteSpace(word)).ToArray();
            UserSettings.Highlightsvalues.Words2 = WordsHighlight2.Lines.ToList().Where(word => !string.IsNullOrWhiteSpace(word)).ToArray();
            UserSettings.Highlightsvalues.Words3 = WordsHighlight3.Lines.ToList().Where(word => !string.IsNullOrWhiteSpace(word)).ToArray();
        }

        private void FormHighlightWords_FormClosing(object sender, FormClosingEventArgs e)
        {
            ApplyWordsHighlight();
        }

        private void WordsHighlight3_MouseLeave(object sender, EventArgs e)
        {
            ApplyWordsHighlight();
        }

        private void FormHighlightWords_Load(object sender, EventArgs e)
        {
            //AutoMeasurement.Client.TrackScreenView("Screen - " + Text);
        }

        private void FormHighlightWords_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == 27)
            {
                Close();
            }
        }

        private void hyperlinkLabelControl1_Click(object sender, EventArgs e)
        {
            var hyperLink = (HyperlinkLabelControl)sender;
            if (hyperLink.Tag != null)
                Process.Start(hyperLink.Tag.ToString());
        }
    }
}
