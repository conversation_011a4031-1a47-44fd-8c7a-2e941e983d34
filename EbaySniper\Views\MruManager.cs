﻿using System.Drawing;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraEditors.Repository;
using uBuyFirst.Prefs;

namespace uBuyFirst.Views
{
    static class MruManager
    {
        public static void AddMissingMruItem(string viewName, RepositoryItemMRUEdit mruEdit)
        {
            //var existingItems = mruEdit.Items.ToArray().ToList().Distinct().Select(item => item.ToString());
            //if (existingItems.Contains(viewName))
            //  return;
            if (mruEdit.Items.IndexOf(viewName) > -1)
                return;

            mruEdit.Items.BeginUpdate();
            mruEdit.Items.Add(viewName);
            mruEdit.Items.EndUpdate();
        }

        public static void DisableEdit(RepositoryItemMRUEdit repositoryItemViews)
        {
            UserSettings.CanShowEbaySearchEditor = false;
            repositoryItemViews.Buttons[0].Appearance.ForeColor = Color.FromArgb(0);
            repositoryItemViews.Buttons[0].Appearance.BackColor = Color.FromArgb(0);
            repositoryItemViews.Buttons[0].Appearance.Options.UseBackColor = false;
            repositoryItemViews.Buttons[0].Appearance.Options.UseForeColor = false;

            repositoryItemViews.Buttons[1].Visible = false;
            repositoryItemViews.Buttons[2].Visible = true;
            repositoryItemViews.TextEditStyle = TextEditStyles.DisableTextEditor;
            repositoryItemViews.Buttons[0].Kind = ButtonPredefines.Plus;
        }

        public static void EnableEdit(RepositoryItemMRUEdit repositoryItemViews)
        {
            repositoryItemViews.Buttons[0].Kind = ButtonPredefines.OK;
            UserSettings.CanShowEbaySearchEditor = true;
            repositoryItemViews.Buttons[0].Appearance.ForeColor = Color.LimeGreen;
            repositoryItemViews.Buttons[0].Appearance.BackColor = Color.LimeGreen;
            repositoryItemViews.Buttons[0].Appearance.Options.UseBackColor = true;
            repositoryItemViews.Buttons[0].Appearance.Options.UseForeColor = true;
            repositoryItemViews.Buttons[1].Visible = true;
            repositoryItemViews.Buttons[2].Visible = false;
            repositoryItemViews.TextEditStyle = TextEditStyles.Standard;
        }
    }
}