﻿using System;
using System.IO;
using System.Text.RegularExpressions;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using uBuyFirst.Prefs;
using uBuyFirst.SearchTerms;

namespace uBuyFirst
{
    public partial class FormSyncSearchTerms : DevExpress.XtraBars.Ribbon.RibbonForm
    {
        private string _filePath;

        public FormSyncSearchTerms()
        {
            InitializeComponent();
            checkEditEnableAutoFetch.Checked = UserSettings.SyncSearchTermsEnabled;
            txtFileUrl.Text = UserSettings.SyncSearchTermsUrl;
            timeSpanEditFetchInterval.EditValue = TimeSpan.FromSeconds(UserSettings.SyncSearchTermsInterval);
        }

        private async void btnFetchFile_Click(object sender, EventArgs e)
        {
            btnFetchFile.Enabled = false;
            var url = txtFileUrl.Text;

            if (Regex.IsMatch(url, SearchTermFetcher.GoogleSpreadsheetRegex))
                url = SearchTermFetcher.GetCsvExportUrl(url);

            _filePath = await SearchTermFetcher.DownloadKeywordsFileAsync(url);
            try
            {
                if (!string.IsNullOrEmpty(_filePath))
                {
                    memoPreviewFile.Text = File.ReadAllText(_filePath);

                    if (Form1.Instance._searchService is { Running: true })
                    {
                        await Form1.Instance.StopWorking();
                        Form1.Instance.ImportKeywordsFromFile(_filePath);
                        Form1.Instance.StartWorking();
                    }
                    else
                    {
                        Form1.Instance.ImportKeywordsFromFile(_filePath);
                    }
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"Failed to copy keywords file: {ex.Message}", "Import Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            btnFetchFile.Enabled = true;
        }

        private void checkEditEnableAutoFetch_CheckedChanged(object sender, EventArgs e)
        {
            UserSettings.SyncSearchTermsEnabled = checkEditEnableAutoFetch.Checked;
        }

        private void timeSpanEditFetchInterval_Properties_EditValueChanged(object sender, EventArgs e)
        {
            UserSettings.SyncSearchTermsInterval = (int)((TimeSpan)timeSpanEditFetchInterval.EditValue).TotalSeconds;
        }

        private void txtFileUrl_EditValueChanged(object sender, EventArgs e)
        {
            UserSettings.SyncSearchTermsUrl = txtFileUrl.Text;
        }
    }
}
