# Findings and Implementation Plan: TreeList Folders

This document outlines the findings from analyzing the existing codebase and presents a plan for implementing folder functionality within the eBay search TreeList (`treeList1`) in the uBuyFirst application.

## 1. Findings: Current System Analysis

*   **UI Control:** `DevExpress.XtraTreeList.TreeList` (`treeList1` in `Form1`) is used to display eBay searches.
*   **Data Source:** The TreeList's `DataSource` is set to an instance of the `QueryList` class (`_ebaySearches` field in `Form1`).
*   **Data Provisioning (`QueryList`):**
    *   `QueryList` implements `DevExpress.XtraTreeList.IVirtualTreeListData`, indicating it supplies data to the TreeList on demand (virtual mode).
    *   It contains a `public List<Keyword2Find> ChildrenCore` which holds the top-level search items.
    *   The `VirtualTreeGetChildNodes` method currently returns the entire `ChildrenCore` list when the TreeList requests root nodes. It does not explicitly handle requests for children of specific nodes within the method.
    *   `VirtualTreeGetCellValue` and `VirtualTreeSetCellValue` are empty, suggesting the TreeList relies on reflection or other events for data binding and editing of node properties.
*   **Hierarchy:** The current structure supports two levels:
    *   Level 0: `Keyword2Find` objects (representing main eBay searches), provided by `QueryList.ChildrenCore`.
    *   Level 1: `ChildTerm` objects (representing sub-searches), which are children of `Keyword2Find` objects and likely discovered by the TreeList through reflection on `Keyword2Find`.
*   **Persistence:**
    *   `QueryList` implements `IXmlSerializable` but the `ReadXml`/`WriteXml` methods are empty.
    *   Serialization and deserialization of the search list (`_ebaySearches.ChildrenCore`) occur externally, likely within `Settings.cs` or `SearchTermManager.cs`, using `System.Xml.Serialization.XmlSerializer`.
*   **Key Files Involved:**
    *   `Form1.cs`: Declares `_ebaySearches`.
    *   `Form1.Treelist.cs`: Contains TreeList event handlers (context menus, editing, drag/drop, styling, validation).
    *   `Search/QueryList.cs`: Defines the virtual data provider.
    *   `Keyword2Find.cs`: Defines the main search item object.
    *   `SubSearch/ChildTerm.cs` (or similar): Defines the sub-search item object.
    *   `Settings.cs` / `SearchTermManager.cs`: Likely handle persistence.

## 2. Implementation Plan for Folders

The goal is to allow users to group `Keyword2Find` items within folders in the TreeList.

*   **Step 1: Define Data Model Contracts**
    *   Create a common interface `ITreeListItem` for objects managed directly by `QueryList`'s hierarchy:
        ```csharp
        public enum TreeListItemType { Folder, Keyword }
        public interface ITreeListItem
        {
            int Id { get; set; } // Unique ID across all Folders & Keywords
            int? ParentId { get; set; } // ID of parent Folder (null for root items)
            string Name { get; set; } // Display Name (Folder Name or Keyword Alias)
            TreeListItemType ItemType { get; }
        }
        ```
    *   Create a new class `KeywordFolder : ITreeListItem` to represent folders. It will primarily contain `Id`, `ParentId`, `Name`, and `ItemType = TreeListItemType.Folder`.
    *   Modify the existing `Keyword2Find` class:
        *   Implement `ITreeListItem`.
        *   Add a `public int? ParentId { get; set; }` property.
        *   Implement `Name` property (e.g., `public string Name => this.Alias;`).
        *   Implement `ItemType` property (`public TreeListItemType ItemType => TreeListItemType.Keyword;`).
        *   Ensure `Keyword2Find` has a unique `Id` property (if not already present).
    *   The `ChildTerm` class remains unchanged; it is not part of the `ITreeListItem` hierarchy managed by `QueryList`.

*   **Step 2: Update `QueryList` (`Search/QueryList.cs`)**
    *   Change the internal storage: Modify `public readonly List<Keyword2Find> ChildrenCore` to `public List<ITreeListItem> ChildrenCore = new List<ITreeListItem>();` (consider removing `readonly` if external loading replaces the instance).
    *   Rewrite `VirtualTreeGetChildNodes(VirtualTreeGetChildNodesInfo info)`:
        *   If `info.Node == null`, set `info.Children` to `ChildrenCore.Where(item => item.ParentId == null).ToList<object>()`.
        *   If `info.Node is KeywordFolder folder`, set `info.Children` to `ChildrenCore.Where(item => item.ParentId == folder.Id).ToList<object>()`.
        *   If `info.Node is Keyword2Find keyword`, set `info.Children = new List<object>();` (allowing TreeList reflection to find `ChildTerm` children).
        *   Otherwise, set `info.Children = new List<object>();`.
    *   Add public helper methods to manage `ChildrenCore`: `AddItem(ITreeListItem)`, `RemoveItem(ITreeListItem)` (with recursive delete for folders), `MoveItem(ITreeListItem, newParentId)`, `GetItemById(int)`, `GenerateUniqueId()`.

*   **Step 3: Refactor TreeList Event Handlers (`Form1.Treelist.cs`)**
    *   **Type Handling:** In all relevant event handlers, get the node data via `treeList1.GetDataRecordByNode(e.Node)` and use type checks (`is KeywordFolder`, `is Keyword2Find`, `is ChildTerm`) to apply specific logic for context menus, cell editing, validation, styling, drag/drop, etc.
    *   **Data Operations:** Replace direct manipulation of `ChildrenCore` with calls to the new helper methods in the `_ebaySearches` (`QueryList`) instance (e.g., `_ebaySearches.AddItem(newFolder)`).
    *   **UI Refresh:** Call `treeList1.RefreshDataSource()` after any operation that modifies the data structure via the helper methods.
    *   **Drag and Drop:** Re-implement `treeList1_DragOver` to validate potential drops (Keyword/Folder into Folder, Keyword/Folder to root). Re-implement `treeList1_DragDrop` to call `_ebaySearches.MoveItem()` and refresh the TreeList.

*   **Step 4: Update Persistence Logic (External)**
    *   Locate the `XmlSerializer` code that saves/loads `_ebaySearches.ChildrenCore` (likely in `Settings.cs` or `SearchTermManager.cs`).
    *   Modify this code to handle `List<ITreeListItem>`.
    *   Ensure the serializer can handle both `KeywordFolder` and `Keyword2Find` types (e.g., using `[XmlInclude(typeof(KeywordFolder))]` on `ITreeListItem` or passing known types to the serializer constructor).
    *   Verify that `Id`, `ParentId`, and type information are correctly serialized and deserialized for all `ITreeListItem` objects to preserve the folder structure.

*   **Step 5: Enhance UI**
    *   Modify `treeList1_PopupMenuShowing` to add "New Folder" and "Rename Folder" menu items.
    *   Adjust the visibility and enabled state of existing menu items based on whether a folder, keyword, or sub-search node is selected.
    *   Update `treeList1_NodeCellStyle` to provide distinct visual styling for folder nodes.

## 3. Architecture Diagram (Mermaid)

```mermaid
graph LR
    subgraph Form1["Form1 / Form1.Treelist.cs"]
        TreeList[treeList1]
        EventHandlers["Event Handlers"]
    end
    subgraph QueryList["QueryList (_ebaySearches)"]
        ChildrenCore["ChildrenCore <br> (List<ITreeListItem>)"]
        IVTLD["IVirtualTreeListData <br> (VirtualTreeGetChildNodes)"]
        Helpers["Helper Methods <br> (AddItem, RemoveItem, MoveItem)"]
    end
    subgraph DataModel["Data Model"]
        ITreeListItem["ITreeListItem Interface"]
        KeywordFolder["KeywordFolder Class"]
        Keyword2Find["Keyword2Find Class"]
        ChildTerm["ChildTerm Class"]
    end
    subgraph Persistence["Settings.cs / SearchTermManager.cs"]
        Serializer["XmlSerializer Logic"]
    end
    TreeList -- DataSource --> QueryList
    TreeList -- Calls --> IVTLD
    EventHandlers -- Read Node Data --> TreeList
    EventHandlers -- Call --> Helpers
    EventHandlers -- Call --> TreeList[RefreshDataSource]
    IVTLD -- Reads --> ChildrenCore
    Helpers -- Modifies --> ChildrenCore
    ITreeListItem <|-- KeywordFolder
    ITreeListItem <|-- Keyword2Find
    Keyword2Find -- Contains --> ChildTerm
    Serializer -- Saves/Loads --> ChildrenCore
```

This plan provides a structured approach to implementing the folder feature by modifying the data model, updating the virtual data provider, refactoring UI interactions, and adjusting the persistence layer.