﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using DevExpress.Utils.About;
using GoogleAnalytics;
using NLog;
using NLog.Config;
using SumoLogic.Logging.NLog;
using uBuyFirst.License;
using uBuyFirst.Prefs;
using uBuyFirst.Tools;
using Analytics = uBuyFirst.Stats.Analytics;

namespace uBuyFirst.Other
{
    internal static class Loggers
    {
        private static readonly List<string> s_foundItemsLog = new();
        public static string BadCertificate { get; set; } = "";
        public static bool BadCertificateSent { get; set; }

        public static void SetupCloudLogging()
        {
            const string Layout = @"${LEVEL},${date:format=yyyy-MM-dd HH\\:mm\\:ss.fff}, ${message}${exception:format=tostring}${newline}";

            var targetInfo = new SumoLogicTarget
            {
                Url =
                    "https://endpoint4.collection.sumologic.com/receiver/v1/http/ZaVnC4dhaV2staGyeuS4UOc7M88ZZq9MwcgTQvLUIo2jIw4LjJMEBK9oOv7k0mMBXhkpgNADKDSTE1Ya-C7ot2oGJz1oAUriJviP_wp0ErvP1XRqlg4JZA==",
                SourceName = "Info",
                Layout = Layout,
                UseConsoleLog = false,
                Name = "uBuyFirst"
            };

            var targetErrors = new BufferedSumoLogicTarget
            {
                Url =
                    "https://endpoint4.collection.sumologic.com/receiver/v1/http/ZaVnC4dhaV1uJRHlDFzcH7NsDMqBrQnpqQKNpRfWqe0Hzwlo8Xl5vVZAANPyoM39e4tK_yNNlCWtw1FWXNhXl01BvxvRijqW8BPJFzf5V9f455BvbDjbEg==",
                SourceName = "Error",
                Layout = Layout,
                UseConsoleLog = false,
                Name = "uBuyFirst"
            };
            var targetAblyLog = new SumoLogicTarget
            {
                Url =
                    "https://endpoint4.collection.sumologic.com/receiver/v1/http/ZaVnC4dhaV11SHLsz0Ag0RJXDoRWg8tBsczIjcYl8PJwnnWHPA_JSmJ42fFUSMjgaHVDPGfWJGk6SmBm18SjOVDkSSfgoU58YdxKUSILEmRKiUG-0duxUw==",
                SourceName = "Ably",
                Layout = Layout,
                UseConsoleLog = false,
                Name = "uBuyFirst"
            };

            LogManager.Configuration.LoggingRules.Add(new LoggingRule("*", LogLevel.Error, LogLevel.Error, targetErrors));
            LogManager.Configuration.LoggingRules.Add(new LoggingRule("*", LogLevel.Warn, LogLevel.Warn, targetErrors));
            LogManager.Configuration.LoggingRules.Add(new LoggingRule("*", LogLevel.Info, LogLevel.Info, targetInfo));
            LogManager.Configuration.LoggingRules.Add(new LoggingRule("*", LogLevel.Trace, LogLevel.Trace, targetAblyLog));
            LogManager.ReconfigExistingLoggers();
        }

        public static void InitGoogleAnalyticsV4(string licenseKeyAndType, LicenseUtility.SubscriptionType subscriptionType)
        {
            Analytics.SetGAid(licenseKeyAndType);
            var applicationName = subscriptionType.ToString();
            var applicationVersion = ProgramState.UBFVersion;
            var userProperties = new UserProperties
            {
                app_version = new UserPropertyValue(applicationVersion), app_name = new UserPropertyValue(applicationName), hwid = new UserPropertyValue(ProgramState.HWID),
            };

            var userId = ProgramState.HWID;
                //if (!string.IsNullOrEmpty(Form1.LicenseUtility.License.SerialNumber))
                //{
                //    var gAid = ProgramState.SerialNumber;
                //    var customDimension1 = WebUtility.UrlEncode(ProgramState.HWID + ProgramState.SerialNumber);
                //    var customDimension3 = ProgramState.SerialNumber;
                //}
                //else
                //{
                //    var gAid = ProgramState.HWID;
                //    var customDimension1 = WebUtility.UrlEncode(ProgramState.HWID.Replace(";", "_") + ";Unknown");
                //    var customDimension3 = "Unknown";
                //}

                Form1.GoogleAnalytics = new Analytics(userId, userProperties);
                //var customDimension2 = WebUtility.UrlEncode(ProgramState.HWID);
        }

        public static void LogFoundItem(string itemId, string title, DateTime? datalistPostedTime, DateTime foundTime, string xFilterAlias, string sellerName)
        {
            var postedTime = datalistPostedTime?.ToString("s").Replace("T", " ");

            var filterLog = "";
            if (Program.LogFilters && !string.IsNullOrEmpty(xFilterAlias))
                filterLog = $",\"{xFilterAlias}\",\"{title}\"";

            var row = $"{itemId},{postedTime},{foundTime.ToString("s").Replace("T", " ")},{filterLog},{sellerName}";
            s_foundItemsLog.Add(row);
            if (s_foundItemsLog.Count > 10)
            {
                WriteFoundItemsLog();
            }
        }

        private static void WriteFoundItemsLog()
        {
            try
            {
                if (s_foundItemsLog.Count == 0)
                    return;

                if (!File.Exists(Path.Combine(Folders.Logs, "FoundItems.csv")))
                {
                    var columns = new[] { "ItemID", "Posted Time", "Found Time", "FilterLog", "Seller" };
                    File.AppendAllText(Path.Combine(Folders.Logs, "FoundItems.csv"), $@"{string.Join(",", columns)}" + "\r\n");
                }

                File.AppendAllText(Path.Combine(Folders.Logs, "FoundItems.csv"), $@"{string.Join("\r\n", s_foundItemsLog)}" + "\r\n");
                s_foundItemsLog.Clear();
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("_(info)_Writing FoundItems.csv :", ex);
            }
        }

        public static void LogError(Exception ex)
        {
            Form1.Log.Error("{0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, ex);

            if (ex.InnerException == null || !ex.Message.Contains("TaskWrapper"))
                return;

            Form1.Log.Error("{0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, ex.InnerException);
            if (ex.InnerException.InnerException != null && ex.Message.Contains("TaskWrapper"))
                Form1.Log.Error("{0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, ex.InnerException.InnerException);
        }

        public static void LogError(string errorMessage)
        {
            Form1.Log.Error("{0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, errorMessage);
        }

        public static void LogCertError(string errorMessage)
        {
            Form1.Log.Error("{0}, {1} CertException:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, errorMessage);
        }

        public static void EnableDebugLogger()
        {

            foreach (TraceListener? listener in Debug.Listeners)
            {
                if (listener?.Name == "debuglogger")
                    return;
            }

            var folder = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "uBuyFirst");
            var debugFileName = Path.Combine(folder, "debuglog.txt");
            Debug.Listeners.Add(new TextWriterTraceListener(debugFileName, @"debuglogger"));
            Debug.AutoFlush = true;
        }
    }
}
