﻿using System;
using System.Diagnostics;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using uBuyFirst.License;
using uBuyFirst.Other;
using uBuyFirst.Prefs;
using uBuyFirst.Properties;
using Microsoft.Win32;

namespace uBuyFirst
{
    public partial class FormTrialSubscriptionPrompt
    {
        public int NumDaysLeft { get; set; }
        public bool DialogCameFromWindowsStartup { get; set; }

        public Form MainForm { get; set; }

        public static LicenseUtility LicenseUtility;

        public FormTrialSubscriptionPrompt(int numDaysLeft=0, Form form = null, bool bFromStartup = false)
        {
            NumDaysLeft = numDaysLeft;
            DialogCameFromWindowsStartup = bFromStartup;
            MainForm = form;

            ProgramState.UBFVersion = ProductVersion;
            ProgramState.HWID = LicenseUtility.GetHWID();
            Folders.SetupFolders();

            InitializeComponent();

            // Set text from resource so if this is ever localized there are no hardcoded strings.
            lblActivationStatus.Text = En_US.TrialSubscriptionStatusLoading;
            ContinueTrialButton.Text = En_US.FormTrialSubscriptionPrompt_ContinueTrial;
            BuyNowButton.Text = En_US.FormTrialSubscriptionPrompt_BuyNow;
            EnterSoftwareKeyButton.Text = En_US.FormTrialSubscriptionPrompt_EnterSoftwareKey;
            FreeTutorialLabel.Text = En_US.FormTrialSubscriptionPrompt_TrialText;
            hyperlinkFreeTutorialLabelControl.Text = En_US.FormTrialSubscriptionPrompt_TrialLinkText;
            this.StartPosition = FormStartPosition.CenterParent;
        }

        private void FormSubscription_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == 27)
            {
                Close();
            }
        }

        private void linkLabel_Click(object sender, EventArgs e)
        {
            var linkLabel = (HyperlinkLabelControl)sender;
            Process.Start(linkLabel.Tag.ToString());
        }

        private void ContinueTrialButton_Click(object sender, EventArgs e)
        {
            if (!DialogCameFromWindowsStartup)
            {
                Close();
            } 
            else
            {
                // Launch ubuyfirst app if comes from windows startup.
                var currentDirectory = Environment.CurrentDirectory;
                Process.Start(currentDirectory + "\\uBuyFirst.exe");
                Close();
            }
        }

        private void CallTagForButton(object sender)
        {
            var sb = (SimpleButton)sender;
            Process.Start(sb.Tag.ToString());
        }

        private void BuyNowButton_Click(object sender, EventArgs e)
        {
            CallTagForButton(sender);
            Close();
        }

        private void EnterSoftwareKeyButton_Click(object sender, EventArgs e)
        {
            var fs = new FormSubscription();
            fs.Owner = MainForm;
            if (MainForm != null)
            {
                fs.Show();
            } 
            else
            {
                fs.ShowDialog();
            }
            Close();
        }

        private void FormTrialSubscriptionPrompt_Shown(object sender, EventArgs e)
        {
            //LicenseUtility
            LicenseUtility = new LicenseUtility();
            LicenseUtility.SetDefaultLimits();
            // await LicenseUtility.CheckLicense();

            var fromCurrentSynchronizationContext = TaskScheduler.FromCurrentSynchronizationContext();
            LicenseUtility
                .CheckLicense()
                    .ContinueWith(_ =>
                    {
                        if (LicenseUtility.CurrentSubscriptionType == LicenseUtility.SubscriptionType.Trial)
                        {
                            NumDaysLeft = LicenseUtility.TrialDaysLeft;
                            lblActivationStatus.Text = string.Format(En_US.FormTrialSubscriptionPrompt_DaysLeft, NumDaysLeft);
                        } 
                        else
                        {
                            // Prevent the Trial Dialog from running when the trial license is expired.
                            var rk = Registry.CurrentUser.OpenSubKey("SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run", true);
                            rk?.DeleteValue("uBuyFirstTrialPrompt", false);
                            // Close the form 
                            Close();
                        }
                        LicenseUtility.LicenseCheckCompleted = true;
                    }, fromCurrentSynchronizationContext);


        }
    }
}
