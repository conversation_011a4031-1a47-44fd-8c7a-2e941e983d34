﻿using System;
using System.ComponentModel;
using System.Data;
using System.Diagnostics.CodeAnalysis;

namespace uBuyFirst.Filters
{
    [SuppressMessage("ReSharper", "UnusedMember.Global")]
    public class MyDescriptor : PropertyDescriptor
    {
        protected MyDescriptor(string name, Attribute[] attrs) : base(name, attrs)
        {
        }

        protected MyDescriptor(MemberDescriptor descr) : base(descr)
        {
        }

        protected MyDescriptor(MemberDescriptor descr, Attribute[] attrs) : base(descr, attrs)
        {
        }

        public MyDescriptor(string name, Type propertyType, Type componentType) : this(name, null)
        {
            PropertyType = propertyType;
            ComponentType = componentType;
        }

        public override bool CanResetValue(object component)
        {
            throw new NotImplementedException();
        }

        public override Type ComponentType { get; }

        public override object GetValue(object component)
        {
            DataRow row = (DataRow) component;
            return row[Name];
        }

        public override bool IsReadOnly => false;

        public override Type PropertyType { get; }

        public override void ResetValue(object component)
        {
            throw new NotImplementedException();
        }

        public override void SetValue(object component, object value)
        {
            DataRow row = (DataRow) component;
            row[Name] = value;
        }

        public override bool ShouldSerializeValue(object component)
        {
            throw new NotImplementedException();
        }
    }
}