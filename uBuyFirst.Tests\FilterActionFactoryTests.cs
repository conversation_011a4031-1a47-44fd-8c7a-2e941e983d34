﻿using Microsoft.VisualStudio.TestTools.UnitTesting;
using uBuyFirst.Filters;


namespace uBuyFirst.Tests
{
    [TestClass]
    public class FilterActionFactoryTests
    {
        [TestMethod]
        public void CreateAction_FormatCellsIdentifier_ReturnsFormatCellsActionInstance()
        {
            // Arrange
            string actionIdentifier = "FORMAT_CELLS"; // Using literal as FormatCellsAction.IDENTIFIER accessibility is not guaranteed

            // Act
            IFilterAction resultAction = FilterActionFactory.CreateAction(actionIdentifier);

            // Assert
            Assert.IsNotNull(resultAction, "Action should not be null.");
            Assert.IsInstanceOfType(resultAction, typeof(FormatCellsAction), "Action should be of type FormatCellsAction.");
            Assert.AreEqual("FORMAT_CELLS", resultAction.ActionTypeIdentifier, "ActionTypeIdentifier should match.");
            Assert.AreEqual("Format cells", resultAction.DisplayName, "DisplayName mismatch.");
        }
[TestMethod]
        public void CreateAction_InvalidIdentifier_ReturnsNull()
        {
            // Arrange
            string actionIdentifier = "INVALID_ACTION_ID";

            // Act
            IFilterAction resultAction = FilterActionFactory.CreateAction(actionIdentifier);

            // Assert
            Assert.IsNull(resultAction, "Action should be null for an invalid identifier.");
        }

        [TestMethod]
        public void CreateFromLegacyAction_RemoveRows_ReturnsRemoveRowsAction()
        {
            // Arrange
            string legacyActionString = "Remove rows";

            // Act
            IFilterAction resultAction = FilterActionFactory.CreateFromLegacyAction(legacyActionString);

            // Assert
            Assert.IsNotNull(resultAction, "Action should not be null.");
            Assert.IsInstanceOfType(resultAction, typeof(RemoveRowsAction), "Action should be of type RemoveRowsAction.");
            Assert.AreEqual(RemoveRowsAction.IDENTIFIER, resultAction.ActionTypeIdentifier, "ActionTypeIdentifier should match RemoveRowsAction.IDENTIFIER.");
            Assert.AreEqual("Remove rows", resultAction.DisplayName, "DisplayName mismatch.");
        }

        [TestMethod]
        public void CreateFromLegacyAction_BuyWithSpecificUser_ReturnsBuyActionAndSetsUsername()
        {
            // Arrange
            string legacyActionString = "Buy with AnotherTestUser";
            string expectedUsername = "AnotherTestUser";

            // Act
            IFilterAction resultAction = FilterActionFactory.CreateFromLegacyAction(legacyActionString);

            // Assert
            Assert.IsNotNull(resultAction, "Action should not be null.");
            Assert.IsInstanceOfType(resultAction, typeof(BuyWithAccountAction), "Action should be of type BuyWithAccountAction.");
            
            BuyWithAccountAction buyAction = resultAction as BuyWithAccountAction;
            Assert.IsNotNull(buyAction, "Casting to BuyWithAccountAction failed.");
            Assert.AreEqual(expectedUsername, buyAction.AccountUsername, "AccountUsername should match the extracted username.");
            Assert.AreEqual(BuyWithAccountAction.IDENTIFIER, resultAction.ActionTypeIdentifier, "ActionTypeIdentifier should match BuyWithAccountAction.IDENTIFIER.");
        }

        [TestMethod]
        public void CreateFromLegacyAction_InvalidLegacyString_ReturnsNull()
        {
            // Arrange
            string legacyActionString = "NonExistentLegacyAction";

            // Act
            IFilterAction resultAction = FilterActionFactory.CreateFromLegacyAction(legacyActionString);

            // Assert
            Assert.IsNull(resultAction, "Action should be null for an invalid legacy string.");
        }

        [TestMethod]
        public void CreateFromLegacyAction_EmptyString_ReturnsNull()
        {
            // Arrange
            string legacyActionString = "";

            // Act
            IFilterAction resultAction = FilterActionFactory.CreateFromLegacyAction(legacyActionString);

            // Assert
            Assert.IsNull(resultAction, "Action should be null for an empty legacy string.");
        }

        [TestMethod]
        public void CreateFromLegacyAction_NullString_ReturnsNull()
        {
            // Arrange
            string legacyActionString = null;

            // Act
            IFilterAction resultAction = FilterActionFactory.CreateFromLegacyAction(legacyActionString);

            // Assert
            Assert.IsNull(resultAction, "Action should be null for a null legacy string.");
        }
    }
}
