﻿using eBay.Service.Call;
using eBay.Service.Core.Sdk;
using eBay.Service.Core.Soap;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Forms;
using uBuyFirst.Other;
using uBuyFirst.Tools;
using ShippingDetailsType = eBay.Service.Core.Soap.ShippingDetailsType;

namespace uBuyFirst.Network
{
    public class ApiService
    {
        private readonly ApiContext _apiContext;

        public ApiService(ApiContext apiContext)
        {
            _apiContext = apiContext;
        }

        public async Task<ShippingDetailsType> GetItemShipping(string itemID, int quantity, CountryCodeType destinationCountry, string zip)
        {
            if (string.IsNullOrEmpty(zip))
                return null;

            Stat.GetItemShippingCounter++;
            var apicall = new GetItemShippingCall(_apiContext);
            apicall.QuantitySold = quantity;
            apicall.DestinationCountryCode = destinationCountry;

            try
            {
                return await Task.Run(() => apicall.GetItemShipping(itemID, zip)).ConfigureAwait(true);
            }
            catch (Exception ex)
            {
                if (ex.Message == "Invalid Destination or PostalCode.")
                    throw;
            }

            return null;
        }

        public async Task<UserType> GetUser()
        {
            var getUser = new GetUserCall(_apiContext);
            getUser.DetailLevelList = new DetailLevelCodeTypeCollection(new[] { DetailLevelCodeType.ReturnAll });
            var user = await Task.Run(() =>
            {
                try
                {
                    return getUser.GetUser();
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                }

                return null;
            });
            return user;
        }

        public static async Task<ItemType> GetItemSafe(string itemID, ApiContext apiContext, bool downloadDescription = false)
        {
            try
            {
                return await GetItemUnsafe(itemID, apiContext, downloadDescription);
            }
            catch (Exception ex)
            {
                Loggers.LogError($"GetItemSafe, ID: {itemID}  {ex.Message}");
            }

            return null;
        }

        public static async Task<ItemType> GetItemUnsafe(string itemID, ApiContext apiContext, bool downloadDescription = false)
        {
            var detailLevel = downloadDescription ? DetailLevelCodeType.ReturnAll : DetailLevelCodeType.ItemReturnAttributes;

            var call = new GetItemCall(apiContext)
            {
                Site = apiContext.Site,
                IncludeItemSpecifics = true,
                DetailLevelList = new DetailLevelCodeTypeCollection { detailLevel },
                ApiRequest = { EndUserIP = $"202.201.{new Random().Next(1, 200)}.{new Random().Next(1, 200)}" } //cache breaker
            };
            var theItem = await Task.Run(() => call.GetItem(itemID));
            return theItem;
        }

        public CategoryTypeCollection DownloadCategories(SiteCodeType siteCodeType)
        {
            var call = new GetCategoriesCall
            {
                Site = siteCodeType,
                EnableCompression = false,
                ApiContext = _apiContext,
                DetailLevelList = new DetailLevelCodeTypeCollection { DetailLevelCodeType.ReturnAll }
            };
            var categories = call.GetCategories();
            return categories;
        }

        public static async Task<UserType?> GetSellerCountry(string sellerName, ApiContext apiContext)
        {
            var getUser = new GetUserCall(apiContext);
            var user = await Task.Run(() =>
            {
                try
                {
                    return getUser.GetUser(sellerName);
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                }

                return null;
            });
            return user;
        }
        public static List<string> GetAllWatchlistItemIds(ApiContext apiContext)
        {
            var watchlistItemIds = new List<string>();
            var pageNumber = 1;
            var morePages = true;
            var entriesPerPage = 200; // Maximum is 200

            while (morePages)
            {
                var pagination = new PaginationType
                {
                    PageNumber = pageNumber,
                    EntriesPerPage = entriesPerPage
                };

                var apiCall = new GetMyeBayBuyingCall(apiContext);
                apiCall.WatchList = new ItemListCustomizationType
                {
                    Include = true, IncludeNotes = true, Pagination = pagination
                };


                // Make the API call
                apiCall.Execute();

                if (apiCall.WatchListReturn != null && apiCall.WatchListReturn.ItemArray != null)
                {
                    foreach (ItemType item in apiCall.WatchListReturn.ItemArray)
                    {
                        watchlistItemIds.Add(item.ItemID);
                    }

                    // Check if there are more pages
                    morePages = (apiCall.WatchListReturn.PaginationResult.TotalNumberOfPages > pageNumber);
                    pageNumber++;
                }
                else
                {
                    morePages = false;
                }
            }

            return watchlistItemIds;
        }
    }
}
