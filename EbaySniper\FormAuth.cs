﻿using System;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Net;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraBars.Ribbon;
using DevExpress.XtraEditors;
using eBay.Service.Call;
using eBay.Service.Core.Sdk;
using eBay.Service.Core.Soap;
using uBuyFirst.Auth;
using uBuyFirst.GUI;
using uBuyFirst.Network;
using uBuyFirst.Prefs;
using uBuyFirst.Properties;
using uBuyFirst.Purchasing.OrderApi;
using uBuyFirst.Tools;

namespace uBuyFirst
{
    public partial class FormAuth : RibbonForm
    {
        private string _sessionID;
        private string _token;
        private readonly ApiContext _apiContext;
        public EbayAccount EbayAccount;
        private readonly Authenticator _authenticator;

        public FormAuth(EbayAccount ebayAccount, Authenticator authenticator)
        {
            InitializeComponent();
            _authenticator = authenticator;
            _apiContext = ConnectionConfig.GetAuthenticatedApiContext(SiteCodeType.US);
            _sessionID = "";
            if (ebayAccount == null)
                EbayAccount = new EbayAccount();
            else
            {
                _token = ebayAccount.TokenPo;
                EbayAccount = ebayAccount;
                if (!string.IsNullOrEmpty(ebayAccount.BrowserName))
                    lblValBrowser.Text = ebayAccount.BrowserName;
            }

            if (Program.Sandbox)
            {
                LoadShippingAddress();
                dxValidationProvider1.Validate();
            }
        }

        private void FormAuth_Load(object sender, EventArgs e)
        {
            //AutoMeasurement.Client.TrackScreenView("Screen - " + Text);
            boxCustomColumnSite.Properties.Items.AddRange(Intl.CountryProvider.GetEbaySiteNameList());

            tabPageShippingOptions.PageVisible = Program.Sandbox;
        }

        private async void FormAuth_Shown(object sender, EventArgs e)
        {
            try
            {
                lblValSessionStatus.BackColor = Color.LightGoldenrodYellow;
                lblStatusCurrent.Text = En_US.FormAuth_FormAuth_Shown_Verifying_token_status___;
                lblValSessionStatus.Text = En_US.FormAuth_FormAuth_Shown_Establishing_connection_with_eBay_server___;

                var gsc = new GetSessionIDCall(_apiContext);
                try
                {
                    _sessionID = await Task.Run(() => gsc.GetSessionID(_apiContext.RuName));
                }
                catch (Exception ex)
                {
                    Form1.Log.Error("{0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, ex);
                }

                if (string.IsNullOrEmpty(_sessionID) || _sessionID.Length <= 10 || _sessionID.Length >= 300)
                {
                    try
                    {
                        _sessionID = await Task.Run(() => gsc.GetSessionID(_apiContext.RuName));
                    }
                    catch (Exception ex)
                    {
                        Form1.Log.Error("{0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, ex);
                    }
                }

                if (string.IsNullOrEmpty(_sessionID) || _sessionID.Length <= 10 || _sessionID.Length >= 300)
                {
                    XtraMessageBox.Show(this, En_US.Cant_get_session_id_for_ebay_auth);
                    //MessageBox.Show(En_US.Can_t_get_session_id_for_ebay_auth);
                    Form1.Log.Error("{0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, "Error getting Session ID - " + _sessionID);
                }

                lblStatusCurrent.Text = await GetTokenDetails();
                if (string.IsNullOrEmpty(_sessionID))
                {
                    lblValSessionStatus.Text = En_US.FormAuth_FormAuth_Shown_Connection_failed__Couldn_t_fetch_session_ID_;

                    return;
                }

                lblValSessionStatus.Text = En_US.FormAuth_FormAuth_Shown_Connection_established_;
                lblValSessionStatus.BackColor = DefaultBackColor;
                btnxEbayDoAuth.Enabled = true;
                btnxEbayDoAuth.Font = new Font(btnxEbayDoAuth.Font.FontFamily, btnxEbayDoAuth.Font.Size, FontStyle.Bold);
                if (Program.Sandbox && !string.IsNullOrEmpty(_authenticator.GetEBayAccount()?.UserName))
                {
                    lblStatusCurrent.Text = "User: " + _authenticator.GetEBayAccount()?.UserName;
                }
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("FormAuth_Shown: ", ex);
            }
        }

        private void btnEbayDoAuth_Click(object sender, EventArgs e)
        {
            if (boxCustomColumnSite.Text == "Select")
            {
                XtraMessageBox.Show(this, En_US.FormAuth_btnEbayDoAuth_Click_Please__choose_your_eBay_website);

                return;
            }

            if (Program.Sandbox)
            {
                OAuthStart();

                return;
            }

            AuthAuthStart();
        }

        private void OAuthStart()
        {
            try
            {
                Intl.EBaySite ebaysite = Intl.CountryProvider.GetEbaySite(boxCustomColumnSite.Text);
                var authUrl = Authenticator.GetOAuthUrl(ebaysite);
                Browser.LaunchBrowser(EbayAccount?.BrowserPath, authUrl);

                btnConfirmAuth.Enabled = true;
                btnConfirmAuth.Font = new Font(btnConfirmAuth.Font.FontFamily, btnConfirmAuth.Font.Size, FontStyle.Bold);
                btnxEbayDoAuth.Enabled = false;
                btnxEbayDoAuth.Font = new Font(btnxEbayDoAuth.Font.FontFamily, btnxEbayDoAuth.Font.Size, FontStyle.Regular);
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("btnEbayDoAuth_Click: ", ex);
            }
        }

        private async Task ConfirmOAuth()
        {
            try
            {
                btnConfirmAuth.Enabled = false;
                btnConfirmAuth.Text = @"Confirming...";
                _authenticator.ConfirmOAuth();

                if (!_authenticator.IsOAuthTokenValid())
                {
                    XtraMessageBox.Show(this, En_US.FormAuth_btnEbayAuthConfirm_Click_2);
                    Form1.Log.Error("{0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, "Error getting OAuth token - " + _token);
                    btnConfirmAuth.Enabled = true;
                    btnConfirmAuth.Text = "Confirm";

                    return;
                }

                EbayAccount = _authenticator.GetEBayAccount();
                var ebaysite = Intl.CountryProvider.GetEbaySite(boxCustomColumnSite.Text);
                var apiService = new ApiService(ConnectionConfig.GetApiContextPlaceOffer(ebaysite.SiteCode, _authenticator.GetOAuthUserToken()));
                var user = await apiService.GetUser();
                if (user?.UserID != null)
                    EbayAccount.UserName = user.UserID;
                lblStatusCurrent.Text = "User:  " + EbayAccount.UserName;

                if (string.IsNullOrEmpty(EbayAccount.BrowserName))
                {
                    EbayAccount.BrowserName = lblValBrowser.Text;
                }

                if (dxValidationProvider1.Validate())
                    btnSaveAuthToken.Enabled = true;
                btnConfirmAuth.Enabled = true;
                btnSaveAuthToken.Font = new Font(btnSaveAuthToken.Font.FontFamily, btnSaveAuthToken.Font.Size, FontStyle.Bold);
                btnConfirmAuth.Font = new Font(btnConfirmAuth.Font.FontFamily, btnConfirmAuth.Font.Size, FontStyle.Regular);

                if (string.IsNullOrEmpty(lciAddressLine1.Control.Text)
                    && string.IsNullOrEmpty(lciAddressLine2.Control.Text)
                    && string.IsNullOrEmpty(lciCity.Control.Text)
                    && string.IsNullOrEmpty(lciPostalCode.Control.Text)
                    && string.IsNullOrEmpty(lciPhone.Control.Text)
                    && string.IsNullOrEmpty(lciFullName.Control.Text)
                    && string.IsNullOrEmpty(lciState.Control.Text))
                {
                    lciAddressLine1.Control.Text = "Street one";
                    lciAddressLine2.Control.Text = "Street two";
                    lciCity.Control.Text = "New York";
                    //lciCountry.Control.Text = EbayAccount.ShippingAddress.country;
                    lciPostalCode.Control.Text = "10001";
                    lciPhone.Control.Text = "************";
                    lciFullName.Control.Text = "John Doe";
                    lciState.Control.Text = "New York";
                    /*
                    var shippingAddress = user.ShippingAddress;                
                    lciAddressLine1.Control.Text = shippingAddress.Street1;
                    lciAddressLine2.Control.Text = user.ShippingAddress.Street2;
                    lciCity.Control.Text = user.ShippingAddress.CityName;
                    //lciCountry.Control.Text = EbayAccount.ShippingAddress.country;
                    lciPostalCode.Control.Text = user.ShippingAddress.PostalCode;
                    lciPhone.Control.Text = user.ShippingAddress.Phone;
                    lciFullName.Control.Text = user.ShippingAddress.Name;
                    lciState.Control.Text = user.ShippingAddress.StateOrProvince;
                    */
                }
            }
            catch (Exception ex)
            {
                Form1.Log.Error("{0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, ex);
                XtraMessageBox.Show(this, @"Authentication response:
"
                                          + ex.Message);
            }

            btnConfirmAuth.Enabled = true;
            btnConfirmAuth.Text = "Confirm";
        }

        private void AuthAuthStart()
        {
            try
            {
                if (boxCustomColumnSite.Text == @"Select")
                {
                    XtraMessageBox.Show(this, En_US.FormAuth_btnEbayDoAuth_Click_Please__choose_your_eBay_website);

                    return;
                }

                Intl.EBaySite ebaysite = Intl.CountryProvider.GetEbaySite(boxCustomColumnSite.Text);

                if (ebaysite == null)
                    return;

                var authUrl = "https://signin." + ebaysite.Domain + "/ws/eBayISAPI.dll?SignIn&runame=" + _apiContext.RuName + "&SessID=" + WebUtility.UrlEncode(_sessionID);
                Browser.LaunchBrowser(EbayAccount?.BrowserPath, authUrl);
                btnConfirmAuth.Enabled = true;
                btnConfirmAuth.Font = new Font(btnConfirmAuth.Font.FontFamily, btnConfirmAuth.Font.Size, FontStyle.Bold);
                btnxEbayDoAuth.Enabled = false;
                btnxEbayDoAuth.Font = new Font(btnxEbayDoAuth.Font.FontFamily, btnxEbayDoAuth.Font.Size, FontStyle.Regular);
            }
            catch (Exception ex)
            {
                ExM.ubuyExceptionHandler("btnEbayDoAuth_Click: ", ex);
            }
        }

        private async void btnEbayAuthConfirm_Click(object sender, EventArgs e)
        {
            if (Program.Sandbox)
            {
                await ConfirmOAuth();

                return;
            }

            await ConfirmAuthNAuth();
        }

        private async Task ConfirmAuthNAuth()
        {
            try
            {
                if (string.IsNullOrEmpty(_sessionID))
                {
                    XtraMessageBox.Show(this, En_US.FormAuth_btnEbayAuthConfirm_Click_);

                    return;
                }

                btnConfirmAuth.Enabled = false;
                btnConfirmAuth.Text = @"Confirming...";
                var ftc = new FetchTokenCall(_apiContext);
                _token = ftc.FetchToken(_sessionID);
                if (!string.IsNullOrEmpty(_token))
                {
                    EbayAccount.TokenPo = _token;
                }
                else
                {
                    XtraMessageBox.Show(this, En_US.FormAuth_btnEbayAuthConfirm_Click_2);
                    Form1.Log.Error("{0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, "Error getting Auth token - " + _token);
                    _token = "";
                }

                lblStatusCurrent.Text = await GetTokenDetails();
                if (string.IsNullOrEmpty(EbayAccount.BrowserName))
                {
                    EbayAccount.BrowserName = lblValBrowser.Text;
                }
            }
            catch (Exception ex)
            {
                Form1.Log.Error("{0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, ex);
                XtraMessageBox.Show(this, @"Authentication response:
"
                                          + ex.Message);
            }

            btnConfirmAuth.Enabled = true;
            btnConfirmAuth.Text = @"Confirm";
        }

        private async Task<string> GetTokenDetails()
        {
            try
            {
                if (!string.IsNullOrEmpty(_token))
                {
                    _apiContext.ApiCredential.eBayToken = _token;

                    GetTokenStatusCall getTokenStatus = new GetTokenStatusCall(_apiContext);
                    var tokenStatus = getTokenStatus.GetTokenStatus();
                    if (tokenStatus.Status == TokenStatusCodeType.Active)
                    {
                        var getUser = new GetUserCall(_apiContext);
                        var user = await Task.Run(() =>
                        {
                            try
                            {
                                return getUser.GetUser();
                            }
                            catch (Exception ex)
                            {
                                XtraMessageBox.Show(this, ex.Message);
                            }

                            return null;
                        });
                        EbayAccount.TokenPo = _token;
                        if (user?.UserID != null)
                            EbayAccount.UserName = user.UserID;
                        btnSaveAuthToken.Enabled = true;
                        btnSaveAuthToken.Font = new Font(btnSaveAuthToken.Font.FontFamily, btnSaveAuthToken.Font.Size, FontStyle.Bold);
                        btnConfirmAuth.Enabled = false;
                        btnConfirmAuth.Font = new Font(btnConfirmAuth.Font.FontFamily, btnConfirmAuth.Font.Size, FontStyle.Regular);
                    }
                    else
                    {
                        btnConfirmAuth.Enabled = true;
                    }

                    return "User:  " + EbayAccount.UserName + "\n" + "Token is " + tokenStatus.Status + ". Expiration: " + tokenStatus.ExpirationTime.ToString("yyyy MMMM dd") + "\nClick Save button";
                }

                return "Not authenticated";
            }
            catch (Exception ex)
            {
                Form1.Log.Error("{0}, {1} Exception:{2}", ProgramState.UBFVersion, ProgramState.HWID + ProgramState.SerialNumber, ex);

                return "Not authenticated";
            }
        }

        private void btnChooseBrowser_Click(object sender, EventArgs e)
        {
            openFileDialog1.FileName = "";
            if (openFileDialog1.ShowDialog() == DialogResult.OK)
            {
                try
                {
                    string fileLocation = openFileDialog1.FileName;
                    if (File.Exists(fileLocation))
                    {
                        string fileNameWithoutExtension = Path.GetFileNameWithoutExtension(fileLocation);
                        if (fileNameWithoutExtension != null)
                        {
                            var fileName = "<" + fileNameWithoutExtension.Substring(0, 1).ToUpper() + fileNameWithoutExtension.Substring(1, fileNameWithoutExtension.Length - 1) + ">";
                            lblValBrowser.Text = fileName;
                            EbayAccount.BrowserName = fileName;
                            EbayAccount.BrowserPath = fileLocation;
                        }
                    }
                }
                catch (Exception ex)
                {
                    ExM.ubuyExceptionHandler("btnChooseBrowser_Click: ", ex);
                }
            }
        }

        private void hyperlinkLabelControl1_Click(object sender, EventArgs e)
        {
            var hyperLink = (HyperlinkLabelControl)sender;
            if (hyperLink.Tag != null)
                Process.Start(hyperLink.Tag.ToString());
        }

        private void FormAuth_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == 27)
            {
                Close();
            }
        }

        private void BtnClose(object sender, EventArgs e)
        {
            if (Program.Sandbox)
                SaveShippingAddress();
            Close();
        }

        private void SaveShippingAddress()
        {
            var ebayAcc = _authenticator.GetEBayAccount();
            if (ebayAcc.ShippingAddress == null)
            {
                EbayAccount.ShippingAddress = new ShippingAddress();
            }

            if (ebayAcc.ShippingAddress != null)
            {
                ebayAcc.ShippingAddress.addressLine1 = lciAddressLine1.Control.Text;
                ebayAcc.ShippingAddress.addressLine2 = lciAddressLine2.Control.Text;
                ebayAcc.ShippingAddress.city = lciCity.Control.Text;
                ebayAcc.ShippingAddress.country = lciCountry.Control.Text;
                ebayAcc.ShippingAddress.postalCode = lciPostalCode.Control.Text;
                ebayAcc.ShippingAddress.phoneNumber = lciPhone.Control.Text;
                ebayAcc.ShippingAddress.recipient = lciFullName.Control.Text;
                ebayAcc.ShippingAddress.stateOrProvince = lciState.Control.Text;
            }
        }

        private void LoadShippingAddress()
        {
            var ebayAccount = _authenticator.GetEBayAccount();

            if (ebayAccount?.ShippingAddress == null)
                return;

            lciAddressLine1.Control.Text = ebayAccount.ShippingAddress.addressLine1;
            lciAddressLine2.Control.Text = ebayAccount.ShippingAddress.addressLine2;
            lciCity.Control.Text = ebayAccount.ShippingAddress.city;
            //lciCountry.Control.Text = EbayAccount.ShippingAddress.country;
            lciPostalCode.Control.Text = ebayAccount.ShippingAddress.postalCode;
            lciPhone.Control.Text = ebayAccount.ShippingAddress.phoneNumber;
            lciFullName.Control.Text = ebayAccount.ShippingAddress.recipient;
            lciState.Control.Text = ebayAccount.ShippingAddress.stateOrProvince;
        }

        private void dxValidationProvider1_ValidationSucceeded(object sender, DevExpress.XtraEditors.DXErrorProvider.ValidationSucceededEventArgs e)
        {
            var invalidControls = dxValidationProvider1.GetInvalidControls();
            if (invalidControls.Count > 0)
            {
                btnSaveAuthToken.Enabled = false;
            }
            else
            {
                btnSaveAuthToken.Enabled = true;
            }
        }

        private void dxValidationProvider1_ValidationFailed(object sender, DevExpress.XtraEditors.DXErrorProvider.ValidationFailedEventArgs e)
        {
            var invalidControls = dxValidationProvider1.GetInvalidControls();
            if (invalidControls.Count > 0)
            {
                btnSaveAuthToken.Enabled = false;
            }
            else
            {
                btnSaveAuthToken.Enabled = true;
            }
        }

        private void ValidateOnTextChange(object sender, EventArgs e)
        {
            if (Program.Sandbox)
                dxValidationProvider1.Validate();
        }
    }
}
