﻿using System;
using DevExpress.XtraGrid.Views.BandedGrid;
using uBuyFirst.GUI;
using Timer = System.Timers.Timer;

namespace uBuyFirst.Grid
{
    internal static class TopRowFocus
    {
        public static readonly Timer FocusTimer = new Timer(50);
        public static double TopRowFocusInterval = 60;
        public static AdvBandedGridView GridView { get; set; }
        public static int RowHandle { get; set; }
        public static DateTimeOffset LastFocusedTime;

        public static void FocusTopRow()
        {
            FocusTopRow(GridView, RowHandle);
        }

        public static void FocusTopRow(AdvBandedGridView grView, int rowHandle)
        {
            try
            {
                if (
                    //ProgramState.Idlesw.Elapsed.TotalSeconds < TopRowFocusInterval 
                    LastFocusedTime.AddSeconds(TopRowFocusInterval) > DateTimeOffset.UtcNow && grView.SelectedRowsCount >= 1)
                {
                    return;
                }

                if (grView.RowCount == 0)
                    return;
                if (FocusRouter.FocusedGridView == null)
                    FocusRouter.FocusedGridView = grView;
                if (FocusRouter.FocusedGridView != grView)
                    return;
                if (grView.GroupCount > 0)
                {
                    if (grView.IsGroupRow(grView.GetParentRowHandle(rowHandle)))
                        if (grView.GetRowExpanded(grView.GetParentRowHandle(rowHandle)))
                        {
                            grView.FocusedRowHandle = rowHandle;
                            grView.ClearSelection();
                            grView.SelectRow(grView.FocusedRowHandle);
                        }
                }
                else
                {
                    grView.FocusedRowHandle = 0;
                    grView.ClearSelection();
                    grView.SelectRow(grView.FocusedRowHandle);
                }
            }
            catch (Exception)
            {
                // ignored
            }
        }
    }
}