﻿using System;
using System.Diagnostics.CodeAnalysis;
using System.Drawing;
using System.Drawing.Imaging;
using eBay.Service.Core.Soap;

namespace uBuyFirst.Images
{
    internal static class ImageProcessor
    {
        public static Image ScaleImage(Image imageToScale, int maxWidth, int maxHeight)
        {
            double ratioX = (double)maxWidth / imageToScale.Width;
            double ratioY = (double)maxHeight / imageToScale.Height;
            double ratio = Math.Min(ratioX, ratioY);

            int newWidth = (int)(imageToScale.Width * ratio);
            int newHeight = (int)(imageToScale.Height * ratio);

            Image newImage = new Bitmap(newWidth, newHeight);
            Graphics.FromImage(newImage).DrawImage(imageToScale, 0, 0, newWidth, newHeight);
            return newImage;
        }

        [SuppressMessage("ReSharper", "RedundantAssignment")]
        [SuppressMessage("ReSharper", "NotAccessedVariable")]
        public static Image ResizeImage(Image image, int maximumWidth, int maximumHeight, bool enforceRatio, bool addPadding)
        {
            var encoderParameters = new EncoderParameters(1);
            encoderParameters.Param[0] = new EncoderParameter(Encoder.Quality, 100L);
            var canvasWidth = maximumWidth;
            var canvasHeight = maximumHeight;
            var newImageWidth = maximumWidth;
            var newImageHeight = maximumHeight;
            var xPosition = 0;
            var yPosition = 0;

            if (enforceRatio)
            {
                var ratioX = maximumWidth / (double)image.Width;
                var ratioY = maximumHeight / (double)image.Height;
                var ratio = ratioX < ratioY ? ratioX : ratioY;
                newImageHeight = (int)(image.Height * ratio);
                newImageWidth = (int)(image.Width * ratio);

                if (addPadding)
                {
                    xPosition = (int)((maximumWidth - image.Width * ratio) / 2);
                    yPosition = (int)((maximumHeight - image.Height * ratio) / 2);
                }
                else
                {
                    canvasWidth = newImageWidth;
                    canvasHeight = newImageHeight;
                }
            }

            return image.GetThumbnailImage(canvasWidth, canvasHeight, null, IntPtr.Zero);
        }

        public static void FixBlurryImages(PictureDetailsType? theItemPictureDetails)
        {
            if (theItemPictureDetails?.PictureURL == null)
            {
                return;
            }

            for (int i = 0; i < theItemPictureDetails.PictureURL.Count; i++)
            {
                if (theItemPictureDetails.PictureURL[i].Contains("$_0.")
                    || theItemPictureDetails.PictureURL[i].Contains("$_1.")
                    || theItemPictureDetails.PictureURL[i].Contains("$_2.")
                    || theItemPictureDetails.PictureURL[i].Contains("$_3."))
                    theItemPictureDetails.PictureURL[i] = theItemPictureDetails.PictureURL[i].Replace("$_0.", "$_10.");
            }

            for (int i = 0; i < theItemPictureDetails.Any.Count; i++)
            {
                if (theItemPictureDetails.Any[i].InnerText.Contains("$_0.")
                    || theItemPictureDetails.Any[i].InnerText.Contains("$_1.")
                    || theItemPictureDetails.Any[i].InnerText.Contains("$_2.")
                    || theItemPictureDetails.Any[i].InnerText.Contains("$_3."))
                    theItemPictureDetails.Any[i].InnerText = theItemPictureDetails.Any[i].InnerText.Replace("$_0.", "$_10.");
            }
        }
    }
}
