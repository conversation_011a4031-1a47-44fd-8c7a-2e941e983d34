﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;
using eBay.Service.Core.Soap;
using uBuyFirst.Data;

namespace uBuyFirst.SubSearch {
    internal static class SubSearch {
        public static async Task<bool> IsSubSearchMatch(Keyword2Find keyword2Find, DataRow row, DataList datalist)
        {
            var isSubSearchMatch = false;

            foreach (var subSearch in keyword2Find.ChildrenCore.Where(term => term.Enabled))
            {
                if (!IsPriceMatches(keyword2Find, datalist, subSearch))
                    continue;

                if (!IsCategoryMatch(datalist, subSearch))
                    continue;

                if (!IsConditionMatch(datalist, subSearch))
                    continue;

                var isDescriptionMatch = await IsDescriptionMatch(datalist, subSearch);
                if (!isDescriptionMatch)
                    continue;

                if (!IsSubFilterMatch(row, datalist, subSearch))
                    continue;

                isSubSearchMatch = true;

                break;
            }

            return isSubSearchMatch;
        }

        private static bool IsSubFilterMatch(DataRow row, DataList datalist, ChildTerm subSearch)
        {
            var subFilterEmpty = string.IsNullOrEmpty(subSearch.SubSearch?.FilterCriteria?.ToString());
            var subFilterMatch = false;
            if (!subFilterEmpty)
            {
                subFilterMatch = subSearch.SubSearch.GetEvaluator().Fit(row);
            }

            if (!subFilterEmpty && !subFilterMatch)
                return false;

            datalist.SubSearch = subSearch.Alias;
            row["Sub Search"] = subSearch.Alias;
            return true;
        }

        private static async Task<bool> IsDescriptionMatch(DataList datalist, ChildTerm subSearch)
        {
            var itemSpecificsList = new List<string>();
            foreach (NameValueListType specific in datalist.ItemSpecifics)
            {
                if (specific.Value != null)
                {
                    itemSpecificsList.Add(string.Join(" | ", specific.Value.ToArray()));
                }
            }
            itemSpecificsList.AddRange(datalist.Model.Split(','));
            itemSpecificsList.AddRange(datalist.MPN.Split(','));
            itemSpecificsList.AddRange(datalist.UPC.Split(','));
            itemSpecificsList.AddRange(datalist.ProductReferenceID.Split(','));

            var itemSpecificsStr = string.Join(" ", itemSpecificsList);
            string description;
            if (datalist is { Row: not null })
            {
                if (datalist.Row["Description"] == DBNull.Value)
                    description = "";
                else
                    description = datalist.Row["Description"].ToString();
                var isDescriptionMatch = await Task.Run(() => subSearch.IsMatch(datalist.Title, $"{description} {itemSpecificsStr}")).ConfigureAwait(true);
                return isDescriptionMatch;
            }

            return false;
        }

        private static bool IsConditionMatch(DataList datalist, ChildTerm subSearch)
        {
            var conditionMatched = true;
            if (subSearch.Condition.Length > 0 && !string.IsNullOrEmpty(subSearch.Condition[0]))
            {
                conditionMatched = subSearch.Condition.Contains(datalist.ConditionID);
            }

            return conditionMatched;
        }

        private static bool IsCategoryMatch(DataList datalist, ChildTerm subSearch)
        {
            var categoryMatched = true;
            if (subSearch.CategoryIDs.Length > 0 && !string.IsNullOrEmpty(subSearch.CategoryIDs[0]))
            {
                categoryMatched = false;
                foreach (var categoryID in subSearch.CategoryIDs)
                {
                    var datalistCategoryID = datalist.CategoryID;
                    if (datalistCategoryID != null && datalistCategoryID.Contains(categoryID))
                    {
                        categoryMatched = true;
                        break;
                    }
                }
            }

            return categoryMatched;
        }

        private static bool IsPriceMatches(Keyword2Find keyword2Find, DataList datalist, ChildTerm subSearch)
        {
            var minPriceOk = datalist.ItemPricing.ItemPrice.Value >= subSearch.PriceMin;
            var maxPriceOk = datalist.ItemPricing.ItemPrice.Value <= subSearch.PriceMax;
            var priceMatches = minPriceOk && maxPriceOk;

            if (priceMatches)
                return true;

            var isAuction = keyword2Find.ListingType.Contains(ListingType.AuctionsStartedNow) || keyword2Find.ListingType.Contains(ListingType.AuctionsEndingNow);
            if (!isAuction)
                return false;

            var subSearchMinPriceOk = datalist.AuctionPrice >= subSearch.PriceMin;
            var subSearchMaxPriceOk = datalist.AuctionPrice <= subSearch.PriceMax;
            priceMatches = subSearchMinPriceOk && subSearchMaxPriceOk;

            return priceMatches;
        }
    }
}
