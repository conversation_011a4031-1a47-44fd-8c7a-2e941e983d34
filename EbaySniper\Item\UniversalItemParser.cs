﻿using System.Linq;
using uBuyFirst.Data;

namespace uBuyFirst.Item
{
    static class UniversalItemParser
    {
        public static bool IsPriceZero(DataList datalist, Keyword2Find keyword2Find)
        {
            if (datalist.ItemPricing.ItemPrice.Value.Equals(0.0)
                && !keyword2Find.ListingType.Contains(ListingType.AuctionsStartedNow)
                && !keyword2Find.ListingType.Contains(ListingType.AuctionsEndingNow))
            {
                return true;
            }

            return false;
        }
    }
}
