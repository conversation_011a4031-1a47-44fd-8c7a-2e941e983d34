﻿using System.Collections.Concurrent;

namespace uBuyFirst.Services.Caching // Corrected namespace
{
    /// <summary>
    /// Provides an in-memory implementation of the ICacheService using ConcurrentDictionary.
    /// </summary>
    /// <typeparam name="TKey">The type of the cache key.</typeparam>
    /// <typeparam name="TValue">The type of the cached value.</typeparam>
    public class InMemoryCacheService<TKey, TValue> : ICacheService<TKey, TValue>
    {
        private readonly ConcurrentDictionary<TKey, TValue> _cache = new ConcurrentDictionary<TKey, TValue>();

        /// <inheritdoc />
        public bool TryGetValue(TKey key, out TValue value)
        {
            return _cache.TryGetValue(key, out value);
        }

        /// <inheritdoc />
        public void Set(TKey key, TValue value)
        {
            // Using AddOrUpdate ensures thread safety for both adding and updating.
            _cache.AddOrUpdate(key, value, (k, oldValue) => value);
        }

        /// <inheritdoc />
        public void Clear()
        {
            _cache.Clear();
        }

        /// <inheritdoc />
        public System.Collections.Generic.IEnumerable<System.Collections.Generic.KeyValuePair<TKey, TValue>> GetAllEntries()
        {
            // ConcurrentDictionary implements IEnumerable<KeyValuePair<TKey, TValue>>
            return _cache;
        }

        // Example implementation for GetOrAdd if needed:
        // public TValue GetOrAdd(TKey key, Func<TKey, TValue> valueFactory)
        // {
        //     return _cache.GetOrAdd(key, valueFactory);
        // }

        // Example implementation for TryRemove if needed:
        // public bool TryRemove(TKey key, out TValue value)
        // {
        //     return _cache.TryRemove(key, out value);
        // }
    }
}
