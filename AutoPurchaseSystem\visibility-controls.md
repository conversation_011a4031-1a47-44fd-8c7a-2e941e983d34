# AutoPurchaseSystem Visibility Controls

## Overview

The AutoPurchaseSystem includes comprehensive visibility controls that allow the system to be completely hidden from users when not needed. This is controlled by a single configuration flag that affects multiple UI components.

## Configuration Flag

### ConnectionConfig.AutoPurchaseSystemEnabled

- **Type**: `static bool`
- **Default Value**: `false`
- **Location**: `EbaySniper\Prefs\ConnectionConfig.cs`
- **Server Configuration**: Can be set via JSON from server configuration

```csharp
public static bool AutoPurchaseSystemEnabled { get; set; } = false;
```

## Controlled Components

When `AutoPurchaseSystemEnabled = false`, the following components are hidden/disabled:

### 1. TreeList Columns

Three columns in the keyword treelist are hidden:

- **JobId**: Job identifier for AutoPurchase system
- **RequiredQuantity**: Required quantity for AutoPurchase system  
- **PurchasedQuantity**: Purchased quantity for AutoPurchase system

**Implementation**: `Form1.cs` - `UpdateAutoPurchaseColumnsVisibility()` method

### 2. Filter Actions

The Restock filter action is not available in the XFilter system:

- **RestockFilterAction**: Not registered in FilterActionFactory
- **RestockFilterUIConfigurator**: Not registered in FilterActionUIRegistry

**Implementation**: `FilterActions.cs` - Conditional registration in `Initialize()` method

## Implementation Details

### Column Visibility Control

```csharp
private void UpdateAutoPurchaseColumnsVisibility()
{
    bool isEnabled = ConnectionConfig.AutoPurchaseSystemEnabled;
    
    colJobId.Visible = isEnabled;
    colRequiredQuantity.Visible = isEnabled;
    colPurchasedQuantity.Visible = isEnabled;
}
```

### Filter Action Registration Control

```csharp
public static void Initialize(ITelegramSender telegramSender)
{
    // ... other registrations ...
    
    // Only register Restock action if AutoPurchaseSystem is enabled
    if (ConnectionConfig.AutoPurchaseSystemEnabled)
    {
        RegisterAction<RestockFilterAction>();
        RegisterLegacyMigration("Restock", RestockFilterAction.IDENTIFIER);
    }
}
```

### Dynamic Updates

The system supports dynamic updates when the configuration changes:

```csharp
public static void Reinitialize(ITelegramSender telegramSender)
{
    ClearRegistrations();
    Initialize(telegramSender);
}
```

## JSON Configuration

The flag can be set via server configuration JSON:

```json
{
  "AutoPurchaseSystemEnabled": true
}
```

**Parsing Logic** (with error handling):

```csharp
try
{
    AutoPurchaseSystemEnabled = json.AutoPurchaseSystemEnabled ?? false;
}
catch
{
    AutoPurchaseSystemEnabled = false;
}
```

## Testing

Comprehensive unit tests cover all visibility control functionality:

### Test Classes

1. **ConnectionConfigAutoPurchaseTests**: Tests the configuration flag
2. **AutoPurchaseColumnVisibilityTests**: Tests column visibility logic
3. **FilterActionRegistrationTests**: Tests conditional filter action registration
4. **Integration Tests**: End-to-end scenarios

### Key Test Scenarios

- Default value verification
- JSON parsing with various inputs
- Column visibility state changes
- Filter action registration/unregistration
- Dynamic configuration updates

## Usage Guidelines

### For Administrators

1. **Enable AutoPurchaseSystem**: Set `AutoPurchaseSystemEnabled = true` in server configuration
2. **Disable AutoPurchaseSystem**: Set `AutoPurchaseSystemEnabled = false` or omit from configuration

### For Developers

1. **Adding New Components**: Check `ConnectionConfig.AutoPurchaseSystemEnabled` before showing AutoPurchase-related UI
2. **Testing**: Use the provided test classes as examples for testing visibility controls
3. **Configuration Changes**: Call appropriate `Reinitialize()` methods when configuration changes

## Backward Compatibility

- **Default Behavior**: System is hidden by default (`false`)
- **Existing Installations**: Will continue to work with AutoPurchase features hidden
- **Gradual Rollout**: Can be enabled selectively via server configuration

## Security Considerations

- **Feature Gating**: Prevents unauthorized access to AutoPurchase functionality
- **Clean UI**: Users only see features they have access to
- **Server Control**: Centralized control via server configuration

## Troubleshooting

### Common Issues

1. **Columns Not Showing**: Verify `AutoPurchaseSystemEnabled = true`
2. **Restock Action Missing**: Check FilterActionFactory initialization
3. **Configuration Not Loading**: Verify JSON parsing and server response

### Debug Steps

1. Check `ConnectionConfig.AutoPurchaseSystemEnabled` value
2. Verify `Form1_Shown` calls `InitializeAutoPurchaseSystemVisibility()`
3. Confirm FilterActionFactory initialization occurs after ConnectionConfig loading
4. Review test results for similar scenarios

## Future Enhancements

- **User-Level Controls**: Per-user visibility settings
- **Role-Based Access**: Integration with user role system
- **Audit Logging**: Track when visibility settings change
- **UI Indicators**: Show when AutoPurchase features are available
