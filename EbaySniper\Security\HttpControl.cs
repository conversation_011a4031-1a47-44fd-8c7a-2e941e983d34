﻿using System;
using System.Diagnostics;
using System.IO;
using System.Net;
using System.Text;

namespace uBuyFirst.Security
{
    public struct ProxySettings
    {
        public bool UseProxy;
        public string ProxyAddress;
        public string ProxyUsername;
        public string ProxyPassword;

        public ProxySettings(string address, string username, string password) : this()
        {
            UseProxy = true;
            ProxyAddress = address;
            ProxyUsername = username;
            ProxyPassword = password;
        }
    }

    public abstract class PostPackageBuilder
    {
        private string _data;
        private bool _firstVariable;

        /// <summary>
        /// The actual string that can be used as the data of the post request.
        /// </summary>
        public string PostDataString => _data;

        protected PostPackageBuilder()
        {
            _firstVariable = false;
            _data = "";
        }

        /// <summary>
        /// Add a variable to the post packet.
        /// </summary>
        /// <param name="postVariableName">The variable name that will be posted.</param>
        /// <param name="postVariableValue">The value of the variable.</param>
        public void AddVariable(string postVariableName, string postVariableValue)
        {
            _data += (_firstVariable ? "" : "&") + postVariableName + "=" + postVariableValue;
            _firstVariable = false;
        }
    }

    public class HttpControl
    {
        // The cookie container allows us to maintain a session with PHP.
        private readonly CookieContainer _cookies;

        public HttpControl()
        {
            _cookies = new CookieContainer();
        }

        /// <summary>
        /// Send a GET request to a web page. Returns the contents of the page.
        /// </summary>
        /// <param name="url">The address to GET.</param>
        /// <param name="settings">Proxy Settings</param>
        private string Get(string url, ProxySettings settings)
        {
            if (url == null)
            {
                return "";
            }

            var request = (HttpWebRequest) WebRequest.Create(url);

            // Use a proxy
            if (settings.UseProxy)
            {
                var myProxy = new WebProxy();
                var newUri = new Uri(settings.ProxyAddress);

                myProxy.Address = newUri;
                myProxy.Credentials = new NetworkCredential(settings.ProxyUsername, settings.ProxyPassword);
                request.Proxy = myProxy;
            }

            request.Method = "GET";
            request.CookieContainer = _cookies;
            var response = request.GetResponse();
            var stream = response.GetResponseStream();
            if (stream != null)
            {
                var sr = new StreamReader(stream, Encoding.UTF8);
                var result = sr.ReadToEnd();
                sr.Close();
                response.Close();

                return result;
            }

            return "";
        }

        /// <summary>
        /// Send a GET request to a web page. Returns the contents of the page.
        /// </summary>
        /// <param name="url">The address to GET.</param>
        public string Get(string url)
        {
            var settings = new ProxySettings
            {
                UseProxy = false
            };
            return Get(url, settings);
        }

        /// <summary>
        /// Send a POST request to a web page. Returns the contents of the page.
        /// </summary>
        /// <param name="url">The address to POST to.</param>
        /// <param name="postVars">The list of variables to POST to the server.</param>
        /// <param name="settings"></param>
        public string Post(string url, PostPackageBuilder postVars, ProxySettings settings)
        {
            return Post(url, postVars.PostDataString, settings);
        }

        /// <summary>
        /// Send a POST request to a web page. Returns the contents of the page.
        /// </summary>
        /// <param name="url">The address to POST to.</param>
        /// <param name="postVars">The list of variables to POST to the server.</param>
        public string Post(string url, PostPackageBuilder postVars)
        {
            var settings = new ProxySettings
            {
                UseProxy = false
            };
            return Post(url, postVars.PostDataString, settings);
        }

        /// <summary>
        /// Send a POST request to a web page. Returns the contents of the page.
        /// </summary>
        /// <param name="url">The address to POST to.</param>
        /// <param name="data">The data to POST.</param>
        public string Post(string url, string data)
        {
            var settings = new ProxySettings
            {
                UseProxy = false
            };
            return Post(url, data, settings);
        }

        /// <summary>
        /// Send a POST request to a web page. Returns the contents of the page.
        /// </summary>
        /// <param name="url">The address to POST to.</param>
        /// <param name="data">The data to POST.</param>
        /// <param name="settings"></param>
        private string Post(string url, string data, ProxySettings settings)
        {
            for (var i = 0; i < 1; i++)
            {
                try
                {
                    var buffer = Encoding.ASCII.GetBytes(data);
                    //HttpWebRequest request = (HttpWebRequest)WebRequest.Create(url+"&t="+DateTime.Now.Ticks);
                    var request = (HttpWebRequest) WebRequest.Create(url);
                    // Use a proxy
                    if (settings.UseProxy)
                    {
                        var myProxy = new WebProxy();

                        var newUri = new Uri(settings.ProxyAddress);
                        myProxy.Address = newUri;

                        myProxy.Credentials = new NetworkCredential(settings.ProxyUsername, settings.ProxyPassword);
                        request.Proxy = myProxy;
                    }

                    // Send request
                    request.Timeout = 30 * 1000;
                    request.Method = "POST";
                    request.ContentType = "application/x-www-form-urlencoded";
                    request.ContentLength = buffer.Length;
                    request.CookieContainer = _cookies;
                    var postData = request.GetRequestStream();
                    postData.Write(buffer, 0, buffer.Length);
                    postData.Close();

                    // Get and return response
                    var response = (HttpWebResponse) request.GetResponse();
                    var responseStream = response.GetResponseStream();
                    if (responseStream == null)
                        return "ERROR: Cannot reach host";

                    string responsetxt;
                    using (var streamReader = new StreamReader(responseStream))
                    {
                        responsetxt = streamReader.ReadToEnd();
                    }

                    return responsetxt;
                }
                catch (Exception ex)
                {
                    Debug.WriteLine("ERROR: " + ex.Message);
                }
            }

            return "ERROR: Cannot reach host";
        }
    }
}