﻿using System;
using System.Collections.Generic;
using System.Data;
using eBay.Service.Core.Soap;
using uBuyFirst.API.ShoppingAPI;
using uBuyFirst.Data;
using uBuyFirst.Parsing;
using uBuyFirst.Time;
using uBuyFirst.Views;

namespace uBuyFirst.Search.Status
{
    internal static class RowStatusUpdater
    {
        private const int LivePeriod = 30;

        public static void RefreshRowsStatus()
        {
            foreach (var view in ResultsView.ViewsDict)
            {
                if (view.Value.DataSource is not DataTable datatable)
                    continue;

                var i = 0;
                while (i < datatable.Rows.Count)
                {
                    var row = datatable.Rows[i];

                    // Skip if row is deleted or detached
                    if (row.RowState is DataRowState.Deleted or DataRowState.Detached)
                    {
                        i++;
                        continue;
                    }

                    // Skip if 'Blob' column is DBNull
                    if (row["Blob"] == DBNull.Value)
                    {
                        i++;
                        continue;
                    }

                    var d = (DataList)row["Blob"];

                    SetUpdatedToActive(d);
                    SetActiveToUnknown(d);
                    SetUnknownToActive(d);

                    if (d.EndTime.HasValue)
                    {
                        DateParser.SetEndTime_Timeleft(d, (DateTimeOffset)d.EndTime);
                        if (row.RowState != DataRowState.Deleted && row.RowState != DataRowState.Detached && row["Time Left"] != DBNull.Value)
                        {
                            row["Time Left"] = d.TimeLeft;
                            if (d.ItemStatus != Data.ItemStatus.Sold && d.ItemStatus != Data.ItemStatus.Ended && d.TimeLeft.TotalSeconds < 0)
                            {
                                GetItemsStatus.MarkItemCompleted(d).ConfigureAwait(true);
                            }
                        }
                    }

                    i++;
                }
            }
        }

        public static Data.ItemStatus SetItemStatus(ItemType theItem)
        {
            switch (theItem.SellingStatus.ListingStatus)
            {
                case ListingStatusCodeType.Active:
                    return Data.ItemStatus.Active;

                case ListingStatusCodeType.Ended:
                case ListingStatusCodeType.Completed:

                    if (theItem.ListingDetails.EndingReasonSpecified)
                        return Data.ItemStatus.Ended;

                    if (theItem.SellingStatus.QuantitySold == 0)
                        return Data.ItemStatus.Ended;
                    return Data.ItemStatus.Sold;

            }

            return Data.ItemStatus.Unknown;
        }

        public static Data.ItemStatus SetItemStatus(ShoppingAPIJson.SimpleItemType theItem)
        {
            switch (theItem.ListingStatus)
            {
                case "Active":
                    return Data.ItemStatus.Active;
                case "Ended":
                case "Completed":
                    if (theItem.QuantitySold == 0)
                        return Data.ItemStatus.Ended;
                    return Data.ItemStatus.Sold;
            }

            return Data.ItemStatus.Unknown;
        }

        private static void SetUnknownToActive(DataList dataList)
        {
            if (dataList.ItemStatus == Data.ItemStatus.Active)
                return;

            if (dataList.ItemStatus != Data.ItemStatus.Active && dataList.ItemStatus != Data.ItemStatus.Unknown && dataList.ItemStatus != Data.ItemStatus.Updated)
                return;

            if (dataList.TimeSinceChecked < LivePeriod)
            {
                dataList.SetStatus(Data.ItemStatus.Active);
            }
        }

        private static void SetUpdatedToActive(DataList d)
        {
            if (d.ItemStatus != Data.ItemStatus.Updated)
            {
                return;
            }

            if (d.TimeSinceChecked <= LivePeriod)
            {
                return;
            }

            if (d.SecondsLeft is > 5)
                d.SetStatus(Data.ItemStatus.Active);
        }

        private static void SetActiveToUnknown(DataList d)
        {
            if (d.ItemStatus != Data.ItemStatus.Active || d.FoundTime == null)
                return;

            if (d.TimeSinceFound <= 60)
                return;

            if (d.TimeSinceChecked < LivePeriod)
                return;

            if (d.SecondsLeft is <= 5)
                return;

            d.SetStatus(Data.ItemStatus.Unknown);
        }

        public static void SetStatusActive(List<string> itemIDs)
        {
            try
            {
                foreach (var view in ResultsView.ViewsDict)
                {
                    var datatable = (DataTable)view.Value.DataSource;
                    var i = 0;
                    while (i < datatable?.Rows.Count)
                    {
                        var row = datatable.Rows[i];
                        if (row.RowState is DataRowState.Deleted or DataRowState.Detached)
                        {
                            i++;
                            continue;
                        }

                        var dataList = (DataList)row["Blob"];
                        if (dataList.ItemStatus != Data.ItemStatus.Active && dataList.ItemStatus != Data.ItemStatus.Unknown && dataList.ItemStatus != Data.ItemStatus.Updated)
                        {
                            i++;

                            continue;
                        }

                        if (itemIDs.Contains(dataList.ItemID))
                        {
                            dataList.LastStatusCheck = new DateTimeWithDiff { TimeDiff = 0, Utc = DateTime.UtcNow };
                        }

                        i++;
                    }
                }
            }
            catch (Exception)
            {
                // ignored
            }
        }
    }
}
