﻿using System;
using System.ComponentModel;
using System.Reflection;

[assembly: Obfuscation(Exclude = false, Feature = "preset(minimum);" + "+anti debug;" + "+anti dump;" + "+anti ildasm;" + "+anti tamper(key=dynamic);" + "+constants;" + "+ctrl flow;" + "+invalid metadata;" + "+ref proxy;" + "-rename;")]
[assembly: Obfuscation(Exclude = false, Feature = "generate debug symbol: true")]

namespace uBuyFirst.CustomClasses
{
    [Serializable]
    public class CustomBindingList<T> : BindingList<T>
    {
        public event EventHandler<T> ItemDeleting;

        protected override void RemoveItem(int index)
        {
            var item = this[index];
            ItemDeleting?.Invoke(this, item);
            base.RemoveItem(index);
        }
    }
}